<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛠️ Lucid Extension Debug Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #007bff, #28a745);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        .section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section h2 {
            color: #2c3e50;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .console-type {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
        .background-console {
            border-left-color: #dc3545;
        }
        .content-console {
            border-left-color: #28a745;
        }
        .command {
            background: #2d3748;
            color: #68d391;
            padding: 12px 16px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            margin: 8px 0;
            cursor: pointer;
            position: relative;
        }
        .command:hover {
            background: #4a5568;
        }
        .command::after {
            content: '📋 点击复制';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #a0aec0;
        }
        .step-list {
            counter-reset: step-counter;
        }
        .step {
            counter-increment: step-counter;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            position: relative;
            padding-left: 50px;
        }
        .step::before {
            content: counter(step-counter);
            position: absolute;
            left: 15px;
            top: 15px;
            background: #007bff;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .tab-selector {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 20px;
        }
        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        .tab.active {
            border-bottom-color: #007bff;
            color: #007bff;
            font-weight: bold;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛠️ Lucid Extension Debug Guide</h1>
        <p>完整的浏览器扩展调试工具使用指南</p>
    </div>

    <div class="warning">
        <strong>⚠️ 重要提示:</strong> 浏览器扩展有两个不同的JavaScript环境：
        <ul>
            <li><strong>Background Script</strong> - 扩展的后台服务</li>
            <li><strong>Content Script</strong> - 注入到网页中的脚本</li>
        </ul>
        不同的调试工具需要在对应的控制台中使用！
    </div>

    <div class="section">
        <div class="tab-selector">
            <div class="tab active" onclick="switchTab('background')">Background Script 调试</div>
            <div class="tab" onclick="switchTab('content')">Content Script 调试</div>
            <div class="tab" onclick="switchTab('setup')">快速设置</div>
        </div>

        <div id="background-tab" class="tab-content active">
            <div class="console-type background-console">
                <h3>🔴 Background Script 控制台</h3>
                <p><strong>如何打开:</strong> Chrome Extensions页面 → 找到扩展 → 点击"service worker"链接</p>
                <p><strong>适用场景:</strong> 测试扩展后台功能、翻译API、消息传递</p>
            </div>

            <h3>可用命令:</h3>
            
            <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
LucidBackgroundDebug.getStatus()
            </div>
            <p>查看Background Script状态和扩展信息</p>

            <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
LucidBackgroundDebug.testContentConnection()
            </div>
            <p>测试与当前标签页的Content Script连接</p>

            <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
LucidBackgroundDebug.getAllTabs()
            </div>
            <p>获取所有浏览器标签页信息</p>

            <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
LucidBackgroundDebug.testTranslateRequest()
            </div>
            <p>直接测试翻译API请求</p>

            <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
LucidBackgroundDebug.sendToContent({type: "PING", test: true})
            </div>
            <p>发送自定义消息到Content Script</p>
        </div>

        <div id="content-tab" class="tab-content">
            <div class="console-type content-console">
                <h3>🟢 Content Script 控制台</h3>
                <p><strong>如何打开:</strong> 在任意网页上按F12 → Console标签</p>
                <p><strong>适用场景:</strong> 测试页面翻译、Mock翻译、UI交互</p>
            </div>

            <div class="warning">
                <strong>注意:</strong> 如果LucidDebug未定义，请先运行快速设置命令启用调试工具
            </div>

            <h3>可用命令:</h3>
            
            <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
LucidDebug.getStatus()
            </div>
            <p>查看Content Script状态和Mock翻译状态</p>

            <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
LucidDebug.enableMock()
            </div>
            <p>启用Mock翻译模式（需要刷新页面生效）</p>

            <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
LucidDebug.testTranslate("Hello World")
            </div>
            <p>测试翻译功能</p>

            <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
LucidDebug.enableDebugLog()
            </div>
            <p>启用详细的调试日志</p>

            <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
clearTranslationCache()
            </div>
            <p>清除翻译缓存</p>

            <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
getCacheStats()
            </div>
            <p>查看翻译缓存统计信息</p>
        </div>

        <div id="setup-tab" class="tab-content">
            <h3>🚀 快速设置步骤</h3>

            <div class="step-list">
                <div class="step">
                    <strong>重新加载扩展</strong><br>
                    访问 <code>chrome://extensions/</code> → 找到Lucid扩展 → 点击刷新按钮
                </div>

                <div class="step">
                    <strong>打开Background Script控制台</strong><br>
                    在扩展页面点击 "service worker" 链接，打开Background Script控制台
                </div>

                <div class="step">
                    <strong>测试Background Script调试工具</strong><br>
                    在Background控制台中运行：
                    <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
LucidBackgroundDebug.getStatus()
                    </div>
                </div>

                <div class="step">
                    <strong>打开任意网页</strong><br>
                    访问任意网站（如 google.com），打开网页的开发者工具控制台
                </div>

                <div class="step">
                    <strong>启用Content Script调试工具</strong><br>
                    如果LucidDebug未定义，运行：
                    <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
localStorage.setItem("lucid-force-debug", "true")
                    </div>
                    然后刷新页面
                </div>

                <div class="step">
                    <strong>测试Content Script调试工具</strong><br>
                    运行：
                    <div class="command" onclick="copyCommand(this.textContent.replace('📋 点击复制', '').trim())">
LucidDebug.getStatus()
                    </div>
                </div>
            </div>

            <div class="success">
                <strong>✅ 设置成功标志:</strong>
                <ul>
                    <li>Background控制台显示: "🛠️ Background Script调试工具已就绪"</li>
                    <li>Content控制台显示: "🛠️ 扩展调试工具已就绪"</li>
                    <li>两个控制台的调试命令都能正常执行</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🔧 常见问题解决</h2>
        
        <h3>Q: LucidDebug is not defined</h3>
        <p><strong>A:</strong> 这通常发生在Content Script控制台中。解决方法：</p>
        <ol>
            <li>确保在网页控制台（不是Background Script控制台）中运行</li>
            <li>运行 <code>localStorage.setItem("lucid-force-debug", "true")</code></li>
            <li>刷新页面</li>
        </ol>

        <h3>Q: LucidBackgroundDebug is not defined</h3>
        <p><strong>A:</strong> 这发生在Background Script控制台中。解决方法：</p>
        <ol>
            <li>确保在Background Script控制台中运行（点击"service worker"链接）</li>
            <li>重新加载扩展</li>
        </ol>

        <h3>Q: localStorage is not defined</h3>
        <p><strong>A:</strong> 这表示您在Background Script控制台中运行了Content Script命令。Background Script中没有localStorage，请使用对应的Background调试工具。</p>
    </div>

    <script>
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活选中的标签
            event.target.classList.add('active');
        }

        function copyCommand(command) {
            navigator.clipboard.writeText(command).then(() => {
                // 创建临时提示
                const notification = document.createElement('div');
                notification.textContent = '✅ 已复制到剪贴板';
                notification.style = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #28a745;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 5px;
                    z-index: 1000;
                    font-size: 14px;
                `;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 2000);
                
                console.log('📋 已复制命令:', command);
            }).catch(err => {
                console.error('复制失败:', err);
            });
        }

        // 页面加载时显示欢迎信息
        window.addEventListener('load', () => {
            console.log('🛠️ Lucid Extension Debug Guide 已加载');
            console.log('💡 点击页面中的命令可以快速复制到剪贴板');
        });
    </script>
</body>
</html>