{"name": "wxt-react-starter", "description": "manifest.json description", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest watch"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@stagewise-plugins/react": "^0.6.2", "@stagewise/toolbar-react": "^0.6.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/chrome": "^0.0.332", "@types/jsdom": "^21.1.7", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "@wxt-dev/module-react": "^1.1.3", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "msw": "^2.10.3", "typescript": "^5.8.3", "vitest": "^3.2.4", "wxt": "^0.20.6"}}