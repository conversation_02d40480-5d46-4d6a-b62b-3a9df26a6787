<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LucidDebug Quick Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .setup-box {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .setup-button {
            background: #007bff;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .setup-button:hover {
            background: #0056b3;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .command-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: left;
            margin: 20px 0;
        }
        .command-list code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="setup-box">
        <h1>🛠️ LucidDebug 快速设置</h1>
        <p>如果 LucidDebug 对象未定义，使用此页面快速启用调试工具。</p>
        
        <div id="status" class="status info">
            等待检查扩展状态...
        </div>

        <button class="setup-button" onclick="forceEnableDebug()">强制启用调试工具</button>
        <button class="setup-button" onclick="checkDebugStatus()">检查调试状态</button>
        <button class="setup-button" onclick="reloadExtension()">重新加载扩展</button>

        <div class="command-list">
            <h3>🧪 启用后可用的命令：</h3>
            <ul>
                <li><code>LucidDebug.getStatus()</code> - 查看状态</li>
                <li><code>LucidDebug.enableMock()</code> - 启用Mock翻译</li>
                <li><code>LucidDebug.testTranslate("Hello")</code> - 测试翻译</li>
                <li><code>LucidDebug.enableDebugLog()</code> - 启用调试日志</li>
            </ul>
        </div>

        <div class="command-list">
            <h3>🔧 手动启用方法：</h3>
            <p>在控制台中运行：</p>
            <code>localStorage.setItem("lucid-force-debug", "true")</code>
            <br><br>
            <p>然后刷新页面</p>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function forceEnableDebug() {
            updateStatus('正在强制启用调试工具...', 'info');
            
            // 设置所有可能的调试标志
            localStorage.setItem('lucid-force-debug', 'true');
            localStorage.setItem('lucid-debug', 'true');
            localStorage.setItem('lucid-force-mock', 'true');
            
            console.log('🚀 调试标志已设置:');
            console.log('  - lucid-force-debug: true');
            console.log('  - lucid-debug: true');
            console.log('  - lucid-force-mock: true');
            
            // 等待一秒后检查状态
            setTimeout(() => {
                if (typeof window.LucidDebug !== 'undefined') {
                    updateStatus('✅ 调试工具已成功启用！可以使用 LucidDebug 对象', 'success');
                } else {
                    updateStatus('⚠️ 调试工具标志已设置，请刷新页面或重新加载扩展', 'info');
                    
                    // 尝试手动触发扩展内容脚本重载
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                }
            }, 1000);
        }

        function checkDebugStatus() {
            updateStatus('正在检查调试状态...', 'info');
            
            const debugFlags = {
                'lucid-force-debug': localStorage.getItem('lucid-force-debug'),
                'lucid-debug': localStorage.getItem('lucid-debug'),
                'lucid-force-mock': localStorage.getItem('lucid-force-mock')
            };
            
            const hasLucidDebug = typeof window.LucidDebug !== 'undefined';
            
            console.log('🔍 调试状态检查:');
            console.log('LocalStorage 标志:', debugFlags);
            console.log('LucidDebug 对象存在:', hasLucidDebug);
            
            if (hasLucidDebug) {
                updateStatus('✅ LucidDebug 对象可用！', 'success');
                
                // 测试调试对象
                try {
                    const status = window.LucidDebug.getStatus();
                    console.log('📊 LucidDebug 状态:', status);
                } catch (error) {
                    console.error('❌ LucidDebug 测试失败:', error);
                }
            } else {
                const enabledFlags = Object.entries(debugFlags).filter(([key, value]) => value === 'true');
                if (enabledFlags.length > 0) {
                    updateStatus(`⚠️ 调试标志已设置但 LucidDebug 不可用。已启用: ${enabledFlags.map(([key]) => key).join(', ')}`, 'error');
                } else {
                    updateStatus('❌ 调试工具未启用，点击"强制启用调试工具"', 'error');
                }
            }
        }

        function reloadExtension() {
            updateStatus('正在尝试重新加载扩展...', 'info');
            
            // 发送消息给background script尝试重载
            if (chrome && chrome.runtime) {
                chrome.runtime.sendMessage({
                    type: 'PING',
                    timestamp: Date.now(),
                    purpose: 'extension-reload-test'
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        updateStatus('❌ 无法连接到扩展background script，请手动重新加载扩展', 'error');
                        console.error('Extension reload failed:', chrome.runtime.lastError);
                    } else {
                        updateStatus('✅ 扩展连接正常，请刷新此页面', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    }
                });
            } else {
                updateStatus('❌ Chrome runtime API 不可用', 'error');
            }
        }

        // 页面加载时自动检查状态
        window.addEventListener('load', () => {
            setTimeout(checkDebugStatus, 1000);
        });
    </script>
</body>
</html>