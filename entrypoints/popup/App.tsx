import { useState } from "react";
// Logo imports removed - not used in the component
import "./App.css";
import { DynamicTooltip } from "../../src/components/DynamicTooltip/DynamicTooltip";
import { StagewiseToolbar } from "@stagewise/toolbar-react";
import ReactPlugin from "@stagewise-plugins/react";

function App() {
  const [count, setCount] = useState(0);
  const [clickLog, setClickLog] = useState<string[]>([]);

  // 清除偏好数据
  const clearPreferences = () => {
    localStorage.removeItem("lucid-word-preferences");
    setClickLog(["偏好数据已清空"]);
  };

  // 清除翻译缓存
  const clearTranslationCache = async () => {
    try {
      // 通过content script清除缓存
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });
      if (tab.id) {
        chrome.tabs.sendMessage(
          tab.id,
          { type: "CLEAR_TRANSLATION_CACHE" },
          (response) => {
            if (response?.success) {
              setClickLog(["✅ 翻译缓存已清除！"]);
            } else {
              setClickLog([
                "❌ 清除缓存失败: " + (response?.error || "未知错误"),
              ]);
            }
          }
        );
      }
    } catch (error) {
      setClickLog(["❌ 清除缓存失败: " + (error as Error).message]);
    }
  };

  return (
    <>
      {/* Stagewise Toolbar - 仅在开发模式下显示 */}
      {import.meta.env.DEV && (
        <StagewiseToolbar
          config={{
            plugins: [ReactPlugin],
          }}
        />
      )}

      <div style={{ padding: "10px", minWidth: "350px" }}>
        <h2>Interactive Tooltip Test</h2>

        <div
          style={{
            margin: "15px 0",
            padding: "10px",
            border: "1px dashed #ccc",
            borderRadius: "4px",
          }}
        >
          <p>测试交互式Tooltip：</p>
          <div style={{ fontSize: "14px", lineHeight: "1.8" }}>
            The word <DynamicTooltip word="hello" interactive={true} /> is very
            common.
          </div>
        </div>

        <div style={{ margin: "10px 0", display: "flex", gap: "10px" }}>
          <button
            onClick={clearPreferences}
            style={{
              padding: "5px 10px",
              background: "#dc2626",
              color: "white",
              border: "none",
              borderRadius: "3px",
              fontSize: "12px",
              cursor: "pointer",
            }}
          >
            清空偏好
          </button>

          <button
            onClick={clearTranslationCache}
            style={{
              padding: "5px 10px",
              background: "#2563eb",
              color: "white",
              border: "none",
              borderRadius: "3px",
              fontSize: "12px",
              cursor: "pointer",
            }}
          >
            🔧 清除翻译缓存
          </button>
        </div>

        <div style={{ fontSize: "12px", maxHeight: "100px", overflow: "auto" }}>
          <strong>功能验证：</strong>
          <ul style={{ margin: "5px 0", paddingLeft: "15px" }}>
            <li>✨ Hover高亮效果</li>
            <li>👆 独立点击功能</li>
            <li>💾 偏好记录</li>
            <li>🔄 自动排序</li>
          </ul>
        </div>

        <div className="card">
          <button onClick={() => setCount((count) => count + 1)}>
            count is {count}
          </button>
        </div>
      </div>
    </>
  );
}

export default App;
