#!/bin/bash

# 同步当前开发环境到主目录的脚本
# 这将保留当前目录的所有配置和改进

set -e

CURRENT_DIR="/Users/<USER>/Workspace/tools/extension/lucid-group/lucid-ext-translation"
MAIN_DIR="/Users/<USER>/Workspace/tools/extension/lucid-group/lucid-ext"
BACKUP_DIR="/Users/<USER>/Workspace/tools/extension/lucid-group/lucid-ext-backup-$(date +%Y%m%d_%H%M%S)"

echo "🔄 开始同步开发环境到主目录..."

# 1. 备份原主目录
if [ -d "$MAIN_DIR" ]; then
    echo "📦 备份原主目录到: $BACKUP_DIR"
    cp -r "$MAIN_DIR" "$BACKUP_DIR"
fi

# 2. 重要文件和目录列表
IMPORTANT_ITEMS=(
    # 核心配置
    "package.json"
    "tsconfig.json" 
    "vitest.config.ts"
    "wxt.config.ts"
    "web-ext.config.ts"
    
    # 源代码
    "src/"
    "entrypoints/"
    "public/"
    
    # 配置和文档
    "CLAUDE.md"
    ".claude/"
    ".promptx/"
    ".serena/"
    "docs/"
    
    # 开发工具
    "scripts/"
    "tests/"
    "*.html"
    "*.md"
    
    # 重要的忽略文件
    ".gitignore"
)

# 3. 创建同步函数
sync_item() {
    local item="$1"
    local source="$CURRENT_DIR/$item"
    local target="$MAIN_DIR/$item"
    
    if [ -e "$source" ]; then
        echo "📋 同步: $item"
        # 确保目标目录存在
        mkdir -p "$(dirname "$target")"
        # 复制文件或目录
        cp -r "$source" "$target"
    fi
}

# 4. 确保主目录存在
mkdir -p "$MAIN_DIR"

# 5. 同步Git仓库状态
if [ -d "$CURRENT_DIR/.git" ]; then
    echo "📍 同步Git状态..."
    cp -r "$CURRENT_DIR/.git" "$MAIN_DIR/"
fi

# 6. 同步所有重要项目
for item in "${IMPORTANT_ITEMS[@]}"; do
    # 处理通配符
    if [[ "$item" == *"*"* ]]; then
        for file in $CURRENT_DIR/$item; do
            if [ -e "$file" ]; then
                relative_path="${file#$CURRENT_DIR/}"
                sync_item "$relative_path"
            fi
        done
    else
        sync_item "$item"
    fi
done

echo "✅ 同步完成!"
echo "📁 主目录: $MAIN_DIR"
echo "💾 备份目录: $BACKUP_DIR"
echo ""
echo "🎯 下一步操作:"
echo "cd $MAIN_DIR"
echo "git status"
echo "pnpm install"