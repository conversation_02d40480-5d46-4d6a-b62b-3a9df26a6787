# 🌐 在任何网站启用Mock翻译

## 🎯 问题分析

刚才在 `blog.google` 网站测试时，系统检测到这是生产环境，因此使用了真实的Google翻译API（导致CORS错误）。

现在已增强Mock翻译系统，支持在任何网站手动启用Mock翻译。

## 🛠️ 启用Mock翻译的3种方法

### 方法1: 快速启用（推荐）
在任何网站的控制台运行：
```javascript
localStorage.setItem('lucid-force-mock', 'true')
console.log('🧪 Mock翻译已启用，刷新页面生效')
```

### 方法2: 调试模式启用
```javascript
localStorage.setItem('lucid-debug', 'true')
console.log('🔧 调试模式已启用，包含Mock翻译')
```

### 方法3: 传统方式
```javascript
localStorage.setItem('lucid-use-mock', 'true')
console.log('✅ Mock翻译已设置')
```

## 🚀 测试步骤

### 1. 重新加载扩展
在Chrome扩展管理页面刷新扩展

### 2. 在当前网站启用Mock翻译
在控制台运行：
```javascript
localStorage.setItem('lucid-force-mock', 'true')
location.reload()  // 刷新页面
```

### 3. 验证Mock翻译已启用
页面刷新后，在控制台应该看到：
```
🧪 Mock translation service loaded for testing
🧪 Auto-detected testing environment, using Mock translation service
```

### 4. 测试右键翻译
1. 选择页面上的英文文字
2. 右键 → "打开 Lucid 设置"
3. 应该看到：
   - 控制台显示: "Using Mock translation service for right-click translation"
   - 绿色成功通知，不是红色CORS错误

## 🧪 验证Mock翻译工作

成功启用后应该看到：
```javascript
// 控制台日志应显示：
📄 [INFO] Using Mock translation service for right-click translation
📄 [INFO] Mock翻译成功 {text: "hello", translation: "你好", engine: "mock"}
```

而不是：
```javascript
// 避免这些CORS错误：
❌ Access to fetch at 'https://translate.googleapis.com' blocked by CORS
❌ POST https://translate.googleapis.com net::ERR_FAILED
```

## 🎛️ 控制Mock翻译

### 禁用Mock翻译
```javascript
localStorage.removeItem('lucid-force-mock')
localStorage.removeItem('lucid-debug')
localStorage.removeItem('lucid-use-mock')
location.reload()
```

### 检查当前状态
```javascript
console.log('Mock翻译状态:', {
  forceMock: localStorage.getItem('lucid-force-mock'),
  debug: localStorage.getItem('lucid-debug'),
  useMock: localStorage.getItem('lucid-use-mock'),
  mockAvailable: !!window.mockTranslate
})
```

## 🌟 优势

现在可以在任何网站测试DOM注入系统：
- ✅ 新闻网站 (BBC, CNN)
- ✅ 技术博客 (Medium, Dev.to)  
- ✅ 文档网站 (MDN, GitHub)
- ✅ 电商网站 (Amazon)
- ✅ 社交媒体 (Twitter, LinkedIn)

**无需担心CORS限制，完整测试所有功能！** 🚀

## 🔄 下次测试流程

1. 访问任何想测试的网站
2. 控制台运行: `localStorage.setItem('lucid-force-mock', 'true')`
3. 刷新页面
4. 开始测试翻译功能

这样就可以在真实网站上测试DOM注入系统的完整功能了！