# 翻译系统分类规则

## 概述
本文档定义了浏览器扩展中DOM元素的翻译行为规则，明确指定哪些元素被排除显示"ooo"标记，哪些元素进行翻译并显示原文。

## 🎯 翻译目标元素（显示原文）

### 主要内容元素
- **标题元素**: `h1`, `h2`, `h3`, `h4`, `h5`, `h6`
- **段落元素**: `p`
- **容器元素**: `div` (新增，用于支持header等区域)
- **列表项**: `li`
- **表格单元格**: `td`, `th`
- **引用块**: `blockquote`
- **图片说明**: `figcaption`
- **显式标记**: `[data-translatable]`

### 特殊支持区域
- **Header区域**: 现已支持，包括网站标题、导航文本等
- **品牌标识**: `div.uni-header__site-title` 及其子元素
- **链接文本**: `a` 标签内的文本内容

### 翻译行为
- Mock模式下显示：**原文本身**
- 目的：测试DOM注入效果和样式覆盖
- 样式：应用翻译样式但保持原文可读性

## ❌ 排除元素（显示"ooo"标记）

### 技术相关元素
```css
code, pre, script, style, kbd, samp, var
```

### 表单控件
```css
input, textarea, select, option, button
```

### 媒体和交互元素
```css
svg, canvas, video, audio, iframe
```

### 导航和布局元素
```css
nav, footer, aside, noscript
```
**注意**: `header` 已从排除列表移除以支持标题翻译

### 翻译标记相关
```css
.lu-skip, .notranslate, [data-no-translate], [translate="no"]
.lu-wrapper, .lu-block
```

### 编辑器相关
```css
[contenteditable="true"], .editor, .monaco-editor
```

### 排除行为
- 显示：**红色"ooo"标记**
- 目的：可视化识别被排除的元素
- 交互：可点击查看排除原因

## 📋 扫描逻辑

### 优先级处理
1. **目标匹配**: 检查是否符合目标选择器
2. **排除检查**: 验证元素及其父元素是否被排除
3. **文本验证**: 确认文本内容可翻译性
4. **最终决策**: 翻译或排除

### 父元素继承
- 如果父元素被排除，所有子元素自动被排除
- 例外：显式标记 `[data-translatable]` 可覆盖父元素排除

### 文本过滤
- 空文本或纯空白符：排除
- 过短文本（<3字符）：排除
- 纯数字或符号：排除
- 已翻译内容：排除

## 🔧 配置示例

### 强制翻译特定元素
```html
<nav data-translatable>这个导航项会被翻译</nav>
```

### 排除特定元素
```html
<div class="notranslate">这个div不会被翻译</div>
```

### CSS选择器测试用例
```css
/* 会被翻译 */
body > header > div.uni-header__site-title > a > div

/* 被排除 */
nav.main-navigation
footer.site-footer
form input[type="text"]
```

## 🎨 视觉反馈

### 翻译元素
- **背景**: 无变化或轻微高亮
- **文本**: 显示原文（测试模式）
- **边框**: 可选的翻译指示器

### 排除元素
- **背景**: 红色半透明 (`rgba(255, 0, 0, 0.2)`)
- **标记**: "ooo" 文本覆盖
- **交互**: 点击显示排除详情

## 🧪 测试建议

### 功能测试
1. 加载包含各种元素类型的页面
2. 验证header区域的div元素能够被翻译
3. 确认排除元素显示红色"ooo"标记
4. 测试特殊选择器 `body > header > div.uni-header__site-title > a > div`

### 视觉测试
1. 检查翻译元素的样式覆盖效果
2. 验证原文显示的可读性
3. 确认排除标记的可见性和一致性

### 边界测试
1. 嵌套的header结构
2. 动态添加的DOM元素
3. 复杂的CSS选择器匹配

---

**版本**: v1.0  
**更新时间**: 2025-01-20  
**维护者**: Lucid Extension Team  