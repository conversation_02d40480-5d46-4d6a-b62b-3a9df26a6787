#!/usr/bin/env node

/**
 * 轻量化翻译系统测试脚本
 * 验证新系统的核心功能和性能改进
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Lucid Extension - Lightweight Translation System Test');
console.log('=' .repeat(60));

// 测试配置
const TEST_CONFIG = {
  targetSuccessRate: 85, // 目标成功率 85%
  previousSuccessRate: 40.5, // 之前的成功率
  expectedImprovement: 44.5, // 预期改进
  testTimeout: 30000 // 30秒超时
};

// 检查项目结构
function checkProjectStructure() {
  console.log('\n📁 Checking project structure...');
  
  const requiredFiles = [
    'src/types/index.ts',
    'src/config/default-config.ts', 
    'src/utils/cache.ts',
    'src/utils/batch.ts',
    'src/core/security-checker.ts',
    'src/core/dom-renderer.ts',
    'src/core/translation-manager.ts',
    'src/index.ts',
    'entrypoints/content.ts'
  ];
  
  const missingFiles = [];
  const existingFiles = [];
  
  requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
      existingFiles.push(file);
      console.log(`  ✅ ${file}`);
    } else {
      missingFiles.push(file);
      console.log(`  ❌ ${file} - MISSING`);
    }
  });
  
  console.log(`\n📊 Structure Check Results:`);
  console.log(`  ✅ Existing: ${existingFiles.length}/${requiredFiles.length}`);
  console.log(`  ❌ Missing: ${missingFiles.length}/${requiredFiles.length}`);
  
  if (missingFiles.length === 0) {
    console.log(`  🎉 All required files are present!`);
    return true;
  } else {
    console.log(`  ⚠️  Some files are missing. Please check the implementation.`);
    return false;
  }
}

// 检查TypeScript编译
function checkTypeScriptCompilation() {
  console.log('\n🔧 Checking TypeScript compilation...');
  
  const { execSync } = require('child_process');
  
  try {
    // 运行编译检查
    const result = execSync('pnpm run compile', { 
      cwd: path.join(__dirname, '..'),
      encoding: 'utf8',
      timeout: 30000
    });
    
    console.log('  ✅ TypeScript compilation successful');
    return true;
  } catch (error) {
    console.log('  ❌ TypeScript compilation failed');
    console.log('  📝 Error details:');
    
    // 只显示相关的错误信息
    const errorLines = error.stdout.split('\n')
      .filter(line => 
        line.includes('entrypoints/content.ts') || 
        line.includes('src/core/') ||
        line.includes('src/index.ts') ||
        line.includes('error TS')
      )
      .slice(0, 10); // 限制显示前10个错误
    
    errorLines.forEach(line => {
      console.log(`    ${line}`);
    });
    
    return false;
  }
}

// 检查构建流程
function checkBuildProcess() {
  console.log('\n🏗️  Checking build process...');
  
  const { execSync } = require('child_process');
  
  try {
    // 运行构建
    const result = execSync('pnpm run build', { 
      cwd: path.join(__dirname, '..'),
      encoding: 'utf8',
      timeout: 60000
    });
    
    // 检查输出文件
    const outputDir = path.join(__dirname, '..', '.output', 'chromium-mv3');
    const requiredOutputFiles = [
      'manifest.json',
      'content-scripts/content.js',
      'background.js'
    ];
    
    const missingOutputs = [];
    requiredOutputFiles.forEach(file => {
      const filePath = path.join(outputDir, file);
      if (!fs.existsSync(filePath)) {
        missingOutputs.push(file);
      }
    });
    
    if (missingOutputs.length === 0) {
      console.log('  ✅ Build successful - all output files generated');
      
      // 检查内容脚本大小
      const contentScriptPath = path.join(outputDir, 'content-scripts/content.js');
      const stats = fs.statSync(contentScriptPath);
      const sizeKB = Math.round(stats.size / 1024);
      
      console.log(`  📦 Content script size: ${sizeKB}KB`);
      
      if (sizeKB > 1000) {
        console.log('  ⚠️  Content script is quite large (>1MB)');
      } else {
        console.log('  ✅ Content script size is reasonable');
      }
      
      return true;
    } else {
      console.log('  ❌ Build incomplete - missing output files:');
      missingOutputs.forEach(file => console.log(`    - ${file}`));
      return false;
    }
    
  } catch (error) {
    console.log('  ❌ Build failed');
    console.log(`  📝 Error: ${error.message}`);
    return false;
  }
}

// 分析代码改进
function analyzeCodeImprovements() {
  console.log('\n📈 Analyzing code improvements...');
  
  try {
    // 检查新的轻量化系统文件
    const coreFiles = [
      'src/core/security-checker.ts',
      'src/core/dom-renderer.ts', 
      'src/core/translation-manager.ts'
    ];
    
    let totalLines = 0;
    let hasSecurityFix = false;
    let hasPerformanceOptimization = false;
    let hasSimplifiedArchitecture = false;
    
    coreFiles.forEach(file => {
      const filePath = path.join(__dirname, '..', file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n').length;
        totalLines += lines;
        
        // 检查关键改进
        if (content.includes('/(^|\\s)on\\w+\\s*=/i')) {
          hasSecurityFix = true;
        }
        
        if (content.includes('processBatch') || content.includes('RequestAnimationFrame')) {
          hasPerformanceOptimization = true;
        }
        
        if (content.includes('SimpleCache') || content.includes('lightweight')) {
          hasSimplifiedArchitecture = true;
        }
      }
    });
    
    console.log(`  📊 Core system analysis:`);
    console.log(`    📝 Total lines of core code: ${totalLines}`);
    console.log(`    🔒 Security regex fix: ${hasSecurityFix ? '✅' : '❌'}`);
    console.log(`    ⚡ Performance optimization: ${hasPerformanceOptimization ? '✅' : '❌'}`);
    console.log(`    🏗️  Simplified architecture: ${hasSimplifiedArchitecture ? '✅' : '❌'}`);
    
    // 检查内容脚本集成
    const contentScriptPath = path.join(__dirname, '..', 'entrypoints/content.ts');
    if (fs.existsSync(contentScriptPath)) {
      const contentScript = fs.readFileSync(contentScriptPath, 'utf8');
      const hasLightweightImports = contentScript.includes('translateElement') && 
                                   contentScript.includes('translatePage');
      const hasNewTestFunctions = contentScript.includes('testLightweightTranslation');
      
      console.log(`    🔌 Content script integration: ${hasLightweightImports ? '✅' : '❌'}`);
      console.log(`    🧪 New test functions: ${hasNewTestFunctions ? '✅' : '❌'}`);
    }
    
    return {
      hasSecurityFix,
      hasPerformanceOptimization, 
      hasSimplifiedArchitecture,
      totalLines
    };
    
  } catch (error) {
    console.log(`  ❌ Analysis failed: ${error.message}`);
    return null;
  }
}

// 生成测试报告
function generateTestReport(results) {
  console.log('\n📋 Test Report Summary');
  console.log('=' .repeat(60));
  
  const {
    structureCheck,
    compilationCheck,
    buildCheck,
    codeAnalysis
  } = results;
  
  let overallScore = 0;
  let maxScore = 4;
  
  console.log(`\n🏗️  Implementation Status:`);
  
  if (structureCheck) {
    console.log(`  ✅ Project structure complete`);
    overallScore++;
  } else {
    console.log(`  ❌ Project structure incomplete`);
  }
  
  if (compilationCheck) {
    console.log(`  ✅ TypeScript compilation successful`);
    overallScore++;
  } else {
    console.log(`  ❌ TypeScript compilation failed`);
  }
  
  if (buildCheck) {
    console.log(`  ✅ Build process successful`);
    overallScore++;
  } else {
    console.log(`  ❌ Build process failed`);
  }
  
  if (codeAnalysis && codeAnalysis.hasSecurityFix && codeAnalysis.hasPerformanceOptimization) {
    console.log(`  ✅ Core improvements implemented`);
    overallScore++;
  } else {
    console.log(`  ❌ Core improvements incomplete`);
  }
  
  const percentage = Math.round((overallScore / maxScore) * 100);
  
  console.log(`\n📊 Overall Implementation Progress: ${overallScore}/${maxScore} (${percentage}%)`);
  
  if (percentage >= 75) {
    console.log(`\n🎉 SUCCESS! Lightweight translation system is ready for testing!`);
    console.log(`\n📋 Next Steps:`);
    console.log(`  1. Load the extension in Chrome/Firefox`);
    console.log(`  2. Open test-lightweight-performance.html`);
    console.log(`  3. Run performance tests to verify 85%+ success rate`);
    console.log(`  4. Compare with previous 40.5% baseline`);
    
    return true;
  } else {
    console.log(`\n⚠️  Implementation incomplete. Please address the failing checks above.`);
    return false;
  }
}

// 主测试函数
async function runTests() {
  try {
    console.log(`🎯 Target: Improve translation success rate from ${TEST_CONFIG.previousSuccessRate}% to ${TEST_CONFIG.targetSuccessRate}%+`);
    console.log(`📈 Expected improvement: +${TEST_CONFIG.expectedImprovement} percentage points\n`);
    
    // 运行所有检查
    const structureCheck = checkProjectStructure();
    const compilationCheck = checkTypeScriptCompilation();
    const buildCheck = checkBuildProcess();
    const codeAnalysis = analyzeCodeImprovements();
    
    // 生成报告
    const success = generateTestReport({
      structureCheck,
      compilationCheck,
      buildCheck,
      codeAnalysis
    });
    
    if (success) {
      console.log(`\n🚀 Ready to test performance improvements!`);
      process.exit(0);
    } else {
      console.log(`\n❌ System not ready for performance testing.`);
      process.exit(1);
    }
    
  } catch (error) {
    console.error(`\n💥 Test script failed: ${error.message}`);
    process.exit(1);
  }
}

// 运行测试
runTests();