#!/bin/bash

# High-concurrency test script for Google Translate API
# This script sends 100 concurrent requests to the translateHtml endpoint

CONCURRENT_REQUESTS=10000
RESULTS_DIR="./scripts/results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
RESULTS_FILE="${RESULTS_DIR}/translate_test_${TIMESTAMP}.log"

echo "High-Concurrency Google Translate API Test"
echo "=========================================="
echo "Concurrent requests: ${CONCURRENT_REQUESTS}"
echo "Results will be saved to: ${RESULTS_FILE}"

# Create results directory if it doesn't exist
mkdir -p "${RESULTS_DIR}"

# Function to execute a single request
execute_request() {
    local request_id=$1
    local start_time=$(date +%s.%N)
    
    local response=$(curl -s -w "\n%{http_code}|%{time_total}|%{time_connect}|%{time_starttransfer}" \
        'https://translate-pa.googleapis.com/v1/translateHtml' \
        -H 'accept: */*' \
        -H 'accept-language: zh-CN,zh;q=0.9' \
        -H 'content-type: application/json+protobuf' \
        -H 'origin: chrome-extension://bpoadfkcbjbfhfodiogcnhhhpibjhbnh' \
        -H 'priority: u=1, i' \
        -H 'sec-fetch-dest: empty' \
        -H 'sec-fetch-mode: cors' \
        -H 'sec-fetch-site: none' \
        -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
        -H 'x-goog-api-key: AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520' \
        --data-raw '[[["Enterprise deployment overview","DEPLOYMENT","Copy page","Provider comparison","Anthropic","Amazon Bedrock","Google Vertex AI","Regions","Multiple AWS <a id=0>regions</a>","Multiple GCP <a id=0>regions</a>","Authentication","API key","AWS credentials (IAM)","Dashboard","AWS Cost Explorer","GCP Billing","Enterprise features","Teams, usage monitoring","IAM policies, CloudTrail","IAM roles, Cloud Audit Logs","Cloud providers","Amazon Bedrock","Google Vertex AI","Access Claude models via Google Cloud Platform with enterprise-grade security and compliance","Corporate Proxy","LLM Gateway","Copy","Using Bedrock with LLM Gateway","Copy","Copy","Using Vertex AI with LLM Gateway","Copy","Authentication configuration","Direct provider access","Corporate proxy","LLM Gateway","Need usage tracking across teams ","Debugging"],"en","zh-CN"],"te_lib"]')
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    
    # Extract metrics from curl output
    local body=$(echo "$response" | head -n -1)
    local metrics=$(echo "$response" | tail -n 1)
    local http_code=$(echo "$metrics" | cut -d'|' -f1)
    local time_total=$(echo "$metrics" | cut -d'|' -f2)
    local time_connect=$(echo "$metrics" | cut -d'|' -f3)
    local time_starttransfer=$(echo "$metrics" | cut -d'|' -f4)
    
    # Log results
    echo "Request $request_id: HTTP $http_code | Total: ${time_total}s | Connect: ${time_connect}s | Transfer: ${time_starttransfer}s" >> "${RESULTS_FILE}"
    
    # Show progress
    echo "Request $request_id completed (HTTP: $http_code, Time: ${time_total}s)"
}

# Initialize results file
echo "=== Google Translate API Concurrent Test Results ===" > "${RESULTS_FILE}"
echo "Timestamp: $(date)" >> "${RESULTS_FILE}"
echo "Concurrent requests: ${CONCURRENT_REQUESTS}" >> "${RESULTS_FILE}"
echo "=============================================" >> "${RESULTS_FILE}"

# Record start time
TEST_START_TIME=$(date +%s.%N)

echo "Starting ${CONCURRENT_REQUESTS} concurrent requests..."
echo "Press Ctrl+C to stop the test"

# Launch concurrent requests
for i in $(seq 1 $CONCURRENT_REQUESTS); do
    execute_request $i &
    
    # Small delay to prevent overwhelming the system
    sleep 0.01
done

# Wait for all background jobs to complete
wait

# Record end time and calculate total duration
TEST_END_TIME=$(date +%s.%N)
TOTAL_DURATION=$(echo "$TEST_END_TIME - $TEST_START_TIME" | bc)

echo ""
echo "============================================="
echo "Test completed!"
echo "Total duration: ${TOTAL_DURATION}s"
echo "Results saved to: ${RESULTS_FILE}"

# Generate summary statistics
echo "" >> "${RESULTS_FILE}"
echo "=== Summary Statistics ===" >> "${RESULTS_FILE}"
echo "Total test duration: ${TOTAL_DURATION}s" >> "${RESULTS_FILE}"

# Count successful and failed requests
SUCCESS_COUNT=$(grep -c "HTTP 200" "${RESULTS_FILE}" || echo "0")
TOTAL_COUNT=$(grep -c "Request.*HTTP" "${RESULTS_FILE}" || echo "0")
FAILURE_COUNT=$((TOTAL_COUNT - SUCCESS_COUNT))

echo "Successful requests: ${SUCCESS_COUNT}/${TOTAL_COUNT}" >> "${RESULTS_FILE}"
echo "Failed requests: ${FAILURE_COUNT}/${TOTAL_COUNT}" >> "${RESULTS_FILE}"

# Display summary
echo "Summary:"
echo "- Successful requests: ${SUCCESS_COUNT}/${TOTAL_COUNT}"
echo "- Failed requests: ${FAILURE_COUNT}/${TOTAL_COUNT}"
echo "- Average requests per second: $(echo "scale=2; $TOTAL_COUNT / $TOTAL_DURATION" | bc)"

# Show top 5 fastest and slowest requests
echo ""
echo "Top 5 fastest requests:"
grep "Request.*HTTP" "${RESULTS_FILE}" | sort -k6 -n | head -5

echo ""
echo "Top 5 slowest requests:"
grep "Request.*HTTP" "${RESULTS_FILE}" | sort -k6 -rn | head -5