#!/bin/bash

# Test script for Google Translate API
# This script tests the translateHtml endpoint

echo "Testing Google Translate API..."
echo "================================"

# Execute the curl request
curl 'https://translate-pa.googleapis.com/v1/translateHtml' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9' \
  -H 'content-type: application/json+protobuf' \
  -H 'origin: chrome-extension://bpoadfkcbjbfhfodiogcnhhhpibjhbnh' \
  -H 'priority: u=1, i' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: none' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'x-goog-api-key: AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520' \
  --data-raw '[[["Enterprise deployment overview","DEPLOYMENT","Copy page","Provider comparison","Anthropic","Amazon Bedrock","Google Vertex AI","Regions","Multiple AWS <a id=0>regions</a>","Multiple GCP <a id=0>regions</a>","Authentication","API key","AWS credentials (IAM)","Dashboard","AWS Cost Explorer","GCP Billing","Enterprise features","Teams, usage monitoring","IAM policies, CloudTrail","IAM roles, Cloud Audit Logs","Cloud providers","Amazon Bedrock","Google Vertex AI","Access Claude models via Google Cloud Platform with enterprise-grade security and compliance","Corporate Proxy","LLM Gateway","Copy","Using Bedrock with LLM Gateway","Copy","Copy","Using Vertex AI with LLM Gateway","Copy","Authentication configuration","Direct provider access","Corporate proxy","LLM Gateway","Need usage tracking across teams ","Debugging"],"en","zh-CN"],"te_lib"]'

echo ""
echo "Test completed."