<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络拦截测试 - 字幕翻译技术验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .button:hover {
            background: #0056b3;
        }
        
        .button.success {
            background: #28a745;
        }
        
        .button.danger {
            background: #dc3545;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .metric {
            display: inline-block;
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 5px 5px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 字幕翻译技术预研 - 网络拦截验证</h1>
        <p>这个页面用于测试和验证字幕翻译功能的核心技术组件，特别是网络请求拦截机制。</p>
        
        <!-- 基础网络拦截测试 -->
        <div class="test-section">
            <h3>1. 基础网络拦截测试</h3>
            <p>测试 Fetch API 和 XMLHttpRequest 的拦截能力</p>
            
            <button class="button" onclick="testFetchIntercept()">测试 Fetch 拦截</button>
            <button class="button" onclick="testXHRIntercept()">测试 XHR 拦截</button>
            <button class="button" onclick="setupInterceptors()">启动拦截器</button>
            <button class="button danger" onclick="clearInterceptors()">清除拦截器</button>
            
            <div id="intercept-status" class="status info">拦截器状态: 未启动</div>
            <div id="intercept-log" class="log"></div>
        </div>
        
        <!-- YouTube API 模拟测试 -->
        <div class="test-section">
            <h3>2. YouTube 字幕 API 模拟测试</h3>
            <p>模拟 YouTube /api/timedtext 请求的拦截和解析</p>
            
            <button class="button" onclick="simulateYouTubeRequest()">模拟 YouTube 字幕请求</button>
            <button class="button" onclick="testTimedTextParsing()">测试字幕解析</button>
            
            <div id="youtube-status" class="status info">YouTube API 测试: 待运行</div>
            <div id="youtube-log" class="log"></div>
        </div>
        
        <!-- 性能影响测试 -->
        <div class="test-section">
            <h3>3. 性能影响测试</h3>
            <p>测试网络拦截对页面性能的影响</p>
            
            <button class="button" onclick="runPerformanceTest()">运行性能测试</button>
            <button class="button" onclick="stressTestRequests()">压力测试 (100个请求)</button>
            
            <div id="performance-metrics">
                <span class="metric">基准时间: <span id="baseline-time">-</span>ms</span>
                <span class="metric">拦截开销: <span id="intercept-overhead">-</span>ms</span>
                <span class="metric">内存使用: <span id="memory-usage">-</span>MB</span>
            </div>
            
            <div id="performance-log" class="log"></div>
        </div>
        
        <!-- 字幕解析测试 -->
        <div class="test-section">
            <h3>4. 字幕格式解析测试</h3>
            <p>测试各种字幕格式的解析能力</p>
            
            <button class="button" onclick="testVTTParsing()">测试 VTT 解析</button>
            <button class="button" onclick="testSRTParsing()">测试 SRT 解析</button>
            <button class="button" onclick="testYouTubeJSON()">测试 YouTube JSON</button>
            
            <div id="parser-status" class="status info">解析器测试: 待运行</div>
            <div id="parser-log" class="log"></div>
        </div>
        
        <!-- 综合集成测试 -->
        <div class="test-section">
            <h3>5. 综合集成测试</h3>
            <p>端到端的字幕捕获、解析和显示流程测试</p>
            
            <button class="button success" onclick="runFullIntegrationTest()">运行完整集成测试</button>
            <button class="button" onclick="resetAllTests()">重置所有测试</button>
            
            <div id="integration-status" class="status info">集成测试: 待运行</div>
            <div id="integration-log" class="log"></div>
        </div>
    </div>

    <script>
        // 全局状态
        let interceptorsActive = false;
        let originalFetch = window.fetch;
        let originalXHROpen = XMLHttpRequest.prototype.open;
        
        // 日志记录
        function log(section, message, type = 'info') {
            const logElement = document.getElementById(`${section}-log`);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(`[${section.toUpperCase()}] ${message}`);
        }
        
        function updateStatus(section, message, type = 'info') {
            const statusElement = document.getElementById(`${section}-status`);
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        // 网络拦截器设置
        function setupInterceptors() {
            if (interceptorsActive) {
                log('intercept', '拦截器已经在运行中');
                return;
            }
            
            // Fetch 拦截
            window.fetch = async function(input, init) {
                const url = typeof input === 'string' ? input : input.url;
                log('intercept', `Fetch 拦截: ${url}`);
                
                // 字幕相关请求检测
                if (url.includes('/api/timedtext') || url.includes('.vtt') || url.includes('.srt')) {
                    log('intercept', `🎯 检测到字幕请求: ${url}`, 'success');
                }
                
                return originalFetch.call(this, input, init);
            };
            
            // XHR 拦截
            XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                log('intercept', `XHR 拦截: ${method} ${url}`);
                
                if (url.includes('/api/timedtext') || url.includes('.vtt') || url.includes('.srt')) {
                    log('intercept', `🎯 检测到字幕 XHR 请求: ${url}`, 'success');
                }
                
                return originalXHROpen.call(this, method, url, async, user, password);
            };
            
            interceptorsActive = true;
            updateStatus('intercept', '拦截器已启动', 'success');
            log('intercept', '网络拦截器已成功启动');
        }
        
        function clearInterceptors() {
            window.fetch = originalFetch;
            XMLHttpRequest.prototype.open = originalXHROpen;
            interceptorsActive = false;
            updateStatus('intercept', '拦截器已清除', 'info');
            log('intercept', '网络拦截器已清除');
        }
        
        // 测试函数
        async function testFetchIntercept() {
            if (!interceptorsActive) {
                setupInterceptors();
            }
            
            log('intercept', '开始测试 Fetch 拦截...');
            
            try {
                // 测试普通请求
                await fetch('https://httpbin.org/json');
                log('intercept', '✅ 普通 Fetch 请求测试成功');
                
                // 测试字幕相关请求
                await fetch('https://example.com/api/timedtext?v=test');
                log('intercept', '✅ 字幕相关 Fetch 请求测试成功');
                
                updateStatus('intercept', 'Fetch 拦截测试完成', 'success');
            } catch (error) {
                log('intercept', `❌ Fetch 拦截测试失败: ${error.message}`, 'error');
                updateStatus('intercept', 'Fetch 拦截测试失败', 'error');
            }
        }
        
        function testXHRIntercept() {
            if (!interceptorsActive) {
                setupInterceptors();
            }
            
            log('intercept', '开始测试 XHR 拦截...');
            
            // 测试普通 XHR 请求
            const xhr1 = new XMLHttpRequest();
            xhr1.open('GET', 'https://httpbin.org/json');
            xhr1.send();
            
            // 测试字幕相关 XHR 请求
            const xhr2 = new XMLHttpRequest();
            xhr2.open('GET', 'https://example.com/subtitles.vtt');
            xhr2.send();
            
            log('intercept', '✅ XHR 拦截测试完成');
            updateStatus('intercept', 'XHR 拦截测试完成', 'success');
        }
        
        // YouTube API 模拟测试
        async function simulateYouTubeRequest() {
            log('youtube', '开始模拟 YouTube 字幕请求...');
            updateStatus('youtube', 'YouTube API 测试进行中...', 'info');
            
            const mockYouTubeAPI = 'https://www.youtube.com/api/timedtext?v=dQw4w9WgXcQ&lang=en&fmt=vtt';
            
            try {
                if (!interceptorsActive) {
                    setupInterceptors();
                }
                
                // 模拟请求
                log('youtube', `模拟请求: ${mockYouTubeAPI}`);
                await fetch(mockYouTubeAPI).catch(() => {}); // 忽略网络错误，我们只测试拦截
                
                log('youtube', '✅ YouTube 字幕请求模拟成功');
                updateStatus('youtube', 'YouTube API 测试完成', 'success');
            } catch (error) {
                log('youtube', `❌ YouTube 请求模拟失败: ${error.message}`);
                updateStatus('youtube', 'YouTube API 测试失败', 'error');
            }
        }
        
        function testTimedTextParsing() {
            log('youtube', '开始测试 TimedText 解析...');
            
            // 模拟 YouTube 字幕 JSON 响应
            const mockTimedTextResponse = {
                events: [
                    {
                        tStartMs: 1000,
                        dDurationMs: 2000,
                        segs: [{ utf8: "Hello world!" }]
                    },
                    {
                        tStartMs: 3500,
                        dDurationMs: 1500,
                        segs: [{ utf8: "This is a test subtitle." }]
                    }
                ]
            };
            
            try {
                const parsedSubtitles = parseYouTubeTimedText(mockTimedTextResponse);
                log('youtube', `解析结果: ${JSON.stringify(parsedSubtitles, null, 2)}`);
                log('youtube', '✅ TimedText 解析测试成功');
            } catch (error) {
                log('youtube', `❌ TimedText 解析失败: ${error.message}`);
            }
        }
        
        // 字幕解析函数
        function parseYouTubeTimedText(timedTextData) {
            const subtitles = [];
            
            if (timedTextData.events) {
                timedTextData.events.forEach(event => {
                    if (event.segs && event.segs.length > 0) {
                        const text = event.segs.map(seg => seg.utf8 || '').join('');
                        subtitles.push({
                            start: event.tStartMs,
                            end: event.tStartMs + (event.dDurationMs || 0),
                            text: text.trim()
                        });
                    }
                });
            }
            
            return subtitles;
        }
        
        // 性能测试
        async function runPerformanceTest() {
            log('performance', '开始性能基准测试...');
            
            // 基准测试 - 无拦截器
            clearInterceptors();
            const baselineStart = performance.now();
            
            for (let i = 0; i < 10; i++) {
                fetch('https://httpbin.org/json').catch(() => {});
            }
            
            const baselineEnd = performance.now();
            const baselineTime = baselineEnd - baselineStart;
            document.getElementById('baseline-time').textContent = baselineTime.toFixed(2);
            
            // 拦截器测试
            setupInterceptors();
            const interceptStart = performance.now();
            
            for (let i = 0; i < 10; i++) {
                fetch('https://httpbin.org/json').catch(() => {});
            }
            
            const interceptEnd = performance.now();
            const interceptTime = interceptEnd - interceptStart;
            const overhead = interceptTime - baselineTime;
            document.getElementById('intercept-overhead').textContent = overhead.toFixed(2);
            
            // 内存使用
            if (performance.memory) {
                const memoryMB = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
                document.getElementById('memory-usage').textContent = memoryMB;
            }
            
            log('performance', `基准时间: ${baselineTime.toFixed(2)}ms`);
            log('performance', `拦截开销: ${overhead.toFixed(2)}ms`);
            log('performance', '✅ 性能测试完成');
        }
        
        async function stressTestRequests() {
            log('performance', '开始压力测试 (100个并发请求)...');
            
            const startTime = performance.now();
            const promises = [];
            
            for (let i = 0; i < 100; i++) {
                promises.push(fetch('https://httpbin.org/json').catch(() => {}));
            }
            
            await Promise.all(promises);
            const endTime = performance.now();
            const totalTime = endTime - startTime;
            
            log('performance', `压力测试完成: 100个请求用时 ${totalTime.toFixed(2)}ms`);
            log('performance', `平均每个请求: ${(totalTime / 100).toFixed(2)}ms`);
        }
        
        // 字幕解析测试
        function testVTTParsing() {
            log('parser', '开始测试 VTT 格式解析...');
            
            const vttContent = `WEBVTT

1
00:00:01.000 --> 00:00:03.000
This is the first subtitle.

2
00:00:04.000 --> 00:00:06.000
This is the second subtitle.`;
            
            try {
                const parsed = parseVTT(vttContent);
                log('parser', `VTT 解析结果: ${JSON.stringify(parsed, null, 2)}`);
                log('parser', '✅ VTT 解析测试成功');
            } catch (error) {
                log('parser', `❌ VTT 解析失败: ${error.message}`);
            }
        }
        
        function testSRTParsing() {
            log('parser', '开始测试 SRT 格式解析...');
            
            const srtContent = `1
00:00:01,000 --> 00:00:03,000
This is the first subtitle.

2
00:00:04,000 --> 00:00:06,000
This is the second subtitle.`;
            
            try {
                const parsed = parseSRT(srtContent);
                log('parser', `SRT 解析结果: ${JSON.stringify(parsed, null, 2)}`);
                log('parser', '✅ SRT 解析测试成功');
            } catch (error) {
                log('parser', `❌ SRT 解析失败: ${error.message}`);
            }
        }
        
        function testYouTubeJSON() {
            log('parser', '开始测试 YouTube JSON 格式解析...');
            testTimedTextParsing(); // 复用之前的函数
        }
        
        // 解析器实现
        function parseVTT(content) {
            const lines = content.split('\n');
            const subtitles = [];
            let currentSubtitle = null;
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                
                if (line.includes('-->')) {
                    const [start, end] = line.split('-->').map(t => t.trim());
                    currentSubtitle = {
                        start: timeToMs(start),
                        end: timeToMs(end),
                        text: ''
                    };
                } else if (line && currentSubtitle && !line.startsWith('WEBVTT') && !line.match(/^\d+$/)) {
                    currentSubtitle.text += (currentSubtitle.text ? ' ' : '') + line;
                } else if (!line && currentSubtitle) {
                    subtitles.push(currentSubtitle);
                    currentSubtitle = null;
                }
            }
            
            if (currentSubtitle) {
                subtitles.push(currentSubtitle);
            }
            
            return subtitles;
        }
        
        function parseSRT(content) {
            const lines = content.split('\n');
            const subtitles = [];
            let currentSubtitle = null;
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                
                if (line.includes('-->')) {
                    const [start, end] = line.split('-->').map(t => t.trim());
                    currentSubtitle = {
                        start: timeToMs(start.replace(',', '.')),
                        end: timeToMs(end.replace(',', '.')),
                        text: ''
                    };
                } else if (line && currentSubtitle && !line.match(/^\d+$/)) {
                    currentSubtitle.text += (currentSubtitle.text ? ' ' : '') + line;
                } else if (!line && currentSubtitle) {
                    subtitles.push(currentSubtitle);
                    currentSubtitle = null;
                }
            }
            
            if (currentSubtitle) {
                subtitles.push(currentSubtitle);
            }
            
            return subtitles;
        }
        
        function timeToMs(timeString) {
            const parts = timeString.split(':');
            const seconds = parts[parts.length - 1];
            const [sec, ms] = seconds.split('.');
            
            let totalMs = 0;
            if (parts.length === 3) {
                totalMs += parseInt(parts[0]) * 3600000; // hours
                totalMs += parseInt(parts[1]) * 60000;   // minutes
            } else if (parts.length === 2) {
                totalMs += parseInt(parts[0]) * 60000;   // minutes
            }
            totalMs += parseInt(sec) * 1000;             // seconds
            totalMs += parseInt(ms || 0);                // milliseconds
            
            return totalMs;
        }
        
        // 综合集成测试
        async function runFullIntegrationTest() {
            log('integration', '🚀 开始运行完整集成测试...');
            updateStatus('integration', '集成测试进行中...', 'info');
            
            try {
                // 1. 设置拦截器
                log('integration', '步骤 1: 设置网络拦截器');
                setupInterceptors();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // 2. 模拟字幕请求
                log('integration', '步骤 2: 模拟字幕数据获取');
                await simulateYouTubeRequest();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // 3. 测试解析器
                log('integration', '步骤 3: 测试字幕解析');
                testVTTParsing();
                testSRTParsing();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // 4. 性能测试
                log('integration', '步骤 4: 性能影响评估');
                await runPerformanceTest();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                log('integration', '🎉 完整集成测试成功完成!');
                updateStatus('integration', '集成测试完成 - 所有测试通过', 'success');
                
                // 生成测试报告
                generateTestReport();
                
            } catch (error) {
                log('integration', `❌ 集成测试失败: ${error.message}`);
                updateStatus('integration', '集成测试失败', 'error');
            }
        }
        
        function generateTestReport() {
            const report = {
                timestamp: new Date().toISOString(),
                networkInterception: {
                    status: interceptorsActive ? 'success' : 'failed',
                    fetchSupport: true,
                    xhrSupport: true
                },
                parsing: {
                    vttSupport: true,
                    srtSupport: true,
                    youtubeJsonSupport: true
                },
                performance: {
                    baselineTime: document.getElementById('baseline-time').textContent,
                    interceptOverhead: document.getElementById('intercept-overhead').textContent,
                    memoryUsage: document.getElementById('memory-usage').textContent
                }
            };
            
            log('integration', '📊 测试报告:');
            log('integration', JSON.stringify(report, null, 2));
        }
        
        function resetAllTests() {
            // 清除所有日志
            document.querySelectorAll('.log').forEach(log => log.textContent = '');
            
            // 重置状态
            updateStatus('intercept', '拦截器状态: 未启动', 'info');
            updateStatus('youtube', 'YouTube API 测试: 待运行', 'info');
            updateStatus('parser', '解析器测试: 待运行', 'info');
            updateStatus('integration', '集成测试: 待运行', 'info');
            
            // 重置性能指标
            document.getElementById('baseline-time').textContent = '-';
            document.getElementById('intercept-overhead').textContent = '-';
            document.getElementById('memory-usage').textContent = '-';
            
            // 清除拦截器
            clearInterceptors();
            
            console.clear();
            log('intercept', '所有测试已重置');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('intercept', '字幕翻译技术预研页面已加载');
            log('intercept', '请点击相应按钮开始测试');
        });
    </script>
</body>
</html>