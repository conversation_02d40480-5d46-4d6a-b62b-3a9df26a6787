/**
 * 字幕解析器原型 - 技术验证
 * 支持多种字幕格式的解析和转换
 */

// 字幕条目接口
interface SubtitleEntry {
    id?: string;
    start: number;     // 开始时间 (毫秒)
    end: number;       // 结束时间 (毫秒)
    text: string;      // 字幕文本
    speaker?: string;  // 说话人 (可选)
}

// 解析器基类
abstract class BaseSubtitleParser {
    abstract format: string;
    abstract parse(content: string): SubtitleEntry[];
    
    protected timeToMs(timeString: string): number {
        // 支持多种时间格式: HH:MM:SS.mmm, MM:SS.mmm, SS.mmm
        const parts = timeString.replace(',', '.').split(':');
        let totalMs = 0;
        
        if (parts.length === 3) {
            // HH:MM:SS.mmm
            totalMs += parseInt(parts[0]) * 3600000; // hours
            totalMs += parseInt(parts[1]) * 60000;   // minutes
            const [seconds, ms] = parts[2].split('.');
            totalMs += parseInt(seconds) * 1000;     // seconds
            totalMs += parseInt(ms || '0');          // milliseconds
        } else if (parts.length === 2) {
            // MM:SS.mmm
            totalMs += parseInt(parts[0]) * 60000;   // minutes
            const [seconds, ms] = parts[1].split('.');
            totalMs += parseInt(seconds) * 1000;     // seconds
            totalMs += parseInt(ms || '0');          // milliseconds
        } else {
            // SS.mmm
            const [seconds, ms] = parts[0].split('.');
            totalMs += parseInt(seconds) * 1000;     // seconds
            totalMs += parseInt(ms || '0');          // milliseconds
        }
        
        return totalMs;
    }
    
    protected cleanText(text: string): string {
        // 清理字幕文本，移除HTML标签和多余空格
        return text
            .replace(/<[^>]*>/g, '') // 移除HTML标签
            .replace(/\s+/g, ' ')    // 合并多个空格
            .trim();
    }
}

// VTT格式解析器
class VTTParser extends BaseSubtitleParser {
    format = 'vtt';
    
    parse(content: string): SubtitleEntry[] {
        const lines = content.split('\n');
        const subtitles: SubtitleEntry[] = [];
        let currentSubtitle: Partial<SubtitleEntry> | null = null;
        let idCounter = 1;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // 跳过空行和WEBVTT头部
            if (!line || line.startsWith('WEBVTT') || line.startsWith('NOTE')) {
                continue;
            }
            
            // 检测时间轴
            if (line.includes('-->')) {
                const [startTime, endTime] = line.split('-->').map(t => t.trim());
                currentSubtitle = {
                    id: `vtt_${idCounter++}`,
                    start: this.timeToMs(startTime.split(' ')[0]), // 移除可能的位置信息
                    end: this.timeToMs(endTime.split(' ')[0]),
                    text: '',
                    speaker: this.extractSpeaker(line)
                };
            }
            // 收集字幕文本
            else if (currentSubtitle && line) {
                currentSubtitle.text += (currentSubtitle.text ? ' ' : '') + this.cleanText(line);
            }
            // 字幕结束
            else if (!line && currentSubtitle && currentSubtitle.text) {
                subtitles.push(currentSubtitle as SubtitleEntry);
                currentSubtitle = null;
            }
        }
        
        // 处理最后一个字幕
        if (currentSubtitle && currentSubtitle.text) {
            subtitles.push(currentSubtitle as SubtitleEntry);
        }
        
        return subtitles;
    }
    
    private extractSpeaker(timeLine: string): string | undefined {
        // 提取VTT中的说话人信息 <v Speaker>
        const speakerMatch = timeLine.match(/<v\s+([^>]+)>/);
        return speakerMatch ? speakerMatch[1].trim() : undefined;
    }
}

// SRT格式解析器
class SRTParser extends BaseSubtitleParser {
    format = 'srt';
    
    parse(content: string): SubtitleEntry[] {
        const blocks = content.split(/\n\s*\n/); // 按空行分割
        const subtitles: SubtitleEntry[] = [];
        
        for (const block of blocks) {
            const lines = block.trim().split('\n');
            if (lines.length < 3) continue;
            
            const id = lines[0].trim();
            const timeLine = lines[1].trim();
            const text = lines.slice(2).join(' ').trim();
            
            if (timeLine.includes('-->')) {
                const [startTime, endTime] = timeLine.split('-->').map(t => t.trim());
                
                subtitles.push({
                    id: `srt_${id}`,
                    start: this.timeToMs(startTime),
                    end: this.timeToMs(endTime),
                    text: this.cleanText(text)
                });
            }
        }
        
        return subtitles;
    }
}

// YouTube TimedText JSON 解析器
class YouTubeTimedTextParser extends BaseSubtitleParser {
    format = 'youtube_json';
    
    parse(content: string): SubtitleEntry[] {
        try {
            const data = typeof content === 'string' ? JSON.parse(content) : content;
            const subtitles: SubtitleEntry[] = [];
            let idCounter = 1;
            
            if (data.events && Array.isArray(data.events)) {
                for (const event of data.events) {
                    if (event.segs && Array.isArray(event.segs)) {
                        const text = event.segs
                            .map((seg: any) => seg.utf8 || '')
                            .join('')
                            .trim();
                        
                        if (text) {
                            subtitles.push({
                                id: `youtube_${idCounter++}`,
                                start: event.tStartMs || 0,
                                end: (event.tStartMs || 0) + (event.dDurationMs || 0),
                                text: this.cleanText(text)
                            });
                        }
                    }
                }
            }
            
            return subtitles;
        } catch (error) {
            console.error('YouTube TimedText parsing error:', error);
            return [];
        }
    }
}

// ASS/SSA 格式解析器 (基础支持)
class ASSParser extends BaseSubtitleParser {
    format = 'ass';
    
    parse(content: string): SubtitleEntry[] {
        const lines = content.split('\n');
        const subtitles: SubtitleEntry[] = [];
        let dialogueSection = false;
        let idCounter = 1;
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            
            if (trimmedLine === '[Events]') {
                dialogueSection = true;
                continue;
            }
            
            if (trimmedLine.startsWith('[') && trimmedLine !== '[Events]') {
                dialogueSection = false;
                continue;
            }
            
            if (dialogueSection && trimmedLine.startsWith('Dialogue:')) {
                const parts = trimmedLine.substring(9).split(','); // 去掉 'Dialogue:' 前缀
                
                if (parts.length >= 10) {
                    const startTime = this.parseASSTime(parts[1]);
                    const endTime = this.parseASSTime(parts[2]);
                    const text = parts.slice(9).join(','); // 文本可能包含逗号
                    
                    subtitles.push({
                        id: `ass_${idCounter++}`,
                        start: startTime,
                        end: endTime,
                        text: this.cleanText(text),
                        speaker: parts[3] || undefined // Name字段
                    });
                }
            }
        }
        
        return subtitles;
    }
    
    private parseASSTime(timeStr: string): number {
        // ASS时间格式: H:MM:SS.cc (centiseconds)
        const parts = timeStr.split(':');
        if (parts.length !== 3) return 0;
        
        const hours = parseInt(parts[0]);
        const minutes = parseInt(parts[1]);
        const [seconds, centiseconds] = parts[2].split('.');
        
        return (hours * 3600 + minutes * 60 + parseInt(seconds)) * 1000 + 
               parseInt(centiseconds || '0') * 10;
    }
}

// 解析器工厂
class SubtitleParserFactory {
    private parsers = new Map<string, BaseSubtitleParser>();
    
    constructor() {
        this.registerParser(new VTTParser());
        this.registerParser(new SRTParser());
        this.registerParser(new YouTubeTimedTextParser());
        this.registerParser(new ASSParser());
    }
    
    registerParser(parser: BaseSubtitleParser): void {
        this.parsers.set(parser.format, parser);
    }
    
    getParser(format: string): BaseSubtitleParser | null {
        return this.parsers.get(format.toLowerCase()) || null;
    }
    
    detectFormat(content: string): string | null {
        const trimmedContent = content.trim();
        
        // VTT 检测
        if (trimmedContent.startsWith('WEBVTT')) {
            return 'vtt';
        }
        
        // SRT 检测 (数字开头，第二行包含时间箭头)
        const lines = trimmedContent.split('\n').slice(0, 3);
        if (lines.length >= 2 && 
            /^\d+$/.test(lines[0].trim()) && 
            lines[1].includes('-->')) {
            return 'srt';
        }
        
        // YouTube JSON 检测
        try {
            const parsed = JSON.parse(trimmedContent);
            if (parsed.events && Array.isArray(parsed.events)) {
                return 'youtube_json';
            }
        } catch {}
        
        // ASS 检测
        if (trimmedContent.includes('[Events]') && trimmedContent.includes('Dialogue:')) {
            return 'ass';
        }
        
        return null;
    }
    
    parse(content: string, format?: string): SubtitleEntry[] {
        const detectedFormat = format || this.detectFormat(content);
        
        if (!detectedFormat) {
            throw new Error('无法检测字幕格式');
        }
        
        const parser = this.getParser(detectedFormat);
        if (!parser) {
            throw new Error(`不支持的字幕格式: ${detectedFormat}`);
        }
        
        return parser.parse(content);
    }
}

// 字幕同步和验证工具
class SubtitleValidator {
    static validate(subtitles: SubtitleEntry[]): { valid: boolean; errors: string[] } {
        const errors: string[] = [];
        
        for (let i = 0; i < subtitles.length; i++) {
            const subtitle = subtitles[i];
            
            // 检查时间有效性
            if (subtitle.start < 0) {
                errors.push(`字幕 ${i + 1}: 开始时间不能为负数`);
            }
            
            if (subtitle.end <= subtitle.start) {
                errors.push(`字幕 ${i + 1}: 结束时间必须大于开始时间`);
            }
            
            // 检查文本有效性
            if (!subtitle.text || subtitle.text.trim().length === 0) {
                errors.push(`字幕 ${i + 1}: 文本内容不能为空`);
            }
            
            // 检查时间重叠
            if (i > 0 && subtitle.start < subtitles[i - 1].end) {
                errors.push(`字幕 ${i + 1}: 与前一条字幕时间重叠`);
            }
        }
        
        return {
            valid: errors.length === 0,
            errors
        };
    }
    
    static sort(subtitles: SubtitleEntry[]): SubtitleEntry[] {
        return subtitles.slice().sort((a, b) => a.start - b.start);
    }
    
    static merge(subtitles: SubtitleEntry[], maxGap = 100): SubtitleEntry[] {
        if (subtitles.length <= 1) return subtitles;
        
        const sorted = this.sort(subtitles);
        const merged: SubtitleEntry[] = [];
        let current = sorted[0];
        
        for (let i = 1; i < sorted.length; i++) {
            const next = sorted[i];
            
            // 如果间隔很小且内容相关，合并字幕
            if (next.start - current.end <= maxGap && 
                current.speaker === next.speaker) {
                current = {
                    ...current,
                    end: next.end,
                    text: `${current.text} ${next.text}`
                };
            } else {
                merged.push(current);
                current = next;
            }
        }
        
        merged.push(current);
        return merged;
    }
}

// 导出主要组件
export {
    SubtitleEntry,
    BaseSubtitleParser,
    VTTParser,
    SRTParser,
    YouTubeTimedTextParser,
    ASSParser,
    SubtitleParserFactory,
    SubtitleValidator
};

// 测试用例和示例
const testData = {
    vtt: `WEBVTT

1
00:00:01.000 --> 00:00:03.000
Hello, this is the first subtitle.

2
00:00:04.000 --> 00:00:06.000 <v Speaker>
This is spoken by someone specific.`,

    srt: `1
00:00:01,000 --> 00:00:03,000
Hello, this is the first subtitle.

2
00:00:04,000 --> 00:00:06,000
This is the second subtitle.`,

    youtubeJson: {
        events: [
            {
                tStartMs: 1000,
                dDurationMs: 2000,
                segs: [{ utf8: "Hello, this is a test." }]
            },
            {
                tStartMs: 4000,
                dDurationMs: 1500,
                segs: [{ utf8: "Another subtitle line." }]
            }
        ]
    }
};

// 测试函数
function runParserTests() {
    console.log('🧪 开始字幕解析器测试...\n');
    
    const factory = new SubtitleParserFactory();
    
    // 测试VTT解析
    console.log('📝 VTT解析测试:');
    try {
        const vttResult = factory.parse(testData.vtt);
        console.log('✅ VTT解析成功:', vttResult);
        
        const validation = SubtitleValidator.validate(vttResult);
        console.log('✅ VTT验证结果:', validation);
    } catch (error) {
        console.error('❌ VTT解析失败:', error);
    }
    
    // 测试SRT解析
    console.log('\n📝 SRT解析测试:');
    try {
        const srtResult = factory.parse(testData.srt);
        console.log('✅ SRT解析成功:', srtResult);
        
        const validation = SubtitleValidator.validate(srtResult);
        console.log('✅ SRT验证结果:', validation);
    } catch (error) {
        console.error('❌ SRT解析失败:', error);
    }
    
    // 测试YouTube JSON解析
    console.log('\n📝 YouTube JSON解析测试:');
    try {
        const jsonResult = factory.parse(JSON.stringify(testData.youtubeJson));
        console.log('✅ YouTube JSON解析成功:', jsonResult);
        
        const validation = SubtitleValidator.validate(jsonResult);
        console.log('✅ YouTube JSON验证结果:', validation);
    } catch (error) {
        console.error('❌ YouTube JSON解析失败:', error);
    }
    
    // 测试格式检测
    console.log('\n🔍 格式检测测试:');
    console.log('VTT格式检测:', factory.detectFormat(testData.vtt));
    console.log('SRT格式检测:', factory.detectFormat(testData.srt));
    console.log('YouTube JSON格式检测:', factory.detectFormat(JSON.stringify(testData.youtubeJson)));
    
    console.log('\n🎉 字幕解析器测试完成!');
}

// 如果在Node.js环境中运行测试
if (typeof require !== 'undefined' && require.main === module) {
    runParserTests();
}

// 浏览器环境下的全局暴露
if (typeof window !== 'undefined') {
    (window as any).SubtitleParser = {
        SubtitleParserFactory,
        SubtitleValidator,
        runParserTests,
        testData
    };
}