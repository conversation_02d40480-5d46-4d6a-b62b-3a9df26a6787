# 后端CORS配置指南

## 问题描述

浏览器扩展在访问后端API时遇到CORS错误：
```
Access to fetch at 'http://localhost:4000/api/auth/signin' from origin 'https://www.google.com' has been blocked by CORS policy
```

## 解决方案

### 1. Express.js 后端配置

```javascript
const express = require('express');
const cors = require('cors');
const app = express();

// CORS配置 - 允许扩展程序访问
app.use(cors({
  origin: [
    // 允许Chrome扩展程序
    'chrome-extension://*',
    // 允许Firefox扩展程序  
    'moz-extension://*',
    // 允许所有HTTPS网站（扩展程序运行环境）
    'https://*',
    // 允许本地开发
    'http://localhost:*',
    'http://127.0.0.1:*'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type', 
    'Authorization', 
    'X-Requested-With',
    'Accept',
    'Origin'
  ]
}));

// 处理预检请求
app.options('*', cors());

// 你的API路由
app.post('/api/auth/signin', (req, res) => {
  // 登录逻辑
});

app.post('/api/auth/signup', (req, res) => {
  // 注册逻辑
});
```

### 2. Fastify 后端配置

```javascript
const fastify = require('fastify')({ logger: true });

// 注册CORS插件
await fastify.register(require('@fastify/cors'), {
  origin: [
    /^chrome-extension:\/\//,
    /^moz-extension:\/\//,
    /^https:\/\//,
    /^http:\/\/localhost/,
    /^http:\/\/127\.0\.0\.1/
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
});
```

### 3. Next.js API Routes 配置

```javascript
// pages/api/auth/signin.js 或 app/api/auth/signin/route.js
export async function POST(request) {
  // 设置CORS头
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Credentials': 'true'
  };

  // 处理预检请求
  if (request.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers });
  }

  // 你的登录逻辑
  const result = await handleLogin(request);
  
  return Response.json(result, { headers });
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    }
  });
}
```

### 4. 通用中间件配置

如果你使用其他框架，可以添加这个通用中间件：

```javascript
function corsMiddleware(req, res, next) {
  // 允许的源
  const allowedOrigins = [
    /^chrome-extension:\/\//,
    /^moz-extension:\/\//,
    /^https:\/\//,
    /^http:\/\/localhost/
  ];

  const origin = req.headers.origin;
  const isAllowed = allowedOrigins.some(pattern => 
    typeof pattern === 'string' ? pattern === origin : pattern.test(origin)
  );

  if (isAllowed) {
    res.header('Access-Control-Allow-Origin', origin);
  }

  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
    return;
  }

  next();
}

// 使用中间件
app.use(corsMiddleware);
```

## 测试CORS配置

### 1. 检查后端是否正确响应预检请求

```bash
curl -X OPTIONS http://localhost:4000/api/auth/signin \
  -H "Origin: chrome-extension://test" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type,Authorization" \
  -v
```

期望响应头包含：
```
Access-Control-Allow-Origin: chrome-extension://test
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

### 2. 测试实际API请求

```bash
curl -X POST http://localhost:4000/api/auth/signin \
  -H "Origin: chrome-extension://test" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}' \
  -v
```

## 扩展程序权限确认

确保 `wxt.config.ts` 中包含正确的权限：

```typescript
manifest: {
  host_permissions: [
    "http://localhost:4000/*",  // 开发环境API
    "https://your-api.com/*"    // 生产环境API
  ]
}
```

## 常见问题

### Q: 为什么要允许 `https://*`？
A: 扩展程序在网页上运行时，请求的origin是当前网页的域名（如 https://www.google.com），而不是扩展程序本身的ID。

### Q: 生产环境如何配置？
A: 生产环境建议更严格的配置：
```javascript
origin: [
  'chrome-extension://your-extension-id',
  'https://your-domain.com'
]
```

### Q: 如何获取扩展程序ID？
A: 在Chrome中访问 `chrome://extensions/`，开启开发者模式后可以看到扩展程序ID。

## 验证配置

配置完成后，重启后端服务，然后测试扩展程序的登录功能。如果仍有问题，请检查：

1. 后端服务是否在 `http://localhost:4000` 运行
2. CORS中间件是否正确加载
3. 浏览器开发者工具中的网络请求详情
4. 后端服务器日志中的请求记录
