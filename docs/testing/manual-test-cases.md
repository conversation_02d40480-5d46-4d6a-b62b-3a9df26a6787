# Lucid Extension 手动测试用例

## 🧪 测试环境准备

### 前置条件
1. Chrome/Edge 浏览器 (最新版本)
2. 扩展已加载到开发者模式
3. 准备测试页面 (见下方测试页面部分)

### 测试数据准备
- 短文本: "Hello World"
- 中等文本: "This is a medium length paragraph with some content to translate."
- 长文本: 500+ 字符的文章段落
- 特殊字符: "Hello <script>alert('xss')</script> World"
- 多语言混合: "Hello 世界 Bonjour мир"

---

## 📋 Phase 1: 核心扫描与过滤测试

### TC-001: 基础节点扫描
**目标**: 验证扫描器正确识别可翻译元素

**测试步骤**:
1. 打开测试页面 (包含 p, h1-h6, li, td 元素)
2. 打开开发者工具 Console
3. 执行: `window.testScanner()`
4. 查看控制台输出

**预期结果**:
- ✅ 正确识别所有 p, h1-h6, li, td 元素
- ✅ 排除 code, pre, script 元素
- ✅ 过滤空白和短文本 (<3字符)
- ✅ 统计信息准确显示

### TC-002: 文本过滤验证
**测试步骤**:
1. Console 执行: `window.testTextFilter()`
2. 检查各种文本类型的过滤结果

**预期结果**:
- ✅ 纯数字/符号被过滤
- ✅ 代码片段被识别并过滤
- ✅ 正常文本通过验证
- ✅ URL和邮箱被正确处理

---

## 🛡️ Phase 2: 安全DOM注入测试

### TC-003: XSS防护测试
**测试步骤**:
1. Console 执行恶意内容注入测试
2. 尝试注入: `window.testXSSProtection()`
3. 检查页面是否受到XSS攻击

**预期结果**:
- ✅ 恶意脚本被阻止执行
- ✅ HTML标签被正确转义
- ✅ 控制台显示安全警告
- ✅ 页面功能正常

### TC-004: DOM注入完整性
**测试步骤**:
1. 选择页面中的段落文本
2. 执行: `window.testDOMInjection()`
3. 检查注入的翻译元素结构

**预期结果**:
- ✅ lu-wrapper 和 lu-block 结构正确
- ✅ notranslate 类名存在
- ✅ 语言属性正确设置
- ✅ 无障碍属性完整

---

## 🎛️ Phase 3: 状态管理测试

### TC-005: 三模式切换测试
**测试步骤**:
1. 确保页面有翻译内容
2. 按快捷键或点击切换按钮
3. 验证三种模式: origin → dual → trans → origin

**预期结果**:
- ✅ origin模式: 只显示原文，翻译隐藏
- ✅ dual模式: 原文+翻译都显示
- ✅ trans模式: 原文弱化，翻译突出
- ✅ 切换动画流畅

### TC-006: MutationObserver测试
**测试步骤**:
1. 在dual/trans模式下
2. 动态添加新内容: `window.testDynamicContent()`
3. 检查新内容是否被自动检测

**预期结果**:
- ✅ 新增节点被自动扫描
- ✅ 符合条件的节点触发翻译
- ✅ origin模式下Observer停止工作
- ✅ 性能无明显影响

---

## ⚡ Phase 4: 并发控制测试

### TC-007: 并发翻译测试
**测试步骤**:
1. 打开包含大量文本的页面 (50+ 段落)
2. 开始翻译: `window.testConcurrentTranslation()`
3. 观察Network面板和Console

**预期结果**:
- ✅ 最多3个并发请求 (可配置)
- ✅ 请求间隔符合速率限制
- ✅ 失败请求自动重试
- ✅ 进度反馈实时更新

### TC-008: 优先级处理测试
**测试步骤**:
1. 滚动到页面中间位置
2. 开始翻译并观察处理顺序
3. 检查哪些元素优先被翻译

**预期结果**:
- ✅ 可见元素优先翻译
- ✅ 标题元素 (h1-h3) 最高优先级
- ✅ 短文本优先于长文本
- ✅ 翻译顺序符合优先级

---

## 👁️ Phase 5: 懒加载优化测试

### TC-009: 大页面性能测试
**测试步骤**:
1. 打开超长页面 (1000+ 段落)
2. 开始翻译并监控性能
3. 执行: `window.testPerformanceMetrics()`

**预期结果**:
- ✅ 内存使用稳定增长，无暴涨
- ✅ FPS保持在45+
- ✅ 页面滚动流畅
- ✅ 懒加载正确触发

### TC-010: IntersectionObserver测试
**测试步骤**:
1. 在长页面上开始翻译
2. 慢速滚动页面
3. 观察翻译元素的出现时机

**预期结果**:
- ✅ 进入视窗前100px开始翻译
- ✅ 离开视窗的元素可能被清理
- ✅ 翻译触发时机准确
- ✅ 无重复翻译

---

## 🔧 集成测试用例

### TC-011: 完整工作流测试
**测试步骤**:
1. 打开新闻网站 (如 BBC, CNN)
2. 点击扩展图标开始翻译
3. 切换不同显示模式
4. 滚动页面查看懒加载
5. 检查内存和性能

**预期结果**:
- ✅ 整个流程无错误
- ✅ 翻译质量可接受
- ✅ 页面布局不被破坏
- ✅ 性能指标正常

### TC-012: 边界情况测试
**测试步骤**:
1. 测试空页面
2. 测试只有图片的页面
3. 测试嵌套很深的DOM结构
4. 测试动态生成的内容

**预期结果**:
- ✅ 空页面优雅处理
- ✅ 无文本页面正常退出
- ✅ 复杂DOM结构正确处理
- ✅ 动态内容实时响应

---

## 🎯 专项测试

### TC-013: 内存泄漏测试
**测试步骤**:
1. 打开Chrome任务管理器
2. 记录初始内存使用
3. 连续翻译/清理 10次
4. 检查内存是否持续增长

**预期结果**:
- ✅ 内存使用趋于稳定
- ✅ 清理后内存有效释放
- ✅ 无明显内存泄漏
- ✅ GC后内存回收正常

### TC-014: 错误恢复测试
**测试步骤**:
1. 断开网络连接
2. 尝试翻译内容
3. 恢复网络连接
4. 检查重试机制

**预期结果**:
- ✅ 网络错误优雅处理
- ✅ 重试机制正常工作
- ✅ 用户得到适当反馈
- ✅ 恢复后继续正常工作

### TC-015: 多标签页测试
**测试步骤**:
1. 打开3个不同网站标签页
2. 在每个标签页启用翻译
3. 快速切换标签页
4. 检查状态同步

**预期结果**:
- ✅ 每个标签页独立工作
- ✅ 切换标签页无冲突
- ✅ 状态正确保持
- ✅ 资源合理分配

---

## 🛠️ 测试工具和命令

### 开发者工具命令
```javascript
// 基础测试命令
window.testScanner()           // 测试节点扫描
window.testTextFilter()        // 测试文本过滤  
window.testXSSProtection()     // 测试XSS防护
window.testDOMInjection()      // 测试DOM注入
window.testConcurrentTranslation() // 测试并发翻译
window.testPerformanceMetrics()     // 测试性能指标

// 状态管理
window.lucidExt.toggleView()   // 切换显示模式
window.lucidExt.getStats()     // 获取统计信息
window.lucidExt.clearAll()     // 清理所有翻译

// 性能分析
window.lucidExt.analyzePerformance() // 性能分析
window.lucidExt.cleanup()      // 执行清理
```

### 快捷键测试
- `Alt + T`: 开始/停止翻译
- `Alt + V`: 切换显示模式  
- `Alt + C`: 清理翻译内容
- `Alt + D`: 开启/关闭调试模式

---

## 📊 测试结果记录模板

### 测试环境信息
- 浏览器版本: ____________
- 操作系统: ______________
- 扩展版本: ______________
- 测试日期: ______________

### 测试结果汇总
| 测试用例 | 状态 | 备注 |
|---------|------|------|
| TC-001  | ✅/❌ |      |
| TC-002  | ✅/❌ |      |
| ...     | ✅/❌ |      |

### 发现的问题
1. **问题描述**: ________________
   - **严重程度**: High/Medium/Low
   - **复现步骤**: ________________
   - **预期结果**: ________________
   - **实际结果**: ________________

### 性能基准
- **页面加载时间**: ______ms
- **翻译完成时间**: ______ms (100个元素)
- **内存峰值使用**: ______MB
- **平均FPS**: ____________

---

## 🔍 调试技巧

### Console调试
```javascript
// 开启详细日志
localStorage.setItem('lucid-debug', 'true')

// 查看内部状态
console.table(window.lucidExt.getInternalState())

// 监控性能
performance.mark('translation-start')
// ... 执行翻译
performance.mark('translation-end')
performance.measure('translation', 'translation-start', 'translation-end')
```

### 问题排查
1. **翻译不显示**: 检查CSS样式和lu-view属性
2. **性能卡顿**: 查看并发设置和懒加载配置
3. **内存泄漏**: 使用Chrome DevTools Memory面板
4. **请求失败**: 检查Network面板和错误日志

---

Ready for testing! 🚀

让我知道测试结果，我可以根据发现的问题进行针对性优化。