<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lucid Extension Test Page</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .btn {
            display: inline-block;
            margin: 5px;
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .btn:hover { background: #45a049; }
        .btn.danger { background: #f44336; }
        .btn.info { background: #2196F3; }
        .long-content {
            height: 2000px;
            background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
            padding: 20px;
            margin: 20px 0;
        }
        .performance-info {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        code {
            background: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
        }
        pre {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .malicious {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h4 style="margin-top: 0;">Test Controls</h4>
        <button class="btn" onclick="runBasicTest()">Basic Test</button>
        <button class="btn" onclick="testScanner()">Test Scanner</button>
        <button class="btn" onclick="testSecurity()">Test Security</button>
        <button class="btn info" onclick="testPerformance()">Performance</button>
        <button class="btn danger" onclick="clearAll()">Clear All</button>
        <div style="margin-top: 10px;">
            <small>Press Alt+T to toggle translation</small>
        </div>
    </div>

    <div class="performance-info" id="performance-info">
        FPS: <span id="fps">--</span> | 
        Memory: <span id="memory">--</span>MB |
        Elements: <span id="elements">--</span>
    </div>

    <div class="container">
        <h1>Lucid Extension Comprehensive Test Page</h1>
        <p>This page contains various elements to test the translation extension functionality, performance, and security features.</p>

        <!-- Basic Text Elements -->
        <div class="test-section">
            <h3>📝 Basic Text Elements</h3>
            <h1>Main Heading (H1)</h1>
            <h2>Secondary Heading (H2)</h2>
            <h3>Tertiary Heading (H3)</h3>
            <h4>Quaternary Heading (H4)</h4>
            <h5>Quinary Heading (H5)</h5>
            <h6>Senary Heading (H6)</h6>
            
            <p>This is a regular paragraph with some meaningful content that should be translated. It contains enough text to be considered worthy of translation by the filter system.</p>
            
            <p>Another paragraph with different content to test multiple element handling and concurrent translation capabilities.</p>
            
            <p>Short text.</p>
            <p>A</p> <!-- Should be filtered out -->
            <p>   </p> <!-- Empty, should be filtered -->
            <p>123456</p> <!-- Numbers only, should be filtered -->
            <p>This paragraph contains mixed content with numbers 123 and symbols !@# but should still be translated.</p>
        </div>

        <!-- List Elements -->
        <div class="test-section">
            <h3>📋 List Elements</h3>
            <ul>
                <li>First list item with translatable content</li>
                <li>Second list item with different text</li>
                <li>Third item containing some technical terms and descriptions</li>
                <li>A</li> <!-- Short, should be filtered -->
            </ul>
            
            <ol>
                <li>Numbered list item one</li>
                <li>Numbered list item two with more detailed explanation</li>
                <li>Final numbered item</li>
            </ol>
        </div>

        <!-- Table Elements -->
        <div class="test-section">
            <h3>📊 Table Elements</h3>
            <table>
                <thead>
                    <tr>
                        <th>Header One</th>
                        <th>Header Two</th>
                        <th>Header Three</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Table cell with translatable content</td>
                        <td>Another cell with different text</td>
                        <td>Third cell content</td>
                    </tr>
                    <tr>
                        <td>Second row first cell</td>
                        <td>Second row second cell with longer content</td>
                        <td>X</td> <!-- Short, should be filtered -->
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Code Elements (Should be excluded) -->
        <div class="test-section">
            <h3>💻 Code Elements (Should NOT be translated)</h3>
            <p>This paragraph should be translated, but the code below should not:</p>
            <pre><code>
function translateText(text) {
    return fetch('/translate', {
        method: 'POST',
        body: JSON.stringify({ text })
    });
}
            </code></pre>
            <p>Inline code like <code>console.log('hello')</code> should not be translated either.</p>
        </div>

        <!-- Security Testing -->
        <div class="test-section">
            <h3>🛡️ Security Testing Elements</h3>
            <p>This section tests XSS protection and content sanitization:</p>
            <p class="malicious">Text with &lt;script&gt;alert('XSS')&lt;/script&gt; should be sanitized</p>
            <p>Content with HTML &lt;img src="x" onerror="alert('XSS')"&gt; should be safe</p>
            <p>JavaScript: protocol should be blocked: &lt;a href="javascript:alert('XSS')"&gt;link&lt;/a&gt;</p>
        </div>

        <!-- Performance Testing -->
        <div class="test-section">
            <h3>⚡ Performance Testing</h3>
            <p>The following section contains many elements to test performance and lazy loading:</p>
            <div id="performance-content">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <!-- Dynamic Content -->
        <div class="test-section">
            <h3>🔄 Dynamic Content Testing</h3>
            <p>This section tests dynamic content detection:</p>
            <div id="dynamic-content">
                <p>Initial content that should be translated</p>
            </div>
            <button class="btn" onclick="addDynamicContent()">Add Dynamic Content</button>
        </div>

        <!-- Long Content for Lazy Loading -->
        <div class="test-section">
            <h3>📜 Long Content (Lazy Loading Test)</h3>
            <p>Scroll down to test lazy loading functionality:</p>
            <div class="long-content" id="long-content">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <!-- Mixed Language Content -->
        <div class="test-section">
            <h3>🌍 Mixed Language Content</h3>
            <p>Hello world this is English text</p>
            <p>Bonjour le monde c'est du texte français</p>
            <p>Hola mundo este es texto en español</p>
            <p>Mixed content: Hello 世界 Bonjour мир</p>
        </div>

        <!-- Edge Cases -->
        <div class="test-section">
            <h3>🎯 Edge Cases</h3>
            <p>Very long paragraph with lots of content to test the handling of large text blocks and ensure that the translation system can handle extensive content without performance degradation or memory issues. This paragraph continues for a while to simulate real-world scenarios where users might encounter lengthy articles or documentation that needs to be translated. The system should handle this gracefully and efficiently.</p>
            
            <p>Paragraph with special characters: !@#$%^&*()_+-=[]{}|;':\",./<>?`~</p>
            
            <p>Text with URLs: Visit https://example.com <NAME_EMAIL></p>
            
            <p>Numbers mixed with text: The temperature is 25°C and the price is $99.99</p>
        </div>
    </div>

    <!-- Mock翻译服务集成提示 -->
    <script>
        // 注意：Mock翻译服务将通过扩展Content Script注入
        console.log('📋 Test page ready - Mock translation will be available via extension');
    </script>
    
    <script>
        // Performance monitoring
        let frameCount = 0;
        let lastTime = performance.now();
        let fps = 0;

        function updatePerformanceInfo() {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                fps = frameCount;
                frameCount = 0;
                lastTime = currentTime;
                
                document.getElementById('fps').textContent = fps;
                
                // Memory info
                if (performance.memory) {
                    const memory = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
                    document.getElementById('memory').textContent = memory;
                }
                
                // Element count
                const elements = document.querySelectorAll('*').length;
                document.getElementById('elements').textContent = elements;
            }
            
            requestAnimationFrame(updatePerformanceInfo);
        }
        updatePerformanceInfo();

        // Generate performance testing content
        function generatePerformanceContent() {
            const container = document.getElementById('performance-content');
            for (let i = 0; i < 100; i++) {
                const p = document.createElement('p');
                p.textContent = `Performance test paragraph ${i + 1}. This is paragraph number ${i + 1} out of 100 paragraphs designed to test the translation system's performance with many elements.`;
                container.appendChild(p);
            }
        }

        // Generate long content for lazy loading
        function generateLongContent() {
            const container = document.getElementById('long-content');
            for (let i = 0; i < 200; i++) {
                const p = document.createElement('p');
                p.textContent = `Long content paragraph ${i + 1}. This paragraph is part of a long document to test lazy loading functionality. It should only be translated when it comes into view.`;
                p.style.marginBottom = '20px';
                container.appendChild(p);
            }
        }

        // Test functions for the extension
        window.runBasicTest = function() {
            console.log('🧪 Running basic translation test...');
            if (window.lucidExt) {
                window.lucidExt.translatePage().then(() => {
                    console.log('✅ Basic test completed');
                }).catch(err => {
                    console.error('❌ Basic test failed:', err);
                });
            } else {
                console.error('❌ Extension not detected');
            }
        };

        window.testScanner = function() {
            console.log('🔍 Testing node scanner...');
            const allElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, td, th');
            const filteredElements = Array.from(allElements).filter(el => {
                const text = el.textContent?.trim() || '';
                return text.length > 3 && !/^\d+$/.test(text) && text !== '';
            });
            
            console.log(`📊 Scanner results:
            - Total elements found: ${allElements.length}
            - After filtering: ${filteredElements.length}
            - Filtered out: ${allElements.length - filteredElements.length}`);
            
            console.table({
                'Total Elements': allElements.length,
                'Valid Elements': filteredElements.length,
                'Filtered Out': allElements.length - filteredElements.length
            });
        };

        window.testSecurity = function() {
            console.log('🛡️ Testing security features...');
            
            // Test XSS protection
            const maliciousTexts = [];
            maliciousTexts.push('<script>alert("XSS")</script>');
            maliciousTexts.push('<img src="x" onerror="alert(' + "'XSS'" + ')">');
            maliciousTexts.push('javascript:alert("XSS")');
            maliciousTexts.push('<iframe src="javascript:alert(' + "'XSS'" + ')"></iframe>');
            maliciousTexts.push('onclick="alert(' + "'XSS'" + ')"');
            
            maliciousTexts.forEach((text, index) => {
                console.log(`Testing malicious content ${index + 1}:`, text);
                // Extension should sanitize this content
            });
            
            console.log('✅ Security test completed - check console for warnings');
        };

        window.testPerformance = function() {
            console.log('⚡ Running performance test...');
            const startTime = performance.now();
            const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            
            // Simulate heavy load
            if (window.lucidExt) {
                window.lucidExt.translatePage().then(() => {
                    const endTime = performance.now();
                    const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
                    
                    console.log(`📊 Performance Results:
                    - Execution time: ${(endTime - startTime).toFixed(2)}ms
                    - Memory delta: ${((endMemory - startMemory) / 1024 / 1024).toFixed(2)}MB
                    - Current FPS: ${fps}
                    - DOM elements: ${document.querySelectorAll('*').length}`);
                });
            }
        };

        window.clearAll = function() {
            console.log('🧹 Clearing all translations...');
            if (window.lucidExt) {
                window.lucidExt.clearTranslations();
                console.log('✅ All translations cleared');
            }
        };

        window.addDynamicContent = function() {
            const container = document.getElementById('dynamic-content');
            const p = document.createElement('p');
            p.textContent = `Dynamic content added at ${new Date().toLocaleTimeString()}. This content was added after page load and should be automatically detected and translated.`;
            container.appendChild(p);
            console.log('➕ Dynamic content added');
        };

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.altKey) {
                switch(e.key.toLowerCase()) {
                    case 't':
                        e.preventDefault();
                        runBasicTest();
                        break;
                    case 'v':
                        e.preventDefault();
                        if (window.lucidExt) {
                            window.lucidExt.toggleView();
                        }
                        break;
                    case 'c':
                        e.preventDefault();
                        clearAll();
                        break;
                    case 'd':
                        e.preventDefault();
                        console.log('🐛 Debug mode toggled');
                        break;
                }
            }
        });

        // Initialize test content
        document.addEventListener('DOMContentLoaded', function() {
            generatePerformanceContent();
            generateLongContent();
            console.log('🚀 Test page initialized. Use the control panel or keyboard shortcuts to test the extension.');
            console.log('📋 Available commands: runBasicTest(), testScanner(), testSecurity(), testPerformance(), clearAll()');
        });

        // Expose test utilities globally
        window.testUtils = {
            generateTestData: function(count = 10) {
                const testData = [];
                for (let i = 0; i < count; i++) {
                    testData.push(`Test content item ${i + 1} with some meaningful text to translate.`);
                }
                return testData;
            },
            
            measureTranslationSpeed: function() {
                const start = performance.now();
                return {
                    stop: function() {
                        return performance.now() - start;
                    }
                };
            },
            
            getPageStats: function() {
                return {
                    totalElements: document.querySelectorAll('*').length,
                    translatableElements: document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, td, th').length,
                    translatedElements: document.querySelectorAll('.lu-wrapper').length,
                    memoryUsage: performance.memory ? (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + 'MB' : 'N/A',
                    currentFPS: fps
                };
            }
        };
    </script>
</body>
</html>