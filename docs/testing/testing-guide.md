# Lucid Extension 测试指南

## 🚀 快速开始测试

### 1. 加载扩展
```bash
# 构建扩展
pnpm run dev

# 在Chrome中:
# 1. 打开 chrome://extensions/
# 2. 开启"开发者模式"
# 3. 点击"加载已解压的扩展程序"
# 4. 选择 .output/chrome-mv3 文件夹
```

### 2. 打开测试页面
- 在浏览器中打开: `docs/testing/test-page.html`
- 或访问任意网站（如 BBC、CNN、GitHub）

### 3. 开始测试
- 按 `F12` 打开开发者工具
- 在 Console 中运行测试命令

---

## 🧪 自动化测试套件

### 运行完整测试
```javascript
// 运行所有自动化测试
testExtension.runAllTests()
```

### 单项测试
```javascript
// 基础翻译功能
testExtension.runBasicTest()

// 扫描器测试
testExtension.testScanner()

// 安全防护测试  
testExtension.testSecurity()

// 性能基准测试
testExtension.testPerformance()
```

---

## 📋 核心功能测试清单

### ✅ Phase 1: 扫描与过滤
**测试命令**: `window.testScanner()`

- [ ] 正确识别 p, h1-h6, li, td 元素
- [ ] 排除 code, pre, script 元素
- [ ] 过滤短文本 (<3字符)
- [ ] 过滤纯数字/符号
- [ ] 性能指标正常

### ✅ Phase 2: 安全注入
**测试命令**: `window.testXSSProtection()`

- [ ] 阻止 `<script>` 标签执行
- [ ] 转义 HTML 特殊字符
- [ ] 过滤 `javascript:` 协议
- [ ] DOM结构完整性
- [ ] notranslate 类名正确

### ✅ Phase 3: 状态管理
**快捷键**: `Alt + V`

- [ ] origin模式: 隐藏翻译
- [ ] dual模式: 显示原文+翻译
- [ ] trans模式: 弱化原文
- [ ] 状态持久化
- [ ] 动态内容检测

### ✅ Phase 4: 并发控制
**观察**: Network 面板

- [ ] 最大3个并发请求
- [ ] 请求间隔正确
- [ ] 失败自动重试
- [ ] 优先级处理
- [ ] 进度反馈

### ✅ Phase 5: 性能优化
**测试命令**: `testExtension.testPerformance()`

- [ ] 内存使用稳定
- [ ] FPS保持45+
- [ ] 懒加载触发正确
- [ ] 大页面处理流畅
- [ ] 自动性能清理

---

## 🎯 实际场景测试

### 测试网站推荐
1. **新闻网站**: BBC News, CNN
2. **技术博客**: Medium, Dev.to
3. **文档网站**: MDN, GitHub README
4. **电商网站**: Amazon 产品页
5. **社交媒体**: Twitter, LinkedIn

### 测试场景
```javascript
// 1. 基础翻译流程
testExtension.runBasicTest()

// 2. 切换显示模式  
testExtension.toggleView()

// 3. 性能压力测试
testExtension.testPerformance()

// 4. 清理功能
testExtension.clearAll()
```

---

## 🔧 调试工具

### 开启调试模式
```javascript
// 开启详细日志
localStorage.setItem('lucid-debug', 'true')

// 重新加载页面生效
location.reload()
```

### 性能监控
```javascript
// 实时性能指标
setInterval(() => {
  console.log('性能状态:', {
    内存: window.testUtils.getPageStats().memoryUsage,
    FPS: window.testUtils.getPageStats().currentFPS,
    翻译元素: window.testUtils.getPageStats().translatedElements
  })
}, 2000)
```

### Chrome DevTools 技巧
1. **Elements面板**: 检查 `lu-view` 属性和翻译结构
2. **Console面板**: 运行测试命令和查看日志
3. **Network面板**: 监控翻译API请求
4. **Performance面板**: 分析渲染性能
5. **Memory面板**: 检测内存泄漏

---

## 🐛 常见问题排查

### 翻译不显示
```javascript
// 检查当前状态
document.documentElement.getAttribute('lu-view')

// 检查翻译元素
document.querySelectorAll('.lu-wrapper').length

// 手动切换模式
testExtension.toggleView()
```

### 性能问题
```javascript
// 检查性能指标
testExtension.testPerformance()

// 强制清理
window.lucidExt?.performanceOptimizer?.performCleanup?.()

// 查看内存使用
performance.memory.usedJSHeapSize / 1024 / 1024 + 'MB'
```

### 安全问题
```javascript
// 测试XSS防护
testExtension.testSecurity()

// 查看安全日志
console.log('检查是否有安全警告')
```

---

## 📊 测试报告模板

### 基础信息
- **测试日期**: ___________
- **浏览器版本**: _________  
- **操作系统**: ___________
- **扩展版本**: ___________

### 功能测试
| 功能模块 | 测试结果 | 执行时间 | 备注 |
|---------|---------|---------|------|
| 节点扫描 | ✅/❌ | ___ms | |
| 安全防护 | ✅/❌ | ___ms | |
| 状态管理 | ✅/❌ | ___ms | |
| 并发控制 | ✅/❌ | ___ms | |
| 性能优化 | ✅/❌ | ___ms | |

### 性能基准
```javascript
// 获取性能数据
const perf = await testExtension.testPerformance()
console.table(perf)
```

- **翻译速度**: ___ms/元素
- **内存峰值**: ___MB
- **FPS稳定性**: ___fps
- **并发效率**: ___%

### 发现的问题
1. **问题描述**: _______________
   - **严重程度**: High/Medium/Low
   - **复现步骤**: _______________
   - **预期vs实际**: _____________

---

## 🚀 高级测试技巧

### 压力测试
```javascript
// 大量元素测试
Array.from({length: 1000}, (_, i) => {
  const p = document.createElement('p')
  p.textContent = `压力测试段落 ${i + 1}`
  document.body.appendChild(p)
})

// 开始翻译
testExtension.runBasicTest()
```

### 内存泄漏检测
```javascript
// 循环测试检测内存泄漏
for(let i = 0; i < 10; i++) {
  await testExtension.runBasicTest()
  await new Promise(r => setTimeout(r, 1000))
  testExtension.clearAll()
  console.log(`第${i+1}轮内存:`, performance.memory.usedJSHeapSize / 1024 / 1024 + 'MB')
}
```

### 并发测试
```javascript
// 多标签页并发测试
// 在多个标签页同时运行
Promise.all([
  testExtension.runBasicTest(),
  testExtension.runBasicTest(),
  testExtension.runBasicTest()
])
```

---

## 📋 测试检查清单

### 安装前检查
- [ ] Chrome/Edge 浏览器最新版
- [ ] 开发者模式已开启
- [ ] 扩展构建成功

### 基础功能检查
- [ ] 扩展图标正常显示
- [ ] 快捷键响应正常 (Alt+T, Alt+V, Alt+C)
- [ ] 控制台无错误信息
- [ ] 翻译API连接正常

### 性能检查
- [ ] 内存使用 < 100MB
- [ ] FPS > 45
- [ ] 页面滚动流畅
- [ ] 无明显卡顿

### 兼容性检查
- [ ] 主流网站正常工作
- [ ] 复杂页面结构支持
- [ ] 动态内容处理
- [ ] 响应式设计适配

---

Ready for comprehensive testing! 🎉

记住：好的测试不仅要验证功能正确性，更要确保用户体验的流畅性和系统的稳定性。