# 🚀 Lucid Extension 快速测试指南

## 1️⃣ 基础设置

### 加载扩展
1. 运行 `pnpm run dev`
2. 在 Chrome 中打开 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `.output/chromium-mv3-dev` 文件夹

### 确认扩展加载成功
- ✅ 扩展列表中显示 "Lucid Extension"
- ✅ 右键菜单中有 "打开 Lucid 设置" 选项

---

## 2️⃣ 通过右键菜单测试

### 基础翻译测试
1. **打开测试页面**: `docs/testing/test-page.html`
2. **选择一段文字**（比如 "This is a regular paragraph..."）
3. **右键 → "打开 Lucid 设置"**
4. **观察结果**:
   - 是否出现翻译内容
   - 页面布局是否正常
   - Console 是否有错误

### 在真实网站测试
1. 打开 **BBC News** 或 **CNN**
2. 右键任意文字 → "打开 Lucid 设置"
3. 观察翻译效果

---

## 3️⃣ 手动功能验证

### A. 状态切换测试
**操作**: 翻译后按 `Alt + V` 或通过扩展弹窗切换
**预期**:
- `origin`: 只显示英文，翻译隐藏
- `dual`: 英文+中文都显示  
- `trans`: 英文变淡，中文突出

### B. 安全测试
**操作**: 在含有以下内容的页面测试翻译：
```html
<script>alert('XSS')</script>
<img src="x" onerror="alert('XSS')">
```
**预期**: 恶意代码不执行，内容被安全清理

### C. 性能测试
**操作**: 在长页面（如 Wikipedia 长文章）测试
**预期**: 
- 页面滚动流畅
- 内存使用稳定
- 翻译逐步加载

---

## 4️⃣ 开发者工具检查

### Console 检查
```javascript
// 检查扩展是否正确加载
window.lucidExt

// 检查当前状态
document.documentElement.getAttribute('lu-view')

// 查看翻译元素数量
document.querySelectorAll('.lu-wrapper').length
```

### Elements 面板检查
1. 查找 `<html lu-view="dual">` 属性
2. 查找 `.lu-wrapper` 和 `.lu-block` 元素
3. 检查元素结构是否正确

### Network 面板检查
- 翻译请求是否正常发送
- 请求数量是否合理（不超过3个并发）
- 是否有失败的请求

---

## 5️⃣ 问题排查

### ❌ 翻译不显示
**检查步骤**:
1. Console 是否有错误信息
2. `document.querySelector('.lu-wrapper')` 是否返回元素
3. CSS 是否正确加载
4. `lu-view` 属性是否设置

**解决方案**:
```javascript
// 强制设置为双语模式
document.documentElement.setAttribute('lu-view', 'dual')

// 检查样式是否生效
getComputedStyle(document.querySelector('.lu-wrapper')).display
```

### ❌ 性能卡顿
**检查步骤**:
1. Task Manager 中扩展内存使用
2. Performance 面板录制分析
3. 页面元素数量是否过多

**解决方案**:
```javascript
// 检查性能指标
performance.memory.usedJSHeapSize / 1024 / 1024 + 'MB'

// 强制清理
document.querySelectorAll('.lu-wrapper').forEach(el => el.remove())
```

### ❌ 右键菜单不显示
**检查步骤**:
1. 扩展是否正确加载
2. Content script 是否注入
3. 权限是否正确设置

---

## 6️⃣ 测试场景清单

### ✅ 基础功能
- [ ] 右键菜单出现
- [ ] 翻译内容显示
- [ ] 状态切换正常
- [ ] 页面布局不破坏

### ✅ 安全功能  
- [ ] XSS 攻击被阻止
- [ ] 恶意内容被清理
- [ ] 无意外脚本执行

### ✅ 性能功能
- [ ] 大页面流畅运行
- [ ] 内存使用合理
- [ ] 懒加载正确触发

### ✅ 兼容性
- [ ] 新闻网站正常
- [ ] 技术博客正常  
- [ ] 社交媒体正常
- [ ] 电商网站正常

---

## 7️⃣ 快速调试命令

### Mock翻译测试（推荐用于本地测试）

如果扩展检测到测试环境（如test-page.html），会自动启用Mock翻译服务来避免CORS问题：

```javascript
// 测试Mock翻译
testTranslation("Hello World")

// 测试整页翻译
testPageTranslation()

// 测试DOM注入
testDOMInjection()

// 测试视图模式切换
testViewModes()
```

### 在页面 Console 中运行：

```javascript
// 1. 基础状态检查
console.log('扩展状态:', {
  loaded: !!window.lucidExt,
  mockEnabled: !!window.mockTranslate,
  viewMode: document.documentElement.getAttribute('lu-view'),
  translatedElements: document.querySelectorAll('.lu-wrapper').length
})

// 2. 强制翻译测试
if (window.lucidExt) {
  window.lucidExt.translateCurrentPage?.()
} else {
  console.error('扩展未正确加载')
}

// 3. 切换显示模式
if (window.lucidExt) {
  window.lucidExt.toggleTranslationView?.()
}

// 4. 性能检查
console.log('性能指标:', {
  memory: performance.memory?.usedJSHeapSize / 1024 / 1024 + 'MB',
  elements: document.querySelectorAll('*').length,
  translated: document.querySelectorAll('.lu-wrapper').length
})
```

---

## 8️⃣ 成功标准

### 🎯 必须通过的测试
1. **功能性**: 能正确翻译和显示内容
2. **安全性**: 不执行恶意脚本，内容安全
3. **性能性**: 大页面(<1000元素)流畅运行
4. **稳定性**: 长时间使用无崩溃
5. **兼容性**: 主流网站正常工作

### 📊 性能基准
- **翻译速度**: < 100ms/元素
- **内存使用**: < 100MB
- **FPS**: > 45fps
- **成功率**: > 95%

---

Ready to test! 🚀

**提示**: 如果遇到问题，先检查 Console 是否有错误信息，然后按照排查步骤逐一检查。