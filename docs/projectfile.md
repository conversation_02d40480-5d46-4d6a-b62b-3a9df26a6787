# Lucid Extension 项目文档

**项目类型**: 浏览器扩展 (Browser Extension)  
**技术栈**: WXT + React + TypeScript  
**功能**: 智能高亮与查词浏览器扩展  

---

## 📦 项目结构

```
📦 lucid-ext/
├── 📋 CLAUDE.md - Claude Code 开发指导文档
├── 📋 INTERACTIVE_TOOLTIP.md - 交互式提示框功能文档
├── 📋 PATH_ALIAS_INVESTIGATION.md - 路径别名调研文档
├── 📋 README.md - 项目说明文档
├── 📁 assets/ - 静态资源目录
│   └── 🖼️ react.svg - React 图标
├── 📁 coverage/ - 测试覆盖率报告
│   ├── 🎨 base.css - 覆盖率报告基础样式
│   ├── 🔧 block-navigation.js - 代码块导航脚本
│   ├── ⚙️ coverage-final.json - 最终覆盖率数据
│   ├── 📁 entrypoints/ - 入口文件覆盖率报告
│   │   ├── 📋 background.ts.html - 后台脚本覆盖率
│   │   ├── 📋 content.ts.html - 内容脚本覆盖率
│   │   ├── 📋 index.html - 覆盖率主页
│   │   └── 📁 popup/ - 弹窗组件覆盖率
│   │       ├── 📋 App.tsx.html - 主应用组件覆盖率
│   │       ├── 📋 index.html - 弹窗覆盖率首页
│   │       └── 📋 main.tsx.html - 弹窗入口覆盖率
│   ├── 🖼️ favicon.png - 覆盖率报告图标
│   ├── 📋 index.html - 覆盖率报告主页
│   ├── 🎨 prettify.css - 代码美化样式
│   ├── 🔧 prettify.js - 代码美化脚本
│   ├── 🖼️ sort-arrow-sprite.png - 排序箭头图标
│   ├── 🔧 sorter.js - 排序功能脚本
│   └── 📁 src/ - 源码覆盖率详情
│       ├── 📁 components/ - 组件覆盖率
│       ├── 📁 features/ - 功能模块覆盖率
│       └── 📁 services/ - 服务层覆盖率
├── 📁 docs/ - 项目文档目录
│   ├── 📁 code2prompt-output/ - 代码提取输出
│   │   ├── 📋 Lucid-ext-config.md - 配置文档
│   │   ├── 📋 Lucid-ext-docs.md - 文档合集
│   │   ├── 📋 Lucid-ext-full.md - 完整项目文档
│   │   ├── ⚙️ Lucid-ext-source.json - 源码数据(JSON)
│   │   ├── 📋 Lucid-ext-source.md - 源码文档
│   │   ├── 📋 lucid-config.md - Lucid 配置说明
│   │   ├── 📋 lucid-docs.md - Lucid 使用文档
│   │   ├── 📋 lucid-full.md - Lucid 完整说明
│   │   ├── ⚙️ lucid-source.json - Lucid 源数据
│   │   └── 📋 lucid-source.md - Lucid 源码说明
│   └── 📁 design/ - 设计文档
│       └── 📁 translate/ - 翻译功能设计
│           ├── 📋 api.md - API 设计文档
│           ├── 📋 plan.md - 功能规划文档
│           └── 📋 task.md - 任务分解文档
├── 📁 entrypoints/ - WXT 扩展入口点
│   ├── ⚙️ background.ts - 后台服务工作脚本
│   ├── ⚙️ content.ts - 内容脚本 (注入网页)
│   └── 📁 popup/ - 扩展弹窗界面
│       ├── 🎨 App.css - 主应用样式
│       ├── 🧩 App.tsx - 主应用组件
│       ├── 📋 index.html - 弹窗HTML模板
│       ├── ⚙️ main.tsx - 弹窗React入口
│       └── 🎨 style.css - 弹窗全局样式
├── 📁 logs/ - 日志文件目录
│   ├── ⚙️ chat.json - 聊天记录日志
│   ├── ⚙️ notification.json - 通知日志
│   ├── ⚙️ post_tool_use.json - 工具使用后日志
│   ├── ⚙️ pre_tool_use.json - 工具使用前日志
│   └── ⚙️ stop.json - 停止操作日志
├── 📁 node_modules/ - NPM 依赖包目录
├── ⚙️ package.json - 项目依赖配置文件
├── ⚙️ pnpm-lock.yaml - PNPM 锁定版本文件
├── 📁 public/ - 公共静态资源
│   ├── 📁 icon/ - 扩展图标资源
│   │   ├── 🖼️ 128.png - 128x128 扩展图标
│   │   ├── 🖼️ 16.png - 16x16 扩展图标
│   │   ├── 🖼️ 32.png - 32x32 扩展图标
│   │   ├── 🖼️ 48.png - 48x48 扩展图标
│   │   └── 🖼️ 96.png - 96x96 扩展图标
│   └── 🖼️ wxt.svg - WXT 框架图标
├── 📁 scripts/ - 自动化脚本目录
│   ├── 📁 results/ - 脚本执行结果
│   │   ├── 📋 translate_test_20250717_214502.log - 翻译测试日志1
│   │   ├── 📋 translate_test_20250717_214548.log - 翻译测试日志2
│   │   ├── 📋 translate_test_20250717_214707.log - 翻译测试日志3
│   │   └── 📋 translate_test_20250717_214727.log - 翻译测试日志4
│   ├── 🔧 test-translate-api-concurrent.sh - 并发翻译API测试脚本
│   └── 🔧 test-translate-api.sh - 翻译API测试脚本
├── 📁 src/ - 主要源代码目录
│   ├── 📁 components/ - React 组件库
│   │   ├── 📁 DynamicTooltip/ - 动态提示框组件
│   │   │   ├── 🎨 DynamicTooltip.module.css - 动态提示框样式模块
│   │   │   ├── 🧩 DynamicTooltip.tsx - 动态提示框主组件
│   │   │   ├── 🧩 SimpleDemo.tsx - 简单演示组件
│   │   │   ├── 📋 TestPage.html - 测试页面
│   │   │   ├── 🧩 TooltipError.tsx - 提示框错误组件
│   │   │   ├── 🧩 TooltipSkeleton.tsx - 提示框骨架屏组件
│   │   │   ├── 📁 __tests__/ - 测试文件目录
│   │   │   │   └── 🧪 integration.test.tsx - 集成测试
│   │   │   ├── 📚 index.ts - 动态提示框导出文件
│   │   │   ├── 🧪 integration-test.tsx - 集成测试文件
│   │   │   ├── 🧪 simple-integration-test.ts - 简单集成测试
│   │   │   └── 📋 test-page.html - 测试页面
│   │   ├── 📁 Slider/ - 滑动面板组件
│   │   │   ├── 🎨 Slider.module.css - 滑动面板样式模块
│   │   │   ├── 🧩 Slider.tsx - 滑动面板主组件
│   │   │   ├── 📁 components/ - 滑动面板子组件
│   │   │   │   ├── 🧩 AccountView.tsx - 账户视图组件
│   │   │   │   ├── 🧩 LearnView.tsx - 学习视图组件
│   │   │   │   ├── 🧩 LoginView.tsx - 登录视图组件
│   │   │   │   ├── 🧩 MyView.tsx - 个人视图组件
│   │   │   │   ├── 🧩 SettingItem.tsx - 设置项组件
│   │   │   │   ├── 🧩 SettingsView.tsx - 设置视图组件
│   │   │   │   ├── 🧩 Switch.tsx - 开关组件
│   │   │   │   ├── 🧩 Typewriter.tsx - 打字机效果组件
│   │   │   │   └── 🧩 WordItem.tsx - 单词项组件
│   │   │   ├── 📁 icons/ - 图标组件
│   │   │   │   └── 🧩 index.tsx - 图标导出文件
│   │   │   └── 📚 index.ts - 滑动面板导出文件
│   │   └── 📁 Tooltip/ - 基础提示框组件
│   │       ├── 🎨 Tooltip.module.css - 提示框样式模块
│   │       ├── 🧩 Tooltip.tsx - 基础提示框组件
│   │       └── 🎨 interactive.css - 交互式样式
│   ├── 📁 constants/ - 常量定义
│   │   └── ⚙️ slider-config.ts - 滑动面板配置常量
│   ├── 📁 content/ - 内容脚本模块
│   │   ├── 🔧 debug-functions.ts - 调试功能函数
│   │   ├── 🔧 highlight-manager.ts - 高亮管理器
│   │   ├── 📚 index.ts - 内容脚本导出文件
│   │   ├── 🔧 interaction-handlers.ts - 交互事件处理器
│   │   ├── 🔧 slider-manager.ts - 滑动面板管理器
│   │   └── 🔧 tooltip-manager.ts - 提示框管理器
│   ├── 📁 features/ - 功能模块
│   │   ├── 📁 dictionary/ - 词典功能模块
│   │   │   ├── 📁 __mocks__/ - Mock 数据
│   │   │   │   └── 🧪 api-responses.ts - API 响应模拟数据
│   │   │   ├── 📁 __tests__/ - 测试文件
│   │   │   │   ├── 🧪 dictionary.api.test.ts - 词典API测试
│   │   │   │   ├── 🧪 dictionary.service.test.ts - 词典服务测试
│   │   │   │   ├── 🧪 preference.test.ts - 偏好设置测试
│   │   │   │   ├── 🧪 useDictionary.simple.test.ts - 简单词典Hook测试
│   │   │   │   └── 🧪 useDictionary.test.ts - 词典Hook测试
│   │   │   ├── 🔧 dictionary.api.ts - 词典API接口
│   │   │   ├── 🔧 dictionary.service.ts - 词典服务层
│   │   │   ├── 📚 index.ts - 词典模块导出
│   │   │   ├── ⚙️ preference.ts - 偏好设置配置
│   │   │   ├── 📚 types.ts - 词典类型定义
│   │   │   └── 🔧 useDictionary.ts - 词典React Hook
│   │   ├── 🔧 highlight.ts - 文本高亮功能
│   │   └── 📁 settings/ - 设置管理模块
│   │       ├── ⚙️ default-settings.ts - 默认设置配置
│   │       ├── 📚 index.ts - 设置模块导出
│   │       ├── 🔧 settings-manager.ts - 设置管理器
│   │       ├── 🔧 settings-storage.ts - 设置存储服务
│   │       ├── 📚 types.ts - 设置类型定义
│   │       └── 🔧 useSettings.ts - 设置React Hook
│   ├── 📁 services/ - 服务层模块
│   │   ├── 📁 __tests__/ - 服务测试
│   │   │   └── 🧪 storage.simple.test.ts - 存储服务简单测试
│   │   └── 🔧 storage.ts - 浏览器存储服务
│   ├── 📁 styles/ - 全局样式文件
│   │   ├── 🎨 highlight.css - 高亮功能样式
│   │   └── 🎨 lucid-ui.css - Lucid UI 主题样式
│   ├── 📁 test/ - 测试辅助工具
│   │   ├── 📋 e2e-tooltip-integration.html - E2E提示框集成测试页
│   │   ├── 📁 mocks/ - 测试模拟数据
│   │   │   └── 🧪 handlers.ts - MSW 请求处理器
│   │   └── ⚙️ setup.ts - 测试环境配置
│   ├── 📁 ui-manager/ - UI管理器模块
│   │   ├── 🔧 ShadowView.ts - Shadow DOM 视图管理
│   │   ├── 🧩 highlight-integration.tsx - 高亮集成组件
│   │   ├── 📚 index.ts - UI管理器导出
│   │   ├── 🎨 slider-styles.ts - 滑动面板样式管理
│   │   ├── 🔧 test-example.ts - 测试示例
│   │   ├── 🎨 tooltip-styles.ts - 提示框样式管理
│   │   ├── 📚 types.ts - UI管理器类型定义
│   │   └── 🔧 uiManager.ts - UI管理器主控制器
│   └── 📁 utils/ - 工具函数库
│       ├── 🔧 debug.ts - 调试工具函数
│       └── 🔧 error-handler.ts - 错误处理工具
├── 📋 test-highlight.html - 高亮功能测试页面
├── 🔧 test-integration.js - 集成测试脚本
├── 📋 test-tooltip.html - 提示框测试页面
├── ⚙️ tsconfig.json - TypeScript 配置文件
├── ⚙️ vitest.config.ts - Vitest 测试配置
├── ⚙️ web-ext.config.ts - Web扩展配置
└── ⚙️ wxt.config.ts - WXT 框架配置文件
```

---

## 🏗️ 架构概览

### 技术栈
- **WXT**: 浏览器扩展开发框架
- **React 19**: 前端UI框架  
- **TypeScript**: 类型安全的JavaScript
- **Vitest**: 现代测试框架
- **CSS Modules**: 模块化样式管理

### 核心功能模块
- 🎯 **智能高亮**: 文本选择高亮功能
- 📚 **词典查询**: 实时翻译和词典服务
- 🔧 **滑动面板**: 可配置的侧边栏界面
- 💬 **动态提示**: 交互式内容提示框
- ⚙️ **设置管理**: 用户偏好设置系统

### 测试覆盖
- 单元测试覆盖率报告生成
- 集成测试和E2E测试支持
- MSW (Mock Service Worker) 网络请求模拟
- 完整的组件测试套件

---

*文档生成时间: 2025-07-18*  
*项目版本: 0.0.0*  
*技术栈: WXT + React + TypeScript*