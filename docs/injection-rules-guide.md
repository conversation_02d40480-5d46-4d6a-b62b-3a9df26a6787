# 智能注入规则系统使用指南

## 📖 概述

智能注入规则系统是一个可配置的翻译内容注入引擎，可以根据元素特征自动选择最合适的注入策略。系统支持多种注入方式，包括行内注入、块级注入、侧边注入等。

## 🎯 核心功能

### 注入策略类型

1. **行内注入 (INLINE)** - 在元素内容后面追加翻译，适合短文本、链接等
2. **块级注入 (BLOCK)** - 在元素下方添加翻译块，适合段落、标题等  
3. **侧边注入 (BESIDE)** - 在元素旁边显示翻译，适合特殊布局需求
4. **覆盖注入 (OVERLAY)** - 临时替换原内容显示翻译
5. **跳过 (SKIP)** - 不注入翻译内容

### 规则匹配条件

- **标签名匹配** - 支持字符串、数组、正则表达式
- **CSS类名匹配** - 支持多种类名模式匹配
- **文本长度范围** - 根据文本长度选择策略
- **显示类型** - 根据CSS display属性匹配
- **父元素条件** - 考虑父元素的上下文
- **子元素条件** - 分析子元素的结构特征
- **自定义函数** - 支持复杂的自定义判断逻辑

## 🚀 快速开始

### 基础使用

```typescript
import { 
  getEnhancedTranslateManager,
  InjectionStrategy,
  RuleManagement 
} from '@/core/translation-integration';

// 1. 获取增强翻译管理器
const manager = getEnhancedTranslateManager({
  enableRuleEngine: true,
  targetLanguage: 'zh-CN',
  debug: true
});

// 2. 开始翻译页面
await manager.translatePage();

// 3. 查看统计信息
const stats = manager.getStrategyStats();
console.log('注入策略统计:', stats);
```

### 添加自定义规则

```typescript
// 创建一个新规则：为短链接使用行内注入
const linkRule = {
  id: 'custom-short-links',
  name: '短链接行内注入',
  description: '为短链接使用行内注入，保持流式布局',
  condition: {
    tagName: 'a',
    textLength: { max: 50 },
    parent: {
      tagName: ['p', 'div', 'span']
    }
  },
  strategy: InjectionStrategy.INLINE,
  priority: 2,
  enabled: true,
  options: {
    animation: 'fade',
    customClasses: ['custom-link-translation']
  }
};

// 添加规则
RuleManagement.addRule(linkRule);

// 保存配置
await RuleManagement.saveConfig();
```

### 高级规则示例

```typescript
// 复杂的自定义规则
const advancedRule = {
  id: 'advanced-content-detection',
  name: '智能内容检测',
  description: '根据内容特征智能选择注入策略',
  condition: {
    custom: (context) => {
      // 检测是否为文章主体内容
      const isMainContent = context.parent.tagName === 'main' ||
                           context.classNames.some(cls => 
                             ['content', 'article', 'post'].includes(cls)
                           );
      
      // 检测是否为重要内容
      const isImportant = context.textLength > 100 &&
                         context.boundingRect.width > 400;
      
      return isMainContent && isImportant;
    }
  },
  strategy: InjectionStrategy.BLOCK,
  priority: 5,
  enabled: true,
  options: {
    animation: 'slide',
    delay: 300,
    customClasses: ['important-content-translation']
  }
};

RuleManagement.addRule(advancedRule);
```

## 📝 配置管理

### 预设配置

系统提供了几种预设配置：

```typescript
import { getRuleConfigManager } from '@/core/rule-config-manager';

const configManager = getRuleConfigManager();

// 获取所有预设
const presets = await configManager.loadPresets();

// 应用简化配置
await configManager.applyPreset('minimal');

// 应用高级配置
await configManager.applyPreset('advanced');

// 重置为默认配置
await configManager.resetToDefaults();
```

### 导入导出配置

```typescript
// 导出当前配置
const configJson = await RuleManagement.exportConfig();
console.log('配置JSON:', configJson);

// 导入配置
const importResult = await RuleManagement.importConfig(configJson);
if (importResult.success) {
  console.log('配置导入成功');
  if (importResult.warnings?.length) {
    console.warn('警告:', importResult.warnings);
  }
} else {
  console.error('导入失败:', importResult.error);
}
```

## 🔧 实时调试和预览

### 预览元素策略

```typescript
// 预览单个元素的注入策略
const element = document.querySelector('h1');
const preview = manager.previewElementStrategy(element);

console.log('元素策略预览:', {
  strategy: preview.strategy,
  rule: preview.rule?.name,
  confidence: preview.confidence
});

// 批量预览多个元素
const elements = document.querySelectorAll('p, h1, h2, h3, a');
const batchPreview = manager.batchPreviewStrategies(Array.from(elements));

batchPreview.forEach(item => {
  console.log(`${item.element.tagName}: ${item.strategy} (${item.confidence})`);
});
```

### 调试模式

```typescript
// 启用调试模式
const debugManager = getEnhancedTranslateManager({
  debug: true,
  enableRuleEngine: true
});

// 翻译时会输出详细的调试信息
await debugManager.translatePage();

/*
输出示例:
🎯 Enhanced DOM Injection
Target: H1 main-title
Translation: 了解如何配置 Claude Code...
Strategy: BLOCK
Rule: 标题块级注入
Result: ✅ Success
Context: { tagName: 'h1', textLength: 45, isBlock: true, ... }
*/
```

## 📊 统计和监控

### 获取详细统计

```typescript
const stats = manager.getStrategyStats();

console.log('注入器统计:', stats.injectorStats);
console.log('规则引擎统计:', stats.ruleEngineStats);
console.log('策略报告:', stats.strategyReport);

/*
输出示例:
策略报告: {
  totalUsage: 156,
  mostUsedStrategy: "BLOCK",
  leastUsedStrategy: "OVERLAY", 
  usagePercentages: {
    INLINE: 35.2,
    BLOCK: 58.3,
    BESIDE: 4.5,
    OVERLAY: 1.3,
    SKIP: 0.7
  },
  ruleEffectiveness: [
    { ruleId: "block-paragraphs", hits: 78, percentage: 50.0 },
    { ruleId: "inline-links", hits: 45, percentage: 28.8 }
  ]
}
*/
```

### 配置状态监控

```typescript
const status = manager.getConfigurationStatus();

console.log('配置状态:', {
  isLoaded: status.isLoaded,
  totalRules: status.totalRules,
  enabledRules: status.enabledRules,
  ruleEngineEnabled: status.ruleEngineEnabled,
  currentConfig: status.currentConfig
});
```

## 🎨 样式定制

### CSS 类名规范

```css
/* 注入容器样式 */
.lu-wrapper {
  word-break: break-word;
  user-select: text;
}

/* 不同策略的特定样式 */
.lu-inline {
  display: inline;
  margin-left: 0.5em;
}

.lu-block-wrapper {
  display: block;
  margin-top: 0.5em;
}

.lu-beside {
  position: absolute;
  z-index: 9999;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
}

.lu-overlay {
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 3px;
}

/* 自定义类名示例 */
.custom-link-translation {
  color: #0066cc;
  font-style: italic;
}

.important-content-translation {
  border-left: 3px solid #007acc;
  padding-left: 12px;
  margin-left: 8px;
}
```

## 🧪 测试和验证

### 规则验证

```typescript
import { getRuleConfigManager } from '@/core/rule-config-manager';

const configManager = getRuleConfigManager();

// 验证规则配置
const rules = RuleManagement.getRules();
const validation = configManager.validateRules(rules);

if (!validation.isValid) {
  console.error('规则验证失败:', validation.errors);
} else if (validation.warnings.length > 0) {
  console.warn('规则警告:', validation.warnings);
} else {
  console.log('规则验证通过');
}
```

### 性能测试

```typescript
// 批量测试性能
const elements = document.querySelectorAll('p, h1, h2, h3, a, span');
const startTime = performance.now();

for (const element of elements) {
  const preview = manager.previewElementStrategy(element);
  // 记录策略选择时间
}

const endTime = performance.now();
console.log(`策略选择性能: ${endTime - startTime}ms for ${elements.length} elements`);
```

## 🔧 故障排除

### 常见问题

1. **规则不生效**
   - 检查规则是否启用：`rule.enabled === true`
   - 检查规则优先级：数字越小优先级越高
   - 使用调试模式查看匹配过程

2. **策略选择不准确**
   - 检查条件设置是否正确
   - 使用预览功能测试匹配结果
   - 调整规则优先级

3. **性能问题**
   - 减少复杂的自定义条件函数
   - 优化规则数量，禁用不需要的规则
   - 使用性能监控检查瓶颈

### 调试技巧

```typescript
// 启用详细日志
console.log('规则引擎统计:', manager.getRuleEngine().getStats());

// 检查特定元素的匹配过程
const element = document.querySelector('.problematic-element');
const context = manager.getRuleEngine().analyzeElement(element);
console.log('元素上下文:', context);

const matchResult = manager.getRuleEngine().matchStrategy(element);
console.log('匹配结果:', matchResult);
```

## 📚 API 参考

### 核心类

- `InjectionRuleEngine` - 规则匹配引擎
- `EnhancedDOMInjector` - 增强注入器
- `RuleConfigManager` - 配置管理器
- `EnhancedTranslateManager` - 增强翻译管理器

### 枚举类型

- `InjectionStrategy` - 注入策略枚举
- `TaskPriority` - 任务优先级枚举

### 接口定义

- `InjectionRule` - 注入规则接口
- `RuleCondition` - 规则条件接口
- `ElementContext` - 元素上下文接口
- `EnhancedInjectionOptions` - 增强注入选项接口

## 🚀 最佳实践

1. **规则设计原则**
   - 优先级设置要合理，避免冲突
   - 条件设置要精确，避免误匹配
   - 使用描述性的规则名称和ID

2. **性能优化**
   - 将最常用的规则设置为较高优先级
   - 避免过于复杂的自定义条件
   - 定期清理不需要的规则

3. **可维护性**
   - 定期导出配置进行备份
   - 为自定义规则添加详细的描述
   - 使用预设配置简化管理

4. **测试验证**
   - 在不同页面测试规则效果
   - 使用预览功能验证策略选择
   - 监控性能和准确率指标

---

通过这个智能注入规则系统，你可以轻松地维护一套逻辑配置，根据不同的元素特征自动选择最合适的注入策略，实现"在后面插入中文"和"在下面插入中文"等多种注入方式的智能切换。