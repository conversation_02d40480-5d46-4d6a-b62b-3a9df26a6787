好的，完全理解。您的目标是**精简右键菜单**，并**清理与移除命令相关的后端测试功能**，使整个系统更聚焦于核心业务和新的开发策略。

遵照您的要求，这里提供一个不包含代码、只梳理文件和行动计划的方案。

### Part 1: 右键菜单命令梳理

根据您的指令（保留“翻译网页”和“切换 Mock 翻译”），我们将菜单命令分为两类：

#### A. 需要保留的命令

- **打开 Lucid 设置**: 扩展的核心入口，必须保留。
- **翻译网页**: 核心功能，按要求保留。
- **切换 Mock 翻译**: 核心开发/调试功能，按要求保留。

#### B. 计划移除的命令

- 测试页面翻译(轻量化)
- 显示翻译统计
- 清除翻译内容
- 切换返回原文模式
- 查看 Mock 翻译状态
- 切换排除标记
- 显示排除统计
- 重置排除标记

---

### Part 2: 清理与重构行动计划

在“清除”上述菜单命令后，我们需要清理对应的后端代码。

#### A. 可以立即删除的文件

这些文件完全对应您计划移除的调试功能，删除它们是安全的。

1.  **`content/exclusion-manager.ts`**

    - **做什么**: 这个文件完整地实现了“切换/显示/重置排除标记”这三个将被移除的命令的所有功能，包括在页面上添加红色标记、显示统计弹窗等。
    - **处理建议**: **立即删除**。

2.  **`utils/exclusion-debug.ts`**
    - **做什么**: 这是上述 `exclusion-manager.ts` 的一个辅助和包装工具。
    - **处理建议**: **立即删除**。

#### B. 需要评估后删除的文件或功能

这些模块的功能与被移除的命令部分相关，需要评估其是否还有其他用途。

1.  **`content/mock-integration.ts`** **中的特定功能**

    - **做什么**: 这个文件实现了完整的 Mock 翻译调试系统。
    - **评估什么**: 您要求保留“切换 Mock 翻译”，但移除了“切换返回原文模式”和“查看 Mock 翻译状态”。因此，需要进入此文件，**只删除实现后两个功能（返回原文、查看状态）的代码逻辑**，保留核心的 Mock 切换功能。

2.  **`__tests__/` 目录及所有 `*.test.tsx` 文件**
    - **做什么**: 这些是项目的**自动化测试**文件。
    - **评估什么**: 您提到“与测试功能已经脱离无关了，现在用了四个新的方案/策略”。请评估这些自动化测试用例是否还与您当前的“四个新方案”相关。**通常，自动化测试是需要保留和维护的**，但您可以借此机会删除那些测试**已被废弃的旧功能**的测试文件，确保测试用例与当前代码逻辑保持同步。

#### C. 需要统一封装的逻辑

这部分是为了代码整洁和避免全局污染，之前我们已经讨论过，现在依然适用。

1.  **`content/mock-integration.ts`** **中剩余的功能**
    - **做什么**: 实现了“切换 Mock 翻译”功能。
    - **处理建议**: 应该将触发这个功能的全局函数（例如`window.lucidExt.enableMock()`等）统一封装到一个开发专用的全局对象下（例如 `window.LucidDevTools`），而不是散落在各处。

#### D. 需要条件性暴露的逻辑

这部分是为了确保生产环境的纯净和安全。

1.  **所有调试相关的代码**
    - **涉及文件**: 上述封装好的 `LucidDevTools` 对象、`mock-integration.ts` 文件本身、以及所有暴露到 `window` 对象的测试函数。
    - **处理建议**: **必须使用 `process.env.NODE_ENV === 'development'` 这样的条件编译指令将它们包裹起来**。这样，在打包生产版本时，这些调试代码会被完全剔除，不会影响最终用户。

#### E. 需要保留并维护的文件/模块

这些是项目的核心部分，在此次清理中应重点保留和维护。

1.  **`background.ts` (或类似的后台脚本)**

    - **做什么**: 负责创建右键菜单。
    - **处理建议**: **修改而非删除**。您需要在这个文件中，删除创建上述**B 部分**中“计划移除的命令”的代码。

2.  **`features/translation_pipeline/orchestrator.ts` 和 `adapter.ts`**

    - **做什么**: 包含了翻译系统的核心控制逻辑。
    - **处理建议**: **保留**。即使“显示翻译统计”和“清除翻译内容”的菜单命令被移除了，其底层的 `getStats()` 和 `clearTranslations()` 方法可能还在被其他调试工具（如暴露到`window`的函数）使用，应予以保留。我们只是移除了一个 UI 入口，而非核心功能本身。

3.  **核心的 Mock 逻辑**
    - **涉及文件**: `features/translate/mock-translate-service-refactored.ts` 及其依赖。
    - **处理建议**: **保留**。这是“切换 Mock 翻译”功能的核心实现，必须保留。


