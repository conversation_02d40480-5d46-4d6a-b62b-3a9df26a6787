# 增强版调试系统使用指南

## 🎯 新增功能

基于现有的统一日志格式，新增了以下通用公共方法：

### ✅ 支持二级日志分组

```typescript
import { debugContent } from '@/utils/debug';

// 创建二级日志分组
debugContent.group('Lucid系统初始化');
debugContent.info('⚙️ 正在初始化翻译系统...');
debugContent.startup('✅ 翻译系统已就绪');
debugContent.info('⚙️ 正在初始化UI管理器...');
debugContent.startup('✅ UI管理器已就绪');
debugContent.groupEnd();

// 结果：在浏览器控制台中显示为分组结构
📄 [内容脚本] Lucid系统初始化
  ├── 📄 [内容脚本|INFO] ⚙️ 正在初始化翻译系统...
  ├── 📄 [内容脚本|STARTUP] ✅ 翻译系统已就绪
  ├── 📄 [内容脚本|INFO] ⚙️ 正在初始化UI管理器...
  └── 📄 [内容脚本|STARTUP] ✅ UI管理器已就绪
```

### ✅ 通用公共日志方法

```typescript
import { quickLog, debugPerformance } from '@/utils/debug';

// 1. 快速日志 - 不需要创建实例
quickLog('translation', 'info', '翻译完成:', result);
quickLog('storage', 'warn', '存储空间不足');

// 2. 性能测量装饰器
const optimizedFunction = debugPerformance.withPerformanceLog(
  function expensiveOperation(data) {
    // 复杂操作
    return processData(data);
  },
  '数据处理操作'
);

// 3. 异步性能测量
const optimizedAsyncFunction = debugPerformance.withAsyncPerformanceLog(
  async function fetchData(url) {
    const response = await fetch(url);
    return response.json();
  },
  'API数据获取'
);

// 4. 批量日志输出
debugContent.batchLog([
  { level: 'startup', args: ['系统启动'] },
  { level: 'info', args: ['配置已加载'] },
  { level: 'debug', args: ['调试模式已启用'] }
]);
```

## 🎨 保持的原有功能

### 统一日志格式
所有日志保持原有的统一格式：`[模块emoji] [模块名称|日志级别] 消息内容`

```javascript
📄 [内容脚本|STARTUP] 🚀 Lucid系统启动中...
🌐 [翻译系统|INFO] 开始翻译文本: "Hello World"
📦 [存储服务|WARN] Extension context invalidated, skipping storage read
⚡ [性能监控|DEBUG] IntersectionObserver created
🎨 [高亮系统|STARTUP] ✅ 高亮系统已就绪
```

### 所有原有调试实例
```typescript
// 所有原有的调试实例都保持不变
export const debugHighlight = new DebugHelper('highlight'); 
export const debugTooltip = new DebugHelper('tooltip');
export const debugSlider = new DebugHelper('slider');
export const debugUI = new DebugHelper('ui');
export const debugContent = new DebugHelper('content');
export const debugTranslation = new DebugHelper('translation');
export const debugStorage = new DebugHelper('storage');
export const debugPerformance = new DebugHelper('performance');
// ... 等等
```

## 🚀 实际使用场景

### 初始化阶段使用二级日志

```typescript
import { debugContent } from '@/utils/debug';

// 系统启动时
debugContent.group('Lucid系统初始化');

// 初始化各个模块
debugContent.info('⚙️ 正在初始化翻译系统...');
// ... 翻译系统初始化代码 ...
debugContent.startup('✅ 翻译系统已就绪');

debugContent.info('⚙️ 正在初始化高亮系统...');
// ... 高亮系统初始化代码 ...
debugContent.startup('✅ 高亮系统已就绪');

debugContent.groupEnd();
```

### 运行时阶段使用通用方法

```typescript
import { quickLog, debugTranslation } from '@/utils/debug';

// 翻译过程中
async function translateText(text: string) {
  quickLog('translation', 'info', '开始翻译:', text.substring(0, 50));
  
  try {
    const result = await performTranslation(text);
    quickLog('translation', 'info', '翻译完成');
    return result;
  } catch (error) {
    quickLog('translation', 'error', '翻译失败:', error.message);
    throw error;
  }
}

// 性能监控
const optimizedTranslate = debugTranslation.withAsyncPerformanceLog(
  translateText,
  '文本翻译操作'
);
```

## 💡 关键改进

1. **✅ 保持了二级日志分组功能** - 通过 `group()` 和 `groupEnd()` 方法
2. **✅ 保持了统一日志格式** - 完全兼容原有格式规范  
3. **✅ 在现有基础上增强** - 没有破坏任何现有功能
4. **✅ 支持全生命周期使用** - 不仅限于初始化阶段
5. **✅ 提供便捷的通用方法** - 如 `quickLog`, 性能装饰器等

---

*基于原有调试系统增强，保持100%向后兼容*