# Lucid Extension 统一日志格式规范

## 📋 日志格式标准

### 🎯 格式规范
```
[模块emoji] [模块名称|日志级别] 日志信息
```

### 📊 实际示例
```javascript
// 启动日志
📄 [内容脚本|STARTUP] 🚀 Lucid系统启动中...
🌐 [翻译系统|STARTUP] ✅ 翻译系统已就绪
🎨 [高亮系统|STARTUP] ✅ 高亮系统已就绪

// 运行时日志
🌐 [翻译系统|INFO] 开始翻译文本: "Hello World"
📦 [存储服务|WARN] Extension context invalidated, skipping storage read
💬 [提示框系统|DEBUG] 显示提示框 at position (100, 200)
🔧 [调试工具|ERROR] 翻译测试失败: Network timeout
```

## 🎨 模块标识映射

### 核心系统模块
- `📄` 内容脚本 (content)
- `⚙️` 后台脚本 (background)

### 功能模块  
- `🌐` 翻译系统 (translation)
- `🎨` 高亮系统 (highlight)
- `💬` 提示框系统 (tooltip)
- `🎛️` 滑动面板系统 (slider)
- `🖼️` UI管理器 (ui)

### 基础服务模块
- `📦` 存储服务 (storage)
- `🗄️` 缓存管理 (cache)
- `🌐` 网络请求 (network)
- `🔒` 安全模块 (security)

### 调试和测试模块
- `🔧` 调试工具 (debug)
- `🧪` 测试模块 (test)
- `🎭` Mock服务 (mock)

### 性能和分析模块
- `⚡` 性能监控 (performance)
- `📊` 数据分析 (analytics)
- `📈` 监控系统 (monitor)

## 🏷️ 日志级别

- `🚀 STARTUP` - 系统启动关键信息
- `❌ ERROR` - 错误信息 (总是显示)
- `⚠️ WARN` - 警告信息 (生产环境显示)
- `📄 INFO` - 一般信息 (开发环境显示)
- `🔍 DEBUG` - 调试信息 (开发环境显示，非初始化阶段)
- `🕵️ TRACE` - 追踪信息 (开发环境显示，最详细)

## 💻 使用方法

### 导入调试实例
```typescript
import { 
  debugContent,     // 内容脚本
  debugTranslation, // 翻译系统
  debugHighlight,   // 高亮系统
  debugTooltip,     // 提示框系统
  debugStorage,     // 存储服务
  debugPerformance  // 性能监控
} from '../utils/debug';
```

### 使用统一日志
```typescript
// 启动信息
debugContent.startup('🚀 Lucid系统启动中...');

// 一般信息
debugTranslation.info('翻译服务已初始化');

// 调试信息
debugHighlight.debug('高亮元素已创建', { element, position });

// 警告信息
debugStorage.warn('Extension context invalidated, skipping storage read');

// 错误信息
debugNetwork.error('API请求失败', { url, error });
```

### 分组日志
```typescript
debugContent.group('Lucid系统初始化');
debugContent.info('⚙️  正在初始化翻译系统...');
debugContent.startup('✅ 翻译系统已就绪');
debugContent.groupEnd();
```

## 🎯 优化效果

### 优化前 (混乱格式)
```
🔧 [INFO] Lucid Content Script initializing...
console.log('📦 [Storage] Extension context invalidated')
[LazyLoadManager] IntersectionObserver created
🎨 [INFO] Highlight system initialized successfully!
```

### 优化后 (统一格式)
```
📄 [内容脚本|STARTUP] 🚀 Lucid系统启动中...
📦 [存储服务|WARN] Extension context invalidated, skipping storage read  
⚡ [性能监控|DEBUG] IntersectionObserver created
🎨 [高亮系统|STARTUP] ✅ 高亮系统已就绪
```

## 📈 一致性优势

1. **视觉统一**: 所有日志采用相同的格式结构
2. **模块识别**: 通过emoji快速识别功能模块
3. **级别区分**: 清晰的日志级别标识
4. **可读性强**: 中文模块名便于理解
5. **易于搜索**: 统一格式便于日志过滤和搜索

---

*统一日志格式 - 让调试更清晰，开发更高效*