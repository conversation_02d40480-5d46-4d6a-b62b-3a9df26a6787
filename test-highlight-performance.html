<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高亮性能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .performance-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .test-text {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
        
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .highlight-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2px 4px;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .timing-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-section">
        <h1>⚡ 高亮性能测试页面</h1>
        
        <div class="performance-info">
            <h3>🔍 性能监控</h3>
            <p>这个页面用于测试高亮系统的性能优化效果。请打开浏览器开发者工具的控制台查看详细的性能日志。</p>
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>打开开发者工具 (F12)</li>
                <li>切换到 Console 标签页</li>
                <li>选择下面的单词并按 Shift 键</li>
                <li>观察控制台中的性能日志</li>
            </ol>
        </div>
        
        <div class="timing-info">
            <h4>📊 期望的性能指标</h4>
            <ul>
                <li><strong>总高亮时间</strong>: &lt; 50ms</li>
                <li><strong>文本节点获取</strong>: &lt; 10ms</li>
                <li><strong>直接处理</strong>: &lt; 20ms</li>
                <li><strong>批处理</strong>: 第一批 &lt; 30ms</li>
            </ul>
        </div>
        
        <div class="test-text">
            <h3>🎯 测试文本 - 简单场景</h3>
            <p>
                This is a simple <strong>test</strong> paragraph with common words like 
                <strong>hello</strong>, <strong>world</strong>, and <strong>example</strong>. 
                These words should be highlighted quickly without any noticeable delay.
            </p>
        </div>
        
        <div class="test-text">
            <h3>🎯 测试文本 - 复杂场景</h3>
            <p>
                The modern web development process involves many <strong>extensions</strong> 
                and tools for <strong>translating</strong> content. Dictionary lookups and 
                contextual <strong>definitions</strong> are essential features.
            </p>
            <p>
                When implementing <strong>highlight</strong> functionality, performance is 
                crucial. The system should respond immediately to user interactions 
                without causing any UI blocking or delays.
            </p>
            <p>
                Advanced features like <strong>tooltip</strong> display and dynamic 
                content loading should not impact the core highlighting performance.
            </p>
        </div>
        
        <div class="test-text">
            <h3>🎯 测试文本 - 重复单词</h3>
            <p>
                Test test test. This test contains multiple instances of the word test 
                to test the performance when highlighting multiple occurrences. Each 
                test should be processed efficiently during the test run.
            </p>
        </div>
        
        <div class="console-output" id="console-output">
            <div>🖥️ 控制台输出将显示在这里...</div>
            <div>请在浏览器开发者工具中查看完整的性能日志</div>
        </div>
    </div>
    
    <script>
        // 模拟控制台输出显示
        const originalConsole = {
            log: console.log,
            time: console.time,
            timeEnd: console.timeEnd
        };
        
        const consoleOutput = document.getElementById('console-output');
        
        // 拦截console.log来显示在页面上
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' ');
            
            if (message.includes('[highlight]')) {
                const div = document.createElement('div');
                div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
                consoleOutput.appendChild(div);
                consoleOutput.scrollTop = consoleOutput.scrollHeight;
            }
        };
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 [highlight] Performance test page loaded');
            console.log('📝 [highlight] Ready for testing - select text and press Shift');
            
            // 添加一些测试用的高亮样式
            const testWords = ['test', 'hello', 'world', 'example', 'extensions', 'translating', 'definitions', 'highlight', 'tooltip'];
            
            // 为演示目的添加点击事件
            document.addEventListener('click', function(e) {
                if (e.target.classList && e.target.classList.contains('highlight-demo')) {
                    console.log('🎯 [highlight] Clicked highlighted word:', e.target.textContent);
                }
            });
            
            // 模拟性能测试
            setTimeout(() => {
                console.log('⏱️ [highlight] Simulating performance test...');
                console.time('🚀 [highlight] Total highlight time');
                console.time('📝 [highlight] getFastTextNodes');
                console.log('📊 [highlight] Found elements: 45');
                console.log('📊 [highlight] Processing elements: 45');
                console.log('📊 [highlight] Found text nodes: 12');
                console.timeEnd('📝 [highlight] getFastTextNodes');
                console.time('⚡ [highlight] Direct processing');
                console.timeEnd('⚡ [highlight] Direct processing');
                console.timeEnd('🚀 [highlight] Total highlight time');
            }, 2000);
        });
    </script>
</body>
</html>
