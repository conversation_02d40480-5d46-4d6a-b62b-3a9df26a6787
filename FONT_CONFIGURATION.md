# Lucid Extension 字体配置指南

## 概述 📝

Lucid 扩展现已全面采用 **Noto Sans SC** 字体，默认字重为 **200 (ExtraLight)**，以提供更优雅和现代的中文显示效果。

## 字体配置 🎨

### 主要字体
- **字体家族**: Noto Sans SC
- **默认字重**: 200 (ExtraLight)
- **备用字体**: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif

### Google Fonts 导入
```html
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100..900&display=swap" rel="stylesheet">
```

## 字重映射表 ⚖️

| 字重名称 | 数值 | 使用场景 |
|---------|------|---------|
| Thin | 100 | 极细装饰文字 |
| **ExtraLight** | **200** | **默认文字（主要使用）** |
| Light | 300 | 标题文字 |
| Normal | 400 | 强调文字 |
| Medium | 500 | 中等强调 |
| SemiBold | 600 | 统计数字 |
| Bold | 700 | Logo、重要标题 |
| ExtraBold | 800 | 特殊强调 |
| Black | 900 | 最强调 |

## 文件更新清单 📋

### 1. 样式文件更新

#### `src/ui-manager/slider-styles.ts`
- ✅ 更新字体导入为 Noto Sans SC
- ✅ 添加字体变量定义
- ✅ 设置默认字重为 200
- ✅ 更新 body 和 .lu-slide 字体设置

#### `src/styles/global-fonts.css` (新建)
- ✅ 全局字体配置文件
- ✅ 统一字体变量定义
- ✅ 所有 Lucid 组件字体覆盖
- ✅ 响应式字体大小调整

#### `entrypoints/popup/style.css`
- ✅ 更新 popup 字体为 Noto Sans SC
- ✅ 设置默认字重为 200

#### `entrypoints/content.ts`
- ✅ 导入全局字体配置文件

### 2. 组件更新

#### `src/components/Slider/components/LoginView.tsx`
- ✅ 更新标题字重为 300
- ✅ 更新描述文字字重为 200
- ✅ 更新错误消息字体设置
- ✅ 添加 fontFamily 内联样式

## CSS 变量系统 🎯

### 字体家族变量
```css
--lu-font-family: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
```

### 字重变量
```css
--lu-font-weight-thin: 100;
--lu-font-weight-extralight: 200;  /* 默认 */
--lu-font-weight-light: 300;
--lu-font-weight-normal: 400;
--lu-font-weight-medium: 500;
--lu-font-weight-semibold: 600;
--lu-font-weight-bold: 700;
--lu-font-weight-extrabold: 800;
--lu-font-weight-black: 900;

--lu-font-weight-default: var(--lu-font-weight-extralight);
```

## 使用指南 📖

### 1. 在新组件中使用字体

```tsx
// React 组件中
<div style={{
  fontFamily: '"Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  fontWeight: '200'
}}>
  文字内容
</div>
```

### 2. 在 CSS 中使用变量

```css
.my-component {
  font-family: var(--lu-font-family);
  font-weight: var(--lu-font-weight-default);
}

.my-title {
  font-family: var(--lu-font-family);
  font-weight: var(--lu-font-weight-light); /* 300 for titles */
}
```

### 3. 特殊场景字重选择

- **普通文字**: 200 (ExtraLight)
- **标题文字**: 300 (Light)
- **按钮文字**: 200 (ExtraLight)
- **Logo文字**: 700 (Bold)
- **统计数字**: 600 (SemiBold)
- **错误提示**: 200 (ExtraLight)

## 响应式设计 📱

### 移动端适配
```css
@media (max-width: 768px) {
  .lu-slide {
    font-size: 14px;
  }
}
```

### 桌面端适配
```css
@media (min-width: 1200px) {
  .lu-slide {
    font-size: 16px;
  }
}
```

## 性能优化 ⚡

### 字体预加载
```html
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
```

### 字体显示策略
```css
font-display: swap; /* 已在 Google Fonts URL 中设置 */
```

## 兼容性说明 🔧

### 浏览器支持
- ✅ Chrome 88+
- ✅ Firefox 89+
- ✅ Safari 14+
- ✅ Edge 88+

### 备用字体链
当 Noto Sans SC 无法加载时，系统会依次尝试：
1. -apple-system (macOS/iOS 系统字体)
2. BlinkMacSystemFont (Chrome on macOS)
3. "Segoe UI" (Windows 系统字体)
4. Roboto (Android 系统字体)
5. sans-serif (通用无衬线字体)

## 测试验证 ✅

### 字体加载测试
1. 打开开发者工具 → Network 标签
2. 刷新页面
3. 确认 Google Fonts 请求成功
4. 检查字体文件下载完成

### 视觉效果测试
1. 检查中文字符显示效果
2. 验证字重 200 的视觉效果
3. 确认不同组件的字体一致性
4. 测试响应式字体大小

## 故障排除 🔧

### 字体未生效
1. 检查网络连接
2. 确认 Google Fonts 可访问
3. 清除浏览器缓存
4. 检查 CSS 优先级

### 字重显示异常
1. 确认字重值正确 (200)
2. 检查 CSS 变量定义
3. 验证 !important 规则
4. 测试备用字体效果

## 未来规划 🚀

1. **字体子集优化**: 仅加载需要的字符集
2. **本地字体缓存**: 减少网络请求
3. **动态字重调整**: 根据用户偏好调整
4. **多语言字体支持**: 支持更多语言的最佳字体

---

**更新时间**: 2025-01-01  
**版本**: v1.0.0  
**维护者**: Lucid Extension Team
