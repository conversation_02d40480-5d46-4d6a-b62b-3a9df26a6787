/**
 * Slider 管理器 - 负责 Lucid Slider 功能
 * 从 content.ts 中提取的 slider 相关逻辑
 */

import { uiManager } from '../ui-manager';
// debugSlider 导入已移除，使用手动 console.log

export interface SliderManagerOptions {
  // 可以添加更多配置选项
}

export class SliderManager {
  private options: SliderManagerOptions;

  constructor(options: SliderManagerOptions = {}) {
    this.options = options;
  }

  /**
   * 初始化 Slider 系统
   */
  initialize(): void {
    this.setupMessageListener();
    console.log('✅ [slider-manager|STARTUP] Slider manager initialized');
  }

  /**
   * 设置消息监听器（来自background的右键菜单触发）
   */
  private setupMessageListener(): void {
    browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'TOGGLE_LUCID_SLIDER') {
        this.toggleSlider().catch((error) => {
          console.log('❌ [slider-manager|ERROR] Error toggling Lucid Slider via context menu:', error);
        });
      }
    });
  }

  /**
   * 切换 Slider 显示/隐藏
   */
  public async toggleSlider(): Promise<void> {
    try {
      const stats = uiManager.getStats();
      if (stats.sliders > 0) {
        await this.hideSlider();
        console.log('✅ [slider-manager|INFO] Lucid Slider hidden');
      } else {
        await this.showSlider();
        console.log('✅ [slider-manager|INFO] Lucid Slider shown');
      }
    } catch (error) {
      console.log('❌ [slider-manager|ERROR] Error toggling Lucid Slider:', error);
      throw error;
    }
  }

  /**
   * 显示 Slider
   */
  public async showSlider(): Promise<string> {
    try {
      console.log('⚙️ [slider-manager|INFO] Showing Lucid Slider...');
      
      const sliderId = await uiManager.showLucidSlider({
        isOpen: true
      });
      
      console.log('✅ [slider-manager|SUCCESS] Lucid Slider shown with ID:', sliderId);
      return sliderId;
    } catch (error) {
      console.log('❌ [slider-manager|ERROR] Error showing Lucid Slider:', error);
      throw error;
    }
  }

  /**
   * 隐藏 Slider
   */
  public async hideSlider(): Promise<void> {
    try {
      console.log('⚙️ [slider-manager|INFO] Hiding Lucid Slider...');
      
      await uiManager.hideLucidSlider();
      
      console.log('✅ [slider-manager|SUCCESS] Lucid Slider hidden');
    } catch (error) {
      console.log('❌ [slider-manager|ERROR] Error hiding Lucid Slider:', error);
      throw error;
    }
  }

  /**
   * 获取 Slider 状态
   */
  public getState(): any {
    const stats = uiManager.getStats();
    return {
      isVisible: stats.sliders > 0,
      sliderCount: stats.sliders
    };
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    // 移除消息监听器（实际上由于onMessage.addListener的特性，这里无法简单移除）
    // 为了简化，这里不移除监听器，由页面卸载时自动清理
    console.log('🧹 [slider-manager|DEBUG] Slider manager destroyed');
  }
}

/**
 * 初始化 Slider 管理器的便捷函数
 */
export function initializeSlider(options?: SliderManagerOptions): SliderManager {
  const manager = new SliderManager(options);
  manager.initialize();
  return manager;
}