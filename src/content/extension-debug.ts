/**
 * 浏览器扩展调试工具
 * 专为浏览器扩展环境设计的轻量级调试接口
 */

/**
 * 扩展调试接口
 * 提供安全的调试功能，适用于浏览器扩展环境
 */
export interface ExtensionDebugTools {
  // Mock 控制
  enableMock(): void;
  disableMock(): void;
  
  // 状态查询
  getStatus(): DebugStatus;
  
  // 测试功能
  testTranslate(text?: string): Promise<string>;
  
  // 日志控制
  enableDebugLog(): void;
  disableDebugLog(): void;
  
  // 帮助信息
  help(): void;
}

interface DebugStatus {
  mockEnabled: boolean;
  debugLogEnabled: boolean;
  environment: string;
  isExtension: boolean;
  extensionId?: string;
}

/**
 * 创建扩展调试工具
 */
export function createExtensionDebugTools(): ExtensionDebugTools {
  return {
    enableMock() {
      localStorage.setItem('lucid-force-mock', 'true');
      console.log('🚀 Mock翻译已启用，请刷新页面生效');
    },
    
    disableMock() {
      localStorage.removeItem('lucid-force-mock');
      console.log('🔌 Mock翻译已禁用，请刷新页面生效');
    },

    getStatus(): DebugStatus {
      return {
        mockEnabled: localStorage.getItem('lucid-force-mock') === 'true',
        debugLogEnabled: localStorage.getItem('lucid-debug-log') === 'true',
        environment: process.env.NODE_ENV || 'unknown',
        isExtension: true,
        extensionId: chrome?.runtime?.id
      };
    },

    async testTranslate(text: string = 'Hello World'): Promise<string> {
      // 简单的Mock翻译测试
      const mockResults: Record<string, string> = {
        'Hello World': '你好世界',
        'Hello': '你好',
        'Test': '测试',
        'Browser Extension': '浏览器扩展'
      };
      
      const result = mockResults[text] || `[Mock] ${text} -> 模拟翻译`;
      console.log(`🧪 测试翻译: "${text}" -> "${result}"`);
      return result;
    },

    enableDebugLog() {
      localStorage.setItem('lucid-debug-log', 'true');
      console.log('📝 调试日志已启用');
    },

    disableDebugLog() {
      localStorage.removeItem('lucid-debug-log');
      console.log('📝 调试日志已禁用');
    },

    help() {
      console.group('🛠️ LucidDebug 扩展调试工具帮助');
      console.log('🎛️ Mock控制:');
      console.log('  • LucidDebug.enableMock() - 启用Mock翻译');  
      console.log('  • LucidDebug.disableMock() - 禁用Mock翻译');
      console.log('📊 状态查询:');
      console.log('  • LucidDebug.getStatus() - 查看调试状态');
      console.log('🧪 测试功能:');
      console.log('  • LucidDebug.testTranslate("Hello") - 测试翻译功能');
      console.log('📝 日志控制:');
      console.log('  • LucidDebug.enableDebugLog() - 启用调试日志');
      console.log('  • LucidDebug.disableDebugLog() - 禁用调试日志');
      console.log('🌐 翻译工具:');
      console.log('  • LucidDebug.translate.test("text") - 测试翻译');
      console.log('  • LucidDebug.translate.status() - 查看引擎状态');
      console.log('  • LucidDebug.translate.stats() - 查看翻译统计');
      console.log('  • LucidDebug.translate.clear() - 清除页面翻译');
      console.groupEnd();
    }
  };
}

/**
 * 设置扩展调试环境
 * 调试工具可在开发环境或手动启用时使用
 */
export function setupExtensionDebug(): void {
  // 检查是否应该启用调试工具
  const shouldEnable = (
    process.env.NODE_ENV === 'development' ||
    localStorage.getItem('lucid-debug') === 'true' ||
    localStorage.getItem('lucid-force-debug') === 'true' ||
    // 在扩展环境中，如果没有明确的NODE_ENV，也允许调试
    typeof process === 'undefined' ||
    !process.env.NODE_ENV
  );

  console.log('🔍 Extension debug setup check:', {
    nodeEnv: process.env.NODE_ENV,
    lucidDebug: localStorage.getItem('lucid-debug'),
    forceDebug: localStorage.getItem('lucid-force-debug'),
    shouldEnable
  });

  if (!shouldEnable) {
    console.log('🔒 调试工具未启用 - 使用 localStorage.setItem("lucid-force-debug", "true") 强制启用');
    return;
  }

  try {
    const debugTools = createExtensionDebugTools();
    
    // 暴露到全局对象，但使用更简洁的命名
    (window as any).LucidDebug = debugTools;
    
    console.log('🛠️ 扩展调试工具已就绪');
    console.log('📋 使用 LucidDebug.help() 查看可用命令');
    
    // 验证对象是否正确设置
    if (typeof (window as any).LucidDebug === 'object') {
      console.log('✅ LucidDebug 对象已成功暴露到全局作用域');
    } else {
      console.error('❌ LucidDebug 对象设置失败');
    }
    
  } catch (error) {
    console.warn('⚠️ 调试工具设置失败:', error);
  }
}

/**
 * 检查是否应该启用调试模式
 */
export function shouldEnableDebug(): boolean {
  return (
    process.env.NODE_ENV === 'development' ||
    localStorage.getItem('lucid-debug') === 'true' ||
    localStorage.getItem('lucid-force-mock') === 'true'
  );
}

/**
 * 调试日志函数
 */
export function debugLog(message: string, ...args: any[]): void {
  if (shouldEnableDebug() && localStorage.getItem('lucid-debug-log') === 'true') {
    console.log(`[Lucid Debug] ${message}`, ...args);
  }
}