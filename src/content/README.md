# Content Scripts 内容脚本

这个目录包含了浏览器扩展的内容脚本，负责在网页中执行翻译相关的操作。

## 核心文件

### scanner.ts - 节点扫描器

负责扫描和识别页面中可翻译的元素，包含以下关键功能：

- **元素扫描**：识别页面中的可翻译文本元素
- **排除逻辑**：过滤掉不应该翻译的元素（如代码、脚本等）
- **隐藏检测**：区分真正隐藏的元素和仅仅在屏幕外的元素
- **调试标记**：为排除的元素添加可视化标记（调试模式）

#### 🔧 最近修复：重要问题修复

**1. 懒加载红色样式问题**

- **问题**：屏幕外元素被错误标记为红色样式
- **原因**：`isOffScreen()` 方法将所有屏幕外元素都视为"隐藏"
- **修复**：
  - 新增 `isIntentionallyHidden()` 方法，只排除故意隐藏的元素
  - 新增 `isElementOffScreen()` 方法，用于懒加载判断
  - 区分"故意隐藏"和"自然屏幕外"的元素

**2. 🔗 链接文字丢失问题**

- **问题**：包含链接的段落翻译后链接内部文字完全消失
- **原因**：使用 `textContent` 提取文本时丢失了 HTML 结构
- **修复**：
  - 对包含链接的元素使用 HTML 格式进行翻译
  - 保持链接结构完整，确保翻译后能正确重建链接
  - 传递链接信息给渲染器进行正确处理

### injector.ts - 翻译注入器

负责将翻译结果注入到页面中：

- **翻译注入**：将翻译文本插入到原文旁边
- **样式管理**：管理翻译内容的显示样式
- **清理功能**：移除翻译内容和相关标记

### view-controller.ts - 视图控制器

管理翻译内容的显示模式：

- **显示模式**：原文、翻译、双语显示
- **DOM 观察**：监听页面变化，自动处理新增内容
- **性能优化**：智能管理 MutationObserver

### exclusion-manager.ts - 排除管理器

处理排除标记的显示和管理：

- **标记显示**：显示/隐藏排除元素的调试标记
- **右键菜单**：集成浏览器右键菜单功能
- **统计信息**：提供排除元素的统计数据

## 工作流程

1. **扫描阶段**：`scanner.ts` 扫描页面，识别可翻译元素
2. **过滤阶段**：排除不应翻译的元素（隐藏、代码等）
3. **分类阶段**：区分立即翻译和懒加载的元素
4. **翻译阶段**：调用翻译服务获取翻译结果
5. **注入阶段**：`injector.ts` 将翻译结果注入页面
6. **显示阶段**：`view-controller.ts` 管理显示模式

## 调试功能

### 排除标记

在调试模式下，被排除的元素会显示红色样式：

```css
color: red;
font-weight: bold;
```

相关属性：

- `data-exclusion-reason`: 排除原因
- `data-exclusion-processed`: 是否已处理
- `data-original-color`: 原始颜色
- `data-original-font-weight`: 原始字重

### 清理方法

```typescript
// 清理单个元素的排除标记
scanner.clearElementExclusionMarker(element);

// 清理所有排除标记
scanner.clearExclusionMarkers();
```

## 性能优化

- **懒加载**：屏幕外元素延迟翻译
- **批处理**：批量处理 DOM 操作
- **智能观察**：根据显示模式启用/禁用 DOM 观察
- **内存管理**：及时清理不需要的引用

## 测试

相关测试文件：

- `test-hidden-detection-validation.js` - 隐藏检测测试
- `test-lazy-loading-fix.js` - 懒加载修复测试

运行测试：

```bash
node test-hidden-detection-validation.js
node test-lazy-loading-fix.js
```
