<body class="template-articlepage keyword-blog">
  <noscript
    ><iframe
      src="https://www.googletagmanager.com/ns.html?id=GTM-TRV24V"
      height="0"
      width="0"
      style="display: none; visibility: hidden"
    ></iframe
  ></noscript>

  <div
    class="data-layer-init-data"
    data-ga4-analytics='
  {
    "event": "dataLayer_initialized",
    
      "page_name": "Our favorite Google Chrome extensions of 2023",
    
    "experiments": "undefined",
    "locale": "en-us",
    "page_type": "blogv2 | article page",
    "primary_tag": "products - chrome",
    "secondary_tags": "undefined",
    
      "landing_page_tags": "undefined",
    
    
      "article_name": "Our favorite Chrome extensions of 2023",
      "author_name": "<PERSON>",
    
    "publish_date": "2023-12-19|17:00",
    "hero_media": "image",
    
      "special_hero": "undefined",
    
    "days_since_published": "579",
    
      "content_category": "Products - Chrome",
    
    "word_count": "medium 300-599",
    "has_audio": "no",
    "has_video": "no"
  }'
  ></div>

  <script type="text/javascript">
    (function () {
      var dataLayerInitElement = document.querySelector(
        ".data-layer-init-data"
      );
      var dataLayerObject = JSON.parse(
        dataLayerInitElement.getAttribute("data-ga4-analytics")
      );

      // Change undefined strings to primitive value undefined.
      Object.entries(dataLayerObject).forEach(([key, value]) => {
        dataLayerObject[key] = value === "undefined" ? undefined : value;
      });

      if (!window["dataLayer"]) {
        window["dataLayer"] = [];
      }
      window["dataLayer"].push(dataLayerObject);
    })();
  </script>

  <div class="surveys">
    <uni-feedback-survey-controller>
      <script id="feedback-surveys-json" type="application/json">
        [
          {
            "model": "blogsurvey.survey",
            "pk": 7,
            "fields": {
              "name": "Article Improvements - March 2025",
              "survey_id": "article-improvements-march-2025_250321",
              "scroll_depth_trigger": 75,
              "previous_survey": null,
              "display_rate": 75,
              "thank_message": "Thank you!",
              "thank_emoji": "✅",
              "questions": "[{\"id\": \"5a12fd89-d978-4a1b-80e5-2442a91422be\", \"type\": \"simple_question\", \"value\": {\"question\": \"How could we improve this article?\", \"responses\": [{\"id\": \"30122b0d-1169-4376-af7c-20c9de52c91c\", \"type\": \"item\", \"value\": \"Make it more concise\"}, {\"id\": \"18f3016a-7235-468b-b246-ffe974911ae9\", \"type\": \"item\", \"value\": \"Add more detail\"}, {\"id\": \"5d19c11d-6a61-49d3-9f1d-dad5d661ba4f\", \"type\": \"item\", \"value\": \"Make it easier to understand\"}, {\"id\": \"97064d1f-d9af-4a83-a44f-a84f8ed899d6\", \"type\": \"item\", \"value\": \"Include more images or videos\"}, {\"id\": \"a9ec2a70-c7c5-4f00-a179-31a7b5641879\", \"type\": \"item\", \"value\": \"It's fine the way it is\"}]}}]",
              "target_article_pages": true
            }
          }
        ]
      </script>
      <!----><!--?lit$806344877$--><!--?--></uni-feedback-survey-controller
    >
  </div>

  <div class="uni-header-content-pusher"></div>
  <header
    class="uni-header h-u-box-shadow-2"
    data-component="uni-header"
    data-component-initialized="true"
  >
    <div class="uni-header__hamburguer-wrapper">
      <button
        class="uni-header__hamburguer-button"
        aria-expanded="false"
        tabindex="0"
        aria-label="Navigation menu"
      >
        <svg
          class="h-c-header__hamburger-img h-c-header__hamburger-img--standard events-off"
          role="presentation"
          aria-hidden="true"
        >
          <use
            xmlns:xlink="http://www.w3.org/1999/xlink"
            href="/static/blogv2/images/icons.svg?version=pr20250710-1658#h-burger"
          ></use>
        </svg>
      </button>
    </div>

    <figure class="uni-header__logo-google" title="Google" tabindex="-1">
      <svg aria-label="Google" role="img">
        <use
          xmlns:xlink="http://www.w3.org/1999/xlink"
          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#h-color-google-logo"
        ></use>
      </svg>
    </figure>

    <div>
      <a href="#jump-content" class="h-c-header__jump-to-content">
        <span class="h-c-header__jump-to-content-text"
          >Skip to main content</span
        >
      </a>
    </div>

    <div
      class="uni-header__site-title"
      data-analytics-module='{
         "module_name": "main nav",
         "section_header": "The Keyword"
       }'
    >
      <a href="/" class="uni-header__site-title-link">
        <div>The Keyword</div>
      </a>
    </div>

    <div
      class="uni-article-progress-bar slide-up"
      data-component="uni-progress-bar"
      role="none"
      data-component-initialized="true"
      aria-hidden="true"
    >
      <div
        class="uni-article-progress-bar__title uni-article-progress-bar__ellipsis"
      >
        Our favorite Chrome extensions of 2023
      </div>
      <div
        class="uni-article-progress-bar__social"
        data-analytics-module='{
      "module_name": "Progress Bar",
      "section_header": "Our favorite Chrome extensions of 2023"
    }'
      >
        <div
          class="uni-social-share hide-progress-bar"
          data-component="uni-social-share-dropdown"
          aria-hidden="true"
          data-component-initialized="true"
        >
          <a
            class="uni-social-share__trigger"
            role="button"
            tabindex="0"
            aria-label="Share"
            aria-expanded="false"
            rel="noopener"
            target="_blank"
          >
            <svg
              class="h-c-icon h-c-icon--color-text"
              aria-hidden="true"
              title="Share"
            >
              <use
                xmlns:xlink="http://www.w3.org/1999/xlink"
                href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-share"
              ></use>
            </svg>

            <div class="uni-social-share__button">Share</div>
          </a>
          <div
            class="uni-social-share__dialog uni-social-share__content"
            aria-labelledby="social-share-icon"
          >
            <a
              aria-label="Share on Twitter"
              class="article-share__link-text uni-click-tracker"
              href="https://twitter.com/intent/tweet?text=Our%20favorite%20Chrome%20extensions%20of%202023%20%40google&amp;url=https://blog.google/products/chrome/favorite-google-chrome-extensions-2023/"
              target="_blank"
              data-ga4-method="twitter"
              rel="noopener"
            >
              <svg
                class="h-c-icon h-c-icon--social h-c-icon--30px"
                aria-hidden="true"
                viewBox="0 0 30 30"
              >
                <use
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-twitter"
                ></use>
              </svg>

              <div class="article-share__title">Twitter</div>
            </a>

            <a
              aria-label="Share on Facebook"
              class="article-share__link-text uni-click-tracker"
              href="https://www.facebook.com/sharer/sharer.php?caption=Our%20favorite%20Chrome%20extensions%20of%202023&amp;u=https://blog.google/products/chrome/favorite-google-chrome-extensions-2023/"
              target="_blank"
              data-ga4-method="facebook"
              rel="noopener"
            >
              <svg
                class="h-c-icon h-c-icon--social h-c-icon--30px"
                aria-hidden="true"
                viewBox="0 0 30 30"
              >
                <use
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-facebook"
                ></use>
              </svg>

              <div class="article-share__title">Facebook</div>
            </a>

            <a
              aria-label="Share on LinkedIn"
              class="article-share__link-text uni-click-tracker"
              href="https://www.linkedin.com/shareArticle?mini=true&amp;url=https://blog.google/products/chrome/favorite-google-chrome-extensions-2023/&amp;title=Our%20favorite%20Chrome%20extensions%20of%202023"
              target="_blank"
              data-ga4-method="linkedin"
              rel="noopener"
            >
              <svg
                class="h-c-icon h-c-icon--social h-c-icon--30px"
                aria-hidden="true"
                viewBox="0 0 30 30"
              >
                <use
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-linkedin"
                ></use>
              </svg>

              <div class="article-share__title">LinkedIn</div>
            </a>

            <a
              aria-label="Share with Email"
              class="article-share__link-text uni-click-tracker article-share__email"
              href="mailto:?subject=Our%20favorite%20Chrome%20extensions%20of%202023&amp;body=Check out this article on the Keyword:%0A%0AOur%20favorite%20Chrome%20extensions%20of%202023%0A%0AHere are some of our favorite Chrome extensions from this year to help you get work done and have fun.%0A%0Ahttps://blog.google/products/chrome/favorite-google-chrome-extensions-2023/"
              target="_blank"
              data-ga4-method="email"
            >
              <svg
                class="h-c-icon h-c-icon--social h-c-icon--30px"
                aria-hidden="true"
                viewBox="0 0 30 30"
              >
                <use
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-mail"
                ></use>
              </svg>

              <div class="article-share__title">Mail</div>
            </a>

            <div
              class="copy-link uni-copy-share uni-click-tracker"
              data-component="uni-copy-popup-component"
              data-ga4-analytics-share-copy-link=""
              data-ga4-method="Copy link"
              data-component-initialized="true"
            >
              <button
                class="copy-link__trigger copy-link__trigger-text"
                data-ga4-method="Copy link"
                title="Copy link"
              >
                <svg
                  class="h-c-icon h-c-icon--color-text"
                  role="presentation"
                  title="Copy link"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-link"
                  ></use>
                </svg>

                <div class="copy-link__title">Copy link</div>
              </button>
              <div
                class="copy-link__dialog copy-link__content"
                uni-options='{"copyTextButton": "COPIED TO CLIPBOARD"}'
                aria-hidden="true"
                tabindex="-1"
                style="display: none"
              >
                <input
                  class="h-c-copy copy-link__url"
                  value="https://blog.google/products/chrome/favorite-google-chrome-extensions-2023/"
                  id="copy-link"
                  readonly="readonly"
                  type="text"
                />
                <div class="copy-link__copy-message" role="status"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="uni-article-progress-bar__indicator hide-progress-bar"
        style="width: 16.6145%"
      ></div>
    </div>

    <nav
      class="uni-navigation uni-navigation--desktop"
      role="navigation"
      aria-label="Main"
      data-analytics-module='{
        "module_name": "main nav",
        "section_header": "Desktop menu"
     }'
    >
      <div class="uni-navigation__list-desktop">
        <ul
          id="uni-main-menu"
          class="uni-main-menu"
          data-component="uni-navigation"
          data-component-initialized="true"
        >
          <li
            data-has-subnav=""
            class="uni-main-menu__item uni-main-menu__item--desktop inactive"
          >
            <a
              href="/"
              class="uni-main-menu__item-label"
              role="button"
              tabindex="0"
            >
              Home
            </a>
          </li>

          <li
            data-has-subnav="yes"
            class="uni-main-menu__item uni-main-menu__item--desktop active has-submenu"
          >
            <button
              class="uni-main-menu__item-label has-submenu"
              tabindex="0"
              id="31939210"
              aria-haspopup="menu"
              aria-expanded="false"
              aria-controls="uni-navigation__item-1-2"
            >
              Product news
              <figure class="expand-caret">
                <svg class="icon" role="presentation">
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                  ></use>
                </svg>
              </figure>
            </button>

            <div class="uni-main-menu__subnav uni-main-menu__subnav--desktop">
              <button
                class="uni-main-menu__subnav-title uni-main-menu__subnav-title--desktop"
              >
                <figure class="expand-caret">
                  <svg class="icon" role="presentation">
                    <use
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                    ></use>
                  </svg>
                </figure>
                Product news
              </button>
              <ul class="uni-main-menu__subnav-list">
                <li
                  class="uni-main-menu__subnav-item uni-main-menu__subnav-item--desktop has-submenu"
                >
                  <button
                    class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--desktop"
                  >
                    Android, Chrome &amp; Play
                    <figure class="expand-caret">
                      <svg class="icon" role="presentation">
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                        ></use>
                      </svg>
                    </figure>
                  </button>

                  <ul
                    class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--desktop"
                  >
                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/android/"
                        data-navigation="product-news"
                      >
                        Android
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/chrome/"
                        data-navigation="product-news"
                      >
                        Chrome
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/chromebooks/"
                        data-navigation="product-news"
                      >
                        Chromebooks
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/google-play/"
                        data-navigation="product-news"
                      >
                        Google Play
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/wear-os/"
                        data-navigation="product-news"
                      >
                        Wear OS
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        href="/products/android-chrome-play/"
                        class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                        data-navigation="Android, Chrome &amp; Play"
                      >
                        See all
                      </a>
                    </li>
                  </ul>
                </li>

                <li
                  class="uni-main-menu__subnav-item uni-main-menu__subnav-item--desktop has-submenu"
                >
                  <button
                    class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--desktop"
                  >
                    Platforms &amp; Devices
                    <figure class="expand-caret">
                      <svg class="icon" role="presentation">
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                        ></use>
                      </svg>
                    </figure>
                  </button>

                  <ul
                    class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--desktop"
                  >
                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/fitbit/"
                        data-navigation="product-news"
                      >
                        Fitbit
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/google-nest/"
                        data-navigation="product-news"
                      >
                        Google Nest
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/pixel/"
                        data-navigation="product-news"
                      >
                        Pixel
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        href="/products/platforms-devices/"
                        class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                        data-navigation="Platforms &amp; Devices"
                      >
                        See all
                      </a>
                    </li>
                  </ul>
                </li>

                <li
                  class="uni-main-menu__subnav-item uni-main-menu__subnav-item--desktop has-submenu"
                >
                  <button
                    class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--desktop"
                  >
                    Explore &amp; Get Answers
                    <figure class="expand-caret">
                      <svg class="icon" role="presentation">
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                        ></use>
                      </svg>
                    </figure>
                  </button>

                  <ul
                    class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--desktop"
                  >
                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/gemini/"
                        data-navigation="product-news"
                      >
                        Gemini
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/maps/"
                        data-navigation="product-news"
                      >
                        Maps
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/news/"
                        data-navigation="product-news"
                      >
                        News
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/search/"
                        data-navigation="product-news"
                      >
                        Search
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/shopping/"
                        data-navigation="product-news"
                      >
                        Shopping
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        href="/products/explore-get-answers/"
                        class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                        data-navigation="Explore &amp; Get Answers"
                      >
                        See all
                      </a>
                    </li>
                  </ul>
                </li>

                <li
                  class="uni-main-menu__subnav-item uni-main-menu__subnav-item--desktop has-submenu"
                >
                  <button
                    class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--desktop"
                  >
                    Connect &amp; Communicate
                    <figure class="expand-caret">
                      <svg class="icon" role="presentation">
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                        ></use>
                      </svg>
                    </figure>
                  </button>

                  <ul
                    class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--desktop"
                  >
                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/classroom/"
                        data-navigation="product-news"
                      >
                        Classroom
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/photos/"
                        data-navigation="product-news"
                      >
                        Photos
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/registry/"
                        data-navigation="product-news"
                      >
                        Registry
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/translate/"
                        data-navigation="product-news"
                      >
                        Translate
                      </a>
                    </li>
                  </ul>
                </li>

                <li
                  class="uni-main-menu__subnav-item uni-main-menu__subnav-item--desktop has-submenu"
                >
                  <button
                    class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--desktop"
                  >
                    In the Cloud
                    <figure class="expand-caret">
                      <svg class="icon" role="presentation">
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                        ></use>
                      </svg>
                    </figure>
                  </button>

                  <ul
                    class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--desktop"
                  >
                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/workspace/"
                        data-navigation="product-news"
                      >
                        Google Workspace
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="https://cloud.google.com/blog/"
                        data-navigation="product-news"
                        rel="noopener"
                        target="_blank"
                      >
                        More on the Cloud Blog

                        <svg
                          class="icon h-c-icon uni-main-menu__subnav-sublist-external-icon"
                          role="presentation"
                          viewBox="0 0 18 18"
                        >
                          <use
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-arrow-forward"
                          ></use>
                        </svg>
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/products/google-cloud/"
                        data-navigation="product-news"
                      >
                        Google Cloud
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        href="/products/cloud/"
                        class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                        data-navigation="In the Cloud"
                      >
                        See all
                      </a>
                    </li>
                  </ul>
                </li>
              </ul>

              <a
                href="/products/"
                class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--landing uni-main-menu__subnav-item-name--desktop"
              >
                See all product updates
              </a>
            </div>

            <div
              class="h-c-page uni-main-menu__submenu-wrapper"
              id="uni-navigation__item-1-2"
              aria-expanded="false"
              aria-hidden="true"
              role="menu"
            >
              <ul class="uni-main-menu__submenu has-view-all">
                <li
                  class="uni-main-menu__submenu-item"
                  role="group"
                  aria-labelledby="uni-main-menu__submenu-item-label-product-news-1"
                >
                  <span
                    class="uni-main-menu__submenu-item-label"
                    id="uni-main-menu__submenu-item-label-product-news-1"
                  >
                    Android, Chrome &amp; Play
                  </span>

                  <ul class="uni-main-menu__submenu-links">
                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/android/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Android
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/chrome/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Chrome
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/chromebooks/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Chromebooks
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/google-play/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Google Play
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/wear-os/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Wear OS
                      </a>
                    </li>
                  </ul>

                  <a
                    href="/products/android-chrome-play/"
                    data-navigation="Android, Chrome &amp; Play"
                    role="menuitem"
                    title="See all Android, Chrome &amp; Play articles"
                    class="uni-main-menu__submenu-item-see-all"
                  >
                    See all
                    <svg id="mi-arrow-forward-no-bg" viewBox="0 0 32 32">
                      <polygon
                        points="16,0 13.2,2.8 24.3,14 0,14 0,18 24.3,18 13.2,29.2 16,32 32,16"
                      ></polygon>
                    </svg>
                  </a>
                </li>

                <li
                  class="uni-main-menu__submenu-item"
                  role="group"
                  aria-labelledby="uni-main-menu__submenu-item-label-product-news-2"
                >
                  <span
                    class="uni-main-menu__submenu-item-label"
                    id="uni-main-menu__submenu-item-label-product-news-2"
                  >
                    Platforms &amp; Devices
                  </span>

                  <ul class="uni-main-menu__submenu-links">
                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/fitbit/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Fitbit
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/google-nest/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Google Nest
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/pixel/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Pixel
                      </a>
                    </li>
                  </ul>

                  <a
                    href="/products/platforms-devices/"
                    data-navigation="Platforms &amp; Devices"
                    role="menuitem"
                    title="See all Platforms &amp; Devices articles"
                    class="uni-main-menu__submenu-item-see-all"
                  >
                    See all
                    <svg id="mi-arrow-forward-no-bg" viewBox="0 0 32 32">
                      <polygon
                        points="16,0 13.2,2.8 24.3,14 0,14 0,18 24.3,18 13.2,29.2 16,32 32,16"
                      ></polygon>
                    </svg>
                  </a>
                </li>

                <li
                  class="uni-main-menu__submenu-item"
                  role="group"
                  aria-labelledby="uni-main-menu__submenu-item-label-product-news-3"
                >
                  <span
                    class="uni-main-menu__submenu-item-label"
                    id="uni-main-menu__submenu-item-label-product-news-3"
                  >
                    Explore &amp; Get Answers
                  </span>

                  <ul class="uni-main-menu__submenu-links">
                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/gemini/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Gemini
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/maps/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Maps
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/news/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        News
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/search/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Search
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/shopping/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Shopping
                      </a>
                    </li>
                  </ul>

                  <a
                    href="/products/explore-get-answers/"
                    data-navigation="Explore &amp; Get Answers"
                    role="menuitem"
                    title="See all Explore &amp; Get Answers articles"
                    class="uni-main-menu__submenu-item-see-all"
                  >
                    See all
                    <svg id="mi-arrow-forward-no-bg" viewBox="0 0 32 32">
                      <polygon
                        points="16,0 13.2,2.8 24.3,14 0,14 0,18 24.3,18 13.2,29.2 16,32 32,16"
                      ></polygon>
                    </svg>
                  </a>
                </li>

                <li
                  class="uni-main-menu__submenu-item"
                  role="group"
                  aria-labelledby="uni-main-menu__submenu-item-label-product-news-4"
                >
                  <span
                    class="uni-main-menu__submenu-item-label"
                    id="uni-main-menu__submenu-item-label-product-news-4"
                  >
                    Connect &amp; Communicate
                  </span>

                  <ul class="uni-main-menu__submenu-links">
                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/classroom/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Classroom
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/photos/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Photos
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/registry/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Registry
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/translate/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Translate
                      </a>
                    </li>
                  </ul>
                </li>

                <li
                  class="uni-main-menu__submenu-item"
                  role="group"
                  aria-labelledby="uni-main-menu__submenu-item-label-product-news-5"
                >
                  <span
                    class="uni-main-menu__submenu-item-label"
                    id="uni-main-menu__submenu-item-label-product-news-5"
                  >
                    In the Cloud
                  </span>

                  <ul class="uni-main-menu__submenu-links">
                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/workspace/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Google Workspace
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="https://cloud.google.com/blog/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                        rel="noopener"
                        target="_blank"
                      >
                        More on the Cloud Blog

                        <svg
                          class="icon h-c-icon uni-main-menu__submenu-external-icon"
                          role="presentation"
                          viewBox="0 0 18 18"
                        >
                          <use
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                            href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-arrow-forward"
                          ></use>
                        </svg>
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/products/google-cloud/"
                        role="menuitem"
                        data-navigation="product-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Google Cloud
                      </a>
                    </li>
                  </ul>

                  <a
                    href="/products/cloud/"
                    data-navigation="In the Cloud"
                    role="menuitem"
                    title="See all In the Cloud articles"
                    class="uni-main-menu__submenu-item-see-all"
                  >
                    See all
                    <svg id="mi-arrow-forward-no-bg" viewBox="0 0 32 32">
                      <polygon
                        points="16,0 13.2,2.8 24.3,14 0,14 0,18 24.3,18 13.2,29.2 16,32 32,16"
                      ></polygon>
                    </svg>
                  </a>
                </li>
              </ul>

              <div class="uni-main-menu__submenu-view-all">
                <a
                  href="/products/"
                  class="uni-main-menu__submenu-view-all-link"
                >
                  See all product updates
                  <svg id="mi-arrow-forward-no-bg" viewBox="0 0 32 32">
                    <polygon
                      points="16,0 13.2,2.8 24.3,14 0,14 0,18 24.3,18 13.2,29.2 16,32 32,16"
                    ></polygon>
                  </svg>
                </a>
              </div>

              <button
                class="uni-main-menu__submenu-close"
                aria-label="Close Menu"
              >
                <svg
                  class="h-c-icon h-c-icon--24px uni-main-menu__submenu-close--icon"
                  aria-hidden="true"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#close_icon"
                  ></use>
                </svg>
              </button>
            </div>
          </li>

          <li
            data-has-subnav="yes"
            class="uni-main-menu__item uni-main-menu__item--desktop inactive has-submenu"
          >
            <button
              class="uni-main-menu__item-label has-submenu"
              tabindex="0"
              xt-marked="ok"
              id="42617139"
              aria-haspopup="menu"
              aria-expanded="false"
              aria-controls="uni-navigation__item-1-3"
            >
              <xt-mark w="company" style="color: #e89b64 !important"
                >Company</xt-mark
              >
              news
              <figure class="expand-caret">
                <svg class="icon" role="presentation">
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                  ></use>
                </svg>
              </figure>
            </button>

            <div class="uni-main-menu__subnav uni-main-menu__subnav--desktop">
              <button
                class="uni-main-menu__subnav-title uni-main-menu__subnav-title--desktop"
                xt-marked="ok"
              >
                <figure class="expand-caret">
                  <svg class="icon" role="presentation">
                    <use
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                    ></use>
                  </svg>
                </figure>
                <xt-mark w="company" style="color: #e89b64 !important"
                  >Company</xt-mark
                >
                news
              </button>
              <ul class="uni-main-menu__subnav-list">
                <li
                  class="uni-main-menu__subnav-item uni-main-menu__subnav-item--desktop has-submenu"
                >
                  <button
                    class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--desktop"
                    xt-marked="ok"
                  >
                    Outreach &amp;
                    <xt-mark w="initiative" style="color: #e89b64 !important"
                      >initiatives</xt-mark
                    >
                    <figure class="expand-caret">
                      <svg class="icon" role="presentation">
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                        ></use>
                      </svg>
                    </figure>
                  </button>

                  <ul
                    class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--desktop"
                  >
                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/outreach-initiatives/arts-culture/"
                        data-navigation="company-news"
                      >
                        Arts &amp; Culture
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/outreach-initiatives/education/"
                        data-navigation="company-news"
                      >
                        Education
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/outreach-initiatives/entrepreneurs/"
                        data-navigation="company-news"
                      >
                        Entrepreneurs
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/outreach-initiatives/public-policy/"
                        data-navigation="company-news"
                        xt-marked="ok"
                      >
                        Public&nbsp;<xt-mark
                          w="policy"
                          style="color: #e89b64 !important"
                          >Policy</xt-mark
                        >&nbsp;
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/outreach-initiatives/sustainability/"
                        data-navigation="company-news"
                      >
                        Sustainability
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        href="/outreach-initiatives/"
                        class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                        data-navigation="Outreach &amp; initiatives"
                      >
                        See all
                      </a>
                    </li>
                  </ul>
                </li>

                <li
                  class="uni-main-menu__subnav-item uni-main-menu__subnav-item--desktop has-submenu"
                >
                  <button
                    class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--desktop"
                  >
                    Technology
                    <figure class="expand-caret">
                      <svg class="icon" role="presentation">
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                        ></use>
                      </svg>
                    </figure>
                  </button>

                  <ul
                    class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--desktop"
                  >
                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/technology/ai/"
                        data-navigation="company-news"
                      >
                        AI
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/technology/developers/"
                        data-navigation="company-news"
                      >
                        Developers
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/technology/health/"
                        data-navigation="company-news"
                      >
                        Health
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/technology/google-deepmind/"
                        data-navigation="company-news"
                      >
                        Google DeepMind
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/technology/google-labs/"
                        data-navigation="company-news"
                      >
                        Google Labs
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/technology/safety-security/"
                        data-navigation="company-news"
                      >
                        Safety and security
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        href="/technology/"
                        class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                        data-navigation="Technology"
                      >
                        See all
                      </a>
                    </li>
                  </ul>
                </li>

                <li
                  class="uni-main-menu__subnav-item uni-main-menu__subnav-item--desktop has-submenu"
                >
                  <button
                    class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--desktop"
                  >
                    Inside Google
                    <figure class="expand-caret">
                      <svg class="icon" role="presentation">
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                        ></use>
                      </svg>
                    </figure>
                  </button>

                  <ul
                    class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--desktop"
                  >
                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/inside-google/infrastructure/"
                        data-navigation="company-news"
                        xt-marked="ok"
                      >
                        Data centers and&nbsp;<xt-mark
                          w="infrastructure"
                          style="color: #e89b64 !important"
                          >infrastructure</xt-mark
                        >&nbsp;
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/inside-google/doodles/"
                        data-navigation="company-news"
                      >
                        Doodles
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/inside-google/googlers/"
                        data-navigation="company-news"
                      >
                        Googlers
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/inside-google/life-at-google/"
                        data-navigation="company-news"
                      >
                        Life at Google
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        href="/inside-google/"
                        class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                        data-navigation="Inside Google"
                      >
                        See all
                      </a>
                    </li>
                  </ul>
                </li>

                <li
                  class="uni-main-menu__subnav-item uni-main-menu__subnav-item--desktop has-submenu"
                >
                  <button
                    class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--desktop"
                  >
                    Around the globe
                    <figure class="expand-caret">
                      <svg class="icon" role="presentation">
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                        ></use>
                      </svg>
                    </figure>
                  </button>

                  <ul
                    class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--desktop"
                  >
                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/around-the-globe/google-asia/"
                        data-navigation="company-news"
                        xt-marked="ok"
                      >
                        Google in&nbsp;<xt-mark
                          w="asia"
                          style="color: #e89b64 !important"
                          >Asia</xt-mark
                        >&nbsp;
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/around-the-globe/google-europe/"
                        data-navigation="company-news"
                      >
                        Google in Europe
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/around-the-globe/google-latin-america/"
                        data-navigation="company-news"
                      >
                        Google in Latin America
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        href="/around-the-globe/"
                        class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                        data-navigation="Around the globe"
                      >
                        See all
                      </a>
                    </li>
                  </ul>
                </li>

                <li
                  class="uni-main-menu__subnav-item uni-main-menu__subnav-item--desktop has-submenu"
                >
                  <button
                    class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--desktop"
                  >
                    Authors
                    <figure class="expand-caret">
                      <svg class="icon" role="presentation">
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                        ></use>
                      </svg>
                    </figure>
                  </button>

                  <ul
                    class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--desktop"
                  >
                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/authors/sundar-pichai/"
                        data-navigation="company-news"
                      >
                        Sundar Pichai, CEO
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/authors/demis-hassabis/"
                        data-navigation="company-news"
                      >
                        Demis Hassabis, CEO and Co-Founder, Google DeepMind
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/authors/kent-walker/"
                        data-navigation="company-news"
                      >
                        Kent Walker, SVP
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/authors/james-manyika/"
                        data-navigation="company-news"
                      >
                        James Manyika, SVP
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        class="uni-main-menu__subnav-sublist-link"
                        href="/authors/ruth-porat/"
                        data-navigation="company-news"
                      >
                        Ruth Porat, President &amp; Chief Investment Officer
                      </a>
                    </li>

                    <li class="uni-main-menu__subnav-sublist-item">
                      <a
                        href="/authors/"
                        class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                        data-navigation="Authors"
                      >
                        See all
                      </a>
                    </li>
                  </ul>
                </li>
              </ul>
            </div>

            <div
              class="h-c-page uni-main-menu__submenu-wrapper"
              id="uni-navigation__item-1-3"
              aria-expanded="false"
              aria-hidden="true"
              role="menu"
            >
              <ul class="uni-main-menu__submenu has-view-all">
                <li
                  class="uni-main-menu__submenu-item"
                  role="group"
                  aria-labelledby="uni-main-menu__submenu-item-label-company-news-1"
                >
                  <span
                    class="uni-main-menu__submenu-item-label"
                    id="uni-main-menu__submenu-item-label-company-news-1"
                    xt-marked="ok"
                  >
                    Outreach &amp;
                    <xt-mark w="initiative" style="color: #e89b64 !important"
                      >initiatives</xt-mark
                    >
                  </span>

                  <ul class="uni-main-menu__submenu-links">
                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/outreach-initiatives/arts-culture/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Arts &amp; Culture
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/outreach-initiatives/education/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Education
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/outreach-initiatives/entrepreneurs/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Entrepreneurs
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/outreach-initiatives/public-policy/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                        xt-marked="ok"
                      >
                        Public&nbsp;<xt-mark
                          w="policy"
                          style="color: #e89b64 !important"
                          >Policy</xt-mark
                        >&nbsp;
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/outreach-initiatives/sustainability/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Sustainability
                      </a>
                    </li>
                  </ul>

                  <a
                    href="/outreach-initiatives/"
                    data-navigation="Outreach &amp; initiatives"
                    role="menuitem"
                    title="See all Outreach &amp; initiatives articles"
                    class="uni-main-menu__submenu-item-see-all"
                  >
                    See all
                    <svg id="mi-arrow-forward-no-bg" viewBox="0 0 32 32">
                      <polygon
                        points="16,0 13.2,2.8 24.3,14 0,14 0,18 24.3,18 13.2,29.2 16,32 32,16"
                      ></polygon>
                    </svg>
                  </a>
                </li>

                <li
                  class="uni-main-menu__submenu-item"
                  role="group"
                  aria-labelledby="uni-main-menu__submenu-item-label-company-news-2"
                >
                  <span
                    class="uni-main-menu__submenu-item-label"
                    id="uni-main-menu__submenu-item-label-company-news-2"
                  >
                    Technology
                  </span>

                  <ul class="uni-main-menu__submenu-links">
                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/technology/ai/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        AI
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/technology/developers/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Developers
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/technology/health/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Health
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/technology/google-deepmind/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Google DeepMind
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/technology/google-labs/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Google Labs
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/technology/safety-security/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Safety and security
                      </a>
                    </li>
                  </ul>

                  <a
                    href="/technology/"
                    data-navigation="Technology"
                    role="menuitem"
                    title="See all Technology articles"
                    class="uni-main-menu__submenu-item-see-all"
                  >
                    See all
                    <svg id="mi-arrow-forward-no-bg" viewBox="0 0 32 32">
                      <polygon
                        points="16,0 13.2,2.8 24.3,14 0,14 0,18 24.3,18 13.2,29.2 16,32 32,16"
                      ></polygon>
                    </svg>
                  </a>
                </li>

                <li
                  class="uni-main-menu__submenu-item"
                  role="group"
                  aria-labelledby="uni-main-menu__submenu-item-label-company-news-3"
                >
                  <span
                    class="uni-main-menu__submenu-item-label"
                    id="uni-main-menu__submenu-item-label-company-news-3"
                  >
                    Inside Google
                  </span>

                  <ul class="uni-main-menu__submenu-links">
                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/inside-google/infrastructure/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                        xt-marked="ok"
                      >
                        Data centers and&nbsp;<xt-mark
                          w="infrastructure"
                          style="color: #e89b64 !important"
                          >infrastructure</xt-mark
                        >&nbsp;
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/inside-google/doodles/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Doodles
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/inside-google/googlers/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Googlers
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/inside-google/life-at-google/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Life at Google
                      </a>
                    </li>
                  </ul>

                  <a
                    href="/inside-google/"
                    data-navigation="Inside Google"
                    role="menuitem"
                    title="See all Inside Google articles"
                    class="uni-main-menu__submenu-item-see-all"
                  >
                    See all
                    <svg id="mi-arrow-forward-no-bg" viewBox="0 0 32 32">
                      <polygon
                        points="16,0 13.2,2.8 24.3,14 0,14 0,18 24.3,18 13.2,29.2 16,32 32,16"
                      ></polygon>
                    </svg>
                  </a>
                </li>

                <li
                  class="uni-main-menu__submenu-item"
                  role="group"
                  aria-labelledby="uni-main-menu__submenu-item-label-company-news-4"
                >
                  <span
                    class="uni-main-menu__submenu-item-label"
                    id="uni-main-menu__submenu-item-label-company-news-4"
                  >
                    Around the globe
                  </span>

                  <ul class="uni-main-menu__submenu-links">
                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/around-the-globe/google-asia/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                        xt-marked="ok"
                      >
                        Google in&nbsp;<xt-mark
                          w="asia"
                          style="color: #e89b64 !important"
                          >Asia</xt-mark
                        >&nbsp;
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/around-the-globe/google-europe/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Google in Europe
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/around-the-globe/google-latin-america/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Google in Latin America
                      </a>
                    </li>
                  </ul>

                  <a
                    href="/around-the-globe/"
                    data-navigation="Around the globe"
                    role="menuitem"
                    title="See all Around the globe articles"
                    class="uni-main-menu__submenu-item-see-all"
                  >
                    See all
                    <svg id="mi-arrow-forward-no-bg" viewBox="0 0 32 32">
                      <polygon
                        points="16,0 13.2,2.8 24.3,14 0,14 0,18 24.3,18 13.2,29.2 16,32 32,16"
                      ></polygon>
                    </svg>
                  </a>
                </li>

                <li
                  class="uni-main-menu__submenu-item"
                  role="group"
                  aria-labelledby="uni-main-menu__submenu-item-label-company-news-5"
                >
                  <span
                    class="uni-main-menu__submenu-item-label"
                    id="uni-main-menu__submenu-item-label-company-news-5"
                  >
                    Authors
                  </span>

                  <ul class="uni-main-menu__submenu-links">
                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/authors/sundar-pichai/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Sundar Pichai, CEO
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/authors/demis-hassabis/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Demis Hassabis, CEO and Co-Founder, Google DeepMind
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/authors/kent-walker/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Kent Walker, SVP
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/authors/james-manyika/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        James Manyika, SVP
                      </a>
                    </li>

                    <li class="uni-main-menu__submenu-link">
                      <a
                        href="/authors/ruth-porat/"
                        role="menuitem"
                        data-navigation="company-news"
                        class="uni-main-menu__submenu-anchor"
                      >
                        Ruth Porat, President &amp; Chief Investment Officer
                      </a>
                    </li>
                  </ul>

                  <a
                    href="/authors/"
                    data-navigation="Authors"
                    role="menuitem"
                    title="See all Authors articles"
                    class="uni-main-menu__submenu-item-see-all"
                  >
                    See all
                    <svg id="mi-arrow-forward-no-bg" viewBox="0 0 32 32">
                      <polygon
                        points="16,0 13.2,2.8 24.3,14 0,14 0,18 24.3,18 13.2,29.2 16,32 32,16"
                      ></polygon>
                    </svg>
                  </a>
                </li>
              </ul>

              <button
                class="uni-main-menu__submenu-close"
                aria-label="Close Menu"
              >
                <svg
                  class="h-c-icon h-c-icon--24px uni-main-menu__submenu-close--icon"
                  aria-hidden="true"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#close_icon"
                  ></use>
                </svg>
              </button>
            </div>
          </li>

          <li
            data-has-subnav=""
            class="uni-main-menu__item uni-main-menu__item--desktop inactive"
          >
            <a
              href="/feed"
              class="uni-main-menu__item-label"
              role="button"
              tabindex="0"
            >
              Feed
            </a>
          </li>
        </ul>
      </div>

      <div class="uni-header__newsletter uni-header__newsletter--mobile">
        <a
          class="uni-header__newsletter--cta uni-header__newsletter--cta--mobile kw-button kw-button--high-emphasis"
          href="/newsletter-subscribe/"
          aria-label="Newsletter subscribe"
          data-content-type="blogv2 | article page"
        >
          Subscribe</a
        >
      </div>
    </nav>

    <script type="application/json" data-catalog-id="search-bar">
      {
        "find_an_article": "Find an article...",
        "rss_link": "/rss/",
        "rss_feed": "RSS feed",
        "press_corner": "Press corner",
        "press_corner_slug": "press/",
        "secondary_menu": "Secondary menu",
        "search": "Search",
        "submit": "Submit",
        "close": "Close",
        "execute_search": "Execute search",
        "dismiss_search": "Dismiss search",
        "suggested_results_for": "[[number]] suggested results for [[query_term]]",
        "by_string": "By",
        "conjunction_symbol": "\u0026"
      }
    </script>

    <uni-search-bar class="uni-search-bar" site-id="2">
      <!----><!--?lit$806344877$-->
      <form
        role="dialog"
        aria-modal="true"
        class="uni-search-bar__container slide-in-from-right"
        id="search-form"
        hidden=""
        data-analytics-module='{"module_name":"search","section_header":"kebab menu"}'
      >
        <input
          id="search-bar-pre-node"
          placeholder="focustrap"
          data-direction="backward"
          type="text"
          class="uni-search-drawer__focus-trap"
        />
        <!--?lit$806344877$--><input
          placeholder="Find an article..."
          class="uni-search-bar__input uni-search-bar__keep"
          arialabel="Find an article..."
          type="search"
        />
        <button
          class="uni-search-bar__icon uni-search-bar__cta uni-search-bar__cta--search uni-search-bar__keep"
          type="submit"
          title="Submit"
          aria-label="Execute search"
        >
          <uni-icon-component
            icon-id="mi-search"
            custom-class="h-c-icon h-c-icon--24px"
            remove-viewbox="true"
            ><!----><svg
              class="icon h-c-icon h-c-icon--24px"
              role="presentation"
            >
              <use
                href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-search"
              ></use></svg
          ></uni-icon-component>
        </button>
        <button
          class="uni-search-bar__icon uni-search-bar__cta uni-search-bar__cta--close"
          aria-controls="search-form"
          aria-expanded="true"
          title="Close"
          aria-label="Dismiss search"
        >
          <uni-icon-component
            custom-class="h-c-icon h-c-icon--24px"
            icon-id="mi-clear"
            remove-viewbox="true"
            ><!----><svg
              class="icon h-c-icon h-c-icon--24px"
              role="presentation"
            >
              <use
                href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-clear"
              ></use></svg
          ></uni-icon-component>
        </button>
        <!--?lit$806344877$-->
        <input
          id="search-bar-post-node"
          placeholder="focustrap"
          data-direction="forward"
          type="text"
          class="uni-search-drawer__focus-trap"
        />
      </form>
      <!--?lit$806344877$--><button
        class="uni-search-bar__icon uni-search-bar__toggle"
        aria-controls="search-form"
        aria-expanded="false"
        type="submit"
        data-analytics-module='{"module_name":"main nav","section_header":"kebab menu"}'
        title="Search"
      >
        <uni-icon-component
          icon-id="mi-search"
          custom-class="h-c-icon h-c-icon--24px"
          remove-viewbox="true"
          ><!----><svg class="icon h-c-icon h-c-icon--24px" role="presentation">
            <use
              href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-search"
            ></use></svg
        ></uni-icon-component>
      </button>
      <!--?lit$806344877$--><!--?lit$806344877$-->
      <div class="uni-search-bar__language-picker">
        <div class="uni-search-bar__language-picker-icon" aria-hidden="true">
          <uni-icon-component
            custom-class="h-c-icon h-c-icon--24px"
            icon-id="uni-icon-language"
            remove-viewbox="true"
            ><!----><svg
              class="icon h-c-icon h-c-icon--24px"
              role="presentation"
            >
              <use
                href="/static/blogv2/images/icons.svg?version=pr20250710-1658#uni-icon-language"
              ></use></svg
          ></uni-icon-component>
        </div>
        <div class="uni-search-bar__language-picker-select-slot">
          <!--?lit$806344877$-->
          <div slot="select-slot" style="display: block">
            <div
              class="uni-picker"
              data-component="uni-lang-picker"
              data-component-initialized="true"
            >
              <select
                name="language"
                class="uni-picker__order-menu"
                aria-label="Change Region"
              >
                <option
                  label="Global (English)"
                  value="/"
                  class="uni-picker__item"
                  selected="selected"
                  data-selected-index="0"
                >
                  Global (English)
                </option>

                <option
                  label="Africa (English)"
                  value="/intl/en-africa/"
                  class="uni-picker__item"
                >
                  Africa (English)
                </option>

                <option
                  label="Australia (English)"
                  value="/intl/en-au/"
                  class="uni-picker__item"
                >
                  Australia (English)
                </option>

                <option
                  label="Brasil (Português)"
                  value="/intl/pt-br/"
                  class="uni-picker__item"
                >
                  Brasil (Português)
                </option>

                <option
                  label="Canada (English)"
                  value="/intl/en-ca/"
                  class="uni-picker__item"
                >
                  Canada (English)
                </option>

                <option
                  label="Canada (Français)"
                  value="/intl/fr-ca/"
                  class="uni-picker__item"
                >
                  Canada (Français)
                </option>

                <option
                  label="Česko (Čeština)"
                  value="/intl/cs-cz/"
                  class="uni-picker__item"
                >
                  Česko (Čeština)
                </option>

                <option
                  label="Deutschland (Deutsch)"
                  value="/intl/de-de/"
                  class="uni-picker__item"
                >
                  Deutschland (Deutsch)
                </option>

                <option
                  label="España (Español)"
                  value="/intl/es-es/"
                  class="uni-picker__item"
                >
                  España (Español)
                </option>

                <option
                  label="France (Français)"
                  value="/intl/fr-fr/"
                  class="uni-picker__item"
                >
                  France (Français)
                </option>

                <option
                  label="India (English)"
                  value="/intl/en-in/"
                  class="uni-picker__item"
                >
                  India (English)
                </option>

                <option
                  label="Indonesia (Bahasa Indonesia)"
                  value="/intl/id-id/"
                  class="uni-picker__item"
                >
                  Indonesia (Bahasa Indonesia)
                </option>

                <option
                  label="日本 (日本語)"
                  value="/intl/ja-jp/"
                  class="uni-picker__item"
                >
                  日本 (日本語)
                </option>

                <option
                  label="대한민국 (한국어)"
                  value="/intl/ko-kr/"
                  class="uni-picker__item"
                >
                  대한민국 (한국어)
                </option>

                <option
                  label="Latinoamérica (Español)"
                  value="/intl/es-419/"
                  class="uni-picker__item"
                >
                  Latinoamérica (Español)
                </option>

                <option
                  label="الشرق الأوسط وشمال أفريقيا (اللغة العربية)"
                  value="/intl/ar-mena/"
                  class="uni-picker__item"
                >
                  الشرق الأوسط وشمال أفريقيا (اللغة العربية)
                </option>

                <option
                  label="Nederlands (Nederland)"
                  value="/intl/nl-nl/"
                  class="uni-picker__item"
                >
                  Nederlands (Nederland)
                </option>

                <option
                  label="Polska (Polski)"
                  value="/intl/pl-pl/"
                  class="uni-picker__item"
                >
                  Polska (Polski)
                </option>

                <option
                  label="Italia (Italiano)"
                  value="/intl/it-it/"
                  class="uni-picker__item"
                >
                  Italia (Italiano)
                </option>

                <option
                  label="ประเทศไทย (ไทย)"
                  value="/intl/th-th/"
                  class="uni-picker__item"
                >
                  ประเทศไทย (ไทย)
                </option>

                <option
                  label="Türkiye (Türkçe)"
                  value="/intl/tr-tr/"
                  class="uni-picker__item"
                >
                  Türkiye (Türkçe)
                </option>

                <option
                  label="台灣 (中文)"
                  value="/intl/zh-tw/"
                  class="uni-picker__item"
                >
                  台灣 (中文)
                </option>

                <option
                  label="MENA (English)"
                  value="/intl/en-mena/"
                  class="uni-picker__item"
                >
                  MENA (English)
                </option>
              </select>
            </div>
          </div>
        </div>
      </div>
      <!--?-->
      <div
        class="uni-search-bar__kebab"
        data-analytics-module='{"module_name":"main nav","section_header":"kebab menu"}'
      >
        <button
          class="uni-search-bar__icon uni-search-bar__icon--kebab"
          title="Secondary menu"
        >
          <uni-icon-component
            custom-class="h-c-icon h-c-icon--24px"
            icon-id="mi-more-vert"
            remove-viewbox="true"
            ><!----><svg
              class="icon h-c-icon h-c-icon--24px"
              role="presentation"
            >
              <use
                href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-more-vert"
              ></use></svg
          ></uni-icon-component>
        </button>
        <!--?lit$806344877$-->
      </div>
      <!--?--></uni-search-bar
    >

    <div class="uni-header__newsletter uni-header__newsletter--desktop">
      <a
        class="uni-header__newsletter--cta uni-header__newsletter--cta--desktop kw-button kw-button--high-emphasis"
        href="/newsletter-subscribe/"
        aria-label="Newsletter subscribe"
        data-content-type="blogv2 | article page"
      >
        Subscribe</a
      >
    </div>
  </header>

  <nav
    class="uni-navigation uni-navigation--mobile"
    role="navigation"
    aria-label="Main"
    data-component="uni-navigation-mobile"
    aria-hidden="true"
    data-analytics-module='{
        "module_name": "mobile nav",
        "section_header": "Mobile menu"
     }'
    data-component-initialized="true"
  >
    <div class="uni-navigation__brand-mobile">
      <button
        class="uni-navigation__close"
        aria-label="Close Menu"
        aria-hidden="true"
      >
        <svg
          aria-label="Close Menu"
          class="h-c-icon h-c-icon--24px uni-main-menu__submenu-close--icon"
          aria-hidden="true"
        >
          <use
            xmlns:xlink="http://www.w3.org/1999/xlink"
            href="/static/blogv2/images/icons.svg?version=pr20250710-1658#close_icon"
          ></use>
        </svg>
      </button>

      <figure class="uni-navigation__logo-google" title="Google">
        <svg aria-label="Google" role="img">
          <use
            xmlns:xlink="http://www.w3.org/1999/xlink"
            href="/static/blogv2/images/icons.svg?version=pr20250710-1658#h-color-google-logo"
          ></use>
        </svg>
      </figure>

      <span class="uni-navigation__site-title">
        <a class="uni-header__site-title-link" href="/"> The Keyword </a>
      </span>
    </div>

    <div class="uni-navigation__list-mobile">
      <ul id="uni-main-menu" class="uni-main-menu">
        <li
          data-has-subnav=""
          class="uni-main-menu__item uni-main-menu__item--mobile inactive"
        >
          <a
            href="/"
            class="uni-main-menu__item-label"
            role="button"
            tabindex="0"
          >
            Home
          </a>
        </li>

        <li
          data-has-subnav="yes"
          class="uni-main-menu__item uni-main-menu__item--mobile active has-submenu"
        >
          <button class="uni-main-menu__item-label has-submenu" tabindex="0">
            Product news
            <figure class="expand-caret">
              <svg class="icon" role="presentation">
                <use
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                ></use>
              </svg>
            </figure>
          </button>

          <div class="uni-main-menu__subnav uni-main-menu__subnav--mobile">
            <button
              class="uni-main-menu__subnav-title uni-main-menu__subnav-title--mobile"
            >
              <figure class="expand-caret">
                <svg class="icon" role="presentation">
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                  ></use>
                </svg>
              </figure>
              Product news
            </button>
            <ul class="uni-main-menu__subnav-list">
              <li
                class="uni-main-menu__subnav-item uni-main-menu__subnav-item--mobile has-submenu"
              >
                <button
                  class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--mobile"
                >
                  Android, Chrome &amp; Play
                  <figure class="expand-caret">
                    <svg class="icon" role="presentation">
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                      ></use>
                    </svg>
                  </figure>
                </button>

                <ul
                  class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--mobile"
                >
                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/android/"
                      data-navigation="product-news"
                    >
                      Android
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/chrome/"
                      data-navigation="product-news"
                    >
                      Chrome
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/chromebooks/"
                      data-navigation="product-news"
                    >
                      Chromebooks
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/google-play/"
                      data-navigation="product-news"
                    >
                      Google Play
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/wear-os/"
                      data-navigation="product-news"
                    >
                      Wear OS
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      href="/products/android-chrome-play/"
                      class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                      data-navigation="Android, Chrome &amp; Play"
                    >
                      See all
                    </a>
                  </li>
                </ul>
              </li>

              <li
                class="uni-main-menu__subnav-item uni-main-menu__subnav-item--mobile has-submenu"
              >
                <button
                  class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--mobile"
                >
                  Platforms &amp; Devices
                  <figure class="expand-caret">
                    <svg class="icon" role="presentation">
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                      ></use>
                    </svg>
                  </figure>
                </button>

                <ul
                  class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--mobile"
                >
                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/fitbit/"
                      data-navigation="product-news"
                    >
                      Fitbit
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/google-nest/"
                      data-navigation="product-news"
                    >
                      Google Nest
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/pixel/"
                      data-navigation="product-news"
                    >
                      Pixel
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      href="/products/platforms-devices/"
                      class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                      data-navigation="Platforms &amp; Devices"
                    >
                      See all
                    </a>
                  </li>
                </ul>
              </li>

              <li
                class="uni-main-menu__subnav-item uni-main-menu__subnav-item--mobile has-submenu"
              >
                <button
                  class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--mobile"
                >
                  Explore &amp; Get Answers
                  <figure class="expand-caret">
                    <svg class="icon" role="presentation">
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                      ></use>
                    </svg>
                  </figure>
                </button>

                <ul
                  class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--mobile"
                >
                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/gemini/"
                      data-navigation="product-news"
                    >
                      Gemini
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/maps/"
                      data-navigation="product-news"
                    >
                      Maps
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/news/"
                      data-navigation="product-news"
                    >
                      News
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/search/"
                      data-navigation="product-news"
                    >
                      Search
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/shopping/"
                      data-navigation="product-news"
                    >
                      Shopping
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      href="/products/explore-get-answers/"
                      class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                      data-navigation="Explore &amp; Get Answers"
                    >
                      See all
                    </a>
                  </li>
                </ul>
              </li>

              <li
                class="uni-main-menu__subnav-item uni-main-menu__subnav-item--mobile has-submenu"
              >
                <button
                  class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--mobile"
                >
                  Connect &amp; Communicate
                  <figure class="expand-caret">
                    <svg class="icon" role="presentation">
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                      ></use>
                    </svg>
                  </figure>
                </button>

                <ul
                  class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--mobile"
                >
                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/classroom/"
                      data-navigation="product-news"
                    >
                      Classroom
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/photos/"
                      data-navigation="product-news"
                    >
                      Photos
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/registry/"
                      data-navigation="product-news"
                    >
                      Registry
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/translate/"
                      data-navigation="product-news"
                    >
                      Translate
                    </a>
                  </li>
                </ul>
              </li>

              <li
                class="uni-main-menu__subnav-item uni-main-menu__subnav-item--mobile has-submenu"
              >
                <button
                  class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--mobile"
                >
                  In the Cloud
                  <figure class="expand-caret">
                    <svg class="icon" role="presentation">
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                      ></use>
                    </svg>
                  </figure>
                </button>

                <ul
                  class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--mobile"
                >
                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/workspace/"
                      data-navigation="product-news"
                    >
                      Google Workspace
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="https://cloud.google.com/blog/"
                      data-navigation="product-news"
                      rel="noopener"
                      target="_blank"
                    >
                      More on the Cloud Blog

                      <svg
                        class="icon h-c-icon uni-main-menu__subnav-sublist-external-icon"
                        role="presentation"
                        viewBox="0 0 18 18"
                      >
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-arrow-forward"
                        ></use>
                      </svg>
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/products/google-cloud/"
                      data-navigation="product-news"
                    >
                      Google Cloud
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      href="/products/cloud/"
                      class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                      data-navigation="In the Cloud"
                    >
                      See all
                    </a>
                  </li>
                </ul>
              </li>
            </ul>

            <a
              href="/products/"
              class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--landing uni-main-menu__subnav-item-name--mobile"
            >
              See all product updates
            </a>
          </div>
        </li>

        <li
          data-has-subnav="yes"
          class="uni-main-menu__item uni-main-menu__item--mobile inactive has-submenu"
        >
          <button
            class="uni-main-menu__item-label has-submenu"
            tabindex="0"
            xt-marked="ok"
          >
            <span class="xt-wrap">
              <xt-mark w="company" style="color: #e89b64 !important"
                >Company</xt-mark
              >
              news
            </span>
            <figure class="expand-caret">
              <svg class="icon" role="presentation">
                <use
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                ></use>
              </svg>
            </figure>
          </button>

          <div class="uni-main-menu__subnav uni-main-menu__subnav--mobile">
            <button
              class="uni-main-menu__subnav-title uni-main-menu__subnav-title--mobile"
              xt-marked="ok"
            >
              <figure class="expand-caret">
                <svg class="icon" role="presentation">
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                  ></use>
                </svg>
              </figure>
              <xt-mark w="company" style="color: #e89b64 !important"
                >Company</xt-mark
              >
              news
            </button>
            <ul class="uni-main-menu__subnav-list">
              <li
                class="uni-main-menu__subnav-item uni-main-menu__subnav-item--mobile has-submenu"
              >
                <button
                  class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--mobile"
                  xt-marked="ok"
                >
                  <span class="xt-wrap">
                    Outreach &amp;
                    <xt-mark w="initiative" style="color: #e89b64 !important"
                      >initiatives</xt-mark
                    >
                  </span>
                  <figure class="expand-caret">
                    <svg class="icon" role="presentation">
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                      ></use>
                    </svg>
                  </figure>
                </button>

                <ul
                  class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--mobile"
                >
                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/outreach-initiatives/arts-culture/"
                      data-navigation="company-news"
                    >
                      Arts &amp; Culture
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/outreach-initiatives/education/"
                      data-navigation="company-news"
                    >
                      Education
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/outreach-initiatives/entrepreneurs/"
                      data-navigation="company-news"
                    >
                      Entrepreneurs
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/outreach-initiatives/public-policy/"
                      data-navigation="company-news"
                      xt-marked="ok"
                    >
                      Public&nbsp;<xt-mark
                        w="policy"
                        style="color: #e89b64 !important"
                        >Policy</xt-mark
                      >&nbsp;
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/outreach-initiatives/sustainability/"
                      data-navigation="company-news"
                    >
                      Sustainability
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      href="/outreach-initiatives/"
                      class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                      data-navigation="Outreach &amp; initiatives"
                    >
                      See all
                    </a>
                  </li>
                </ul>
              </li>

              <li
                class="uni-main-menu__subnav-item uni-main-menu__subnav-item--mobile has-submenu"
              >
                <button
                  class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--mobile"
                >
                  Technology
                  <figure class="expand-caret">
                    <svg class="icon" role="presentation">
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                      ></use>
                    </svg>
                  </figure>
                </button>

                <ul
                  class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--mobile"
                >
                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/technology/ai/"
                      data-navigation="company-news"
                    >
                      AI
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/technology/developers/"
                      data-navigation="company-news"
                    >
                      Developers
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/technology/health/"
                      data-navigation="company-news"
                    >
                      Health
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/technology/google-deepmind/"
                      data-navigation="company-news"
                    >
                      Google DeepMind
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/technology/google-labs/"
                      data-navigation="company-news"
                    >
                      Google Labs
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/technology/safety-security/"
                      data-navigation="company-news"
                    >
                      Safety and security
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      href="/technology/"
                      class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                      data-navigation="Technology"
                    >
                      See all
                    </a>
                  </li>
                </ul>
              </li>

              <li
                class="uni-main-menu__subnav-item uni-main-menu__subnav-item--mobile has-submenu"
              >
                <button
                  class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--mobile"
                >
                  Inside Google
                  <figure class="expand-caret">
                    <svg class="icon" role="presentation">
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                      ></use>
                    </svg>
                  </figure>
                </button>

                <ul
                  class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--mobile"
                >
                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/inside-google/infrastructure/"
                      data-navigation="company-news"
                      xt-marked="ok"
                    >
                      Data centers and&nbsp;<xt-mark
                        w="infrastructure"
                        style="color: #e89b64 !important"
                        >infrastructure</xt-mark
                      >&nbsp;
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/inside-google/doodles/"
                      data-navigation="company-news"
                    >
                      Doodles
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/inside-google/googlers/"
                      data-navigation="company-news"
                    >
                      Googlers
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/inside-google/life-at-google/"
                      data-navigation="company-news"
                    >
                      Life at Google
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      href="/inside-google/"
                      class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                      data-navigation="Inside Google"
                    >
                      See all
                    </a>
                  </li>
                </ul>
              </li>

              <li
                class="uni-main-menu__subnav-item uni-main-menu__subnav-item--mobile has-submenu"
              >
                <button
                  class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--mobile"
                >
                  Around the globe
                  <figure class="expand-caret">
                    <svg class="icon" role="presentation">
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                      ></use>
                    </svg>
                  </figure>
                </button>

                <ul
                  class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--mobile"
                >
                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/around-the-globe/google-asia/"
                      data-navigation="company-news"
                      xt-marked="ok"
                    >
                      Google in&nbsp;<xt-mark
                        w="asia"
                        style="color: #e89b64 !important"
                        >Asia</xt-mark
                      >&nbsp;
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/around-the-globe/google-europe/"
                      data-navigation="company-news"
                    >
                      Google in Europe
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/around-the-globe/google-latin-america/"
                      data-navigation="company-news"
                    >
                      Google in Latin America
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      href="/around-the-globe/"
                      class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                      data-navigation="Around the globe"
                    >
                      See all
                    </a>
                  </li>
                </ul>
              </li>

              <li
                class="uni-main-menu__subnav-item uni-main-menu__subnav-item--mobile has-submenu"
              >
                <button
                  class="uni-main-menu__subnav-item-name uni-main-menu__subnav-item-name--mobile"
                >
                  Authors
                  <figure class="expand-caret">
                    <svg class="icon" role="presentation">
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-expand"
                      ></use>
                    </svg>
                  </figure>
                </button>

                <ul
                  class="uni-main-menu__subnav-sublist uni-main-menu__subnav-sublist--mobile"
                >
                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/authors/sundar-pichai/"
                      data-navigation="company-news"
                    >
                      Sundar Pichai, CEO
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/authors/demis-hassabis/"
                      data-navigation="company-news"
                    >
                      Demis Hassabis, CEO and Co-Founder, Google DeepMind
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/authors/kent-walker/"
                      data-navigation="company-news"
                    >
                      Kent Walker, SVP
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/authors/james-manyika/"
                      data-navigation="company-news"
                    >
                      James Manyika, SVP
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      class="uni-main-menu__subnav-sublist-link"
                      href="/authors/ruth-porat/"
                      data-navigation="company-news"
                    >
                      Ruth Porat, President &amp; Chief Investment Officer
                    </a>
                  </li>

                  <li class="uni-main-menu__subnav-sublist-item">
                    <a
                      href="/authors/"
                      class="uni-main-menu__subnav-sublist-link uni-main-menu__subnav-sublist-link--all"
                      data-navigation="Authors"
                    >
                      See all
                    </a>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </li>

        <li
          data-has-subnav=""
          class="uni-main-menu__item uni-main-menu__item--mobile inactive"
        >
          <a
            href="/feed"
            class="uni-main-menu__item-label"
            role="button"
            tabindex="0"
          >
            Feed
          </a>
        </li>
      </ul>

      <ul class="uni-header__kebab-menu uni-header__kebab-menu--mobile">
        <li class="uni-header__kebab-menu-item"></li>
        <li class="uni-header__kebab-menu-item">
          <a href="/press/" title="Press corner">Press corner</a>
        </li>

        <li class="uni-header__kebab-menu-item">
          <a href="/rss/" title="RSS feed"> RSS feed </a>
        </li>
      </ul>
    </div>

    <div class="uni-header__newsletter uni-header__newsletter--mobile">
      <a
        class="uni-header__newsletter--cta uni-header__newsletter--cta--mobile kw-button kw-button--high-emphasis"
        href="/newsletter-subscribe/"
        aria-label="Newsletter subscribe"
        data-content-type="blogv2 | article page"
      >
        Subscribe</a
      >
    </div>
  </nav>

  <div class="overlay-body-menu--mobile"></div>

  <div class="loader-mask">
    <div class="loader-mask__loader"></div>
  </div>

  <main id="jump-content" class="site-content" tabindex="-1">
    <article class="uni-article-wrapper">
      <section class="article-hero">
        <div class="article-hero__container">
          <div class="article-hero__breadcrumb">
            <div
              class="breadcrumb__container"
              data-component="uni-breadcrumb"
              data-component-initialized="true"
            >
              <button class="breadcrumb__prev-btn hide" aria-label="Previous">
                <svg role="presentation" aria-hidden="true">
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#uni-icon-chevron-right"
                  ></use>
                </svg>
              </button>
              <div
                class="breadcrumb__focusable breadcrumb__focusable--start"
              ></div>
              <nav
                aria-label="Breadcrumb"
                class="breadcrumb breadcrumb__scrollable"
              >
                <span class="breadcrumb__label">Breadcrumb</span>
                <ol
                  data-analytics-module='{
      "module_name": "breadcrumbs",
      "section_header": "Our favorite Chrome extensions of 2023"
    }'
                >
                  <li>
                    <a
                      href="https://blog.google/"
                      class="breadcrumb__button breadcrumb__button--homepage"
                      title="The Keyword"
                      aria-label="The Keyword"
                      data-ga4-analytics-landing-lead='{
  "event": "landing_page_lead",
  "link_text": "The Keyword"
}'
                    >
                      <svg role="presentation" aria-hidden="true">
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#uni-icon-homepage"
                        ></use>
                      </svg>
                    </a>
                  </li>

                  <li>
                    <svg
                      class="breadcrumb__chevron"
                      role="presentation"
                      aria-hidden="true"
                    >
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#uni-icon-chevron-right"
                      ></use>
                    </svg>

                    <a
                      href="https://blog.google/products/"
                      class="breadcrumb__button"
                      data-ga4-analytics-landing-lead='{
  "event": "landing_page_lead",
  "link_text": "Products"
}'
                    >
                      Products
                    </a>
                  </li>

                  <li>
                    <svg
                      class="breadcrumb__chevron"
                      role="presentation"
                      aria-hidden="true"
                    >
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#uni-icon-chevron-right"
                      ></use>
                    </svg>

                    <a
                      href="https://blog.google/products/chrome/"
                      class="breadcrumb__button"
                      data-ga4-analytics-landing-lead='{
  "event": "landing_page_lead",
  "link_text": "Google Chrome"
}'
                    >
                      Google Chrome
                    </a>
                  </li>
                </ol>
              </nav>
              <div
                class="breadcrumb__focusable breadcrumb__focusable--end"
              ></div>
              <button class="breadcrumb__next-btn hide" aria-label="Next">
                <svg role="presentation" aria-hidden="true">
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#uni-icon-chevron-right"
                  ></use>
                </svg>
              </button>
            </div>
          </div>

          <h1 class="article-hero__h1">
            Our favorite Chrome extensions of 2023<font
              class="notranslate immersive-translate-target-wrapper"
              lang="zh-CN"
              style="display: unset"
              ><br /><font
                class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-block-wrapper-theme-weakening immersive-translate-target-translation-block-wrapper"
                data-immersive-translate-translation-element-mark="1"
                ><font
                  class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                  data-immersive-translate-translation-element-mark="1"
                  >2023 年我们最喜欢的 Chrome 扩展程序</font
                ></font
              ></font
            >
          </h1>
        </div>
      </section>

      <script type="application/json" data-catalog-id="article">
        {
          "reading_time": "[[read\u002Dtime]] min read"
        }
      </script>

      <div
        class="article-meta__author-container"
        data-analytics-module='{
    "module_name": "Hero Menu",
    "section_header": "Our favorite Chrome extensions of 2023"
  }'
      >
        <div class="article-meta__author-wrapper">
          <div class="article-meta__abstract">
            <div class="article-meta__abstract-aside">
              <div class="article-meta__published-wrapper">
                <p class="article-meta__published-at uni-body--small">
                  Dec 19, 2023<font
                    class="notranslate immersive-translate-target-wrapper"
                    lang="zh-CN"
                    style="display: unset"
                    ><font
                      class="notranslate"
                      data-immersive-translate-translation-element-mark="1"
                      >&nbsp;&nbsp;</font
                    ><font
                      class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-inline-wrapper-theme-weakening immersive-translate-target-translation-inline-wrapper"
                      data-immersive-translate-translation-element-mark="1"
                      ><font
                        class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                        data-immersive-translate-translation-element-mark="1"
                        >2023 年 12 月 19 日</font
                      ></font
                    ></font
                  >
                </p>

                <span aria-hidden="true" class="article-meta__separator"
                  >·</span
                >

                <uni-reading-time
                  ><!---->
                  <p class="article-meta__read-time uni-body--small">
                    <!--?lit$806344877$-->3 min read
                  </p></uni-reading-time
                >
              </div>

              <div
                class="uni-social-share uni-social-share--mobile"
                data-component="uni-social-share-dropdown"
                data-component-initialized="true"
              >
                <a
                  class="uni-social-share__trigger"
                  role="button"
                  tabindex="0"
                  aria-label="Share"
                  aria-expanded="false"
                  rel="noopener"
                  target="_blank"
                >
                  <svg
                    class="h-c-icon h-c-icon--color-text"
                    aria-hidden="true"
                    title="Share"
                  >
                    <use
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-share"
                    ></use>
                  </svg>

                  <div class="uni-social-share__button">Share</div>
                </a>
                <div
                  class="uni-social-share__dialog uni-social-share__content"
                  aria-labelledby="social-share-icon"
                >
                  <a
                    aria-label="Share on Twitter"
                    class="article-share__link-text uni-click-tracker"
                    href="https://twitter.com/intent/tweet?text=Our%20favorite%20Chrome%20extensions%20of%202023%20%40google&amp;url=https://blog.google/products/chrome/favorite-google-chrome-extensions-2023/"
                    target="_blank"
                    data-ga4-method="twitter"
                    rel="noopener"
                  >
                    <svg
                      class="h-c-icon h-c-icon--social h-c-icon--30px"
                      aria-hidden="true"
                      viewBox="0 0 30 30"
                    >
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-twitter"
                      ></use>
                    </svg>

                    <div class="article-share__title">Twitter</div>
                  </a>

                  <a
                    aria-label="Share on Facebook"
                    class="article-share__link-text uni-click-tracker"
                    href="https://www.facebook.com/sharer/sharer.php?caption=Our%20favorite%20Chrome%20extensions%20of%202023&amp;u=https://blog.google/products/chrome/favorite-google-chrome-extensions-2023/"
                    target="_blank"
                    data-ga4-method="facebook"
                    rel="noopener"
                  >
                    <svg
                      class="h-c-icon h-c-icon--social h-c-icon--30px"
                      aria-hidden="true"
                      viewBox="0 0 30 30"
                    >
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-facebook"
                      ></use>
                    </svg>

                    <div class="article-share__title">Facebook</div>
                  </a>

                  <a
                    aria-label="Share on LinkedIn"
                    class="article-share__link-text uni-click-tracker"
                    href="https://www.linkedin.com/shareArticle?mini=true&amp;url=https://blog.google/products/chrome/favorite-google-chrome-extensions-2023/&amp;title=Our%20favorite%20Chrome%20extensions%20of%202023"
                    target="_blank"
                    data-ga4-method="linkedin"
                    rel="noopener"
                  >
                    <svg
                      class="h-c-icon h-c-icon--social h-c-icon--30px"
                      aria-hidden="true"
                      viewBox="0 0 30 30"
                    >
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-linkedin"
                      ></use>
                    </svg>

                    <div class="article-share__title">LinkedIn</div>
                  </a>

                  <a
                    aria-label="Share with Email"
                    class="article-share__link-text uni-click-tracker article-share__email"
                    href="mailto:?subject=Our%20favorite%20Chrome%20extensions%20of%202023&amp;body=Check out this article on the Keyword:%0A%0AOur%20favorite%20Chrome%20extensions%20of%202023%0A%0AHere are some of our favorite Chrome extensions from this year to help you get work done and have fun.%0A%0Ahttps://blog.google/products/chrome/favorite-google-chrome-extensions-2023/"
                    target="_blank"
                    data-ga4-method="email"
                  >
                    <svg
                      class="h-c-icon h-c-icon--social h-c-icon--30px"
                      aria-hidden="true"
                      viewBox="0 0 30 30"
                    >
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-mail"
                      ></use>
                    </svg>

                    <div class="article-share__title">Mail</div>
                  </a>

                  <div
                    class="copy-link uni-copy-share uni-click-tracker"
                    data-component="uni-copy-popup-component"
                    data-ga4-analytics-share-copy-link=""
                    data-ga4-method="Copy link"
                    data-component-initialized="true"
                  >
                    <button
                      class="copy-link__trigger copy-link__trigger-text"
                      data-ga4-method="Copy link"
                      title="Copy link"
                    >
                      <svg
                        class="h-c-icon h-c-icon--color-text"
                        role="presentation"
                        title="Copy link"
                      >
                        <use
                          xmlns:xlink="http://www.w3.org/1999/xlink"
                          href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-link"
                        ></use>
                      </svg>

                      <div class="copy-link__title">Copy link</div>
                    </button>
                    <div
                      class="copy-link__dialog copy-link__content"
                      uni-options='{"copyTextButton": "COPIED TO CLIPBOARD"}'
                      aria-hidden="true"
                      tabindex="-1"
                      style="display: none"
                    >
                      <input
                        class="h-c-copy copy-link__url"
                        value="https://blog.google/products/chrome/favorite-google-chrome-extensions-2023/"
                        id="copy-link"
                        readonly="readonly"
                        type="text"
                      />
                      <div class="copy-link__copy-message" role="status"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <p class="article-meta__abstract-text uni-body--large">
              We’re rounding up some of our favorite Chrome extensions of the
              year that can help with task management, online shopping and
              accessibility.
              <font
                class="notranslate immersive-translate-target-wrapper"
                lang="zh-CN"
                style="display: unset"
                ><br /><font
                  class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-block-wrapper-theme-weakening immersive-translate-target-translation-block-wrapper"
                  data-immersive-translate-translation-element-mark="1"
                  ><font
                    class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                    data-immersive-translate-translation-element-mark="1"
                    >我们整理了一些今年的 Chrome
                    扩展程序，这些扩展程序可以帮助你进行任务管理、在线购物和提高
                    accessibility。</font
                  ></font
                ></font
              >
            </p>
          </div>
        </div>

        <div class="article-meta__container">
          <div class="article-meta__content">
            <div class="article-meta__author">
              <div
                class="article-meta__author-noimage"
                data-component="uni-monogram"
                data-author="Jamie Anderson"
                data-component-initialized="true"
                style="background-color: rgb(190, 33, 78)"
              >
                J
              </div>

              <div class="article-meta__author-info">
                <div class="article-meta__author-name">
                  Jamie Anderson<font
                    class="notranslate immersive-translate-target-wrapper"
                    lang="zh-CN"
                    style="display: unset"
                    ><font
                      class="notranslate"
                      data-immersive-translate-translation-element-mark="1"
                      >&nbsp;&nbsp;</font
                    ><font
                      class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-inline-wrapper-theme-weakening immersive-translate-target-translation-inline-wrapper"
                      data-immersive-translate-translation-element-mark="1"
                      ><font
                        class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                        data-immersive-translate-translation-element-mark="1"
                        >杰米·安德森</font
                      ></font
                    ></font
                  >
                </div>

                <div class="article-meta__author-title">
                  Merchandising Manager, Chrome Web Store
                  <font
                    class="notranslate immersive-translate-target-wrapper"
                    lang="zh-CN"
                    style="display: unset"
                    ><br /><font
                      class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-block-wrapper-theme-weakening immersive-translate-target-translation-block-wrapper"
                      data-immersive-translate-translation-element-mark="1"
                      ><font
                        class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                        data-immersive-translate-translation-element-mark="1"
                        >Chrome 网上应用店商品主管</font
                      ></font
                    ></font
                  >
                </div>
              </div>
            </div>
          </div>

          <div
            class="article-share__wrapper"
            data-analytics-module='{
        "module_name": "Hero Share",
        "section_header": "Our favorite Chrome extensions of 2023"
      }'
          >
            <div
              class="uni-social-share uni-social-share--desktop"
              data-component="uni-social-share-dropdown"
              data-component-initialized="true"
            >
              <a
                class="uni-social-share__trigger"
                role="button"
                tabindex="0"
                aria-label="Share"
                aria-expanded="false"
                rel="noopener"
                target="_blank"
              >
                <svg
                  class="h-c-icon h-c-icon--color-text"
                  aria-hidden="true"
                  title="Share"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-share"
                  ></use>
                </svg>

                <div class="uni-social-share__button">Share</div>
              </a>
              <div
                class="uni-social-share__dialog uni-social-share__content"
                aria-labelledby="social-share-icon"
              >
                <a
                  aria-label="Share on Twitter"
                  class="article-share__link-text uni-click-tracker"
                  href="https://twitter.com/intent/tweet?text=Our%20favorite%20Chrome%20extensions%20of%202023%20%40google&amp;url=https://blog.google/products/chrome/favorite-google-chrome-extensions-2023/"
                  target="_blank"
                  data-ga4-method="twitter"
                  rel="noopener"
                >
                  <svg
                    class="h-c-icon h-c-icon--social h-c-icon--30px"
                    aria-hidden="true"
                    viewBox="0 0 30 30"
                  >
                    <use
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-twitter"
                    ></use>
                  </svg>

                  <div class="article-share__title">Twitter</div>
                </a>

                <a
                  aria-label="Share on Facebook"
                  class="article-share__link-text uni-click-tracker"
                  href="https://www.facebook.com/sharer/sharer.php?caption=Our%20favorite%20Chrome%20extensions%20of%202023&amp;u=https://blog.google/products/chrome/favorite-google-chrome-extensions-2023/"
                  target="_blank"
                  data-ga4-method="facebook"
                  rel="noopener"
                >
                  <svg
                    class="h-c-icon h-c-icon--social h-c-icon--30px"
                    aria-hidden="true"
                    viewBox="0 0 30 30"
                  >
                    <use
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-facebook"
                    ></use>
                  </svg>

                  <div class="article-share__title">Facebook</div>
                </a>

                <a
                  aria-label="Share on LinkedIn"
                  class="article-share__link-text uni-click-tracker"
                  href="https://www.linkedin.com/shareArticle?mini=true&amp;url=https://blog.google/products/chrome/favorite-google-chrome-extensions-2023/&amp;title=Our%20favorite%20Chrome%20extensions%20of%202023"
                  target="_blank"
                  data-ga4-method="linkedin"
                  rel="noopener"
                >
                  <svg
                    class="h-c-icon h-c-icon--social h-c-icon--30px"
                    aria-hidden="true"
                    viewBox="0 0 30 30"
                  >
                    <use
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-linkedin"
                    ></use>
                  </svg>

                  <div class="article-share__title">LinkedIn</div>
                </a>

                <a
                  aria-label="Share with Email"
                  class="article-share__link-text uni-click-tracker article-share__email"
                  href="mailto:?subject=Our%20favorite%20Chrome%20extensions%20of%202023&amp;body=Check out this article on the Keyword:%0A%0AOur%20favorite%20Chrome%20extensions%20of%202023%0A%0AHere are some of our favorite Chrome extensions from this year to help you get work done and have fun.%0A%0Ahttps://blog.google/products/chrome/favorite-google-chrome-extensions-2023/"
                  target="_blank"
                  data-ga4-method="email"
                >
                  <svg
                    class="h-c-icon h-c-icon--social h-c-icon--30px"
                    aria-hidden="true"
                    viewBox="0 0 30 30"
                  >
                    <use
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-mail"
                    ></use>
                  </svg>

                  <div class="article-share__title">Mail</div>
                </a>

                <div
                  class="copy-link uni-copy-share uni-click-tracker"
                  data-component="uni-copy-popup-component"
                  data-ga4-analytics-share-copy-link=""
                  data-ga4-method="Copy link"
                  data-component-initialized="true"
                >
                  <button
                    class="copy-link__trigger copy-link__trigger-text"
                    data-ga4-method="Copy link"
                    title="Copy link"
                  >
                    <svg
                      class="h-c-icon h-c-icon--color-text"
                      role="presentation"
                      title="Copy link"
                    >
                      <use
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-link"
                      ></use>
                    </svg>

                    <div class="copy-link__title">Copy link</div>
                  </button>
                  <div
                    class="copy-link__dialog copy-link__content"
                    uni-options='{"copyTextButton": "COPIED TO CLIPBOARD"}'
                    aria-hidden="true"
                    tabindex="-1"
                    style="display: none"
                  >
                    <input
                      class="h-c-copy copy-link__url"
                      value="https://blog.google/products/chrome/favorite-google-chrome-extensions-2023/"
                      id="copy-link"
                      readonly="readonly"
                      type="text"
                    />
                    <div class="copy-link__copy-message" role="status"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="article-image-hero">
        <div class="article-image-hero__container">
          <figure class="article-image--full-aspect article-module">
            <div class="aspect-ratio-image" style="--height-percentage: 41.7%">
              <div class="aspect-ratio-image__container">
                <img
                  alt="Celebratory image with a gold trophy and Chrome logo with the year “2023”"
                  class="aspect-ratio-image__image"
                  data-component="uni-progressive-image"
                  fetchpriority="high"
                  height="150px"
                  src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Favorite--Extensions-2023_.width-200.format-webp.webp"
                  width="360px"
                  data-component-initialized="true"
                  srcset="
                    https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Favorite--Extensions-2023_.width-800.format-webp.webp  800w,
                    https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Favorite--Extensions-2023.width-1200.format-webp.webp 1200w,
                    https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Favorite--Extensions-2023.width-1600.format-webp.webp 1600w,
                    https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Favorite--Extensions-2023.width-2200.format-webp.webp 2200w
                  "
                  sizes="(max-width: 1023px) 100vw,(min-width: 1024px and max-width: 1259) 80vw, 1046px"
                />
              </div>
            </div>
          </figure>
        </div>
      </div>

      <script>
        class ProgressiveImage {
          EVENTS = {
            TRANSITION_END: "transitionend",
          };

          CSS_CLASSES = {
            BLUR: "uni-progressive-image--blur",
            NO_BLUR: "uni-progressive-image--no-blur",
          };

          init(el) {
            this.el = el;
            this._events();
            this._upgradeImage();
          }

          _upgradeImage() {
            // For gif format images we don't include data-srcset and data-sizes
            // We can safely remove the blur filter.
            if (!this.el.dataset.srcset || !this.el.dataset.sizes) {
              this.el.classList.add(this.CSS_CLASSES.NO_BLUR);

              return;
            }

            this.el.setAttribute("srcset", this.el.dataset.srcset);
            this.el.setAttribute("sizes", this.el.dataset.sizes);
            requestAnimationFrame(() => {
              this.el.classList.add(this.CSS_CLASSES.NO_BLUR);
            });
          }

          _events() {
            // Once the transition completes is safe to clean some attributes
            this.el.addEventListener(this.EVENTS.TRANSITION_END, () => {
              this.el.classList.remove(
                this.CSS_CLASSES.BLUR,
                this.CSS_CLASSES.NO_BLUR
              );
              this.el.removeAttribute("data-srcset");
              this.el.removeAttribute("data-sizes");
            });
          }
        }

        document.addEventListener("DOMContentLoaded", () => {
          const images = document.querySelectorAll(
            '[data-component="uni-progressive-image"]'
          );
          images.forEach((el) => {
            el.setAttribute("data-component-initialized", true);
            new ProgressiveImage().init(el);
          });
        });
      </script>

      <section class="uni-container article-container">
        <div class="uni-wrapper article-container__wrapper">
          <div
            class="uni-content uni-blog-article-container article-container__content"
            data-reading-time="true"
            data-component="uni-article-body"
            data-component-initialized="true"
          >
            <uni-article-speakable
              style="height: 55px; margin-bottom: 36px; display: block"
              page-title="Our favorite Chrome extensions of 2023"
              listen-to-article="Listen to article"
              data-date-modified="2023-12-19T16:57:58.071706+00:00"
              data-tracking-ids="G-HGNBTNCHCQ,G-6NKTLKV14N"
              data-voice-list="en.ioh-pngnat:Cyan,en.usb-pngnat:Lime"
              data-script-src="https://www.gstatic.com/readaloud/player/web/api/js/api.js"
              ><!---->
              <div id="uni-article-speakable">
                <!--?lit$806344877$--><google-read-aloud-player
                  data-progress-bar-style="half-wave"
                  data-api-key="AIzaSyBLT6VkYe-x7sWLZI2Ep26-fNkBKgND-Ac"
                  data-article-style="style9"
                  data-layout-style="style1"
                  data-highlight-mode="word-over-paragraph"
                  data-highlight-text-color="#000000"
                  data-highlight-word-background="#8AB4F8"
                  data-highlight-paragraph-background="#D2E3FC"
                  data-background="linear-gradient(180deg, #F1F3F4 0%, #F8F9FA 100%)"
                  data-foreground-color="#202124"
                  data-font="600 16px Google Sans, sans-serif"
                  data-box-shadow="0px 1px 3px 1px rgba(60, 64, 67, 0.15)"
                  style="
                    min-height: 55px;
                    display: block;
                    max-width: 726px;
                    margin-left: auto;
                    margin-right: auto;
                    margin-bottom: 36px;
                  "
                  data-analytics-module='{"event":"module_impression","module_name":"ai_audio","section_header":"Our favorite Chrome extensions of 2023"}'
                  data-page-title="Our favorite Chrome extensions of 2023"
                  data-call-to-action-text="Listen to article"
                  data-date-modified="2023-12-19T16:57:58.071706+00:00"
                  data-tracking-ids="G-HGNBTNCHCQ,G-6NKTLKV14N"
                  data-voice-list="en.ioh-pngnat:Cyan,en.usb-pngnat:Lime"
                ></google-read-aloud-player></div
            ></uni-article-speakable>

            <!--article text-->

            <div
              class="module--text module--text__article"
              role="presentation"
              data-analytics-module='{
           "module_name": "Paragraph",
           "section_header": "Our favorite Chrome extensions of 2023"
         }'
            >
              <div
                class="uni-paragraph article-paragraph"
                data-component="uni-article-paragraph"
                data-component-initialized="true"
              >
                <div class="rich-text">
                  <p data-block-key="cjr6a" class="drop-cap">
                    Every year, developers build unique and creative Chrome
                    extensions to help with everything from productivity to
                    accessibility on the web. And 2023 was certainly no
                    exception. From saving money while online shopping to
                    quickly translating websites, here are some of our favorite
                    extensions from the year.<font
                      class="notranslate immersive-translate-target-wrapper"
                      lang="zh-CN"
                      style="display: unset"
                      ><br /><font
                        class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-block-wrapper-theme-weakening immersive-translate-target-translation-block-wrapper"
                        data-immersive-translate-translation-element-mark="1"
                        ><font
                          class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                          data-immersive-translate-translation-element-mark="1"
                          >每年，开发者都会构建独特且富有创意的 Chrome
                          扩展程序，以帮助用户在网页上提高生产力或实现无障碍访问。2023
                          年也不例外。从在线购物省钱到快速翻译网站，以下是我们在这一年中的一些最爱的扩展程序。</font
                        ></font
                      ></font
                    >
                  </p>
                  <h2 data-block-key="8m5jf">
                    Get tasks done faster<font
                      class="notranslate immersive-translate-target-wrapper"
                      lang="zh-CN"
                      style="display: unset"
                      ><font
                        class="notranslate"
                        data-immersive-translate-translation-element-mark="1"
                        >&nbsp;&nbsp;</font
                      ><font
                        class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-inline-wrapper-theme-weakening immersive-translate-target-translation-inline-wrapper"
                        data-immersive-translate-translation-element-mark="1"
                        ><font
                          class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                          data-immersive-translate-translation-element-mark="1"
                          >提高工作效率，更快完成任务</font
                        ></font
                      ></font
                    >
                  </h2>
                  <p data-block-key="42h08" xt-marked="ok">
                    As your days get busier this time of year, new AI-powered
                    extensions can help you get more done in less time.
                    <a
                      href="https://chromewebstore.google.com/detail/scribe-ai-documentation-s/okfkdaglfjjjfefdcppliegebpoegaii"
                      rel="noopener"
                      target="_blank"
                      >Scribe</a
                    >
                    uses AI to document your workflows and create step-by-step
                    guides to easily train colleagues,
                    <a
                      href="https://chromewebstore.google.com/detail/deepl-translate-reading-w/cofdbpoegempjloogbagkncekinflcnj"
                      rel="noopener"
                      target="_blank"
                      >DeepL Translate</a
                    >
                    instantly translates web pages to help you quickly overcome
                    language
                    <xt-mark w="barrier" style="color: #e89b64 !important"
                      >barriers</xt-mark
                    >, and
                    <a
                      href="https://chromewebstore.google.com/detail/quillbot-ai-writing-and-g/iidnbdjijdkbmajdffnidomddglmieko"
                      rel="noopener"
                      target="_blank"
                      >QuillBot</a
                    >
                    allows you to compose and respond to emails in just a few
                    clicks.<font
                      class="notranslate immersive-translate-target-wrapper"
                      lang="zh-CN"
                      style="display: unset"
                      ><br /><font
                        class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-block-wrapper-theme-weakening immersive-translate-target-translation-block-wrapper"
                        data-immersive-translate-translation-element-mark="1"
                        ><font
                          class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                          data-immersive-translate-translation-element-mark="1"
                          >随着年底日程变得繁忙，新的 AI
                          驱动扩展程序可以帮助你更高效地完成工作。Scribe 利用 AI
                          记录你的工作流程，并创建详细的步骤指南，方便培训同事。DeepL
                          Translate
                          可以即时翻译网页，帮助你迅速克服语言障碍。QuillBot
                          则允许你只需几下点击就能撰写和回复邮件。</font
                        ></font
                      ></font
                    >
                  </p>
                </div>
              </div>
            </div>

            <script
              type="application/json"
              data-catalog-id="reduce-motion-player"
            >
              {
                "play_video": "Play video",
                "pause_video": "Pause video"
              }
            </script>

            <uni-image-full-width
              alignment="large"
              alt-text="Side-by-side images of Scribes’ user interface that includes an explanation of how the extension works to help you build a workflow for work projects."
              external-image=""
              or-mp4-video-title=""
              or-mp4-video-url=""
              section-header="Our favorite Chrome extensions of 2023"
              custom-class="image-full-width--constrained-width uni-component-spacing"
            >
              <!---->
              <div
                class="image-full-width image-full-width--large image-full-width--constrained-width uni-component-spacing"
                data-analytics-module='{"module_name":"Inline Images","section_header":"Our favorite Chrome extensions of 2023"}'
              >
                <!--?lit$806344877$--><!--?lit$806344877$-->
                <div class="image-full-width__media-asset">
                  <!--?lit$806344877$-->
                  <div slot="image-slot" style="display: block">
                    <img
                      alt="Side-by-side images of Scribes’ user interface that includes an explanation of how the extension works to help you build a workflow for work projects."
                      src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Scribe_Extension.width-1000.format-webp.webp"
                      loading="lazy"
                      data-loading='{
            "mobile": "https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Scribe_Extension.width-500.format-webp.webp",
            "desktop": "https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Scribe_Extension.width-1000.format-webp.webp"
          }'
                      class="lazy-image--no-blur"
                    />
                  </div>
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                </div>
                <!--?-->
                <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
              </div></uni-image-full-width
            >

            <div
              class="module--text module--text__article"
              role="presentation"
              data-analytics-module='{
           "module_name": "Paragraph",
           "section_header": "Our favorite Chrome extensions of 2023"
         }'
            >
              <div
                class="uni-paragraph article-paragraph"
                data-component="uni-article-paragraph"
                data-component-initialized="true"
              >
                <div class="rich-text">
                  <p data-block-key="cjr6a">
                    Meanwhile,
                    <a
                      href="https://chromewebstore.google.com/detail/sider-chatgpt-sidebar-+-v/difoiogjjojoaoomphldepapgpbgkhkb"
                      rel="noopener"
                      target="_blank"
                      >Sider</a
                    >, an in-browser sidebar, lets you use generative AI tools
                    like ChatGPT, Claude and Bard without having to open another
                    tab. And if you’re looking for a career change, check out
                    <a
                      href="https://chromewebstore.google.com/detail/teal-free-job-search-cont/opafjjlpbiaicbbgifbejoochmmeikep"
                      rel="noopener"
                      target="_blank"
                      >Teal</a
                    >, which lets you bookmark jobs from popular job boards and
                    easily track and manage all your job applications in one
                    place.<font
                      class="notranslate immersive-translate-target-wrapper"
                      lang="zh-CN"
                      style="display: unset"
                      ><br /><font
                        class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-block-wrapper-theme-weakening immersive-translate-target-translation-block-wrapper"
                        data-immersive-translate-translation-element-mark="1"
                        ><font
                          class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                          data-immersive-translate-translation-element-mark="1"
                          >与此同时，Sider
                          是一款内置浏览器的侧边栏，让你无需打开新标签页就能使用
                          ChatGPT、Claude 和 Bard 等生成式 AI
                          工具。如果你正在寻找转行的机会，不妨试试
                          Teal，它允许你从各大招聘网站收藏工作，并在一个地方轻松跟踪和管理所有求职申请。</font
                        ></font
                      ></font
                    >
                  </p>
                  <h2 data-block-key="o505">
                    Easily share meeting notes<font
                      class="notranslate immersive-translate-target-wrapper"
                      lang="zh-CN"
                      style="display: unset"
                      ><br /><font
                        class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-block-wrapper-theme-weakening immersive-translate-target-translation-block-wrapper"
                        data-immersive-translate-translation-element-mark="1"
                        ><font
                          class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                          data-immersive-translate-translation-element-mark="1"
                          >轻松分享会议笔记</font
                        ></font
                      ></font
                    >
                  </h2>
                  <p data-block-key="5saca">
                    No need to stress about taking detailed notes during team
                    meetings.
                    <a
                      href="https://chromewebstore.google.com/detail/transkriptor-transcribe-a/pbajmiiebklfjhkeahpgjdlgclelihjh"
                      rel="noopener"
                      target="_blank"
                      >Transkriptor</a
                    >
                    automatically transcribes your meetings from
                    <a
                      href="https://transkriptor.com/"
                      rel="noopener"
                      target="_blank"
                      >audio to text</a
                    >
                    so you can stay focused on the conversation. And it supports
                    more than 100 languages so you can easily share notes with
                    international colleagues.<font
                      class="notranslate immersive-translate-target-wrapper"
                      lang="zh-CN"
                      style="display: unset"
                      ><br /><font
                        class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-block-wrapper-theme-weakening immersive-translate-target-translation-block-wrapper"
                        data-immersive-translate-translation-element-mark="1"
                        ><font
                          class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                          data-immersive-translate-translation-element-mark="1"
                          >无需担心在团队会议中记下详细的笔记。Transkriptor
                          可以自动将会议录音转录为文本，让你专注于对话。它还支持超过
                          100 种语言，方便你与国际同事分享笔记。</font
                        ></font
                      ></font
                    >
                  </p>
                </div>
              </div>
            </div>

            <script
              type="application/json"
              data-catalog-id="reduce-motion-player"
            >
              {
                "play_video": "Play video",
                "pause_video": "Pause video"
              }
            </script>

            <uni-image-full-width
              alignment="large"
              alt-text="Side-by-side images of Transkriptor’s user interface that includes an explanation of how the extension converts audio to text during work meetings."
              external-image=""
              or-mp4-video-title=""
              or-mp4-video-url=""
              section-header="Our favorite Chrome extensions of 2023"
              custom-class="image-full-width--constrained-width uni-component-spacing"
            >
              <!---->
              <div
                class="image-full-width image-full-width--large image-full-width--constrained-width uni-component-spacing"
                data-analytics-module='{"module_name":"Inline Images","section_header":"Our favorite Chrome extensions of 2023"}'
              >
                <!--?lit$806344877$--><!--?lit$806344877$-->
                <div class="image-full-width__media-asset">
                  <!--?lit$806344877$-->
                  <div slot="image-slot" style="display: block">
                    <img
                      alt="Side-by-side images of Transkriptor’s user interface that includes an explanation of how the extension converts audio to text during work meetings."
                      src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Transkriptor_Extension.width-100.format-webp.webp"
                      loading="lazy"
                      data-loading='{
            "mobile": "https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Transkriptor_Extension.width-500.format-webp.webp",
            "desktop": "https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Transkriptor_Extension.width-1000.format-webp.webp"
          }'
                    />
                  </div>
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                </div>
                <!--?-->
                <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
              </div></uni-image-full-width
            >

            <div
              class="module--text module--text__article"
              role="presentation"
              data-analytics-module='{
           "module_name": "Paragraph",
           "section_header": "Our favorite Chrome extensions of 2023"
         }'
            >
              <div
                class="uni-paragraph article-paragraph"
                data-component="uni-article-paragraph"
                data-component-initialized="true"
              >
                <div class="rich-text">
                  <h2 data-block-key="cjr6a">
                    Style your browser<font
                      class="notranslate immersive-translate-target-wrapper"
                      lang="zh-CN"
                      style="display: unset"
                      ><font
                        class="notranslate"
                        data-immersive-translate-translation-element-mark="1"
                        >&nbsp;&nbsp;</font
                      ><font
                        class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-inline-wrapper-theme-weakening immersive-translate-target-translation-inline-wrapper"
                        data-immersive-translate-translation-element-mark="1"
                        ><font
                          class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                          data-immersive-translate-translation-element-mark="1"
                          >美化浏览器</font
                        ></font
                      ></font
                    >
                  </h2>
                  <p data-block-key="odhs">
                    Customize your browser exactly how you like it. For those
                    who prefer a minimalist approach,
                    <a
                      href="https://chromewebstore.google.com/detail/bonjourr-%C2%B7-minimalist-sta/dlnejlppicbjfcfcedcflplfjajinajd"
                      rel="noopener"
                      target="_blank"
                      >Bonjourr</a
                    >
                    keeps your homepage clean, beautiful and
                    distraction-free.<font
                      class="notranslate immersive-translate-target-wrapper"
                      lang="zh-CN"
                      style="display: unset"
                      ><br /><font
                        class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-block-wrapper-theme-weakening immersive-translate-target-translation-block-wrapper"
                        data-immersive-translate-translation-element-mark="1"
                        ><font
                          class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                          data-immersive-translate-translation-element-mark="1"
                          >按照你的喜好完全自定义浏览器。对于喜欢极简风格的用户，Bonjourr
                          可以让你的主页保持干净、美观且无干扰。</font
                        ></font
                      ></font
                    >
                  </p>
                </div>
              </div>
            </div>

            <script
              type="application/json"
              data-catalog-id="reduce-motion-player"
            >
              {
                "play_video": "Play video",
                "pause_video": "Pause video"
              }
            </script>

            <uni-image-full-width
              alignment="large"
              alt-text="Side-by-side images of Bonjourr’s user interface, which shows off the minimalist design of the homepage on a desktop computer."
              external-image=""
              or-mp4-video-title=""
              or-mp4-video-url=""
              section-header="Our favorite Chrome extensions of 2023"
              custom-class="image-full-width--constrained-width uni-component-spacing"
            >
              <!---->
              <div
                class="image-full-width image-full-width--large image-full-width--constrained-width uni-component-spacing"
                data-analytics-module='{"module_name":"Inline Images","section_header":"Our favorite Chrome extensions of 2023"}'
              >
                <!--?lit$806344877$--><!--?lit$806344877$-->
                <div class="image-full-width__media-asset">
                  <!--?lit$806344877$-->
                  <div slot="image-slot" style="display: block">
                    <img
                      alt="Side-by-side images of Bonjourr’s user interface, which shows off the minimalist design of the homepage on a desktop computer."
                      src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Bonjourr_Extension.width-100.format-webp.webp"
                      loading="lazy"
                      data-loading='{
            "mobile": "https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Bonjourr_Extension.width-500.format-webp.webp",
            "desktop": "https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Bonjourr_Extension.width-1000.format-webp.webp"
          }'
                    />
                  </div>
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                </div>
                <!--?-->
                <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
              </div></uni-image-full-width
            >

            <div
              class="module--text module--text__article"
              role="presentation"
              data-analytics-module='{
           "module_name": "Paragraph",
           "section_header": "Our favorite Chrome extensions of 2023"
         }'
            >
              <div
                class="uni-paragraph article-paragraph"
                data-component="uni-article-paragraph"
                data-component-initialized="true"
              >
                <div class="rich-text">
                  <h2 data-block-key="cjr6a">
                    Add more accessibility tools<font
                      class="notranslate immersive-translate-target-wrapper"
                      lang="zh-CN"
                      style="display: unset"
                      ><br /><font
                        class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-block-wrapper-theme-weakening immersive-translate-target-translation-block-wrapper"
                        data-immersive-translate-translation-element-mark="1"
                        ><font
                          class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                          data-immersive-translate-translation-element-mark="1"
                          >添加更多无障碍工具</font
                        ></font
                      ></font
                    >
                  </h2>
                  <p data-block-key="1pp3u">
                    We’re also seeing more extensions that support an accessible
                    and inclusive browsing experience.
                    <a
                      href="https://chromewebstore.google.com/detail/speechify-text-to-speech/ljflmlehinmoeknoonhibbjpldiijjmm"
                      rel="noopener"
                      target="_blank"
                      >Speechify</a
                    >
                    is a
                    <a
                      href="https://speechify.com/text-to-speech-online"
                      rel="noopener"
                      target="_blank"
                      >text-to-speech</a
                    >
                    extension that reads articles, emails and PDFs using natural
                    voices and accents, including celebrity voices. And
                    <a
                      href="https://chromewebstore.google.com/detail/equalizer-for-chrome-brow/abikfbojmghmfjdjlbagiamkinbmbaic"
                      rel="noopener"
                      target="_blank"
                      >Equalizer</a
                    >
                    gives you more fine-tuned audio controls to improve the
                    sound quality of content you’re listening to online.<font
                      class="notranslate immersive-translate-target-wrapper"
                      lang="zh-CN"
                      style="display: unset"
                      ><br /><font
                        class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-block-wrapper-theme-weakening immersive-translate-target-translation-block-wrapper"
                        data-immersive-translate-translation-element-mark="1"
                        ><font
                          class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                          data-immersive-translate-translation-element-mark="1"
                          >我们还看到了更多支持无障碍和平等浏览体验的扩展程序。Speechify
                          是一个文本转语音的扩展程序，可以使用自然的声音和口音（包括名人声音）朗读文章、电子邮件和
                          PDF 文件。Equalizer
                          则提供了更精细的音频控制，以提高您在线听内容时的音质。</font
                        ></font
                      ></font
                    >
                  </p>
                </div>
              </div>
            </div>

            <script
              type="application/json"
              data-catalog-id="reduce-motion-player"
            >
              {
                "play_video": "Play video",
                "pause_video": "Pause video"
              }
            </script>

            <uni-image-full-width
              alignment="large"
              alt-text="Side-by-side images of Speechify’s user interface that includes an explanation of how the extension converts text-to-speech to read your emails and other documents in a natural way."
              external-image=""
              or-mp4-video-title=""
              or-mp4-video-url=""
              section-header="Our favorite Chrome extensions of 2023"
              custom-class="image-full-width--constrained-width uni-component-spacing"
            >
              <!---->
              <div
                class="image-full-width image-full-width--large image-full-width--constrained-width uni-component-spacing"
                data-analytics-module='{"module_name":"Inline Images","section_header":"Our favorite Chrome extensions of 2023"}'
              >
                <!--?lit$806344877$--><!--?lit$806344877$-->
                <div class="image-full-width__media-asset">
                  <!--?lit$806344877$-->
                  <div slot="image-slot" style="display: block">
                    <img
                      alt="Side-by-side images of Speechify’s user interface that includes an explanation of how the extension converts text-to-speech to read your emails and other documents in a natural way."
                      src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Speechify_Extension.width-100.format-webp.webp"
                      loading="lazy"
                      data-loading='{
            "mobile": "https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Speechify_Extension.width-500.format-webp.webp",
            "desktop": "https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Speechify_Extension.width-1000.format-webp.webp"
          }'
                    />
                  </div>
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                </div>
                <!--?-->
                <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
              </div></uni-image-full-width
            >

            <div
              class="module--text module--text__article"
              role="presentation"
              data-analytics-module='{
           "module_name": "Paragraph",
           "section_header": "Our favorite Chrome extensions of 2023"
         }'
            >
              <div
                class="uni-paragraph article-paragraph"
                data-component="uni-article-paragraph"
                data-component-initialized="true"
              >
                <div class="rich-text">
                  <h2 data-block-key="cjr6a">Game on</h2>
                  <p data-block-key="3j5br">
                    Need a quick break? See how far you can get in
                    <a
                      href="https://chromewebstore.google.com/detail/boxel-3d/********************************"
                      rel="noopener"
                      target="_blank"
                      >Boxel 3D</a
                    >, a platformer game that challenges you to create your own
                    levels and think outside the box. And for all you Roblox
                    lovers,
                    <a
                      href="https://chromewebstore.google.com/detail/btroblox-making-roblox-be/hbkpclpemjeibhioopcebchdmohaieln"
                      rel="noopener"
                      target="_blank"
                      >BTRoblox</a
                    >
                    enhances the game’s website with a variety of new features.
                  </p>
                </div>
              </div>
            </div>

            <script
              type="application/json"
              data-catalog-id="reduce-motion-player"
            >
              {
                "play_video": "Play video",
                "pause_video": "Pause video"
              }
            </script>

            <uni-image-full-width
              alignment="large"
              alt-text="Side-by-side images of Boxel 3D’s user interface that includes an explanation of how the extension converts text-to-speech to read your emails and other documents in a natural way."
              external-image=""
              or-mp4-video-title=""
              or-mp4-video-url=""
              section-header="Our favorite Chrome extensions of 2023"
              custom-class="image-full-width--constrained-width uni-component-spacing"
            >
              <!---->
              <div
                class="image-full-width image-full-width--large image-full-width--constrained-width uni-component-spacing"
                data-analytics-module='{"module_name":"Inline Images","section_header":"Our favorite Chrome extensions of 2023"}'
              >
                <!--?lit$806344877$--><!--?lit$806344877$-->
                <div class="image-full-width__media-asset">
                  <!--?lit$806344877$-->
                  <div slot="image-slot" style="display: block">
                    <img
                      alt="Side-by-side images of Boxel 3D’s user interface that includes an explanation of how the extension converts text-to-speech to read your emails and other documents in a natural way."
                      src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Boxel_3D_Extension.width-100.format-webp.webp"
                      loading="lazy"
                      data-loading='{
            "mobile": "https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Boxel_3D_Extension.width-500.format-webp.webp",
            "desktop": "https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Boxel_3D_Extension.width-1000.format-webp.webp"
          }'
                    />
                  </div>
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                </div>
                <!--?-->
                <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
              </div></uni-image-full-width
            >

            <div
              class="module--text module--text__article"
              role="presentation"
              data-analytics-module='{
           "module_name": "Paragraph",
           "section_header": "Our favorite Chrome extensions of 2023"
         }'
            >
              <div
                class="uni-paragraph article-paragraph"
                data-component="uni-article-paragraph"
                data-component-initialized="true"
              >
                <div class="rich-text">
                  <h2 data-block-key="cjr6a">Find better prices</h2>
                  <p data-block-key="96opr">
                    To help you save a few bucks,
                    <a
                      href="https://chromewebstore.google.com/detail/coupert-automatic-coupon/mfidniedemcgceagapgdekdbmanojomk"
                      rel="noopener"
                      target="_blank"
                      >Coupert</a
                    >
                    finds the best promo codes from across the web and
                    automatically applies them when you check out.
                  </p>
                </div>
              </div>
            </div>

            <script
              type="application/json"
              data-catalog-id="reduce-motion-player"
            >
              {
                "play_video": "Play video",
                "pause_video": "Pause video"
              }
            </script>

            <uni-image-full-width
              alignment="large"
              alt-text="Side-by-side images of Coupert’s user interface that includes an explanation of how the extension searches for coupons and then tests if they work before applying it to your cart."
              external-image=""
              or-mp4-video-title=""
              or-mp4-video-url=""
              section-header="Our favorite Chrome extensions of 2023"
              custom-class="image-full-width--constrained-width uni-component-spacing"
            >
              <!---->
              <div
                class="image-full-width image-full-width--large image-full-width--constrained-width uni-component-spacing"
                data-analytics-module='{"module_name":"Inline Images","section_header":"Our favorite Chrome extensions of 2023"}'
              >
                <!--?lit$806344877$--><!--?lit$806344877$-->
                <div class="image-full-width__media-asset">
                  <!--?lit$806344877$-->
                  <div slot="image-slot" style="display: block">
                    <img
                      alt="Side-by-side images of Coupert’s user interface that includes an explanation of how the extension searches for coupons and then tests if they work before applying it to your cart."
                      src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Coupert_Extension.width-100.format-webp.webp"
                      loading="lazy"
                      data-loading='{
            "mobile": "https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Coupert_Extension.width-500.format-webp.webp",
            "desktop": "https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Coupert_Extension.width-1000.format-webp.webp"
          }'
                    />
                  </div>
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                  <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
                </div>
                <!--?-->
                <!--?lit$806344877$--><!--?lit$806344877$--><!--?-->
              </div></uni-image-full-width
            >

            <div
              class="module--text module--text__article"
              role="presentation"
              data-analytics-module='{
           "module_name": "Paragraph",
           "section_header": "Our favorite Chrome extensions of 2023"
         }'
            >
              <div
                class="uni-paragraph article-paragraph"
                data-component="uni-article-paragraph"
                data-component-initialized="true"
              >
                <div class="rich-text">
                  <p data-block-key="cjr6a">
                    Visit the
                    <a
                      href="https://chromewebstore.google.com/collection/2023_favorites"
                      rel="noopener"
                      target="_blank"
                      >Chrome Web Store Favorites of 2023 collection</a
                    >
                    to install and learn more about these helpful extensions.
                    And if you’re a developer looking for guidance on how to
                    design high-quality Chrome extensions, check out our
                    <a
                      href="https://developer.chrome.com/docs/webstore/best_practices/#design-a-high-quality-extension"
                      rel="noopener"
                      target="_blank"
                      >best practices</a
                    >.<span class="tombstone"></span>
                  </p>
                </div>
              </div>
            </div>

            <div
              class="uni-blog-article-tags article-tags"
              data-analytics-module='{
    "module_name": "Article Tags",
    "section_header": "Our favorite Chrome extensions of 2023"
  }'
            >
              <div class="uni-blog-article-tags__wrapper">
                <span class="uni-blog-article-tags__label uni-eyebrow"
                  >POSTED IN:</span
                >
              </div>
              <nav class="uni-blog-article-tags__container uni-click-tracker">
                <ul class="uni-blog-article-tags__tags-list">
                  <li>
                    <a
                      class="uni-blog-article-tags-value uni-body--small uni-link-active"
                      href=" https://blog.google/products/chrome/ "
                      data-ga4-analytics-landing-lead='{
  "event": "landing_page_lead",
  "link_text": "Chrome"
}'
                    >
                      Chrome
                    </a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </section>
    </article>

    <div
      class="uni-related-articles-cards kw-speakable-hidden ga4-carousel"
      data-analytics-module='{
    "module_name": "Article Footer Related Stories",
    "section_header": "Related stories"
  }'
    >
      <div
        class="uni-related-articles-cards-container"
        data-component="uni-related-articles"
        aria-roledescription="carousel"
        data-component-initialized="true"
      >
        <div class="uni-related-articles-grid-title">
          <div class="uni-related-articles-grid-wrapper">
            <h3 class="uni-related-articles-cards__title">
              <div class="uni-related-articles-cards__title-content">
                Related stories
              </div>
            </h3>
          </div>
        </div>

        <button
          class="uni-related-articles-cards__arrow uni-related-articles-cards__arrow--prev hide"
          aria-label="Previous item"
          data-ga4-analytics-carousel-scroll-click='{
  "toggle_type_direction": "arrow - left",
  "position": ""
}'
          aria-controls="uni-related-articles-cards-1753106640139"
          aria-hidden="false"
          disabled=""
        >
          <span class="uni-related-articles-cards__arrow--fig"></span>
        </button>
        <button
          class="uni-related-articles-cards__arrow uni-related-articles-cards__arrow--next"
          aria-label="Next item"
          data-ga4-analytics-carousel-scroll-click='{
  "toggle_type_direction": "arrow - right",
  "position": ""
}'
          aria-controls="uni-related-articles-cards-1753106640139"
          aria-hidden="false"
        >
          <span class="uni-related-articles-cards__arrow--fig"></span>
        </button>

        <div
          class="uni-related-articles-cards__track"
          id="uni-related-articles-cards-1753106640139"
        >
          <div
            role="region"
            aria-live="polite"
            aria-atomic="false"
            class="uni-related-articles-cards__wrap"
          >
            <div
              id="uni-related-articles-cards__item--1"
              class="uni-related-articles-cards__item uni-related-articles-cards__item--img"
            >
              <a
                href="https://blog.google/outreach-initiatives/education/chromebook-iste-2025/"
                class="uni-related-articles-cards__link"
                aria-label="Learning &amp; Education -
                          New Chromebooks and tools for even better teaching and learning -
                          
                          By
                          
                            
                            Tom Chapman
                          
                            
                              &amp;
                            
                            Andy Russell
                          
                          - Jun 30, 2025"
                data-index="1"
                data-target="card"
                data-primarytag="topics - learning &amp; education"
                data-image="true"
                data-ga4-analytics-footer-lead-click='{
                "link_text": "New Chromebooks and tools for even better teaching and learning",
                "link_url":  "https://blog.google/outreach-initiatives/education/chromebook-iste-2025/",
                "source_content": "Related stories",
                "related_index": "1",
                "related_article_tag": "topics - learning &amp; education",
                "article_name": "New Chromebooks and tools for even better teaching and learning",
                "author_name": "Tom Chapman, Andy Russell",
                "content_type": "article page"
              }'
              >
                <div
                  class="uni-related-articles-cards__item-top-info"
                  aria-hidden="true"
                  data-target="card"
                >
                  <div class="uni-related-articles-cards__img">
                    <img
                    src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/ISTE2025_ChromeOS_KWblog-header_J.width-300.format-webp.webp"
                    alt="" sizes="[{" width":"600",="" "height":"208"}]"=""
                    srcset="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/ISTE2025_ChromeOS_KWblog-header_J.width-300.format-webp.webp
                    300w,
                    https://storage.googleapis.com/gweb-uniblog-publish-prod/images/ISTE2025_ChromeOS_KWblog-header_J.width-600.format-webp.webp
                    600w" loading="lazy">
                  </div>

                  <span
                    class="uni-eyebrow uni-eyebrow--ellipsize uni-related-articles-cards__category"
                    data-target="eyebrow"
                    >Learning &amp; Education</span
                  >
                  <h4
                    class="uni-related-articles-cards__headline"
                    data-target="title"
                  >
                    New Chromebooks and tools for even better teaching and
                    learning
                  </h4>
                </div>
                <div
                  class="uni-related-articles-cards__info"
                  aria-hidden="true"
                >
                  <div class="uni-related-articles-cards__author">
                    By

                    <span
                      class="uni-related-articles-cards__author--name"
                      data-target="author"
                    >
                      Tom Chapman
                    </span>

                    &amp;

                    <span
                      class="uni-related-articles-cards__author--name"
                      data-target="author"
                    >
                      Andy Russell
                    </span>
                  </div>

                  <span
                    class="uni-related-articles-cards__date"
                    data-target="date"
                  >
                    Jun 30, 2025
                  </span>
                </div>
                <svg
                  class="uni-related-articles-cards__item-arrow"
                  data-target="arrow"
                  role="presentation"
                  aria-hidden="true"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-arrow-forward-no-bg"
                  ></use>
                </svg>
              </a>
            </div>

            <div
              id="uni-related-articles-cards__item--2"
              class="uni-related-articles-cards__item uni-related-articles-cards__item--img"
            >
              <a
                href="https://blog.google/products/chrome/address-bar-position-change/"
                class="uni-related-articles-cards__link"
                aria-label="Chrome -
                          You can now move your Chrome address bar to the bottom of your Android screen. -
                          
                            It's now possible to customize Chrome on Android by moving your address bar to the bottom of the screen.Depending on the size of your hand and your device, one address b… -
                          
                          By
                          
                            
                            Nick Kim Sexton
                          
                          - Jun 24, 2025"
                data-index="2"
                data-target="card"
                data-primarytag="products - chrome"
                data-image="false"
                data-ga4-analytics-footer-lead-click='{
                "link_text": "You can now move your Chrome address bar to the bottom of your Android screen.",
                "link_url":  "https://blog.google/products/chrome/address-bar-position-change/",
                "source_content": "Related stories",
                "related_index": "2",
                "related_article_tag": "products - chrome",
                "article_name": "You can now move your Chrome address bar to the bottom of your Android screen.",
                "author_name": "Nick Kim Sexton",
                "content_type": "Short Post"
              }'
              >
                <div
                  class="uni-related-articles-cards__item-top-info"
                  aria-hidden="true"
                  data-target="card"
                >
                  <div class="uni-related-articles-cards__img">
                    <img
                    src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Android_Bar_Blog_Header_V4.width-300.format-webp.webp"
                    alt="" sizes="[{" width":"600",="" "height":"208"}]"=""
                    srcset="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Android_Bar_Blog_Header_V4.width-300.format-webp.webp
                    300w,
                    https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_Android_Bar_Blog_Header_V4.width-600.format-webp.webp
                    600w" loading="lazy">
                  </div>

                  <span
                    class="uni-eyebrow uni-eyebrow--ellipsize uni-related-articles-cards__category"
                    data-target="eyebrow"
                    >Chrome</span
                  >
                  <h4
                    class="uni-related-articles-cards__headline"
                    data-target="title"
                  >
                    You can now move your Chrome address bar to the bottom of
                    your Android screen.
                  </h4>
                </div>
                <div
                  class="uni-related-articles-cards__info"
                  aria-hidden="true"
                >
                  <div class="uni-related-articles-cards__author">
                    By

                    <span
                      class="uni-related-articles-cards__author--name"
                      data-target="author"
                    >
                      Nick Kim Sexton
                    </span>
                  </div>

                  <span
                    class="uni-related-articles-cards__date"
                    data-target="date"
                  >
                    Jun 24, 2025
                  </span>
                </div>
                <svg
                  class="uni-related-articles-cards__item-arrow"
                  data-target="arrow"
                  role="presentation"
                  aria-hidden="true"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-arrow-forward-no-bg"
                  ></use>
                </svg>
              </a>
            </div>

            <div
              id="uni-related-articles-cards__item--3"
              class="uni-related-articles-cards__item uni-related-articles-cards__item--img"
            >
              <a
                href="https://blog.google/products/google-one/google-ai-ultra/"
                class="uni-related-articles-cards__link"
                aria-label="Google One -
                          Introducing Google AI Ultra: The best of Google AI in one subscription -
                          
                          By
                          
                            
                            Shimrit Ben-Yair
                          
                          - May 20, 2025"
                data-index="3"
                data-target="card"
                data-primarytag="products - google one"
                data-image="true"
                data-ga4-analytics-footer-lead-click='{
                "link_text": "Introducing Google AI Ultra: The best of Google AI in one subscription",
                "link_url":  "https://blog.google/products/google-one/google-ai-ultra/",
                "source_content": "Related stories",
                "related_index": "3",
                "related_article_tag": "products - google one",
                "article_name": "Introducing Google AI Ultra: The best of Google AI in one subscription",
                "author_name": "Shimrit Ben-Yair",
                "content_type": "article page"
              }'
              >
                <div
                  class="uni-related-articles-cards__item-top-info"
                  aria-hidden="true"
                  data-target="card"
                >
                  <div class="uni-related-articles-cards__img">
                    <img
                    src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Google_AI_Ultra_Blog_Header_Image.width-300.format-webp.webp"
                    alt="" sizes="[{" width":"600",="" "height":"208"}]"=""
                    srcset="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Google_AI_Ultra_Blog_Header_Image.width-300.format-webp.webp
                    300w,
                    https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Google_AI_Ultra_Blog_Header_Image.width-600.format-webp.webp
                    600w" loading="lazy">
                  </div>

                  <span
                    class="uni-eyebrow uni-eyebrow--ellipsize uni-related-articles-cards__category"
                    data-target="eyebrow"
                    >Google One</span
                  >
                  <h4
                    class="uni-related-articles-cards__headline"
                    data-target="title"
                  >
                    Introducing Google AI Ultra: The best of Google AI in one
                    subscription
                  </h4>
                </div>
                <div
                  class="uni-related-articles-cards__info"
                  aria-hidden="true"
                >
                  <div class="uni-related-articles-cards__author">
                    By

                    <span
                      class="uni-related-articles-cards__author--name"
                      data-target="author"
                    >
                      Shimrit Ben-Yair
                    </span>
                  </div>

                  <span
                    class="uni-related-articles-cards__date"
                    data-target="date"
                  >
                    May 20, 2025
                  </span>
                </div>
                <svg
                  class="uni-related-articles-cards__item-arrow"
                  data-target="arrow"
                  role="presentation"
                  aria-hidden="true"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-arrow-forward-no-bg"
                  ></use>
                </svg>
              </a>
            </div>

            <div
              id="uni-related-articles-cards__item--4"
              class="uni-related-articles-cards__item uni-related-articles-cards__item--img"
            >
              <a
                href="https://blog.google/outreach-initiatives/accessibility/android-gemini-ai-gaad-2025/"
                class="uni-related-articles-cards__link"
                aria-label="Accessibility -
                          New AI and accessibility updates across Android, Chrome and more -
                          
                          By
                          
                            
                            Angana Ghosh
                          
                          - May 15, 2025"
                data-index="4"
                data-target="card"
                data-primarytag="topics - accessibility"
                data-image="true"
                data-ga4-analytics-footer-lead-click='{
                "link_text": "New AI and accessibility updates across Android, Chrome and more",
                "link_url":  "https://blog.google/outreach-initiatives/accessibility/android-gemini-ai-gaad-2025/",
                "source_content": "Related stories",
                "related_index": "4",
                "related_article_tag": "topics - accessibility",
                "article_name": "New AI and accessibility updates across Android, Chrome and more",
                "author_name": "Angana Ghosh",
                "content_type": "article page"
              }'
              >
                <div
                  class="uni-related-articles-cards__item-top-info"
                  aria-hidden="true"
                  data-target="card"
                >
                  <div class="uni-related-articles-cards__img">
                    <img
                    src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Blog_header_2096x1182_Option_2.width-300.format-webp.webp"
                    alt="" sizes="[{" width":"600",="" "height":"208"}]"=""
                    srcset="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Blog_header_2096x1182_Option_2.width-300.format-webp.webp
                    300w,
                    https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Blog_header_2096x1182_Option_2.width-600.format-webp.webp
                    600w" loading="lazy">
                  </div>

                  <span
                    class="uni-eyebrow uni-eyebrow--ellipsize uni-related-articles-cards__category"
                    data-target="eyebrow"
                    >Accessibility</span
                  >
                  <h4
                    class="uni-related-articles-cards__headline"
                    data-target="title"
                  >
                    New AI and accessibility updates across Android, Chrome and
                    more
                  </h4>
                </div>
                <div
                  class="uni-related-articles-cards__info"
                  aria-hidden="true"
                >
                  <div class="uni-related-articles-cards__author">
                    By

                    <span
                      class="uni-related-articles-cards__author--name"
                      data-target="author"
                    >
                      Angana Ghosh
                    </span>
                  </div>

                  <span
                    class="uni-related-articles-cards__date"
                    data-target="date"
                  >
                    May 15, 2025
                  </span>
                </div>
                <svg
                  class="uni-related-articles-cards__item-arrow"
                  data-target="arrow"
                  role="presentation"
                  aria-hidden="true"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-arrow-forward-no-bg"
                  ></use>
                </svg>
              </a>
            </div>

            <div
              id="uni-related-articles-cards__item--5"
              class="uni-related-articles-cards__item uni-related-articles-cards__item--img"
            >
              <a
                href="https://blog.google/technology/safety-security/how-were-using-ai-to-combat-the-latest-scams/"
                class="uni-related-articles-cards__link"
                aria-label="Chrome -
                          How we’re using AI to combat the latest scams -
                          
                          By
                          
                            
                            Jasika Bawa
                          
                            
                              &amp;
                            
                            Phiroze Parakh
                          
                          - May 08, 2025"
                data-index="5"
                data-target="card"
                data-primarytag="products - chrome"
                data-image="true"
                data-ga4-analytics-footer-lead-click='{
                "link_text": "How we’re using AI to combat the latest scams",
                "link_url":  "https://blog.google/technology/safety-security/how-were-using-ai-to-combat-the-latest-scams/",
                "source_content": "Related stories",
                "related_index": "5",
                "related_article_tag": "products - chrome",
                "article_name": "How we’re using AI to combat the latest scams",
                "author_name": "Jasika Bawa, Phiroze Parakh",
                "content_type": "article page"
              }'
              >
                <div
                  class="uni-related-articles-cards__item-top-info"
                  aria-hidden="true"
                  data-target="card"
                >
                  <div class="uni-related-articles-cards__img">
                    <img
                    src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_TS_KeywordBlog_Header_01.width-300.format-webp.webp"
                    alt="" sizes="[{" width":"600",="" "height":"208"}]"=""
                    srcset="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_TS_KeywordBlog_Header_01.width-300.format-webp.webp
                    300w,
                    https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Chrome_TS_KeywordBlog_Header_01.width-600.format-webp.webp
                    600w" loading="lazy">
                  </div>

                  <span
                    class="uni-eyebrow uni-eyebrow--ellipsize uni-related-articles-cards__category"
                    data-target="eyebrow"
                    >Chrome</span
                  >
                  <h4
                    class="uni-related-articles-cards__headline"
                    data-target="title"
                  >
                    How we’re using AI to combat the latest scams
                  </h4>
                </div>
                <div
                  class="uni-related-articles-cards__info"
                  aria-hidden="true"
                >
                  <div class="uni-related-articles-cards__author">
                    By

                    <span
                      class="uni-related-articles-cards__author--name"
                      data-target="author"
                    >
                      Jasika Bawa
                    </span>

                    &amp;

                    <span
                      class="uni-related-articles-cards__author--name"
                      data-target="author"
                    >
                      Phiroze Parakh
                    </span>
                  </div>

                  <span
                    class="uni-related-articles-cards__date"
                    data-target="date"
                  >
                    May 08, 2025
                  </span>
                </div>
                <svg
                  class="uni-related-articles-cards__item-arrow"
                  data-target="arrow"
                  role="presentation"
                  aria-hidden="true"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-arrow-forward-no-bg"
                  ></use>
                </svg>
              </a>
            </div>

            <div
              id="uni-related-articles-cards__item--6"
              class="uni-related-articles-cards__item uni-related-articles-cards__item--img"
            >
              <a
                href="https://blog.google/products/google-lens/lens-on-ios-ai-overviews/"
                class="uni-related-articles-cards__link"
                aria-label="Google Lens -
                          Use Lens to search your screen while you browse on iOS -
                          
                          By
                          
                            
                            Jenny Blair
                          
                            
                              &amp;
                            
                            Nick Kim Sexton
                          
                          - Feb 19, 2025"
                data-index="6"
                data-target="card"
                data-primarytag="products - google lens"
                data-image="true"
                data-ga4-analytics-footer-lead-click='{
                "link_text": "Use Lens to search your screen while you browse on iOS",
                "link_url":  "https://blog.google/products/google-lens/lens-on-ios-ai-overviews/",
                "source_content": "Related stories",
                "related_index": "6",
                "related_article_tag": "products - google lens",
                "article_name": "Use Lens to search your screen while you browse on iOS",
                "author_name": "Jenny Blair, Nick Kim Sexton",
                "content_type": "article page"
              }'
              >
                <div
                  class="uni-related-articles-cards__item-top-info"
                  aria-hidden="true"
                  data-target="card"
                >
                  <div class="uni-related-articles-cards__img">
                    <img
                    src="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Lens_blog_header_j9e4UZY.width-300.format-webp.webp"
                    alt="" sizes="[{" width":"600",="" "height":"208"}]"=""
                    srcset="https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Lens_blog_header_j9e4UZY.width-300.format-webp.webp
                    300w,
                    https://storage.googleapis.com/gweb-uniblog-publish-prod/images/Lens_blog_header_j9e4UZY.width-600.format-webp.webp
                    600w" loading="lazy">
                  </div>

                  <span
                    class="uni-eyebrow uni-eyebrow--ellipsize uni-related-articles-cards__category"
                    data-target="eyebrow"
                    >Google Lens</span
                  >
                  <h4
                    class="uni-related-articles-cards__headline"
                    data-target="title"
                  >
                    Use Lens to search your screen while you browse on iOS
                  </h4>
                </div>
                <div
                  class="uni-related-articles-cards__info"
                  aria-hidden="true"
                >
                  <div class="uni-related-articles-cards__author">
                    By

                    <span
                      class="uni-related-articles-cards__author--name"
                      data-target="author"
                    >
                      Jenny Blair
                    </span>

                    &amp;

                    <span
                      class="uni-related-articles-cards__author--name"
                      data-target="author"
                    >
                      Nick Kim Sexton
                    </span>
                  </div>

                  <span
                    class="uni-related-articles-cards__date"
                    data-target="date"
                  >
                    Feb 19, 2025
                  </span>
                </div>
                <svg
                  class="uni-related-articles-cards__item-arrow"
                  data-target="arrow"
                  role="presentation"
                  aria-hidden="true"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-arrow-forward-no-bg"
                  ></use>
                </svg>
              </a>
            </div>

            <div class="uni-related-articles-cards__item--placeholder">.</div>
          </div>
        </div>

        <div
          class="uni-related-articles-cards__pagination ga4-bullets"
          aria-label="Pagination"
          role="tablist"
        >
          <button
            class="uni-related-articles-cards__dot active"
            type="button"
            role="tab"
            aria-selected="true"
            aria-controls="uni-related-articles-cards__item--1"
            data-index="1"
            aria-live="polite"
            data-ga4-analytics-carousel-scroll-click='{
  "toggle_type_direction": "bullet",
  "position": "1"
}'
          >
            <span class="uni-related-articles-cards__dot-text">
              Jump to position 1</span
            >
          </button>

          <button
            class="uni-related-articles-cards__dot"
            type="button"
            role="tab"
            aria-selected="false"
            aria-controls="uni-related-articles-cards__item--2"
            data-index="2"
            aria-live="polite"
            data-ga4-analytics-carousel-scroll-click='{
  "toggle_type_direction": "bullet",
  "position": "2"
}'
          >
            <span class="uni-related-articles-cards__dot-text">
              Jump to position 2</span
            >
          </button>

          <button
            class="uni-related-articles-cards__dot"
            type="button"
            role="tab"
            aria-selected="false"
            aria-controls="uni-related-articles-cards__item--3"
            data-index="3"
            aria-live="polite"
            data-ga4-analytics-carousel-scroll-click='{
  "toggle_type_direction": "bullet",
  "position": "3"
}'
          >
            <span class="uni-related-articles-cards__dot-text">
              Jump to position 3</span
            >
          </button>

          <button
            class="uni-related-articles-cards__dot"
            type="button"
            role="tab"
            aria-selected="false"
            aria-controls="uni-related-articles-cards__item--4"
            data-index="4"
            aria-live="polite"
            data-ga4-analytics-carousel-scroll-click='{
  "toggle_type_direction": "bullet",
  "position": "4"
}'
          >
            <span class="uni-related-articles-cards__dot-text">
              Jump to position 4</span
            >
          </button>

          <button
            class="uni-related-articles-cards__dot"
            type="button"
            role="tab"
            aria-selected="false"
            aria-controls="uni-related-articles-cards__item--5"
            data-index="5"
            aria-live="polite"
            data-ga4-analytics-carousel-scroll-click='{
  "toggle_type_direction": "bullet",
  "position": "5"
}'
          >
            <span class="uni-related-articles-cards__dot-text">
              Jump to position 5</span
            >
          </button>

          <button
            class="uni-related-articles-cards__dot"
            type="button"
            role="tab"
            aria-selected="false"
            aria-controls="uni-related-articles-cards__item--6"
            data-index="6"
            aria-live="polite"
            data-ga4-analytics-carousel-scroll-click='{
  "toggle_type_direction": "bullet",
  "position": "6"
}'
          >
            <span class="uni-related-articles-cards__dot-text">
              Jump to position 6</span
            >
          </button>
        </div>
      </div>
    </div>
  </main>

  <div
    class="uni-newsletter-toast"
    data-component="uni-newsletter-toast"
    data-component-initialized="true"
    style="bottom: -63.0938px"
  >
    <div class="h-c-page">
      <div class="uni-newsletter-toast--container h-c-grid">
        <div
          class="uni-newsletter-toast__info h-c-grid__col h-c-grid__col--12 h-c-grid__col-l--9 h-c-grid__col--align-middle"
        >
          <div class="uni-newsletter-toast__img">
            <img
              src="/static/blogv2/images/newsletter_toast.svg?version=pr20250710-1658"
              alt=""
            />
          </div>
          <p class="uni-newsletter-toast__text">
            Let’s stay in touch. Get the latest news from Google in your
            inbox.<font
              class="notranslate immersive-translate-target-wrapper"
              lang="zh-CN"
              style="display: unset"
              ><br /><font
                class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-block-wrapper-theme-weakening immersive-translate-target-translation-block-wrapper"
                data-immersive-translate-translation-element-mark="1"
                ><font
                  class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                  data-immersive-translate-translation-element-mark="1"
                  >让我们保持联系。将最新的 Google 新闻收件到你的邮箱。</font
                ></font
              ></font
            >
          </p>
        </div>
        <div
          class="uni-newsletter-toast__buttons h-c-grid__col h-c-grid__col--8 h-c-grid__col-l--3"
        >
          <a
            class="uni-newsletter-toast__cta uni-newsletter-toast__cta--sub"
            href="/newsletter-subscribe/"
            tabindex="0"
            aria-label="Newsletter subscribe"
            data-content-type="blogv2 | article page"
            >Subscribe<font
              class="notranslate immersive-translate-target-wrapper"
              lang="zh-CN"
              style="display: unset"
              ><font
                class="notranslate"
                data-immersive-translate-translation-element-mark="1"
                >&nbsp;&nbsp;</font
              ><font
                class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-inline-wrapper-theme-weakening immersive-translate-target-translation-inline-wrapper"
                data-immersive-translate-translation-element-mark="1"
                ><font
                  class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                  data-immersive-translate-translation-element-mark="1"
                  >订阅</font
                ></font
              ></font
            ></a
          >
          <button
            class="uni-newsletter-toast__cta uni-newsletter-toast__cta--no"
            tabindex="0"
            data-content-type="blogv2 | article page"
          >
            No thanks<font
              class="notranslate immersive-translate-target-wrapper"
              lang="zh-CN"
              style="display: unset"
              ><font
                class="notranslate"
                data-immersive-translate-translation-element-mark="1"
                >&nbsp;&nbsp;</font
              ><font
                class="notranslate immersive-translate-target-translation-theme-weakening immersive-translate-target-translation-inline-wrapper-theme-weakening immersive-translate-target-translation-inline-wrapper"
                data-immersive-translate-translation-element-mark="1"
                ><font
                  class="notranslate immersive-translate-target-inner immersive-translate-target-translation-theme-weakening-inner"
                  data-immersive-translate-translation-element-mark="1"
                  >不用谢</font
                ></font
              ></font
            >
          </button>
        </div>
      </div>
    </div>
  </div>

  <uni-feedback-survey
    class="feedback-survey-container"
    survey-id="article-improvements-march-2025_250321"
    survey-data='{"displayRate":0.75,"thankYouMessage":"Thank you!","questions":[{"question":"How could we improve this article?","answers":["Make it more concise","Add more detail","Make it easier to understand","Include more images or videos","It&apos;s fine the way it is"]}],"scrollDepth":0.75,"surveyID":"article-improvements-march-2025_250321","thankYouEmoji":"✅"}'
    style="top: 2806.78px"
    ><!---->
    <div
      translate="no"
      aria-labelledby="feedback-survey__title-sr"
      class="feedback-survey"
      data-survey-id="article-improvements-march-2025_250321"
      style="height: 498px"
    >
      <div id="feedback-survey__title-sr" class="feedback-survey__title-sr">
        Survey
      </div>
      <!--?lit$806344877$-->
      <!--?lit$806344877$-->
      <div class="feedback-survey__question-container">
        <div
          class="feedback-survey__question uni-title-1 feedback-survey__question--no-eyebrow"
        >
          <div class="feedback-survey__question--current" style="height: 28px">
            <!--?lit$806344877$-->How could we improve this article?
          </div>
          <!--?lit$806344877$-->
        </div>
        <div class="feedback-survey__answers">
          <div class="feedback-survey__answers--current" style="height: 374px">
            <!--?lit$806344877$--><!----><button
              class="feedback-survey__answer uni-body"
            >
              <!--?lit$806344877$-->Make it more concise</button
            ><!----><!----><button class="feedback-survey__answer uni-body">
              <!--?lit$806344877$-->Add more detail</button
            ><!----><!----><button class="feedback-survey__answer uni-body">
              <!--?lit$806344877$-->Make it easier to understand</button
            ><!----><!----><button class="feedback-survey__answer uni-body">
              <!--?lit$806344877$-->Include more images or videos</button
            ><!----><!----><button class="feedback-survey__answer uni-body">
              <!--?lit$806344877$-->It's fine the way it is</button
            ><!---->
          </div>
        </div>
        <div class="feedback-survey__answers--next" inert="true">
          <!--?lit$806344877$-->
        </div>
      </div>
      <!--?lit$806344877$-->
      <div class="feedback-survey__thank-you-message" inert="true">
        <!--?lit$806344877$--><span
          class="feedback-survey__emoji uni-headline-1 uni-emoji"
          ><!--?lit$806344877$-->✅</span
        >
        <p class="uni-title-1"><!--?lit$806344877$-->Thank you!</p>
      </div>
      <button class="feedback-survey__close-button" aria-label="close">
        <uni-icon-component
          custom-class="icon h-c-icon h-c-icon--30px"
          icon-id="mi-close"
          ><!----><svg
            class="icon icon h-c-icon h-c-icon--30px"
            role="presentation"
            viewBox="0 0 18 18"
          >
            <use
              href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-close"
            ></use></svg
        ></uni-icon-component>
      </button></div
  ></uni-feedback-survey>
  <footer
    class="h-c-footer h-c-footer--topmargin h-c-footer--standard h-has-social"
    id="footer-standard"
    data-component="uni-footer-component"
    data-analytics-module='{
          "module_name": "footer",
          "section_header": "Our favorite Chrome extensions of 2023"
        }'
    data-component-initialized="true"
    default-translate="no"
  >
    <section class="h-c-footer__upper">
      <section class="h-c-social">
        <div class="h-c-social__group">
          <p class="h-c-social__title h-c-social__title--inline">Follow Us</p>
          <ul class="h-c-social__list h-no-bullet">
            <li class="h-c-social__item">
              <a
                class="h-c-social__link uni-click-tracker"
                aria-label="Instagram"
                href="https://www.instagram.com/google/"
                rel="noopener"
                target="_blank"
              >
                <svg
                  class="h-c-icon h-c-icon--24px h-c-icon--social"
                  aria-hidden="true"
                  viewBox="0 0 18 18"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-instagram"
                  ></use>
                </svg>
              </a>
            </li>

            <li class="h-c-social__item">
              <a
                class="h-c-social__link uni-click-tracker"
                aria-label="Twitter"
                href="https://twitter.com/google"
                rel="noopener"
                target="_blank"
              >
                <svg
                  class="h-c-icon h-c-icon--24px h-c-icon--social"
                  aria-hidden="true"
                  viewBox="0 0 18 18"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-twitter"
                  ></use>
                </svg>
              </a>
            </li>

            <li class="h-c-social__item">
              <a
                class="h-c-social__link uni-click-tracker"
                aria-label="YouTube"
                href="https://www.youtube.com/google"
                rel="noopener"
                target="_blank"
              >
                <svg
                  class="h-c-icon h-c-icon--24px h-c-icon--social"
                  aria-hidden="true"
                  viewBox="0 0 18 18"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-youtube"
                  ></use>
                </svg>
              </a>
            </li>

            <li class="h-c-social__item">
              <a
                class="h-c-social__link uni-click-tracker"
                aria-label="Facebook"
                href="https://www.facebook.com/Google"
                rel="noopener"
                target="_blank"
              >
                <svg
                  class="h-c-icon h-c-icon--24px h-c-icon--social"
                  aria-hidden="true"
                  viewBox="0 0 18 18"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-facebook"
                  ></use>
                </svg>
              </a>
            </li>

            <li class="h-c-social__item">
              <a
                class="h-c-social__link uni-click-tracker"
                aria-label="LinkedIn"
                href="https://www.linkedin.com/company/google"
                rel="noopener"
                target="_blank"
              >
                <svg
                  class="h-c-icon h-c-icon--24px h-c-icon--social"
                  aria-hidden="true"
                  viewBox="0 0 18 18"
                >
                  <use
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    href="/static/blogv2/images/icons.svg?version=pr20250710-1658#social-linkedin"
                  ></use>
                </svg>
              </a>
            </li>
          </ul>
        </div>
      </section>
    </section>

    <section class="h-c-footer__global">
      <div class="h-c-footer__logo">
        <a
          class="uni-click-tracker"
          href="https://www.google.com"
          title="Google"
          rel="noopener"
          target="_blank"
        >
          <svg
            class="h-c-footer__logo-img"
            aria-hidden="true"
            viewBox="0 0 396 130"
          >
            <use
              xmlns:xlink="http://www.w3.org/1999/xlink"
              href="/static/blogv2/images/icons.svg?version=pr20250710-1658#google-logo"
            ></use>
          </svg>
        </a>
      </div>
      <ul class="h-c-footer__global-links h-no-bullet">
        <li class="h-c-footer__global-links-list-item">
          <a
            class="h-c-footer__link uni-click-tracker"
            href="https://policies.google.com/privacy"
            rel="noopener"
            target="_blank"
            >Privacy
          </a>
        </li>
        <li class="h-c-footer__global-links-list-item">
          <a
            class="h-c-footer__link uni-click-tracker"
            href="https://policies.google.com/terms"
            rel="noopener"
            target="_blank"
            >Terms
          </a>
        </li>
        <li class="h-c-footer__global-links-list-item">
          <a
            class="h-c-footer__link uni-click-tracker"
            href="https://about.google/"
            rel="noopener"
            target="_blank"
            >About Google
          </a>
        </li>
        <li class="h-c-footer__global-links-list-item">
          <a
            class="h-c-footer__link uni-click-tracker"
            href="https://about.google/products/"
            rel="noopener"
            target="_blank"
            >Google Products
          </a>
        </li>

        <li class="h-c-footer__global-links-list-item">
          <a class="h-c-footer__link uni-click-tracker" href="/about/"
            >About the Keyword
          </a>
        </li>
      </ul>
      <ul
        class="h-c-footer__global-links h-c-footer__global-links--extra h-no-bullet"
      >
        <li
          class="h-c-footer__global-links-list-item h-c-footer__global-links-list-item--extra"
        >
          <a
            class="h-c-footer__link uni-click-tracker"
            href="https://support.google.com"
            rel="noopener"
            target="_blank"
          >
            <svg
              class="h-c-icon h-c-icon--24px h-c-icon--footer"
              aria-hidden="true"
            >
              <use
                xmlns:xlink="http://www.w3.org/1999/xlink"
                href="/static/blogv2/images/icons.svg?version=pr20250710-1658#mi-help"
              ></use>
            </svg>

            Help
          </a>
        </li>
        <li
          class="h-c-footer__global-links-list-item h-c-footer__global-links-list-item--extra uni-footer-language"
        >
          <div
            class="uni-picker"
            data-component="uni-lang-picker"
            data-component-initialized="true"
          >
            <select
              name="language"
              class="uni-picker__order-menu"
              aria-label="Change Region"
            >
              <option
                label="Global (English)"
                value="/"
                class="uni-picker__item"
                selected="selected"
                data-selected-index="0"
              >
                Global (English)
              </option>

              <option
                label="Africa (English)"
                value="/intl/en-africa/"
                class="uni-picker__item"
              >
                Africa (English)
              </option>

              <option
                label="Australia (English)"
                value="/intl/en-au/"
                class="uni-picker__item"
              >
                Australia (English)
              </option>

              <option
                label="Brasil (Português)"
                value="/intl/pt-br/"
                class="uni-picker__item"
              >
                Brasil (Português)
              </option>

              <option
                label="Canada (English)"
                value="/intl/en-ca/"
                class="uni-picker__item"
              >
                Canada (English)
              </option>

              <option
                label="Canada (Français)"
                value="/intl/fr-ca/"
                class="uni-picker__item"
              >
                Canada (Français)
              </option>

              <option
                label="Česko (Čeština)"
                value="/intl/cs-cz/"
                class="uni-picker__item"
              >
                Česko (Čeština)
              </option>

              <option
                label="Deutschland (Deutsch)"
                value="/intl/de-de/"
                class="uni-picker__item"
              >
                Deutschland (Deutsch)
              </option>

              <option
                label="España (Español)"
                value="/intl/es-es/"
                class="uni-picker__item"
              >
                España (Español)
              </option>

              <option
                label="France (Français)"
                value="/intl/fr-fr/"
                class="uni-picker__item"
              >
                France (Français)
              </option>

              <option
                label="India (English)"
                value="/intl/en-in/"
                class="uni-picker__item"
              >
                India (English)
              </option>

              <option
                label="Indonesia (Bahasa Indonesia)"
                value="/intl/id-id/"
                class="uni-picker__item"
              >
                Indonesia (Bahasa Indonesia)
              </option>

              <option
                label="日本 (日本語)"
                value="/intl/ja-jp/"
                class="uni-picker__item"
              >
                日本 (日本語)
              </option>

              <option
                label="대한민국 (한국어)"
                value="/intl/ko-kr/"
                class="uni-picker__item"
              >
                대한민국 (한국어)
              </option>

              <option
                label="Latinoamérica (Español)"
                value="/intl/es-419/"
                class="uni-picker__item"
              >
                Latinoamérica (Español)
              </option>

              <option
                label="الشرق الأوسط وشمال أفريقيا (اللغة العربية)"
                value="/intl/ar-mena/"
                class="uni-picker__item"
              >
                الشرق الأوسط وشمال أفريقيا (اللغة العربية)
              </option>

              <option
                label="Nederlands (Nederland)"
                value="/intl/nl-nl/"
                class="uni-picker__item"
              >
                Nederlands (Nederland)
              </option>

              <option
                label="Polska (Polski)"
                value="/intl/pl-pl/"
                class="uni-picker__item"
              >
                Polska (Polski)
              </option>

              <option
                label="Italia (Italiano)"
                value="/intl/it-it/"
                class="uni-picker__item"
              >
                Italia (Italiano)
              </option>

              <option
                label="ประเทศไทย (ไทย)"
                value="/intl/th-th/"
                class="uni-picker__item"
              >
                ประเทศไทย (ไทย)
              </option>

              <option
                label="Türkiye (Türkçe)"
                value="/intl/tr-tr/"
                class="uni-picker__item"
              >
                Türkiye (Türkçe)
              </option>

              <option
                label="台灣 (中文)"
                value="/intl/zh-tw/"
                class="uni-picker__item"
              >
                台灣 (中文)
              </option>

              <option
                label="MENA (English)"
                value="/intl/en-mena/"
                class="uni-picker__item"
              >
                MENA (English)
              </option>
            </select>
          </div>
        </li>
      </ul>
    </section>
  </footer>

  <div
    id="base-scripts"
    data-scripts='[
              { "url": "/static/blogv2/js/csp/gtm.js?version=pr20250710-1658",
                "options": {
                  "async": false,
                  "defer": true
                }
              },
              { "url": "/static/keyword/js/all/index.js?version=pr20250710-1658",
                "options": {
                  "async": false,
                  "defer": false
                }
              },
              {
                "url": "https://www.gstatic.com/glue/cookienotificationbar/cookienotificationbar.min.js",
                "options": {
                  "async": false,
                  "defer": true
                },
                "attributes": {
                  "data-glue-cookie-notification-bar-category": "2B",
                  "data-glue-cookie-notification-bar-site-id": "blog.google"
                }
              }
            ]'
  ></div>
  <div class="extra-scripts">
    <div
      async=""
      data-src="https://cdn.ampproject.org/amp-story-player-v0.js"
      data-id="amp-cdn"
    ></div>
  </div>

  <script>
    const baseScripts = JSON.parse(
      document.querySelector("#base-scripts")?.getAttribute("data-scripts") ||
        "[]"
    );

    const userbar =
      typeof userbarScripts !== "undefined" && userbarScripts
        ? userbarScripts
        : [];

    const extraScripts = Array.from(
      document.querySelectorAll(".extra-scripts div[data-src]")
    ).map((el) => {
      const attrs = {};
      Array.from(el.attributes).forEach((attr) => {
        attrs[attr.name === "data-src" ? "src" : attr.name] = attr.value;
      });
      return {
        url: attrs.src,
        options: {},
        attributes: Object.fromEntries(
          Object.entries(attrs).filter(([k]) => k !== "src")
        ),
      };
    });

    [...baseScripts, ...userbar, ...extraScripts].forEach(
      ({ url, options = {}, attributes = {} }) => {
        const script = Object.assign(document.createElement("script"), {
          async: options.async,
          defer: options.defer,
          src: url,
        });

        Object.entries(attributes).forEach(([k, v]) =>
          script.setAttribute(k, v)
        );
        document.head.appendChild(script);
      }
    );
  </script>

  <doubao-ai-csui
    id="255fb067ed3344ceb92013945146c040"
    data-host-id="d749c39f-3d9f-49f6-9f2b-e950541b6d92"
  ></doubao-ai-csui>
  <div
    id="doubao-ai-translate-image-assistantd749c39f-3d9f-49f6-9f2b-e950541b6d92"
    data-collection-img-host-id="d749c39f-3d9f-49f6-9f2b-e950541b6d92"
  ></div>
  <div
    style="width: 0px; height: 0px; position: absolute; z-index: 200000"
  ></div>
  <div
    id="doubao-ai-assistant"
    aria-label="flow-ai-assistant"
    class="flow-ai-select-bar-enabled flow-ai-select-bar-support-iframe"
    aria-description="flow-ai-select-bar-enabled flow-ai-select-bar-support-iframe"
    style="
      width: 0px;
      height: 0px;
      position: absolute;
      top: 0px;
      left: 0px;
      overflow: hidden;
    "
  ></div>
</body>
