/**
 * Mock翻译服务集成脚本
 * 用于在测试环境中提供Mock翻译功能
 */

import { getMockTranslator, autoSetupMockTranslation as autoSetupMockTranslationService } from '../features/translate/mock-translate-service-refactored';
import { setupTestInterface, createTestManagerWithMock } from '../utils/test-helpers';

/**
 * 检测是否为测试环境
 */
function isTestEnvironment(): boolean {
  const result = (
    // 严格的测试环境检测 - 文件协议或localhost
    (window.location.protocol === 'file:' ||
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1') &&

    // 必须同时满足测试标识符之一
    (document.title.toLowerCase().includes('test') ||
      document.querySelector('[data-testid]') !== null ||
      // 只在本地环境时才允许localStorage标志
      localStorage.getItem('lucid-debug') === 'true' ||
      localStorage.getItem('lucid-use-mock') === 'true' ||
      localStorage.getItem('lucid-force-mock') === 'true')
  );

  console.log('🔍 Test environment detection:', {
    protocol: window.location.protocol,
    hostname: window.location.hostname,
    title: document.title,
    hasTestId: !!document.querySelector('[data-testid]'),
    debug: localStorage.getItem('lucid-debug'),
    useMock: localStorage.getItem('lucid-use-mock'),
    forceMock: localStorage.getItem('lucid-force-mock'),
    result
  });

  return result;
}

// 防止重复初始化的标志
let isSetupComplete = false;

/**
 * 设置Mock翻译环境
 */
export function setupMockTranslationEnvironment(): void {
  // 防止重复执行
  if (isSetupComplete) {
    console.log('✅ Mock translation environment already set up, skipping...');
    return;
  }

  const shouldSetup = isTestEnvironment();

  if (!shouldSetup) {
    console.log('🔍 Not in test environment, setting up minimal fallback functions');
    setupFallbackFunctions();
    return;
  }

  console.log('🧪 Setting up mock translation environment...');
  isSetupComplete = true; // 标记为已完成

  try {
    // 1. 直接创建Mock翻译服务（避免递归调用）
    const mockTranslator = getMockTranslator();

    // 2. 创建带Mock翻译的测试管理器
    const testManager = createTestManagerWithMock();

    // 3. 设置测试接口
    setupTestInterface(testManager);

    // 4. 安全属性分配函数
    const assignGlobalProperty = (obj: any, key: string, value: any) => {
      try {
        // 检查属性是否已存在并且相同
        if (obj[key] === value) {
          console.log(`✅ Property ${key} already set correctly`);
          return;
        }

        const descriptor = Object.getOwnPropertyDescriptor(obj, key);
        if (descriptor && (!descriptor.writable || !descriptor.configurable)) {
          console.log(`🔧 Property ${key} is non-writable, using namespace instead`);
          // 使用命名空间避免冲突
          if (!obj.lucidTest) obj.lucidTest = {};
          obj.lucidTest[key] = value;
          console.log(`✅ Successfully assigned to namespace: lucidTest.${key}`);
          return;
        }

        obj[key] = value;
        console.log(`✅ Successfully assigned: ${key}`);
      } catch (error) {
        console.warn(`⚠️ Failed to assign ${key}:`, error);
        // 降级到命名空间
        try {
          if (!obj.lucidTest) obj.lucidTest = {};
          obj.lucidTest[key] = value;
          console.log(`✅ Fallback: assigned to lucidTest.${key}`);
        } catch (fallbackError) {
          console.error(`❌ Complete failure assigning ${key}:`, fallbackError);
        }
      }
    };

    // 只在开发环境中暴露调试工具
    if (process.env.NODE_ENV === 'development') {
      // 暴露到全局对象供控制台使用
      const globalObj = window as any;

      // 统一封装到 LucidDevTools 对象下
      globalObj.LucidDevTools = {
        translatePage: () => testManager.translatePage(),
        clearTranslations: () => testManager.clearTranslations(),
        toggleView: () => testManager.toggleViewMode(),
        getStats: () => testManager.getStats(),
        getCurrentState: () => testManager.getCurrentState(),

        // Mock翻译相关
        mockTranslator,
        translateText: (text: string, lang: string = 'zh-CN') => mockTranslator.translateText(text, { to: lang }),

        // 状态管理
        setViewMode: (mode: 'origin' | 'dual' | 'trans') => {
          const modeMap = { origin: 'origin', dual: 'dual', trans: 'trans' };
          testManager.setViewMode(modeMap[mode] as any);
        },

        // Mock开关功能
        enableMock: () => {
          localStorage.setItem('lucid-force-mock', 'true');
          console.log('🚀 已启用Mock翻译，请刷新页面生效');
        },
        
        disableMock: () => {
          localStorage.removeItem('lucid-force-mock');
          console.log('🔌 已禁用Mock翻译模式，请刷新页面生效');
        }
      };

      // 添加便捷的测试函数
      const testTranslation = async (text: string = 'Hello World') => {
        console.log(`🧪 Testing translation: "${text}"`);
        try {
          const result = await mockTranslator.translateText(text, { to: 'zh-CN' });
          console.log(`✅ Translation result: "${result}"`);
          return result;
        } catch (error) {
          console.error('❌ Translation failed:', error);
          throw error;
        }
      };

      const testPageTranslation = async () => {
        console.log('🌐 Testing full page translation...');
        try {
          await testManager.translatePage();
          console.log('✅ Page translation completed');
          return testManager.getStats();
        } catch (error) {
          console.error('❌ Page translation failed:', error);
          throw error;
        }
      };

      // DOM注入测试
      const testDOMInjection = () => {
        console.log('🔧 Testing DOM injection...');

        // 创建测试元素
        const testP = document.createElement('p');
        testP.textContent = 'This is a test paragraph for DOM injection';
        testP.style.cssText = 'border: 2px solid #4CAF50; padding: 10px; margin: 10px; background: #f0f8ff;';
        document.body.appendChild(testP);

        // 翻译并注入
        return mockTranslator.translateText(testP.textContent, { to: 'zh-CN' }).then(translation => {
          const wrapper = document.createElement('div');
          wrapper.className = 'lu-wrapper';
          wrapper.innerHTML = `
            <div class="lu-original">${testP.textContent}</div>
            <div class="lu-translation" style="color: #2196F3; font-weight: bold;">${translation}</div>
          `;
          testP.innerHTML = '';
          testP.appendChild(wrapper);

          console.log('✅ DOM injection test completed');
          return { original: testP.textContent, translation };
        });
      };

      // 视图模式测试
      const testViewModes = () => {
        console.log('👁️ Testing view modes...');
        const modes = ['origin', 'dual', 'trans'];
        let index = 0;

        const switchMode = () => {
          if (index < modes.length) {
            const mode = modes[index];
            console.log(`Switching to ${mode} mode...`);
            document.documentElement.setAttribute('lu-view', mode);
            index++;
            setTimeout(switchMode, 2000);
          } else {
            console.log('✅ View mode test completed');
          }
        };

        switchMode();
      };

      // 扩展 LucidDevTools 对象，添加更多测试工具
      globalObj.LucidDevTools.testTranslation = testTranslation;
      globalObj.LucidDevTools.testPageTranslation = testPageTranslation;
      globalObj.LucidDevTools.testDOMInjection = testDOMInjection;
      globalObj.LucidDevTools.testViewModes = testViewModes;

      // 添加CSS样式支持Mock翻译显示
      const style = document.createElement('style');
      style.textContent = `
        .lu-wrapper {
          position: relative;
          display: block;
          margin: 2px 0;
        }
        
        .lu-original {
          display: block;
        }
        
        .lu-translation {
          display: block;
          color: #2196F3;
          font-style: italic;
          margin-top: 4px;
          padding-left: 10px;
          border-left: 3px solid #2196F3;
        }
        
        /* 视图模式控制 */
        [lu-view="origin"] .lu-translation {
          display: none;
        }
        
        [lu-view="trans"] .lu-original {
          opacity: 0.5;
          color: #999;
        }
        
        [lu-view="dual"] .lu-original,
        [lu-view="dual"] .lu-translation {
          display: block;
        }
      `;
      document.head.appendChild(style);

      console.log('🎉 Mock translation environment setup complete!');
      console.log('📋 Available commands:');
      console.log('  - LucidDevTools.enableMock() - 启用Mock翻译');
      console.log('  - LucidDevTools.disableMock() - 禁用Mock翻译');
      console.log('  - LucidDevTools.translateText("Hello") - 测试翻译');
      console.log('  - LucidDevTools.testTranslation("Hello") - 测试单个文本翻译');
      console.log('  - LucidDevTools.testPageTranslation() - 测试整页翻译');
      console.log('  - LucidDevTools.testDOMInjection() - 测试DOM注入');
      console.log('  - LucidDevTools.testViewModes() - 测试视图模式切换');
    }

  } catch (error) {
    console.error('❌ Failed to setup mock translation environment:', error);
  }
}

/**
 * 设置基础回退函数（即使不在测试环境中）
 */
function setupFallbackFunctions(): void {
  console.log('⚙️ Setting up fallback functions for non-test environment...');

  // 只在开发环境中提供回退函数
  if (process.env.NODE_ENV === 'development') {
    const globalObj = window as any;

    // 基本的翻译函数，显示未实现消息
    globalObj.LucidDevTools = {
      translatePage: () => {
        console.warn('❌ DOM injection system not available in production. Use localStorage.setItem("lucid-force-mock", "true") and reload to enable testing.');
        return Promise.reject(new Error('DOM injection system not available. Enable mock translation for testing.'));
      },
      clearTranslations: () => console.warn('clearTranslations not available'),
      toggleView: () => console.warn('toggleView not available'),
      getStats: () => ({ message: 'Stats not available in production' }),
      getCurrentState: () => ({ message: 'State not available in production' }),
      enableMock: () => {
        localStorage.setItem('lucid-force-mock', 'true');
        console.log('🚀 已启用Mock翻译，请刷新页面生效');
      },
      disableMock: () => {
        localStorage.removeItem('lucid-force-mock');
        console.log('🔌 已禁用Mock翻译模式，请刷新页面生效');
      },
      testPageTranslation: () => {
        console.warn('❌ Test functions not available in production. Use localStorage.setItem("lucid-force-mock", "true") and reload to enable testing.');
        return Promise.reject(new Error('Test functions not available. Enable mock translation for testing.'));
      }
    };

    console.log('✅ Fallback functions setup complete');
  } else {
    console.log('🔒 Development tools disabled in production environment');
  }
}

/**
 * 强制设置Mock翻译环境（绕过环境检测）
 */
export function forceSetupMockTranslation(): void {
  console.log('🚀 Force setting up mock translation environment...');

  try {
    // 安全属性分配函数
    const assignGlobalProperty = (obj: any, key: string, value: any) => {
      try {
        // 检查属性是否已存在并且相同
        if (obj[key] === value) {
          console.log(`✅ Property ${key} already set correctly`);
          return;
        }

        const descriptor = Object.getOwnPropertyDescriptor(obj, key);
        if (descriptor && (!descriptor.writable || !descriptor.configurable)) {
          console.log(`🔧 Property ${key} is non-writable, using namespace instead`);
          // 使用命名空间避免冲突
          if (!obj.lucidTest) obj.lucidTest = {};
          obj.lucidTest[key] = value;
          console.log(`✅ Successfully assigned to namespace: lucidTest.${key}`);
          return;
        }

        obj[key] = value;
        console.log(`✅ Successfully assigned: ${key}`);
      } catch (error) {
        console.warn(`⚠️ Failed to assign ${key}:`, error);
        // 降级到命名空间
        try {
          if (!obj.lucidTest) obj.lucidTest = {};
          obj.lucidTest[key] = value;
          console.log(`✅ Fallback: assigned to lucidTest.${key}`);
        } catch (fallbackError) {
          console.error(`❌ Complete failure assigning ${key}:`, fallbackError);
        }
      }
    };

    // 1. 设置全局Mock翻译服务
    autoSetupMockTranslationService();

    // 2. 创建带Mock翻译的测试管理器
    const testManager = createTestManagerWithMock();

    // 3. 设置测试接口
    setupTestInterface(testManager);

    // 4. 添加额外的测试工具
    const mockTranslator = getMockTranslator();

    // 只在开发环境中暴露调试工具
    if (process.env.NODE_ENV === 'development') {
      // 暴露到全局对象供控制台使用
      const globalObj = window as any;

      // 统一封装到 LucidDevTools 对象下
      globalObj.LucidDevTools = {
        translatePage: () => testManager.translatePage(),
        clearTranslations: () => testManager.clearTranslations(),
        toggleView: () => testManager.toggleViewMode(),
        getStats: () => testManager.getStats(),
        getCurrentState: () => testManager.getCurrentState(),

        // Mock翻译相关
        mockTranslator,
        translateText: (text: string, lang: string = 'zh-CN') => mockTranslator.translateText(text, { to: lang }),

        // 状态管理
        setViewMode: (mode: 'origin' | 'dual' | 'trans') => {
          const modeMap = { origin: 'origin', dual: 'dual', trans: 'trans' };
          testManager.setViewMode(modeMap[mode] as any);
        },

        // Mock开关功能
        enableMock: () => {
          localStorage.setItem('lucid-force-mock', 'true');
          console.log('🚀 已启用Mock翻译，请刷新页面生效');
        },
        
        disableMock: () => {
          localStorage.removeItem('lucid-force-mock');
          console.log('🔌 已禁用Mock翻译模式，请刷新页面生效');
        }
      };

      // 添加便捷的测试函数
      const testTranslation = async (text: string = 'Hello World') => {
        console.log(`🧪 Testing translation: "${text}"`);
        try {
          const result = await mockTranslator.translateText(text, { to: 'zh-CN' });
          console.log(`✅ Translation result: "${result}"`);
          return result;
        } catch (error) {
          console.error('❌ Translation failed:', error);
          throw error;
        }
      };

      const testPageTranslation = async () => {
        console.log('🌐 Testing full page translation...');
        try {
          await testManager.translatePage();
          console.log('✅ Page translation completed');
          return testManager.getStats();
        } catch (error) {
          console.error('❌ Page translation failed:', error);
          throw error;
        }
      };

      // 扩展 LucidDevTools 对象，添加更多测试工具  
      globalObj.LucidDevTools.testTranslation = testTranslation;
      globalObj.LucidDevTools.testPageTranslation = testPageTranslation;

      console.log('🎉 Force Mock translation environment setup complete!');
      console.log('📋 Available commands:');
      console.log('  - LucidDevTools.enableMock() - 启用Mock翻译');
      console.log('  - LucidDevTools.disableMock() - 禁用Mock翻译');
      console.log('  - LucidDevTools.translateText("Hello") - 测试翻译');
      console.log('  - LucidDevTools.testTranslation("Hello") - 测试单个文本翻译');
      console.log('  - LucidDevTools.testPageTranslation() - 测试整页翻译');
    }

  } catch (error) {
    console.error('❌ Failed to force setup mock translation environment:', error);
  }
}

/**
 * 自动设置Mock翻译环境（如果在测试环境中）
 */
export function autoSetupMockTranslation(): void {
  // 立即检查并初始化
  const initializeNow = () => {
    setupMockTranslationEnvironment();
    setupDynamicMockDetection();
  };

  // 延迟初始化，确保DOM已加载
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeNow);
  } else {
    initializeNow();
  }
}

/**
 * 设置动态Mock检测（移除setTimeout，使用事件监听）
 */
function setupDynamicMockDetection(): void {
  // 立即检查一次
  checkAndReinitialize();

  // 设置localStorage变化监听器
  window.addEventListener('storage', (event) => {
    if (event.key === 'lucid-force-mock' && event.newValue === 'true') {
      console.log('🔄 Storage event: Force-mock detected, reinitializing...');
      forceSetupMockTranslation();
    }
  });

  // 禁用周期性检查，避免死循环和性能问题
  // 只依赖storage事件监听，它更高效且不会造成死循环
  console.log('✅ Dynamic mock detection setup completed (storage events only)');
}

/**
 * 检查并重新初始化
 */
function checkAndReinitialize(): boolean {
  if (localStorage.getItem('lucid-force-mock') === 'true') {
    console.log('🔄 Immediate check: Force-mock detected, reinitializing...');
    forceSetupMockTranslation();
    return true;
  }
  return false;
}