/**
 * 翻译视图状态控制器
 * 管理原文/双语/译文三种显示模式，智能切换和DOM监听
 * 同时管理翻译引擎的健康检查状态，避免在原文模式下不必要的API调用
 */

// debug 导入已移除，使用手动 console.log

/**
 * 视图模式枚举
 */
export enum ViewMode {
  /** 仅显示原文 */
  ORIGIN = 'origin',
  /** 双语对照显示 */
  DUAL = 'dual',
  /** 仅显示译文 */
  TRANS = 'trans'
}

/**
 * 状态变化事件
 */
export interface ViewModeChangeEvent {
  /** 之前的模式 */
  previousMode: ViewMode;
  /** 当前模式 */
  currentMode: ViewMode;
  /** 变化时间戳 */
  timestamp: number;
  /** 触发原因 */
  trigger: 'user' | 'auto' | 'restore';
}

/**
 * 控制器配置
 */
export interface ViewControllerOptions {
  /** 默认视图模式 */
  defaultMode?: ViewMode;
  /** 是否自动保存状态 */
  autoSave?: boolean;
  /** 是否启用DOM监听 */
  enableDOMObserver?: boolean;
  /** 状态变化回调 */
  onModeChange?: (event: ViewModeChangeEvent) => void;
  /** 调试模式 */
  debug?: boolean;
  /** 翻译引擎管理器（用于控制健康检查） */
  engineManager?: any;
}

/**
 * 视图状态控制器类
 */
export class ViewModeController {
  private currentMode: ViewMode;
  private previousMode: ViewMode;
  private observer?: MutationObserver;
  private options: Required<ViewControllerOptions>;
  private readonly STORAGE_KEY = 'lu-view-mode';
  private readonly ATTRIBUTE_NAME = 'lu-view';

  constructor(options: ViewControllerOptions = {}) {
    this.options = {
      defaultMode: ViewMode.ORIGIN,
      autoSave: true,
      enableDOMObserver: true,
      onModeChange: () => {},
      debug: false,
      engineManager: null,
      ...options
    };

    // 初始化状态
    this.currentMode = this.loadSavedMode() || this.options.defaultMode;
    this.previousMode = this.currentMode;

    // 应用初始状态
    this.applyMode(this.currentMode, 'restore');

    // 启动DOM监听
    if (this.options.enableDOMObserver) {
      this.setupDOMObserver();
    }

    this.log('ViewModeController initialized', { mode: this.currentMode });
  }

  /**
   * 设置视图模式
   */
  setViewMode(mode: ViewMode, trigger: 'user' | 'auto' = 'user'): void {
    if (mode === this.currentMode) {
      return;
    }

    const previousMode = this.currentMode;
    this.previousMode = previousMode;
    this.currentMode = mode;

    // 应用视图模式
    this.applyMode(mode, trigger);

    // 保存状态
    if (this.options.autoSave) {
      this.saveMode(mode);
    }

    // 触发事件
    const event: ViewModeChangeEvent = {
      previousMode,
      currentMode: mode,
      timestamp: Date.now(),
      trigger
    };

    this.options.onModeChange(event);
    this.log('Mode changed', event);
  }

  /**
   * 切换到下一个模式
   */
  toggleMode(): ViewMode {
    const modes = [ViewMode.ORIGIN, ViewMode.DUAL, ViewMode.TRANS];
    const currentIndex = modes.indexOf(this.currentMode);
    const nextMode = modes[(currentIndex + 1) % modes.length];
    
    this.setViewMode(nextMode, 'user');
    return nextMode;
  }

  /**
   * 循环切换原文和双语模式（常用）
   */
  toggleOriginDual(): ViewMode {
    const nextMode = this.currentMode === ViewMode.ORIGIN 
      ? ViewMode.DUAL 
      : ViewMode.ORIGIN;
    
    this.setViewMode(nextMode, 'user');
    return nextMode;
  }

  /**
   * 设置翻译引擎管理器
   */
  setEngineManager(engineManager: any): void {
    this.options.engineManager = engineManager;
    
    // 根据当前模式决定是否启动健康检查
    if (this.isTranslationVisible()) {
      engineManager.startHealthCheck();
      this.log('Engine manager set and health check started');
    } else {
      this.log('Engine manager set (health check not started in origin mode)');
    }
  }

  /**
   * 获取当前视图模式
   */
  getCurrentMode(): ViewMode {
    return this.currentMode;
  }

  /**
   * 获取上一个视图模式
   */
  getPreviousMode(): ViewMode {
    return this.previousMode;
  }

  /**
   * 检查是否显示翻译内容
   */
  isTranslationVisible(): boolean {
    return this.currentMode === ViewMode.DUAL || this.currentMode === ViewMode.TRANS;
  }

  /**
   * 检查是否显示原文内容
   */
  isOriginVisible(): boolean {
    return this.currentMode === ViewMode.ORIGIN || this.currentMode === ViewMode.DUAL;
  }

  /**
   * 应用视图模式到DOM
   */
  private applyMode(mode: ViewMode, trigger: 'user' | 'auto' | 'restore'): void {
    const startTime = performance.now();

    // 设置根元素属性
    document.documentElement.setAttribute(this.ATTRIBUTE_NAME, mode);

    // 智能Observer管理
    this.manageObserver(mode);

    // 管理翻译引擎健康检查
    this.manageEngineHealthCheck(mode);

    // 触发自定义事件
    this.dispatchModeChangeEvent(mode, trigger);

    const duration = performance.now() - startTime;
    this.log('Mode applied', { mode, trigger, duration: `${duration.toFixed(2)}ms` });
  }

  /**
   * 管理翻译引擎健康检查
   */
  private manageEngineHealthCheck(mode: ViewMode): void {
    if (!this.options.engineManager) {
      return;
    }

    // 仅在需要翻译的模式下启用健康检查，原文模式禁用以避免不必要的API调用
    if (mode === ViewMode.ORIGIN) {
      this.options.engineManager.stopHealthCheck();
      this.log('Engine health check stopped (origin mode)');
    } else if (this.isTranslationVisible()) {
      this.options.engineManager.startHealthCheck();
      this.log('Engine health check started (translation mode)');
    }
  }

  /**
   * 智能管理MutationObserver
   */
  private manageObserver(mode: ViewMode): void {
    if (!this.options.enableDOMObserver) {
      return;
    }

    // 仅在dual/trans模式启用Observer，origin模式禁用以节省资源
    if (mode === ViewMode.ORIGIN) {
      this.disconnectObserver();
    } else if (this.isTranslationVisible()) {
      this.setupDOMObserver();
    }
  }

  /**
   * 设置DOM变化监听
   */
  private setupDOMObserver(): void {
    if (this.observer) {
      return; // 已经存在
    }

    this.observer = new MutationObserver((mutations) => {
      this.handleDOMChanges(mutations);
    });

    // 配置观察选项 - 减少监听范围以提高性能
    this.observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false, // 不监听属性变化
      characterData: false, // 不监听文本变化
      attributeOldValue: false,
      characterDataOldValue: false
    });

    this.log('DOM observer started');
  }

  /**
   * 断开DOM监听
   */
  private disconnectObserver(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = undefined;
      this.log('DOM observer disconnected');
    }
  }

  /**
   * 处理DOM变化
   */
  private handleDOMChanges(mutations: MutationRecord[]): void {
    let hasRelevantChanges = false;

    for (const mutation of mutations) {
      if (mutation.type === 'childList') {
        // 检查是否有新增的可翻译节点
        const addedNodes = Array.from(mutation.addedNodes);
        const relevantNodes = addedNodes.filter(node => 
          node.nodeType === Node.ELEMENT_NODE &&
          this.isTranslatableNode(node as HTMLElement)
        );

        if (relevantNodes.length > 0) {
          hasRelevantChanges = true;
          this.log('New translatable nodes detected', { count: relevantNodes.length });
          break;
        }
      }
    }

    // 触发新节点事件
    if (hasRelevantChanges) {
      this.dispatchNewNodesEvent();
    }
  }

  /**
   * 检查节点是否可翻译
   */
  private isTranslatableNode(element: HTMLElement): boolean {
    const translatableSelectors = 'p, h1, h2, h3, h4, h5, h6, li, td, th';
    const excludeSelectors = '.lu-wrapper, .lu-block, .notranslate, [data-no-translate]';

    return element.matches(translatableSelectors) && 
           !element.matches(excludeSelectors);
  }

  /**
   * 触发模式变化自定义事件
   */
  private dispatchModeChangeEvent(mode: ViewMode, trigger: 'user' | 'auto' | 'restore'): void {
    // Create CustomEvent compatible with both browser and test environments
    let event: Event;

    try {
      // Try modern CustomEvent constructor
      event = new CustomEvent('lu-view-mode-change', {
        detail: {
          mode,
          trigger,
          timestamp: Date.now()
        },
        bubbles: false
      });
    } catch (error) {
      // Fallback for environments that don't support CustomEvent constructor
      event = document.createEvent('CustomEvent');
      (event as any).initCustomEvent('lu-view-mode-change', false, false, {
        mode,
        trigger,
        timestamp: Date.now()
      });
    }

    document.dispatchEvent(event);
  }

  /**
   * 触发新节点检测事件
   */
  private dispatchNewNodesEvent(): void {
    const event = new CustomEvent('lu-new-nodes-detected', {
      detail: {
        timestamp: Date.now(),
        currentMode: this.currentMode
      },
      bubbles: false
    });

    document.dispatchEvent(event);
  }

  /**
   * 保存模式到存储
   */
  private saveMode(mode: ViewMode): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, mode);
    } catch (error) {
      this.log('Failed to save mode', error);
    }
  }

  /**
   * 从存储加载模式
   */
  private loadSavedMode(): ViewMode | null {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY);
      if (saved && Object.values(ViewMode).includes(saved as ViewMode)) {
        return saved as ViewMode;
      }
    } catch (error) {
      this.log('Failed to load saved mode', error);
    }
    return null;
  }

  /**
   * 重置为默认模式
   */
  reset(): void {
    this.setViewMode(this.options.defaultMode, 'auto');
    this.log('Controller reset');
  }

  /**
   * 销毁控制器
   */
  destroy(): void {
    this.disconnectObserver();
    
    // 移除DOM属性
    document.documentElement.removeAttribute(this.ATTRIBUTE_NAME);
    
    this.log('Controller destroyed');
  }

  /**
   * 获取当前状态信息
   */
  getState(): {
    currentMode: ViewMode;
    previousMode: ViewMode;
    isObserverActive: boolean;
    translationVisible: boolean;
    originVisible: boolean;
  } {
    return {
      currentMode: this.currentMode,
      previousMode: this.previousMode,
      isObserverActive: !!this.observer,
      translationVisible: this.isTranslationVisible(),
      originVisible: this.isOriginVisible()
    };
  }

  /**
   * 添加模式变化监听器
   */
  addModeChangeListener(callback: (event: ViewModeChangeEvent) => void): () => void {
    const handler = (event: CustomEvent) => {
      const detail = event.detail;
      callback({
        previousMode: this.previousMode,
        currentMode: detail.mode,
        timestamp: detail.timestamp,
        trigger: detail.trigger
      });
    };

    document.addEventListener('lu-view-mode-change', handler as EventListener);

    // 返回清理函数
    return () => {
      document.removeEventListener('lu-view-mode-change', handler as EventListener);
    };
  }

  /**
   * 添加新节点检测监听器
   */
  addNewNodesListener(callback: () => void): () => void {
    const handler = () => callback();
    document.addEventListener('lu-new-nodes-detected', handler);

    return () => {
      document.removeEventListener('lu-new-nodes-detected', handler);
    };
  }

  /**
   * 调试日志
   */
  private log(message: string, data?: any): void {
    if (this.options.debug) {
      console.log('🔧 [debug|DEBUG] ' + message, data || '');
    }
  }
}

/**
 * 全局视图控制器实例
 */
let globalController: ViewModeController | null = null;

/**
 * 获取全局视图控制器实例
 */
export function getViewModeController(): ViewModeController {
  if (!globalController) {
    globalController = new ViewModeController();
  }
  return globalController;
}

/**
 * 设置全局视图控制器
 */
export function setViewModeController(controller: ViewModeController): void {
  if (globalController) {
    globalController.destroy();
  }
  globalController = controller;
}

/**
 * 便捷的模式切换函数
 */
export function toggleViewMode(): ViewMode {
  return getViewModeController().toggleMode();
}

/**
 * 便捷的原文/双语切换函数
 */
export function toggleOriginDual(): ViewMode {
  return getViewModeController().toggleOriginDual();
}

/**
 * 设置视图模式的便捷函数
 */
export function setViewMode(mode: ViewMode): void {
  getViewModeController().setViewMode(mode);
}