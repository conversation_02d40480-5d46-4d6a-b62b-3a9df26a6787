/**
 * 交互事件处理器 - 负责快捷键和全局事件处理
 * 从 content.ts 中提取的事件处理逻辑
 */

// debugContent, debugSlider 导入已移除，使用手动 console.log
import type { HighlightManager } from './highlight-manager';
import type { TooltipManager } from './tooltip-manager';
import type { SliderManager } from './slider-manager';

export interface InteractionHandlersOptions {
  // 可以添加更多配置选项
}

export interface ManagerInstances {
  highlightManager: HighlightManager;
  tooltipManager: TooltipManager;
  sliderManager: SliderManager;
}

export class InteractionHandlers {
  private options: InteractionHandlersOptions;
  private managers: ManagerInstances;

  constructor(managers: ManagerInstances, options: InteractionHandlersOptions = {}) {
    this.managers = managers;
    this.options = options;
  }

  /**
   * 初始化交互事件处理
   */
  initialize(): void {
    this.setupKeyboardHandlers();
    this.setupCleanupHandlers();
    console.log('✅ [interaction-handlers|STARTUP] Interaction handlers initialized');
  }

  /**
   * 设置键盘事件处理
   */
  private setupKeyboardHandlers(): void {
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
  }

  /**
   * 设置清理事件处理
   */
  private setupCleanupHandlers(): void {
    // Clean up on page unload
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
  }

  /**
   * 处理键盘事件
   */
  private async handleKeyDown(event: KeyboardEvent): Promise<void> {
    // ESC键关闭tooltip
    if (event.code === 'Escape') {
      const tooltipState = this.managers.tooltipManager.getState();
      if (tooltipState.isTooltipVisible) {
        event.preventDefault();
        await this.managers.tooltipManager.forceHide();
        console.log('✅ [interaction-handlers|INFO] Tooltip closed via ESC key');
        return;
      }
    }

    // Ctrl+Shift+C 清除所有高亮
    if (event.ctrlKey && event.shiftKey && event.code === 'KeyC') {
      event.preventDefault();
      try {
        await this.managers.highlightManager.clearAll();
        console.log('✅ [interaction-handlers|INFO] Highlights cleared via keyboard shortcut');
      } catch (error) {
        console.log('❌ [interaction-handlers|ERROR] Error clearing highlights via keyboard:', error);
      }
      return;
    }

    // Ctrl+Shift+S 显示/隐藏 Lucid Slider（设置面板）
    if (event.ctrlKey && event.shiftKey && event.code === 'KeyS') {
      event.preventDefault();
      try {
        await this.managers.sliderManager.toggleSlider();
        const state = this.managers.sliderManager.getState();
        if (state.isVisible) {
          console.log('✅ [interaction-handlers|INFO] Lucid Slider shown via keyboard shortcut');
        } else {
          console.log('✅ [interaction-handlers|INFO] Lucid Slider hidden via keyboard shortcut');
        }
      } catch (error) {
        console.log('❌ [interaction-handlers|ERROR] Error toggling Lucid Slider via keyboard:', error);
      }
      return;
    }
  }

  /**
   * 处理页面卸载前的清理
   */
  private handleBeforeUnload(): void {
    try {
      // 清理高亮器
      this.managers.highlightManager.destroy();
      
      // 清理其他管理器
      this.managers.tooltipManager.destroy();
      this.managers.sliderManager.destroy();
      
      console.log('✅ [interaction-handlers|INFO] All managers cleaned up before page unload');
    } catch (error) {
      console.log('❌ [interaction-handlers|ERROR] Error during cleanup:', error);
    }
  }

  /**
   * 获取当前快捷键状态
   */
  public getShortcutHelp(): string[] {
    return [
      '⌨️  Keyboard shortcuts:',
      '  Ctrl+Shift+C - Clear all highlights',
      '  Ctrl+Shift+S - Toggle Lucid Slider (settings panel)',
      '  ESC - Close tooltip',
      '🖱️  User Interface:',
      '  Extension Icon Click - Toggle Lucid Slider',
      '  Right-click → "打开 Lucid 设置" - Toggle Lucid Slider',
      '🖱️  Mouse interactions:',
      '  Hover on highlighted word - Show tooltip (50ms delay)',
      '  Move to tooltip area - Keep tooltip visible',
      '  Leave tooltip area - Hide tooltip (500ms delay)',
      '  Click outside - Close tooltip immediately'
    ];
  }

  /**
   * 销毁交互处理器
   */
  public destroy(): void {
    // 移除事件监听器（实际上由于使用了bind，这里需要保存引用才能移除）
    // 为了简化，这里不移除事件监听器，由页面卸载时自动清理
    console.log('🧹 [interaction-handlers|DEBUG] Interaction handlers destroyed');
  }
}

/**
 * 初始化交互事件处理器的便捷函数
 */
export function setupInteractionHandlers(
  managers: ManagerInstances, 
  options?: InteractionHandlersOptions
): InteractionHandlers {
  const handlers = new InteractionHandlers(managers, options);
  handlers.initialize();
  return handlers;
}