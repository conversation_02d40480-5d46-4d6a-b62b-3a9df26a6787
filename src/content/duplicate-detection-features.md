# Enhanced Duplicate Detection Logic

This document explains the comprehensive duplicate detection mechanisms implemented in both `DOMInjector` and `EnhancedDOMInjector` classes.

## Overview

The enhanced duplicate detection system prevents race conditions and ensures reliable translation injection by implementing multiple layers of checks and atomic operations.

## Key Features

### 1. Multi-Layer Duplicate Detection

#### Basic Checks
- **`data-lu-translated` attribute**: Detects already translated elements
- **`.lu-wrapper` element**: Detects existing translation wrappers
- **`data-lu-processing` attribute**: Detects elements currently being processed

#### Enhanced Checks (EnhancedDOMInjector)
- **Parent element validation**: Prevents nested translations by checking if parent elements are already translated
- **Children element validation**: Avoids translating containers that already have translated children
- **Comprehensive logging**: Detailed skip reason logging with element context

### 2. Atomic Operations for Race Condition Prevention

#### Processing State Management
```javascript
// Step 1: Initial duplicate check
if (this.hasExistingTranslation(targetElement)) {
  return { success: false, error: 'Translation already exists or processing' };
}

// Step 2: Atomic marking (prevents race conditions)
targetElement.setAttribute('data-lu-processing', Date.now().toString());

// Step 3: Double-check after marking
if (this.hasExistingTranslationAfterMark(targetElement)) {
  targetElement.removeAttribute('data-lu-processing');
  return { success: false, error: 'Race condition detected' };
}

// Step 4: Proceed with injection...
```

#### Guaranteed Cleanup
- Processing attributes are cleaned up in **all code paths**:
  - Success: After marking as translated
  - Failure: In failure handlers
  - Exception: In catch blocks

### 3. Enhanced Logging and Debugging

#### Skip Reason Logging (EnhancedDOMInjector)
```javascript
this.logSkipReason(element, 'already-translated', {
  timestamp: element.getAttribute('data-lu-timestamp'),
  targetLang: element.getAttribute('data-lu-target-lang')
});
```

Available skip reasons:
- `already-translated`: Element has `data-lu-translated` attribute
- `currently-processing`: Element has `data-lu-processing` attribute
- `has-wrapper`: Element contains `.lu-wrapper` child
- `parent-translated`: Parent element is already translated
- `has-translated-children`: Element has translated child elements

### 4. Processing State Utilities

#### State Inspection
```javascript
// Check if element is being processed
injector.isProcessing(element);

// Get comprehensive translation info
const info = injector.getTranslationInfo(element);
// Returns: { isTranslated, isProcessing, targetLanguage, timestamp, processingStartTime }
```

#### Cleanup Utilities
```javascript
// Clean up stuck processing states (with timeout)
injector.cleanupStuckProcessingStates(30000); // 30 second timeout

// Force clean specific element
injector.forceCleanProcessingState(element);

// Clean all translations and processing states
injector.clearAllTranslations();
```

## Implementation Details

### Attribute Schema

#### Translation State Attributes
- `data-lu-translated="true"`: Marks element as translated
- `data-lu-timestamp="1234567890"`: Translation completion timestamp
- `data-lu-target-lang="zh-CN"`: Target language for translation

#### Processing State Attributes
- `data-lu-processing="1234567890"`: Processing start timestamp

### Performance Considerations

#### Optimizations
- **Early exit**: Most common cases (already translated) are checked first
- **Selective parent traversal**: Parent checking stops at document.body
- **Efficient DOM queries**: Uses attribute selectors for fast lookups
- **Batch cleanup**: Cleanup operations handle multiple elements efficiently

#### Memory Management
- **Automatic cleanup**: Processing states are automatically cleaned up
- **Timeout-based cleanup**: Stuck states are cleaned after configurable timeout
- **Exception-safe**: All cleanup happens in try-finally patterns

## Usage Examples

### Basic Usage
```javascript
const injector = new DOMInjector();
const result = await injector.injectTranslation(element, 'Translation text');

if (!result.success) {
  console.log('Injection skipped:', result.error);
}
```

### Enhanced Usage with Custom Rules
```javascript
const enhancedInjector = new EnhancedDOMInjector();
const result = await enhancedInjector.injectTranslation(element, 'Translation', {
  debug: true, // Enable detailed logging
  forceStrategy: InjectionStrategy.INLINE // Override auto-detection
});
```

### Maintenance and Monitoring
```javascript
// Clean up stuck states every 5 minutes
setInterval(() => {
  injector.cleanupStuckProcessingStates(30000);
}, 5 * 60 * 1000);

// Monitor processing states
const processingElements = document.querySelectorAll('[data-lu-processing]');
if (processingElements.length > 10) {
  console.warn('High number of processing elements detected');
}
```

## Testing

The duplicate detection logic is comprehensively tested in `/src/content/__tests__/duplicate-detection.test.ts` with coverage for:
- Basic duplicate detection scenarios
- Race condition prevention
- Atomic operation verification
- Processing state management
- Cleanup operations
- Enhanced logging features

All tests validate that the system correctly prevents duplicate translations while maintaining robust error handling and cleanup procedures.