// JWT认证相关类型定义

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number; // 秒数
}

export interface JWTPayload {
  sub: string; // 用户ID
  email: string;
  iat: number; // 签发时间
  exp: number; // 过期时间
  jti: string; // JWT ID
  clientType: 'extension';
  scopes: string[];
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  tokens: TokenPair | null;
  loading: boolean;
  error: string | null;
}

export interface LoginRequest {
  email: string;
  password: string;
  clientType: 'extension';
  redirect: false;
}

export interface LoginResponse {
  success: boolean;
  user?: User;
  accessToken?: string;
  refreshToken?: string;
  expiresIn?: number;
  error?: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name?: string;
  clientType: 'extension';
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  success: boolean;
  data?: TokenPair;
  error?: string;
}

export interface AuthConfig {
  baseURL: string;
  clientType: 'extension';
  tokenStorageKeys: {
    accessToken: string;
    refreshToken: string;
    expiresAt: string;
    user: string;
  };
}

// 认证事件类型
export type AuthEventType = 
  | 'login'
  | 'logout' 
  | 'token_refresh'
  | 'auth_error'
  | 'session_expired';

export interface AuthEvent {
  type: AuthEventType;
  data?: any;
  timestamp: number;
}

// 消息通信类型 (Background <-> Content/Popup)
export interface AuthMessage {
  type: 'AUTH_STATE_REQUEST' | 'AUTH_STATE_RESPONSE' | 'AUTH_LOGIN' | 'AUTH_LOGOUT';
  payload?: any;
}

// 导出错误处理相关类型
export type { AuthError, AuthErrorCode } from './errors';
export { AuthErrorHandler } from './errors';