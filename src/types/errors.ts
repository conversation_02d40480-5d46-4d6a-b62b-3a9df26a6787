/**
 * 认证相关错误类型
 */
export class AuthError extends Error {
  constructor(
    message: string,
    public readonly code: AuthErrorCode,
    public readonly userMessage: string,
    public readonly originalError?: unknown
  ) {
    super(message);
    this.name = 'AuthError';
  }

  /**
   * 创建网络错误
   */
  static network(message: string, userMessage: string = '网络连接失败，请检查网络设置'): AuthError {
    return new AuthError(message, 'NETWORK_ERROR', userMessage);
  }

  /**
   * 创建认证失败错误
   */
  static authFailed(message: string, userMessage: string = '登录失败，请检查邮箱和密码'): AuthError {
    return new AuthError(message, 'AUTH_FAILED', userMessage);
  }

  /**
   * 创建令牌过期错误
   */
  static tokenExpired(message: string, userMessage: string = '登录已过期，请重新登录'): AuthError {
    return new AuthError(message, 'TOKEN_EXPIRED', userMessage);
  }

  /**
   * 创建存储错误
   */
  static storage(message: string, userMessage: string = '数据存储失败，请重试'): AuthError {
    return new AuthError(message, 'STORAGE_ERROR', userMessage);
  }

  /**
   * 创建验证错误
   */
  static validation(message: string, userMessage: string = '输入信息无效，请检查后重试'): AuthError {
    return new AuthError(message, 'VALIDATION_ERROR', userMessage);
  }

  /**
   * 创建服务器错误
   */
  static server(message: string, userMessage: string = '服务器错误，请稍后重试'): AuthError {
    return new AuthError(message, 'SERVER_ERROR', userMessage);
  }

  /**
   * 创建未知错误
   */
  static unknown(message: string, originalError?: unknown, userMessage: string = '未知错误，请重试'): AuthError {
    return new AuthError(message, 'UNKNOWN_ERROR', userMessage, originalError);
  }
}

/**
 * 认证错误代码
 */
export type AuthErrorCode = 
  | 'NETWORK_ERROR'      // 网络连接错误
  | 'AUTH_FAILED'        // 认证失败
  | 'TOKEN_EXPIRED'      // 令牌过期
  | 'STORAGE_ERROR'      // 存储错误
  | 'VALIDATION_ERROR'   // 验证错误
  | 'SERVER_ERROR'       // 服务器错误
  | 'UNKNOWN_ERROR';     // 未知错误

/**
 * 错误处理工具函数
 */
export const AuthErrorHandler = {
  /**
   * 处理并标准化错误
   */
  handle(error: unknown, context: string = 'Auth'): AuthError {
    console.error(`[${context}] Error:`, error);

    if (error instanceof AuthError) {
      return error;
    }

    if (error instanceof Error) {
      // 网络相关错误
      if (error.message.includes('fetch') || error.message.includes('network')) {
        return AuthError.network(error.message);
      }

      // 存储相关错误
      if (error.message.includes('storage') || error.message.includes('Storage')) {
        return AuthError.storage(error.message);
      }

      return AuthError.unknown(error.message, error);
    }

    return AuthError.unknown('Unknown error occurred', error);
  },

  /**
   * 从HTTP响应创建错误
   */
  fromResponse(response: Response, defaultMessage: string): AuthError {
    if (response.status === 401) {
      return AuthError.authFailed(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    if (response.status === 400) {
      return AuthError.validation(`HTTP ${response.status}: ${response.statusText}`);
    }

    if (response.status >= 500) {
      return AuthError.server(`HTTP ${response.status}: ${response.statusText}`);
    }

    if (response.status >= 400) {
      return AuthError.network(`HTTP ${response.status}: ${response.statusText}`);
    }

    return AuthError.unknown(defaultMessage);
  },

  /**
   * 获取用户友好的错误消息
   */
  getUserMessage(error: unknown): string {
    if (error instanceof AuthError) {
      return error.userMessage;
    }

    if (error instanceof Error) {
      // 一些常见错误的用户友好消息
      if (error.message.includes('fetch')) {
        return '网络连接失败，请检查网络设置';
      }
    }

    return '操作失败，请重试';
  }
};