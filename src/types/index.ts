/**
 * 智能注入翻译系统类型定义
 * 集成智能注入规则引擎
 */

import { InjectionStrategy } from '../core/injection-rules';

export type TranslationFormat = 'text' | 'html';
export type SecurityLevel = 'strict' | 'normal' | 'lenient';
export type RenderMode = 'append' | 'replace';

/**
 * 翻译选项
 */
export interface TranslationOptions {
  format?: TranslationFormat;
  securityLevel?: SecurityLevel;
  renderMode?: RenderMode;
  timeout?: number;
  enableCache?: boolean;
  debug?: boolean;
}

/**
 * 翻译结果
 */
export interface TranslationResult {
  success: boolean;
  element: HTMLElement;
  originalText: string;
  translatedText?: string;
  error?: string;
  duration: number;
}

/**
 * 批量翻译结果
 */
export interface BatchTranslationResult {
  totalCount: number;
  successCount: number;
  failureCount: number;
  results: TranslationResult[];
  totalDuration: number;
}

/**
 * 文本提取结果
 */
export interface TextExtraction {
  element: HTMLElement;
  text: string;
  hasHtmlStructure: boolean;
  htmlContent?: string;
  links: LinkInfo[];
}

/**
 * 链接信息
 */
export interface LinkInfo {
  href: string;
  text: string;
  attributes: Record<string, string>;
}

/**
 * 渲染操作
 */
export interface RenderOperation {
  element: HTMLElement;
  content: string;
  format: TranslationFormat;
  mode: RenderMode;
}

/**
 * 缓存项
 */
export interface CacheEntry {
  value: string;
  timestamp: number;
  accessCount: number;
}

/**
 * 配置选项
 */
export interface TranslationConfig {
  security: {
    maxLength: number;
    allowedTags: string[];
    strictMode: boolean;
  };
  cache: {
    maxSize: number;
    ttl: number; // 毫秒
    enabled: boolean;
  };
  performance: {
    batchSize: number;
    concurrency: number;
    debounceMs: number;
  };
  rendering: {
    defaultMode: RenderMode;
    enableHtmlStructure: boolean;
    preserveLinks: boolean;
  };
  // 智能注入相关配置
  enableSmartInjection?: boolean;
  forceStrategy?: InjectionStrategy;
  targetLanguage?: string;
  debug: boolean;
}

/**
 * 错误类型
 */
export enum TranslationErrorType {
  VALIDATION_FAILED = 'validation_failed',
  SECURITY_BLOCKED = 'security_blocked',
  TRANSLATION_FAILED = 'translation_failed',
  RENDERING_FAILED = 'rendering_failed',
  TIMEOUT = 'timeout',
  NETWORK_ERROR = 'network_error'
}

/**
 * 翻译错误
 */
export class TranslationError extends Error {
  constructor(
    public type: TranslationErrorType,
    message: string,
    public recoverable: boolean = true
  ) {
    super(message);
    this.name = 'TranslationError';
  }
}

/**
 * 统计信息
 */
export interface TranslationStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  cacheHitRate: number;
  securityBlocked: number;
}