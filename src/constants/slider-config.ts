/**
 * Slider 配置常量
 * 统一管理Lucid Slider的配置参数，避免硬编码
 */

// 尺寸配置
export const SLIDER_DIMENSIONS = {
  // Slider 宽度
  WIDTH: 380,
  // 移动端时使用全屏宽度
  MOBILE_WIDTH: '100vw',
  // 高度总是全屏
  HEIGHT: '100vh'
} as const;

// 定位配置
export const SLIDER_POSITIONING = {
  // Z-index 层级 (保证在最顶层)
  Z_INDEX: 2147483647,
  // 固定定位在右侧
  POSITION: 'fixed' as const,
  TOP: 0,
  RIGHT: 0
} as const;

// 响应式断点
export const BREAKPOINTS = {
  // 移动端断点
  MOBILE: 767
} as const;

// 动画配置
export const SLIDER_ANIMATIONS = {
  // 快速过渡时间
  TRANSITION_FAST: '200ms cubic-bezier(0.4, 0, 0.2, 1)',
  // 慢速过渡时间
  TRANSITION_SLOW: '350ms cubic-bezier(0.4, 0, 0.2, 1)'
} as const;

// 颜色配置
export const SLIDER_COLORS = {
  // 品牌橙色
  BRAND_COLOR: '#f97316',
  // 成功绿色
  SUCCESS_COLOR: '#22c55e', 
  // 危险/喜爱红色
  DANGER_COLOR: '#ef4444',
  // 新标签背景色
  NEW_TAG_BG: 'rgba(59, 130, 246, 0.2)',
  // 新标签文字色
  NEW_TAG_TEXT: '#3b82f6'
} as const;

// 背景和玻璃态效果配置
export const SLIDER_GLASS_EFFECT = {
  // 背景模糊效果
  BACKDROP_FILTER: 'blur(28px) saturate(150%)',
  // WebKit 兼容性
  WEBKIT_BACKDROP_FILTER: 'blur(28px) saturate(150%)',
  // 半透明背景色
  BACKGROUND: 'rgba(28, 28, 30, 0.92)',
  // 边框渐变
  BORDER_GRADIENT: 'linear-gradient(to bottom, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.03))',
  // 阴影
  BOX_SHADOW: '0 8px 40px rgba(0, 0, 0, 0.6)'
} as const;

// 开关控件配置
export const SWITCH_CONFIG = {
  WIDTH: 44,
  HEIGHT: 24,
  THUMB_SIZE: 20,
  THUMB_OFFSET: 2,
  // 激活时滑块移动距离
  ACTIVE_TRANSFORM: 20
} as const;

// Tooltip交互配置
export const TOOLTIP_CONFIG = {
  // 显示延迟 (ms) - 立即显示，不延迟
  SHOW_DELAY: 0,
  // 隐藏延迟 (ms)
  HIDE_DELAY: 500,
  // 容错边距 (px)
  GRACE_MARGIN: 20,
  // 鼠标移动节流时间 (ms)
  MOUSE_THROTTLE: 100
} as const;

// CSS 类名常量 (避免字符串硬编码)
export const CSS_CLASSES = {
  // Slider 主容器
  SLIDER: 'lu-slide',
  // 隐藏状态
  HIDDEN: 'hidden',
  // 头部
  HEADER: 'lu-slider-header',
  // 内容区
  CONTENT: 'lu-slider-content',
  // 底部
  FOOTER: 'lu-slider-footer',
  // 卡片
  CARD: 'lu-card',
  // 设置项
  SETTING_ITEM: 'lu-setting-item',
  // 开关
  SWITCH: 'lu-switch',
  SWITCH_ACTIVE: 'active',
  SWITCH_THUMB: 'lu-switch-thumb'
} as const;

// 导出所有配置的类型
export type SliderDimensions = typeof SLIDER_DIMENSIONS;
export type SliderPositioning = typeof SLIDER_POSITIONING;
export type SliderAnimations = typeof SLIDER_ANIMATIONS;
export type SliderColors = typeof SLIDER_COLORS;
export type SliderGlassEffect = typeof SLIDER_GLASS_EFFECT;
export type SwitchConfig = typeof SWITCH_CONFIG;
export type TooltipConfig = typeof TOOLTIP_CONFIG;
export type CssClasses = typeof CSS_CLASSES;