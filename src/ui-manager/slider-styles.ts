/**
 * Slider Styles - 完整的CSS样式字符串
 * 从 Slider.module.css 提取的完整样式，用于Shadow DOM注入
 */

export const SLIDER_CSS = `
/* 基础样式与变量 */
:host, :root {
  /* 动画过渡 */
  --transition-fast: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-all-fast: all var(--transition-fast);
  --transition-bg-fast: background-color var(--transition-fast);
  
  /* 品牌颜色 */
  --brand-color: #f97316; /* 品牌橙色 */
  --success-color: #22c55e; /* 成功绿色 */
  --danger-color: #ef4444; /* 危险/喜爱红色 */
  --new-tag-bg: rgba(59, 130, 246, 0.2);
  --new-tag-text: #3b82f6;
  
  /* 圆角半径 */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-full: 50%;
  
  /* 字体 */
  --font-family: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-weight-light: 200;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 间距 */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 12px;
  --space-lg: 16px;
  --space-xl: 20px;
  --space-2xl: 24px;
  
  /* 文字颜色 */
  --text-primary: #f5f5f5;
  --text-secondary: #e2e2e2;
  --text-muted: #c0c0c0;
  --text-disabled: #9e9e9e;
  --text-subtle: #757575;
  
  /* 背景颜色 */
  --bg-primary: rgba(45, 45, 48, 0.7);
  --bg-secondary: rgba(30, 30, 32, 0.8);
  --bg-hover: rgba(255, 255, 255, 0.1);
  --bg-overlay: rgba(255, 255, 255, 0.05);
  
  /* 边框颜色 */
  --border-primary: rgba(255, 255, 255, 0.08);
  --border-secondary: rgba(255, 255, 255, 0.15);
  --border-hover: rgba(255, 255, 255, 0.15);
}

/* 字体导入 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100..900&display=swap');

/* 全局字体配置导入 */
/* 注意：在实际使用中，这个CSS会通过其他方式注入 */

/* 全局样式 */
body {
  margin: 0;
  background-color: #111113;
  background-image: radial-gradient(
      circle at 1px 1px,
      rgba(255, 255, 255, 0.04) 1px,
      transparent 0
    ),
    radial-gradient(
      circle at 10px 10px,
      rgba(255, 255, 255, 0.04) 1px,
      transparent 0
    );
  background-size: 20px 20px;
  font-family: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  font-weight: 200;
  color: #e2e2e2;
}

#root {
  isolation: isolate;
}

#open-slider-btn {
  padding: 10px 20px;
  border-radius: 8px;
  border: none;
  background-color: var(--brand-color);
  color: white;
  cursor: pointer;
  font-weight: var(--font-weight-light);
}

/* 滑动面板容器 - 现在使用直接类名 */
.lu-slide {
  position: fixed;
  top: 0;
  right: 0;
  width: 380px;
  height: 100vh;
  z-index: 2147483647;
  backdrop-filter: blur(28px) saturate(150%);
  -webkit-backdrop-filter: blur(28px) saturate(150%);
  background: rgba(28, 28, 30, 0.92);
  border-left: 1px solid;
  border-image: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.15),
      rgba(255, 255, 255, 0.03)
    )
    1;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.6);
  border-radius: 16px 0 0 16px;
  transition: transform var(--transition-slow), opacity var(--transition-slow);
  transform: translateX(0);
  opacity: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: var(--font-family);
  font-weight: var(--font-weight-light);
}

.lu-slide.hidden {
  transform: translateX(100%);
  opacity: 0;
  pointer-events: none;
}

@media (max-width: 767px) {
  .lu-slide {
    width: 100vw;
    border-radius: 0;
    border-left: none;
  }
}

/* 头部 */
.lu-slider-header {
  flex-shrink: 0;
  padding: 20px 24px;
  border-bottom: 1px solid;
  border-image: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.15),
      rgba(255, 255, 255, 0.03)
    )
    1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.lu-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}
.lu-logo {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--brand-color), #ff9d57);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 18px;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(249, 115, 22, 0.3);
}
.lu-title {
  color: #f5f5f5;
  font-size: 18px;
  font-weight: 600;
}
.lu-vip-tag {
  padding: 4px 8px;
  background: rgba(250, 204, 21, 0.15);
  color: #facd15;
  font-size: 10px;
  font-weight: 700;
  border-radius: 6px;
  text-transform: uppercase;
  border: 1px solid rgba(250, 204, 21, 0.2);
}
.lu-header-button {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--text-disabled);
  cursor: pointer;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-all-fast);
}
.lu-header-button:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
  transform: scale(1.1);
}

/* 动态头部 */
.lu-slider-header #header-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

/* 内容区域 */
.lu-slider-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px 24px;
}

/* 卡片 */
.lu-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  transition: var(--transition-all-fast);
}
.lu-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 24px rgba(0, 0, 0, 0.3);
  border-color: var(--border-hover);
}

/* "我的" 视图样式 */
.lu-stats-group {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}
.lu-stat-card {
  flex: 1;
  padding: 16px;
  text-align: center;
  cursor: pointer;
}
.lu-stat-number {
  color: #ffffff;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}
.lu-stat-title {
  color: #9e9e9e;
  font-size: 12px;
}

/* 单词列表 */
.lu-word-section .lu-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.lu-section-title {
  color: #f0f0f0;
  font-size: 16px;
  font-weight: 600;
}
.lu-word-count {
  color: #9e9e9e;
  font-size: 14px;
}
.lu-word-item {
  padding: 0;
  cursor: pointer;
  background: rgba(45, 45, 48, 0.7);
}
.lu-word-item-header {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.lu-word-item-header:hover .lu-word-text {
  color: var(--brand-color);
}
.lu-word-text-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}
.lu-word-text {
  color: #f0f0f0;
  font-size: 14px;
  font-weight: 500;
}
.lu-expand-icon {
  color: #9e9e9e;
  transition: transform var(--transition-slow);
}
.lu-word-item.expanded .lu-expand-icon {
  transform: rotate(180deg);
}
.lu-word-actions {
  display: flex;
  gap: 8px;
}
.lu-word-action {
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  color: var(--text-disabled);
  cursor: pointer;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-all-fast);
}
.lu-word-action:hover {
  background: var(--bg-hover);
  transform: scale(1.1);
  color: var(--text-primary);
}
.lu-word-action.favorite.active {
  color: var(--danger-color);
}
.lu-word-action.master.active {
  color: var(--success-color);
}
.lu-word-details {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-slow), padding var(--transition-slow);
  padding: 0 16px;
  border-top: 1px solid transparent;
  color: #c0c0c0;
  font-size: 13px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.lu-word-item.expanded .lu-word-details {
  max-height: 600px;
  padding: 16px;
  border-top-color: rgba(255, 255, 255, 0.1);
}
.lu-word-detail-item {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}
.lu-word-detail-item strong {
  color: #9e9e9e;
  font-weight: 600;
  margin-right: 0px;
  display: inline-block;
  width: auto;
  min-width: 60px;
  text-transform: capitalize;
  flex-shrink: 0;
}
.lu-phonetic {
  font-family: "Lucida Sans Unicode", "Arial Unicode MS", sans-serif;
}
.lu-phonetic-group div {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.lu-phonetic-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.lu-phonetic-label {
  background-color: rgba(255, 255, 255, 0.1);
  color: #c0c0c0;
  font-size: 10px;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.15);
}
.lu-pos-section .lu-definitions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.lu-definition-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.lu-definition-cn {
  color: #e2e2e2;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
}
.lu-definition-en {
  color: #9e9e9e;
  line-height: 1.5;
  font-size: 13px;
}
.lu-word-forms-grid {
  display: grid;
  grid-template-columns: 100px 1fr;
  gap: 8px 16px;
  width: 100%;
}
.lu-word-form-name {
  color: #9e9e9e;
}
.lu-word-form-value {
  color: #e2e2e2;
  font-weight: 500;
}

/* "设置" & "账户" 视图样式 */
.lu-settings-section {
  margin-bottom: 24px;
}
.lu-settings-header {
  font-size: 12px;
  color: #9e9e9e;
  font-weight: 500;
  padding-left: 16px;
  margin-bottom: 8px;
}
.lu-settings-card {
  padding: 8px;
}
.lu-setting-item {
  display: flex;
  align-items: center;
  padding: var(--space-md) var(--space-sm);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-bg-fast);
}
.lu-settings-card .lu-setting-item:not(:last-child) {
  border-bottom: 1px solid var(--bg-overlay);
}
.lu-setting-item:hover {
  background-color: var(--bg-overlay);
}
.lu-setting-icon {
  width: 20px;
  height: 20px;
  color: #c0c0c0;
  margin-right: 16px;
}
.lu-setting-title {
  flex: 1;
  color: #f0f0f0;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}
.lu-setting-value {
  color: #9e9e9e;
  font-size: 14px;
  margin-right: 8px;
}
.lu-setting-arrow {
  width: 16px;
  height: 16px;
  color: #757575;
}
.lu-setting-item .lu-new-tag,
.lu-big-setting-details .lu-new-tag {
  font-size: 9px;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: var(--new-tag-bg);
  color: var(--new-tag-text);
  text-transform: uppercase;
}
.lu-user-info {
  display: flex;
  align-items: center;
}
.lu-user-info .lu-setting-value {
  display: flex;
  align-items: center;
  gap: 4px;
}
.lu-user-info .lu-verified-icon {
  color: var(--success-color);
}
.lu-account-view .lu-setting-item {
  padding: 16px 8px;
}
.lu-account-view .lu-setting-value {
  color: #f0f0f0;
}
.lu-account-view .lu-membership-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 6px;
  border: 1px solid #757575;
  color: #9e9e9e;
}
.lu-vip-tag.early-bird,
.lu-account-view .lu-membership-tag.early-bird {
  color: #818cf8;
  border-color: rgba(129, 140, 248, 0.4);
  background-color: rgba(129, 140, 248, 0.1);
}

.lu-logout-button {
  width: 100%;
  padding: 14px;
  border: none;
  background-color: rgba(45, 45, 48, 0.9);
  color: var(--danger-color);
  font-size: 14px;
  font-weight: 500;
  border-radius: 12px;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  margin-top: 24px;
}
.lu-logout-button:hover {
  background-color: rgba(60, 60, 63, 0.9);
}

/* Floating Button Setting Card */
.lu-floating-button-card {
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 130px;
  overflow: hidden;
}

.lu-floating-button-icon-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.lu-floating-button-icon-container svg {
  width: 120px;
  height: 120px;
  color: #f0f0f0;
  stroke-width: 1.5;
  opacity: 0.9;
  transform: rotate(-15deg);
}

.lu-floating-button-controls {
  display: flex;
  align-items: center;
  gap: 24px;
  padding-left: 24px;
}

.lu-floating-button-label {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  color: #e0e0e0;
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 4px;
}

/* --- Login View --- */
.lu-login-container {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.lu-login-box {
  width: 100%;
  max-width: 360px;
  background: rgba(30, 30, 32, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.5);
}

.lu-social-login-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.lu-social-login-btn {
  width: 100%;
  padding: 10px var(--space-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
  transition: var(--transition-all-fast);
}

.lu-social-login-btn:hover {
  border-color: var(--border-secondary);
}

.lu-social-login-btn svg {
  width: 20px;
  height: 20px;
}

.lu-social-login-btn.google {
  color: #fff;
  background-color: #333;
}
.lu-social-login-btn.google:hover {
  background-color: #444;
}

.lu-social-login-btn.github {
  color: #fff;
  background-color: #333;
}
.lu-social-login-btn.github:hover {
  background-color: #444;
}

.lu-divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 24px 0;
  color: #757575;
  font-size: 12px;
  font-weight: 500;
}

.lu-divider::before,
.lu-divider::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.lu-divider:not(:empty)::before {
  margin-right: 0.5em;
}

.lu-divider:not(:empty)::after {
  margin-left: 0.5em;
}

.lu-email-form {
  display: flex;
  flex-direction: column;
   margin-top: 12px;
}

.lu-email-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.lu-email-input-wrapper svg {
  position: absolute;
  left: 14px;
  color: #757575;
}

.lu-email-input {
  width: 100%;
  box-sizing: border-box;
  background-color: rgba(20, 20, 22, 0.7);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-lg) var(--space-md) 44px;
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
  transition: var(--transition-all-fast);
}

.lu-email-input::placeholder {
  color: #757575;
}

.lu-email-input:focus {
  border-color: var(--brand-color);
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
}

.lu-send-code-btn {
  width: 100%;
  padding: var(--space-md);
  border: none;
  background-color: var(--brand-color);
  color: white;
  font-size: 14px;
  font-weight: 600;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-bg-fast);
}

.lu-send-code-btn:hover {
  background-color: #fb8b48;
}

/* 开关 */
.lu-switch {
  position: relative;
  width: 44px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: background var(--transition-fast);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.lu-switch.active {
  background: var(--brand-color);
}
.lu-switch-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  transition: transform var(--transition-fast);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}
.lu-switch.active .lu-switch-thumb {
  transform: translateX(20px);
}

/* 底部操作栏 */
.lu-slider-footer {
  flex-shrink: 0;
  border-top: 1px solid;
  border-image: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.15),
      rgba(255, 255, 255, 0.03)
    )
    1;
  padding: 12px 24px;
  display: flex;
  gap: 12px;
}
.lu-action-button {
  flex: 1;
  padding: 10px;
  border: none;
  background: transparent;
  color: #9e9e9e;
  cursor: pointer;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  transition: all var(--transition-fast);
  font-size: 12px;
  font-weight: 500;
}
.lu-action-button:hover {
  background: rgba(255, 255, 255, 0.08);
  color: #ffffff;
}
.lu-action-button.active {
  color: var(--brand-color);
  background: rgba(249, 115, 22, 0.15);
}

/* 滚动条 */
.lu-slider-content::-webkit-scrollbar {
  width: 6px;
}
.lu-slider-content::-webkit-scrollbar-track {
  background: transparent;
}
.lu-slider-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 3px;
}
.lu-slider-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.25);
}

/* 现在所有组件都使用直接的CSS类名，无需CSS Module兼容性处理 */

`;