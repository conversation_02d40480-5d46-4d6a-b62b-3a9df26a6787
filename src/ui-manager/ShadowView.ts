/**
 * ShadowView - 简化的Shadow DOM实现
 * 使用<lucid>自定义标签包裹，样式通过外部CSS控制
 */

// ReactElement import removed - not used in this file
import { createRoot, Root } from 'react-dom/client';
import { ShadowViewOptions } from './types';
import { SLIDER_DIMENSIONS, SLIDER_POSITIONING } from '../constants/slider-config';
// debugUI 导入已移除，使用手动 console.log

export class ShadowView {
  private hostElement!: HTMLElement;
  private shadowRoot!: ShadowRoot;
  private reactRoot: Root | null = null;
  private options: ShadowViewOptions;
  private isVisible = false;
  private isDestroyed = false;

  // 事件处理器引用
  private clickOutsideHandler: ((e: MouseEvent) => void) | null = null;
  private escapeHandler: ((e: KeyboardEvent) => void) | null = null;

  constructor(options: ShadowViewOptions) {
    this.options = options;
    this.createHostElement();
    this.createShadowRoot();
    this.injectStyles();
    this.setupEventListeners();
  }

  /**
   * 显示UI组件
   */
  show(): void {
    if (this.isDestroyed || this.isVisible) return;

    // 渲染React组件
    this.renderComponent();

    // 添加到DOM
    document.body.appendChild(this.hostElement);

    // 设置位置
    this.updatePosition();

    this.isVisible = true;
  }

  /**
   * 隐藏UI组件
   */
  hide(): void {
    if (this.isDestroyed || !this.isVisible) return;

    // 从DOM移除
    if (this.hostElement?.parentNode) {
      this.hostElement.parentNode.removeChild(this.hostElement);
    }

    this.isVisible = false;
  }

  /**
   * 更新位置（由Shadow DOM内部处理）
   */
  updatePosition(): void {
    if (this.isDestroyed || !this.options.position) return;

    // 对于 Lucid Slider，使用右边定位（复刻demo设计）
    if (this.options.className === 'lucid-slider-host') {
      this.hostElement.style.position = SLIDER_POSITIONING.POSITION;
      this.hostElement.style.top = `${SLIDER_POSITIONING.TOP}px`;
      this.hostElement.style.right = `${SLIDER_POSITIONING.RIGHT}px`;
      this.hostElement.style.left = 'auto';
      this.hostElement.style.bottom = 'auto';
      this.hostElement.style.width = `${SLIDER_DIMENSIONS.WIDTH}px`;
      this.hostElement.style.height = SLIDER_DIMENSIONS.HEIGHT;
      this.hostElement.style.zIndex = `${SLIDER_POSITIONING.Z_INDEX}`;
      this.hostElement.style.pointerEvents = 'auto';
      this.hostElement.style.transform = 'none';
      return;
    }

    // 位置计算完全由Shadow DOM内部的React组件处理
    // 外层需要精确的绝对定位，确保tooltip出现在正确位置
    this.hostElement.style.position = 'absolute';
    this.hostElement.style.left = `${this.options.position.x}px`;
    this.hostElement.style.top = `${this.options.position.y}px`;
    this.hostElement.style.transform = 'translateX(-50%)'; // 水平居中
    this.hostElement.style.zIndex = '10000'; // 确保在最顶层
    
    // 对于tooltip类型，确保可以接收鼠标事件进行交互
    if (this.options.className === 'lucid-tooltip') {
      this.hostElement.style.pointerEvents = 'auto'; // 允许tooltip内部交互
    } else {
      this.hostElement.style.pointerEvents = 'none'; // 其他类型避免影响页面交互
    }
  }

  /**
   * 销毁实例
   */
  destroy(): void {
    if (this.isDestroyed) return;

    this.isDestroyed = true;

    // 清理事件监听器
    this.removeEventListeners();

    // 隐藏组件
    if (this.isVisible) {
      this.hide();
    }

    // 卸载React组件
    if (this.reactRoot) {
      this.reactRoot.unmount();
      this.reactRoot = null;
    }
  }

  /**
   * 检查是否可见
   */
  isVisibleNow(): boolean {
    return this.isVisible && !this.isDestroyed;
  }

  /**
   * 获取宿主元素
   */
  getElement(): HTMLElement {
    return this.hostElement;
  }

  // === 私有方法 ===

  /**
   * 创建<lucid>自定义标签作为宿主元素
   */
  private createHostElement(): void {
    // 外层 <lucid> 元素 - 用于语义和CSS控制
    this.hostElement = document.createElement('lucid');
    this.hostElement.id = this.options.id;

    // 添加类名用于CSS样式
    if (this.options.className) {
      this.hostElement.className = this.options.className;
    }
  }

  /**
   * 创建Shadow Root - 混合架构方案
   */
  private createShadowRoot(): void {
    // 内层 <div> 元素 - 专门用于附加 Shadow DOM
    const shadowHost = document.createElement('div');
    shadowHost.style.cssText = 'width: 100%; height: 100%;';
    
    // 将内层div添加到外层lucid元素中
    this.hostElement.appendChild(shadowHost);
    
    // 在内层div上附加Shadow DOM（div天然支持attachShadow）
    this.shadowRoot = shadowHost.attachShadow({ mode: 'open' });

    // 创建React渲染容器
    const reactContainer = document.createElement('div');
    reactContainer.id = 'react-root';
    reactContainer.style.cssText = 'width: 100%; height: 100%;';

    this.shadowRoot.appendChild(reactContainer);
  }

  /**
   * 注入样式到Shadow DOM
   */
  private injectStyles(): void {
    if (this.options.styles?.length) {
      this.options.styles.forEach((styleContent) => {
        const styleEl = document.createElement('style');
        styleEl.textContent = styleContent;
        this.shadowRoot.appendChild(styleEl);
      });
    }

    // 注入基础样式
    const baseStyles = `
      :host {
        all: initial;
        contain: layout style paint;
      }
      
      #react-root {
        font-family: system-ui, -apple-system, sans-serif;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        box-sizing: border-box;
      }
      
      *, *::before, *::after {
        box-sizing: inherit;
      }
    `;

    const styleEl = document.createElement('style');
    styleEl.textContent = baseStyles;
    this.shadowRoot.insertBefore(styleEl, this.shadowRoot.firstChild);
  }

  /**
   * 渲染React组件
   */
  private renderComponent(): void {
    const container = this.shadowRoot.querySelector('#react-root') as HTMLElement;
    if (!container) {
      console.log('❌ [shadow-view|ERROR] React container not found in shadow root');
      return;
    }

    if (!this.reactRoot) {
      this.reactRoot = createRoot(container);
    }

    this.reactRoot.render(this.options.component);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 点击外部关闭 - 对于tooltip类型，由content.ts统一管理
    if (this.options.closeOnClickOutside !== false && this.options.className !== 'lucid-tooltip') {
      this.clickOutsideHandler = (e: MouseEvent) => {
        if (!this.hostElement.contains(e.target as Node)) {
          this.hide();
        }
      };
    }

    // ESC键关闭
    if (this.options.closeOnEscape !== false) {
      this.escapeHandler = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          this.hide();
        }
      };
    }

    // 延迟添加事件监听器，避免立即触发
    setTimeout(() => {
      if (this.clickOutsideHandler) {
        document.addEventListener('click', this.clickOutsideHandler);
      }
      if (this.escapeHandler) {
        document.addEventListener('keydown', this.escapeHandler);
      }
    }, 100);
  }

  /**
   * 移除事件监听器
   */
  private removeEventListeners(): void {
    if (this.clickOutsideHandler) {
      document.removeEventListener('click', this.clickOutsideHandler);
      this.clickOutsideHandler = null;
    }

    if (this.escapeHandler) {
      document.removeEventListener('keydown', this.escapeHandler);
      this.escapeHandler = null;
    }
  }
}