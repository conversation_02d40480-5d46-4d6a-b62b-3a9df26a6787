/**
 * UI Manager - Shadow DOM 管理系统导出文件
 * 提供统一的导出接口，方便外部模块使用
 */

// 主要API导出
export { uiManager, UIManager } from './uiManager';
export { ShadowView } from './ShadowView';

// 类型导出
export type {
  // 基础类型
  Position,
  Size,
  UIType,
  AnimationConfig,
  
  // 配置选项类型
  ShadowViewOptions,
  BaseUIOptions,
  TooltipOptions,
  ToolfullOptions,
  SliderOptions,
  ModalOptions,
  
  // 内部管理类型
  ShadowViewInstance,
  UIManagerEvents,
  StyleConfig,
  PositionAdjustment,
  
  // 工具类型
  StyleInjectionMethod
} from './types';

// 常量导出
export {
  DEFAULT_TOOLTIP_OPTIONS,
  DEFAULT_TOOLFULL_OPTIONS
} from './types';

// 默认导出主要的单例实例
import { uiManager } from './uiManager';
export default uiManager;