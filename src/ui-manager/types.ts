/**
 * Shadow DOM UI Manager Types
 * 核心类型定义，支持多种UI组件的灵活配置
 */

import { ReactElement } from 'react';

// === 基础类型 ===
export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width?: number;
  height?: number;
}

export type UIType = 'tooltip' | 'toolfull' | 'slider' | 'modal';

// === 动画配置 ===
export interface AnimationConfig {
  duration?: number;        // 动画时长 (ms)
  easing?: string;         // CSS easing function
  enterFrom?: string;      // 入场动画起始状态
  exitTo?: string;         // 退场动画结束状态
}

// === Shadow DOM 配置 ===
export interface ShadowViewOptions {
  id: string;                           // 唯一标识符
  position: Position;                   // 显示位置
  component: ReactElement;              // React组件
  styles?: string[];                    // 样式字符串数组（内联CSS）
  closeOnClickOutside?: boolean;        // 点击外部关闭
  closeOnEscape?: boolean;              // ESC键关闭
  className?: string;                   // 宿主元素额外类名
}

// === 高级UI选项 ===
export interface BaseUIOptions {
  id?: string;                          // 可选ID，不提供则自动生成
  position: Position;                   // 显示位置
  closeOnClickOutside?: boolean;        // 点击外部关闭，默认true
  closeOnEscape?: boolean;              // ESC键关闭，默认true
}

// === Tooltip 配置 ===
export interface TooltipOptions extends BaseUIOptions {
  word: string;                         // 目标单词
  content?: string | ReactElement;      // 显示内容（可选，当使用component时）
  component?: ReactElement;             // React组件（优先于content）
  maxWidth?: number;                    // 最大宽度
  delay?: number;                       // 显示延迟
  theme?: 'dark' | 'light';             // 主题色彩，默认dark
}

// === Toolfull 配置 ===
export interface ToolfullOptions extends BaseUIOptions {
  word: string;                         // 目标单词
  data: {                              // 详细数据
    definitions: string[];
    examples: string[];
    pronunciation?: string;
    etymology?: string;
  };
  maxWidth?: number;                    // 最大宽度
  maxHeight?: number;                   // 最大高度
}

// === Slider 配置 ===
export interface SliderOptions extends BaseUIOptions {
  title: string;                        // 滑窗标题
  content: ReactElement;                // 滑窗内容
  width?: number;                       // 固定宽度
  height?: number;                      // 固定高度
  resizable?: boolean;                  // 是否可调整大小
}

// === Lucid Slider 配置 ===
export interface LucidSliderOptions {
  id?: string;                          // 可选ID，不提供则自动生成
  isOpen?: boolean;                     // 是否打开，默认true
  onClose?: () => void;                 // 关闭回调
  className?: string;                   // 额外的CSS类名
}

// === Modal 配置 ===
export interface ModalOptions extends BaseUIOptions {
  title: string;                        // 模态框标题
  content: ReactElement;                // 模态框内容
  width?: number;                       // 固定宽度
  height?: number;                      // 固定高度
  backdrop?: boolean;                   // 是否显示背景遮罩
  centered?: boolean;                   // 是否居中显示
}

// === 内部管理类型 ===
export interface ShadowViewInstance {
  id: string;
  view: any;  // ShadowView实例，避免循环依赖暂时用any
  isVisible: boolean;
  createdAt: number;
}

// === 事件类型 ===
export interface UIManagerEvents {
  onShow?: (id: string) => void;
  onHide?: (id: string) => void;
  onDestroy?: (id: string) => void;
}

// === 工具类型 ===
export type StyleInjectionMethod = 'inline' | 'link';

export interface StyleConfig {
  content: string;
  method: StyleInjectionMethod;
  id?: string;
}

// === 边界检测结果 ===
export interface PositionAdjustment {
  adjustedPosition: Position;
  adjustments: {
    horizontal?: 'left' | 'right' | 'none';
    vertical?: 'up' | 'down' | 'none';
  };
  withinBounds: boolean;
}

// === 默认配置 ===
export const DEFAULT_TOOLTIP_OPTIONS: Partial<TooltipOptions> = {
  closeOnClickOutside: true,
  closeOnEscape: true,
  maxWidth: 300,
  delay: 0,
  theme: 'dark'
};;

export const DEFAULT_TOOLFULL_OPTIONS: Partial<ToolfullOptions> = {
  closeOnClickOutside: true,
  closeOnEscape: true,
  maxWidth: 400,
  maxHeight: 500
};