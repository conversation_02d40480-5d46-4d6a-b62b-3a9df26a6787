/**
 * Tooltip样式定义
 * 从CSS文件提取到这里避免硬编码在uiManager中
 */

export const TOOLTIP_DARK_THEME_CSS = `
  /* CSS 变量定义 - 深色主题（在Shadow DOM根部定义） */
  :host {
    --glass-bg: rgba(30, 30, 30, 0.45);
    --glass-border: rgba(255, 255, 255, 0.15);
    --text-gray: #dadada;
    --text-white: #ffffff;
  }

  /* 主要的 Tooltip 容器 - 深色主题 */
  .lu-tooltip {
    /* 毛玻璃效果 - 与参考样式完全一致 */
    backdrop-filter: blur(14px) saturate(160%);
    -webkit-backdrop-filter: blur(14px) saturate(160%);
    
    /* 背景和边框 */
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    
    /* 布局和间距 */
    padding: 4px 12px;
    display: inline-block;
    white-space: nowrap;
    
    /* 文本样式 - 与参考样式完全一致 */
    font-size: 0.88em;
    line-height: 1.45;
    color: var(--text-gray);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", 
                 "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", 
                 "Helvetica Neue", Helvetica, Arial, sans-serif;
    
    /* 动画效果 */
    transition: color 0.15s ease-out;
    
    /* 阴影效果 */
    box-shadow: 0 4px 32px rgba(0, 0, 0, 0.45);
    
    /* 防止文本选择 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 词性样式 */
  .lu-pos {
    font-weight: 500;
    color: inherit;
    margin-right: 2px;
  }

  /* 中文简短释义样式 */
  .lu-chinese-short {
    font-size: 0.9em;
    color: inherit;
    margin-right: 3px;
  }

  .lu-chinese-short:hover {
    color: var(--text-white) !important;
  }

  /* 交互式中文释义的特殊hover效果 - 背景变白 */
  .lu-chinese-short.interactive {
    cursor: pointer;
    user-select: none;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.15s ease;
    /* 确保可以接收事件 */
    pointer-events: auto;
  }

  .lu-chinese-short.interactive:hover {
    background-color: white !important;
    color: #333 !important;
    /* 在hover状态下略微突出 */
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .lu-chinese-short.interactive:active {
    transform: translateY(0);
    background-color: #f0f0f0 !important;
  }

  /* 分隔符样式 */
  .lu-separator {
    margin: 0 4px;
    opacity: 0.6;
  }
`;

export const TOOLTIP_LIGHT_THEME_CSS = `
  /* CSS 变量定义 - 浅色主题（在Shadow DOM根部定义） */
  :host {
    --glass-bg: rgba(240, 240, 240, 0.85);
    --glass-border: rgba(0, 0, 0, 0.12);
    --text-gray: #374151;
    --text-white: #111827;
  }

  /* 主要的 Tooltip 容器 - 浅色主题 */
  .lu-tooltip {
    /* 毛玻璃效果 */
    backdrop-filter: blur(14px) saturate(160%);
    -webkit-backdrop-filter: blur(14px) saturate(160%);
    
    /* 背景和边框 */
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    
    /* 布局和间距 */
    padding: 4px 12px;
    display: inline-block;
    white-space: nowrap;
    
    /* 文本样式 */
    font-size: 0.88em;
    line-height: 1.45;
    color: var(--text-gray);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", 
                 "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", 
                 "Helvetica Neue", Helvetica, Arial, sans-serif;
    
    /* 动画效果 */
    transition: color 0.15s ease-out;
    
    /* 阴影效果 */
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
    
    /* 防止文本选择 */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 词性样式 */
  .lu-pos {
    font-weight: 500;
    color: inherit;
    margin-right: 2px;
  }

  /* 中文简短释义样式 */
  .lu-chinese-short {
    font-size: 0.9em;
    color: inherit;
    margin-right: 3px;
  }

  .lu-chinese-short:hover {
    color: var(--text-white) !important;
  }

  /* 交互式中文释义的特殊hover效果 - 背景变白 */
  .lu-chinese-short.interactive {
    cursor: pointer;
    user-select: none;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.15s ease;
    /* 确保可以接收事件 */
    pointer-events: auto;
  }

  .lu-chinese-short.interactive:hover {
    background-color: white !important;
    color: #333 !important;
    /* 在hover状态下略微突出 */
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .lu-chinese-short.interactive:active {
    transform: translateY(0);
    background-color: #f0f0f0 !important;
  }

  /* 分隔符样式 */
  .lu-separator {
    margin: 0 4px;
    opacity: 0.6;
  }
`;