/**
 * useAuth Hook - React Hook for AuthManager integration
 * 提供认证状态管理和操作的React Hook
 */

import { useState, useEffect, useCallback } from 'react';
import { authManager } from '../services/auth';
import type { AuthState, User, LoginResponse } from '../types/auth';

export interface UseAuthReturn {
  // 状态
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
  
  // 操作
  login: (email: string, password: string) => Promise<LoginResponse>;
  register: (email: string, password: string, name?: string) => Promise<LoginResponse>;
  logout: () => Promise<void>;
  refreshUserInfo: () => Promise<User | null>;
  clearError: () => void;
}

/**
 * 认证状态管理Hook
 */
export function useAuth(): UseAuthReturn {
  const [authState, setAuthState] = useState<AuthState>(() => authManager.getState());

  // 订阅认证状态变化
  useEffect(() => {
    const unsubscribe = authManager.subscribe((newState: AuthState) => {
      setAuthState(newState);
    });

    // 确保初始化完成
    authManager.initialize().catch((error: any) => {
      console.error('[useAuth] Initialization error:', error);
    });

    return unsubscribe;
  }, []);

  // 登录操作
  const login = useCallback(async (email: string, password: string): Promise<LoginResponse> => {
    try {
      const result = await authManager.login(email, password);
      return result;
    } catch (error) {
      console.error('[useAuth] Login error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '登录失败'
      };
    }
  }, []);

  // 注册操作
  const register = useCallback(async (email: string, password: string, name?: string): Promise<LoginResponse> => {
    try {
      const result = await authManager.register(email, password, name);
      return result;
    } catch (error) {
      console.error('[useAuth] Register error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '注册失败'
      };
    }
  }, []);

  // 登出操作
  const logout = useCallback(async (): Promise<void> => {
    try {
      await authManager.logout();
    } catch (error) {
      console.error('[useAuth] Logout error:', error);
    }
  }, []);

  // 刷新用户信息
  const refreshUserInfo = useCallback(async (): Promise<User | null> => {
    try {
      return await authManager.refreshUserInfo();
    } catch (error) {
      console.error('[useAuth] Refresh user info error:', error);
      return null;
    }
  }, []);

  // 清除错误
  const clearError = useCallback(() => {
    // 这里可以通过AuthManager提供一个clearError方法，或者通过状态更新
    // 暂时通过重新获取状态来实现
    const currentState = authManager.getState();
    if (currentState.error) {
      // 如果AuthManager有clearError方法，调用它
      // 否则这里可能需要其他方式来清除错误
      console.log('[useAuth] Clear error called');
    }
  }, []);

  return {
    // 状态
    isAuthenticated: authState.isAuthenticated,
    user: authState.user,
    loading: authState.loading,
    error: authState.error,
    
    // 操作
    login,
    register,
    logout,
    refreshUserInfo,
    clearError
  };
}

/**
 * 简化的认证状态Hook - 只返回基本状态
 */
export function useAuthState() {
  const [authState, setAuthState] = useState<AuthState>(() => authManager.getState());

  useEffect(() => {
    const unsubscribe = authManager.subscribe(setAuthState);
    return unsubscribe;
  }, []);

  return authState;
}
