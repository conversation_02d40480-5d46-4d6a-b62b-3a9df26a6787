/**
 * TooltipError 组件
 * 词典数据加载失败时的错误提示组件
 */

import React from 'react';

export interface TooltipErrorProps {
  /** 错误对象 */
  error: Error;
  /** 出错的单词 */
  word: string;
  /** 重试回调函数 */
  onRetry?: () => void;
  /** 额外的类名 */
  className?: string;
  /** 主题，默认 dark */
  theme?: 'dark' | 'light';
  /** 错误显示的变体 */
  variant?: 'minimal' | 'standard' | 'detailed';
  /** 是否显示重试按钮 */
  showRetry?: boolean;
  /** 是否显示错误详情 */
  showDetails?: boolean;
}

export const TooltipError: React.FC<TooltipErrorProps> = ({
  error,
  word,
  onRetry,
  className = '',
  theme = 'dark',
  variant = 'standard',
  showRetry = true,
  showDetails = false,
}) => {
  const baseClasses = `lu-tooltip-error ${className}`;
  
  // 根据错误类型生成友好的错误消息
  const getErrorMessage = (error: Error): string => {
    const message = error.message.toLowerCase();
    
    if (message.includes('timeout')) {
      return '请求超时，请稍后重试';
    }
    
    if (message.includes('network') || message.includes('fetch')) {
      return '网络连接异常';
    }
    
    if (message.includes('404') || message.includes('not found')) {
      return '未找到该词的释义';
    }
    
    if (message.includes('500') || message.includes('server')) {
      return '服务器异常，请稍后重试';
    }
    
    return '加载失败，请重试';
  };

  const errorMessage = getErrorMessage(error);

  // 最小化错误显示
  if (variant === 'minimal') {
    return (
      <span 
        className={`${baseClasses} lu-error-minimal`}
        data-theme={theme}
        title={`${word}: ${errorMessage}`}
      >
        <span className="lu-error-icon">⚠</span>
        <span className="lu-error-word">{word}</span>
      </span>
    );
  }

  // 详细错误显示
  if (variant === 'detailed') {
    return (
      <div 
        className={`${baseClasses} lu-error-detailed`}
        data-theme={theme}
      >
        <div className="lu-error-header">
          <span className="lu-error-icon">⚠</span>
          <span className="lu-error-title">词典加载失败</span>
        </div>
        
        <div className="lu-error-content">
          <div className="lu-error-word-info">
            <span className="lu-error-label">单词:</span>
            <span className="lu-error-word">{word}</span>
          </div>
          
          <div className="lu-error-message">
            <span className="lu-error-label">错误:</span>
            <span className="lu-error-text">{errorMessage}</span>
          </div>
          
          {showDetails && (
            <div className="lu-error-details">
              <span className="lu-error-label">详情:</span>
              <span className="lu-error-detail-text">{error.message}</span>
            </div>
          )}
        </div>
        
        {showRetry && onRetry && (
          <div className="lu-error-actions">
            <button 
              className="lu-error-retry-btn"
              onClick={onRetry}
              type="button"
            >
              重试
            </button>
          </div>
        )}
      </div>
    );
  }

  // 标准错误显示
  return (
    <span 
      className={`${baseClasses} lu-error-standard`}
      data-theme={theme}
    >
      <span className="lu-error-icon">⚠</span>
      <span className="lu-error-message">{errorMessage}</span>
      {showRetry && onRetry && (
        <button 
          className="lu-error-retry-btn"
          onClick={onRetry}
          type="button"
          title="重试"
        >
          ↻
        </button>
      )}
    </span>
  );
};

/**
 * 最小化错误组件
 */
export const MinimalTooltipError: React.FC<Omit<TooltipErrorProps, 'variant'>> = (props) => {
  return <TooltipError {...props} variant="minimal" />;
};

/**
 * 标准错误组件
 */
export const StandardTooltipError: React.FC<Omit<TooltipErrorProps, 'variant'>> = (props) => {
  return <TooltipError {...props} variant="standard" />;
};

/**
 * 详细错误组件
 */
export const DetailedTooltipError: React.FC<Omit<TooltipErrorProps, 'variant'>> = (props) => {
  return <TooltipError {...props} variant="detailed" />;
};

/**
 * 静默错误组件（不显示重试按钮）
 */
export const SilentTooltipError: React.FC<TooltipErrorProps> = (props) => {
  return <TooltipError {...props} showRetry={false} />;
};

/**
 * 创建自定义错误组件
 */
export const createTooltipError = (
  defaultProps: Partial<TooltipErrorProps> = {}
) => {
  return (props: TooltipErrorProps) => {
    return <TooltipError {...defaultProps} {...props} />;
  };
};

// 预设的错误组件变体
export const ErrorVariants = {
  // 最小化错误（只显示图标和单词）
  minimal: createTooltipError({
    variant: 'minimal',
    showRetry: false,
  }),
  
  // 标准错误（显示图标、消息和重试按钮）
  standard: createTooltipError({
    variant: 'standard',
    showRetry: true,
  }),
  
  // 详细错误（显示完整错误信息）
  detailed: createTooltipError({
    variant: 'detailed',
    showRetry: true,
    showDetails: true,
  }),
  
  // 静默错误（不显示重试按钮）
  silent: createTooltipError({
    variant: 'standard',
    showRetry: false,
  }),
  
  // 高亮专用错误（紧凑显示）
  highlight: createTooltipError({
    variant: 'minimal',
    showRetry: false,
  }),
} as const;

// 导出类型
export type TooltipErrorVariant = 'minimal' | 'standard' | 'detailed';
export type TooltipErrorTheme = 'dark' | 'light';