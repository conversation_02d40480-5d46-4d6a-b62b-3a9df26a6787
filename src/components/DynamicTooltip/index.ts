/**
 * DynamicTooltip 组件统一导出
 * 
 * 提供基于 useDictionary Hook 的动态词典 Tooltip 组件
 * 包括加载状态、错误状态和各种预设配置
 */

// 主要组件
export {
  DynamicTooltip,
  DynamicTooltipFresh,
  DynamicTooltipNoCache,
  DynamicTooltipSilent,
  createDynamicTooltip,
  HighlightDynamicTooltip,
  TestDynamicTooltip,
} from './DynamicTooltip';

// 骨架屏组件
export {
  TooltipSkeleton,
  CompactTooltipSkeleton,
  FullTooltipSkeleton,
  StaticTooltipSkeleton,
  createTooltipSkeleton,
  SkeletonVariants,
} from './TooltipSkeleton';

// 错误组件
export {
  TooltipError,
  MinimalTooltipError,
  StandardTooltipError,
  DetailedTooltipError,
  SilentTooltipError,
  createTooltipError,
  ErrorVariants,
} from './TooltipError';

// 类型导出
export type {
  DynamicTooltipProps,
  UseDictionaryOptions,
} from './DynamicTooltip';

export type {
  TooltipSkeletonProps,
  TooltipSkeletonVariant,
  TooltipSkeletonTheme,
} from './TooltipSkeleton';

export type {
  TooltipErrorProps,
  TooltipErrorVariant,
  TooltipErrorTheme,
} from './TooltipError';

// 默认导出主要组件
export { DynamicTooltip as default } from './DynamicTooltip';