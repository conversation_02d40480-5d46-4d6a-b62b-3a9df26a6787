/**
 * TooltipSkeleton 组件
 * 词典数据加载时的骨架屏组件
 */

import React from 'react';

export interface TooltipSkeletonProps {
  /** 正在加载的单词 */
  word: string;
  /** 额外的类名 */
  className?: string;
  /** 主题，默认 dark */
  theme?: 'dark' | 'light';
  /** 是否显示动画 */
  animated?: boolean;
  /** 骨架屏的变体 */
  variant?: 'compact' | 'full';
}

export const TooltipSkeleton: React.FC<TooltipSkeletonProps> = ({
  word,
  className = '',
  theme = 'dark',
  animated = true,
  variant = 'compact',
}) => {
  const baseClasses = `lu-tooltip-skeleton ${className}`;
  const animationClass = animated ? 'lu-skeleton-animated' : '';
  
  if (variant === 'compact') {
    return (
      <span 
        className={`${baseClasses} ${animationClass}`} 
        data-theme={theme}
        data-variant="compact"
      >
        <span className="lu-skeleton-text lu-skeleton-short"></span>
        <span className="lu-skeleton-separator">{' '}</span>
        <span className="lu-skeleton-text lu-skeleton-medium"></span>
      </span>
    );
  }

  return (
    <div 
      className={`${baseClasses} ${animationClass}`} 
      data-theme={theme}
      data-variant="full"
    >
      <div className="lu-skeleton-header">
        <div className="lu-skeleton-word">
          <span className="lu-skeleton-text lu-skeleton-word-text"></span>
        </div>
        <div className="lu-skeleton-phonetic">
          <span className="lu-skeleton-text lu-skeleton-phonetic-text"></span>
        </div>
      </div>
      
      <div className="lu-skeleton-content">
        <div className="lu-skeleton-definition">
          <span className="lu-skeleton-pos"></span>
          <span className="lu-skeleton-text lu-skeleton-definition-text"></span>
        </div>
        <div className="lu-skeleton-definition">
          <span className="lu-skeleton-pos"></span>
          <span className="lu-skeleton-text lu-skeleton-definition-text"></span>
        </div>
      </div>
    </div>
  );
};

/**
 * 紧凑版骨架屏组件
 */
export const CompactTooltipSkeleton: React.FC<Omit<TooltipSkeletonProps, 'variant'>> = (props) => {
  return <TooltipSkeleton {...props} variant="compact" />;
};

/**
 * 完整版骨架屏组件
 */
export const FullTooltipSkeleton: React.FC<Omit<TooltipSkeletonProps, 'variant'>> = (props) => {
  return <TooltipSkeleton {...props} variant="full" />;
};

/**
 * 不带动画的骨架屏组件
 */
export const StaticTooltipSkeleton: React.FC<TooltipSkeletonProps> = (props) => {
  return <TooltipSkeleton {...props} animated={false} />;
};

/**
 * 创建自定义骨架屏组件
 */
export const createTooltipSkeleton = (
  defaultProps: Partial<TooltipSkeletonProps> = {}
) => {
  return (props: TooltipSkeletonProps) => {
    return <TooltipSkeleton {...defaultProps} {...props} />;
  };
};

// 预设的骨架屏变体
export const SkeletonVariants = {
  // 最小化骨架屏
  minimal: createTooltipSkeleton({
    variant: 'compact',
    animated: false,
  }),
  
  // 标准骨架屏
  standard: createTooltipSkeleton({
    variant: 'compact',
    animated: true,
  }),
  
  // 详细骨架屏
  detailed: createTooltipSkeleton({
    variant: 'full',
    animated: true,
  }),
  
  // 静态详细骨架屏
  staticDetailed: createTooltipSkeleton({
    variant: 'full',
    animated: false,
  }),
} as const;

// 导出类型
export type TooltipSkeletonVariant = 'compact' | 'full';
export type TooltipSkeletonTheme = 'dark' | 'light';