/**
 * 简单的交互式Tooltip演示
 */

import React, { useState } from 'react';
import { DynamicTooltip } from './DynamicTooltip';

export const SimpleDemo: React.FC = () => {
  const [clickLog, setClickLog] = useState<string[]>([]);

  const handlePreferenceClick = (word: string, pos: string, chinese: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `${timestamp}: 点击了 ${word} 的 ${pos}.${chinese}`;
    setClickLog(prev => [logEntry, ...prev.slice(0, 9)]);
  };

  const clearStorage = () => {
    localStorage.removeItem('lucid-word-preferences');
    setClickLog(['已清空偏好数据']);
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'system-ui' }}>
      <h2>交互式Tooltip演示</h2>
      
      <div style={{ marginBottom: '20px', padding: '15px', border: '2px dashed #ccc' }}>
        <p>点击下面单词的中文释义来记录偏好：</p>
        
        <div style={{ fontSize: '16px', lineHeight: '2' }}>
          The word{' '}
          <DynamicTooltip 
            word="hello" 
            interactive={true}
          />
          {' '}can have different meanings.
        </div>
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={clearStorage}
          style={{ 
            padding: '8px 16px', 
            backgroundColor: '#dc2626', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          清空偏好数据
        </button>
      </div>

      <div style={{ fontSize: '14px' }}>
        <h3>点击日志：</h3>
        {clickLog.length === 0 ? (
          <p style={{ color: '#666', fontStyle: 'italic' }}>暂无点击记录</p>
        ) : (
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            {clickLog.map((log, index) => (
              <li key={index} style={{ marginBottom: '5px' }}>{log}</li>
            ))}
          </ul>
        )}
      </div>

      <div style={{ fontSize: '14px', color: '#666', marginTop: '20px' }}>
        <p>功能说明：</p>
        <ul>
          <li>✨ Hover：鼠标悬停显示高亮边框</li>
          <li>👆 点击：记录偏好并重新排序释义</li>
          <li>💾 持久化：偏好保存在localStorage中</li>
          <li>🔄 排序：根据点击次数自动调整顺序</li>
        </ul>
      </div>
    </div>
  );
};