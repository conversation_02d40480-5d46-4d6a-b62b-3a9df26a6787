/**
 * 简化的 DynamicTooltip 集成测试
 * 验证核心功能而不依赖复杂的环境配置
 */

// 模拟数据类型
interface MockTooltipData {
  word: string;
  phonetic?: {
    us?: string;
    uk?: string;
  };
  explain: Array<{
    pos: string;
    definitions: Array<{
      definition: string;
      chinese: string;
      chinese_short: string;
    }>;
  }>;
}

// 模拟 useDictionary Hook
interface MockUseDictionaryReturn {
  data: MockTooltipData | null;
  loading: boolean;
  error: Error | null;
  isValidating: boolean;
  refetch: () => Promise<void>;
  mutate: (data: MockTooltipData) => void;
}

// 模拟词典服务
class MockDictionaryService {
  private mockData: Record<string, MockTooltipData> = {
    hello: {
      word: 'hello',
      phonetic: { us: '/həˈloʊ/', uk: '/həˈləʊ/' },
      explain: [
        {
          pos: 'noun',
          definitions: [
            {
              definition: 'A greeting or expression of goodwill.',
              chinese: '问候或善意的表达',
              chinese_short: '问候'
            }
          ]
        },
        {
          pos: 'verb',
          definitions: [
            {
              definition: 'To greet someone by saying hello.',
              chinese: '通过说你好来问候某人',
              chinese_short: '问候'
            }
          ]
        }
      ]
    },
    test: {
      word: 'test',
      phonetic: { us: '/test/', uk: '/test/' },
      explain: [
        {
          pos: 'noun',
          definitions: [
            {
              definition: 'A procedure to establish quality.',
              chinese: '测试，考验，试验的过程',
              chinese_short: '测试'
            }
          ]
        }
      ]
    }
  };

  async getWord(word: string): Promise<MockTooltipData | null> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 100));

    if (word === 'error-word') {
      throw new Error('Network error');
    }

    return this.mockData[word] || null;
  }
}

// 模拟 Hook 实现
function createMockUseDictionary() {
  const service = new MockDictionaryService();

  return function useDictionary(word: string): MockUseDictionaryReturn {
    let data: MockTooltipData | null = null;
    let loading = true;
    let error: Error | null = null;
    let isValidating = false;

    const refetch = async () => {
      try {
        loading = true;
        error = null;
        data = await service.getWord(word);
        loading = false;
      } catch (err) {
        error = err instanceof Error ? err : new Error(String(err));
        loading = false;
      }
    };

    const mutate = (newData: MockTooltipData) => {
      data = newData;
      loading = false;
      error = null;
    };

    // 立即开始获取数据
    refetch();

    return { data, loading, error, isValidating, refetch, mutate };
  };
}

// Tooltip 渲染逻辑
function renderTooltip(data: MockTooltipData): string {
  if (!data || !data.explain || data.explain.length === 0) {
    return '<span class="lu-tooltip">No data</span>';
  }

  const parts: string[] = [];

  data.explain.forEach((item, index) => {
    // 添加词性简写
    const posShort = item.pos === 'noun' ? 'n.' :
      item.pos === 'verb' ? 'v.' :
        item.pos === 'adjective' ? 'adj.' :
          item.pos === 'adverb' ? 'adv.' :
            item.pos.substring(0, 3) + '.';

    parts.push(`<span class="lu-pos">${posShort}</span>`);

    // 添加该词性下的所有中文简短释义
    item.definitions.forEach(def => {
      parts.push(`<span class="lu-chinese-short">${def.chinese_short}</span>`);
    });

    // 如果不是最后一个词性，添加空格分隔
    if (index < data.explain.length - 1) {
      parts.push(`<span class="lu-separator"> </span>`);
    }
  });

  return `<span class="lu-tooltip">${parts.join('')}</span>`;
}

// 骨架屏渲染
function renderSkeleton(): string {
  return `
    <span class="lu-tooltip-skeleton">
      <span class="lu-skeleton-text lu-skeleton-short"></span>
      <span class="lu-separator"> </span>
      <span class="lu-skeleton-text lu-skeleton-medium"></span>
    </span>
  `;
}

// 错误状态渲染
function renderError(error: Error): string {
  return `
    <span class="lu-tooltip-error">
      <span class="lu-error-icon">⚠</span>
      <span class="lu-error-message">${error.message}</span>
      <button class="lu-error-retry-btn">重试</button>
    </span>
  `;
}

// 集成测试函数
async function runIntegrationTests(): Promise<TestResults> {
  const useDictionary = createMockUseDictionary();
  const results: TestResults = {
    tests: [],
    summary: { total: 0, passed: 0, failed: 0 }
  };

  console.log('🧪 开始 DynamicTooltip 集成测试...');

  // 测试 1: 基础数据获取和渲染
  try {
    console.log('📝 测试 1: 基础数据获取和渲染');
    const hookResult = useDictionary('hello');

    // 等待数据加载
    await new Promise(resolve => setTimeout(resolve, 200));

    if (hookResult.data) {
      const tooltipHtml = renderTooltip(hookResult.data);
      const isValid = tooltipHtml.includes('n.') &&
        tooltipHtml.includes('v.') &&
        tooltipHtml.includes('问候');

      results.tests.push({
        name: '基础数据获取和渲染',
        passed: isValid,
        details: { tooltipHtml, data: hookResult.data }
      });
    } else {
      results.tests.push({
        name: '基础数据获取和渲染',
        passed: false,
        details: { error: 'No data returned' }
      });
    }
  } catch (error) {
    results.tests.push({
      name: '基础数据获取和渲染',
      passed: false,
      details: { error: error instanceof Error ? error.message : String(error) }
    });
  }

  // 测试 2: 数据格式验证
  try {
    console.log('📝 测试 2: 数据格式验证');
    const hookResult = useDictionary('test');

    await new Promise(resolve => setTimeout(resolve, 200));

    if (hookResult.data) {
      const isValidFormat =
        typeof hookResult.data.word === 'string' &&
        Array.isArray(hookResult.data.explain) &&
        hookResult.data.explain.every(item =>
          typeof item.pos === 'string' &&
          Array.isArray(item.definitions) &&
          item.definitions.every(def =>
            typeof def.definition === 'string' &&
            typeof def.chinese === 'string' &&
            typeof def.chinese_short === 'string'
          )
        );

      results.tests.push({
        name: '数据格式验证',
        passed: isValidFormat,
        details: {
          format: 'TooltipProps格式',
          valid: isValidFormat,
          dataStructure: {
            word: typeof hookResult.data.word,
            explainLength: hookResult.data.explain.length,
            firstExplainStructure: hookResult.data.explain[0]
          }
        }
      });
    } else {
      results.tests.push({
        name: '数据格式验证',
        passed: false,
        details: { error: 'No data for validation' }
      });
    }
  } catch (error) {
    results.tests.push({
      name: '数据格式验证',
      passed: false,
      details: { error: error instanceof Error ? error.message : String(error) }
    });
  }

  // 测试 3: 错误处理
  try {
    console.log('📝 测试 3: 错误处理');
    const hookResult = useDictionary('error-word');

    await new Promise(resolve => setTimeout(resolve, 200));

    if (hookResult.error) {
      const errorHtml = renderError(hookResult.error);
      const hasErrorElements = errorHtml.includes('lu-error-icon') &&
        errorHtml.includes('lu-error-message') &&
        errorHtml.includes('重试');

      results.tests.push({
        name: '错误处理',
        passed: hasErrorElements,
        details: {
          errorMessage: hookResult.error.message,
          errorHtml,
          hasErrorElements
        }
      });
    } else {
      results.tests.push({
        name: '错误处理',
        passed: false,
        details: { error: 'Expected error but got none' }
      });
    }
  } catch (error) {
    results.tests.push({
      name: '错误处理',
      passed: false,
      details: { error: error instanceof Error ? error.message : String(error) }
    });
  }

  // 测试 4: 骨架屏渲染
  try {
    console.log('📝 测试 4: 骨架屏渲染');
    const skeletonHtml = renderSkeleton();
    const hasSkeletonElements = skeletonHtml.includes('lu-tooltip-skeleton') &&
      skeletonHtml.includes('lu-skeleton-text') &&
      skeletonHtml.includes('lu-skeleton-short');

    results.tests.push({
      name: '骨架屏渲染',
      passed: hasSkeletonElements,
      details: { skeletonHtml, hasSkeletonElements }
    });
  } catch (error) {
    results.tests.push({
      name: '骨架屏渲染',
      passed: false,
      details: { error: error instanceof Error ? error.message : String(error) }
    });
  }

  // 计算测试摘要
  results.summary.total = results.tests.length;
  results.summary.passed = results.tests.filter(t => t.passed).length;
  results.summary.failed = results.summary.total - results.summary.passed;

  console.log('✅ 集成测试完成');
  console.log(`📊 结果: ${results.summary.passed}/${results.summary.total} 通过`);

  return results;
}

// 测试结果类型
interface TestResult {
  name: string;
  passed: boolean;
  details: any;
}

interface TestResults {
  tests: TestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
  };
}

// 格式化测试报告
function formatTestReport(results: TestResults): string {
  let report = '\n=== DynamicTooltip 集成测试报告 ===\n\n';

  report += `总计: ${results.summary.total} 个测试\n`;
  report += `通过: ${results.summary.passed} 个\n`;
  report += `失败: ${results.summary.failed} 个\n`;
  report += `成功率: ${((results.summary.passed / results.summary.total) * 100).toFixed(1)}%\n\n`;

  results.tests.forEach((test, index) => {
    const status = test.passed ? '✅ PASS' : '❌ FAIL';
    report += `${index + 1}. ${status} ${test.name}\n`;

    if (!test.passed) {
      report += `   错误: ${JSON.stringify(test.details, null, 2)}\n`;
    }
    report += '\n';
  });

  return report;
}

// 导出测试函数
export {
  runIntegrationTests,
  formatTestReport,
  renderTooltip,
  renderSkeleton,
  renderError,
  createMockUseDictionary
};

// 全局测试函数（用于浏览器环境）
if (typeof window !== 'undefined') {
  (window as any).runDynamicTooltipTests = async () => {
    const results = await runIntegrationTests();
    const report = formatTestReport(results);
    console.log(report);
    return results;
  };

  (window as any).DynamicTooltipTestUtils = {
    runIntegrationTests,
    formatTestReport,
    renderTooltip,
    renderSkeleton,
    renderError
  };
}