<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DynamicTooltip Hook 集成测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e1e1e1;
            border-radius: 6px;
            background: #fafafa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }
        
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 4px;
        }
        
        .controls input, .controls select {
            margin: 5px 10px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            background: #007acc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #005c99;
        }
        
        .tooltip-demo {
            padding: 20px;
            margin: 15px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            min-height: 60px;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.loading { background: #fff3cd; color: #856404; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .highlight-word {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
            cursor: pointer;
            margin: 0 5px;
        }
        
        .highlight-word:hover {
            background: #ffc107;
        }
        
        /* Tooltip 样式 */
        .lu-tooltip {
            display: inline-block;
            padding: 8px 12px;
            background: #2d2d2d;
            color: white;
            border-radius: 6px;
            font-size: 14px;
            line-height: 1.4;
            max-width: 300px;
        }
        
        .lu-tooltip[data-theme="light"] {
            background: white;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .lu-pos {
            color: #ffc107;
            font-weight: bold;
            margin-right: 5px;
        }
        
        .lu-chinese-short {
            margin-right: 8px;
        }
        
        .lu-separator {
            margin: 0 5px;
        }
        
        /* 骨架屏样式 */
        .lu-tooltip-skeleton {
            display: inline-block;
            padding: 8px 12px;
            background: #2d2d2d;
            color: white;
            border-radius: 6px;
        }
        
        .lu-skeleton-text {
            display: inline-block;
            height: 16px;
            background: #666;
            border-radius: 3px;
            animation: skeleton-pulse 1.5s ease-in-out infinite;
        }
        
        .lu-skeleton-short { width: 40px; }
        .lu-skeleton-medium { width: 80px; }
        
        @keyframes skeleton-pulse {
            0%, 100% { opacity: 0.4; }
            50% { opacity: 0.8; }
        }
        
        /* 错误样式 */
        .lu-tooltip-error {
            display: inline-block;
            padding: 8px 12px;
            background: #dc3545;
            color: white;
            border-radius: 6px;
        }
        
        .lu-error-retry-btn {
            background: transparent;
            border: 1px solid white;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            cursor: pointer;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 DynamicTooltip Hook 集成测试</h1>
        <p>测试 useDictionary Hook 与 Tooltip 组件的完整集成</p>
        
        <div class="controls">
            <label>
                测试单词: 
                <input type="text" id="testWord" value="hello" placeholder="输入要测试的单词">
            </label>
            <label>
                <input type="checkbox" id="enableFresh"> 强制刷新
            </label>
            <label>
                <input type="checkbox" id="enableLogs" checked> 显示日志
            </label>
            <button onclick="runTest()">运行测试</button>
            <button onclick="clearLogs()">清除日志</button>
        </div>
        
        <div class="test-section">
            <h3>📊 测试状态</h3>
            <div id="testStatus" class="status">准备测试...</div>
        </div>
        
        <div class="test-section">
            <h3>🎯 Tooltip 渲染测试</h3>
            <div class="tooltip-demo" id="tooltipDemo">
                点击"运行测试"开始...
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔄 交互式高亮测试</h3>
            <p>将鼠标悬停在高亮单词上查看动态 Tooltip：</p>
            <div id="highlightDemo">
                <span class="highlight-word" data-word="hello">hello</span>
                <span class="highlight-word" data-word="test">test</span>
                <span class="highlight-word" data-word="example">example</span>
                <span class="highlight-word" data-word="escalade">escalade</span>
            </div>
            <div id="highlightTooltip" style="margin-top: 15px;"></div>
        </div>
        
        <div class="test-section">
            <h3>📝 数据格式验证</h3>
            <div id="dataValidation">等待测试数据...</div>
        </div>
        
        <div class="test-section">
            <h3>📋 测试日志</h3>
            <div id="testLogs" class="log">日志将在这里显示...</div>
        </div>
    </div>
    
    <script>
        // 模拟 useDictionary Hook 的行为
        class MockUseDictionary {
            constructor() {
                this.mockData = {
                    hello: {
                        word: 'hello',
                        phonetic: { us: '/həˈloʊ/', uk: '/həˈləʊ/' },
                        explain: [
                            {
                                pos: 'noun',
                                definitions: [
                                    {
                                        definition: 'A greeting or expression of goodwill.',
                                        chinese: '问候或善意的表达',
                                        chinese_short: '问候'
                                    }
                                ]
                            },
                            {
                                pos: 'verb',
                                definitions: [
                                    {
                                        definition: 'To greet someone by saying hello.',
                                        chinese: '通过说你好来问候某人',
                                        chinese_short: '问候'
                                    }
                                ]
                            }
                        ]
                    },
                    test: {
                        word: 'test',
                        phonetic: { us: '/test/', uk: '/test/' },
                        explain: [
                            {
                                pos: 'noun',
                                definitions: [
                                    {
                                        definition: 'A procedure to establish quality.',
                                        chinese: '测试，考验，试验的过程',
                                        chinese_short: '测试'
                                    }
                                ]
                            },
                            {
                                pos: 'verb',
                                definitions: [
                                    {
                                        definition: 'To check quality or performance.',
                                        chinese: '测试，检验某物的质量',
                                        chinese_short: '测试'
                                    }
                                ]
                            }
                        ]
                    },
                    example: {
                        word: 'example',
                        phonetic: { us: '/ɪɡˈzæmpəl/', uk: '/ɪɡˈzɑːmpəl/' },
                        explain: [
                            {
                                pos: 'noun',
                                definitions: [
                                    {
                                        definition: 'A thing characteristic of its kind.',
                                        chinese: '例子，实例，范例',
                                        chinese_short: '例子'
                                    }
                                ]
                            }
                        ]
                    }
                };
            }
            
            async fetch(word, options = {}) {
                this.log(`🔄 开始获取单词: ${word}`);
                this.log(`📝 选项: ${JSON.stringify(options)}`);
                
                // 模拟加载延迟
                await new Promise(resolve => setTimeout(resolve, 500));
                
                if (this.mockData[word]) {
                    this.log(`✅ 成功获取数据: ${word}`);
                    return {
                        data: this.mockData[word],
                        loading: false,
                        error: null
                    };
                } else {
                    this.log(`❌ 未找到单词: ${word}`);
                    return {
                        data: null,
                        loading: false,
                        error: new Error(`Word "${word}" not found`)
                    };
                }
            }
            
            log(message) {
                if (document.getElementById('enableLogs').checked) {
                    const logs = document.getElementById('testLogs');
                    const timestamp = new Date().toLocaleTimeString();
                    logs.innerHTML += `[${timestamp}] ${message}\n`;
                    logs.scrollTop = logs.scrollHeight;
                }
            }
        }
        
        // 创建 Hook 实例
        const useDictionary = new MockUseDictionary();
        
        // Tooltip 渲染函数
        function renderTooltip(data) {
            if (!data || !data.explain) return '';
            
            const parts = [];
            data.explain.forEach((item, index) => {
                const posShort = item.pos === 'noun' ? 'n.' :
                                item.pos === 'verb' ? 'v.' :
                                item.pos === 'adjective' ? 'adj.' :
                                item.pos === 'adverb' ? 'adv.' :
                                item.pos.substring(0, 3) + '.';
                
                parts.push(`<span class="lu-pos">${posShort}</span>`);
                
                item.definitions.forEach(def => {
                    parts.push(`<span class="lu-chinese-short">${def.chinese_short}</span>`);
                });
                
                if (index < data.explain.length - 1) {
                    parts.push(`<span class="lu-separator"> </span>`);
                }
            });
            
            return `<span class="lu-tooltip">${parts.join('')}</span>`;
        }
        
        // 骨架屏渲染
        function renderSkeleton() {
            return `
                <span class="lu-tooltip-skeleton">
                    <span class="lu-skeleton-text lu-skeleton-short"></span>
                    <span class="lu-separator"> </span>
                    <span class="lu-skeleton-text lu-skeleton-medium"></span>
                </span>
            `;
        }
        
        // 错误状态渲染
        function renderError(error, onRetry) {
            return `
                <span class="lu-tooltip-error">
                    ⚠ ${error.message}
                    <button class="lu-error-retry-btn" onclick="${onRetry}">重试</button>
                </span>
            `;
        }
        
        // 数据格式验证
        function validateDataFormat(data) {
            const validations = [
                {
                    name: 'word 字段',
                    valid: typeof data.word === 'string' && data.word.length > 0,
                    value: data.word
                },
                {
                    name: 'phonetic 字段',
                    valid: !data.phonetic || (typeof data.phonetic === 'object' && 
                        (typeof data.phonetic.us === 'string' || typeof data.phonetic.uk === 'string')),
                    value: data.phonetic ? `us: ${data.phonetic.us}, uk: ${data.phonetic.uk}` : 'undefined'
                },
                {
                    name: 'explain 数组',
                    valid: Array.isArray(data.explain) && data.explain.length > 0,
                    value: `${data.explain?.length || 0} 个词性`
                },
                {
                    name: 'explain 结构',
                    valid: data.explain?.every(item => 
                        typeof item.pos === 'string' &&
                        Array.isArray(item.definitions) &&
                        item.definitions.every(def => 
                            typeof def.definition === 'string' &&
                            typeof def.chinese === 'string' &&
                            typeof def.chinese_short === 'string'
                        )
                    ) || false,
                    value: 'pos, definitions 结构检查'
                }
            ];
            
            return validations;
        }
        
        // 运行测试
        async function runTest() {
            const word = document.getElementById('testWord').value.trim();
            const enableFresh = document.getElementById('enableFresh').checked;
            
            if (!word) {
                alert('请输入要测试的单词');
                return;
            }
            
            // 更新状态
            const statusEl = document.getElementById('testStatus');
            const demoEl = document.getElementById('tooltipDemo');
            const validationEl = document.getElementById('dataValidation');
            
            // 开始测试
            statusEl.className = 'status loading';
            statusEl.textContent = `正在测试单词: ${word}`;
            
            // 显示加载状态
            demoEl.innerHTML = renderSkeleton();
            
            try {
                const result = await useDictionary.fetch(word, { fresh: enableFresh });
                
                if (result.error) {
                    // 错误状态
                    statusEl.className = 'status error';
                    statusEl.textContent = `测试失败: ${result.error.message}`;
                    demoEl.innerHTML = renderError(result.error, 'runTest()');
                    validationEl.innerHTML = '<span style="color: red;">无法验证：获取数据失败</span>';
                } else {
                    // 成功状态
                    statusEl.className = 'status success';
                    statusEl.textContent = `测试成功: ${word}`;
                    demoEl.innerHTML = renderTooltip(result.data);
                    
                    // 数据格式验证
                    const validations = validateDataFormat(result.data);
                    const validationHtml = validations.map(v => 
                        `<div style="margin: 5px 0;">
                            <span style="color: ${v.valid ? 'green' : 'red'};">
                                ${v.valid ? '✅' : '❌'}
                            </span>
                            <strong>${v.name}:</strong> ${v.value}
                        </div>`
                    ).join('');
                    
                    validationEl.innerHTML = `
                        <h4>📋 数据格式验证结果</h4>
                        ${validationHtml}
                        <h4>🔍 完整数据结构</h4>
                        <pre style="font-size: 12px; background: #f5f5f5; padding: 10px; overflow: auto;">
${JSON.stringify(result.data, null, 2)}
                        </pre>
                    `;
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `测试异常: ${error.message}`;
                demoEl.innerHTML = renderError(error, 'runTest()');
            }
        }
        
        // 清除日志
        function clearLogs() {
            document.getElementById('testLogs').innerHTML = '日志已清除...\n';
        }
        
        // 高亮交互
        document.addEventListener('DOMContentLoaded', function() {
            const highlightWords = document.querySelectorAll('.highlight-word');
            const highlightTooltip = document.getElementById('highlightTooltip');
            
            highlightWords.forEach(word => {
                word.addEventListener('mouseenter', async function() {
                    const wordText = this.getAttribute('data-word');
                    highlightTooltip.innerHTML = renderSkeleton();
                    
                    try {
                        const result = await useDictionary.fetch(wordText);
                        if (result.data) {
                            highlightTooltip.innerHTML = renderTooltip(result.data);
                        } else {
                            highlightTooltip.innerHTML = `<span style="color: #666;">未找到 "${wordText}" 的定义</span>`;
                        }
                    } catch (error) {
                        highlightTooltip.innerHTML = renderError(error, '');
                    }
                });
                
                word.addEventListener('mouseleave', function() {
                    highlightTooltip.innerHTML = '';
                });
            });
        });
        
        // 初始化页面
        useDictionary.log('🚀 DynamicTooltip Hook 集成测试页面已加载');
        useDictionary.log('📌 可以开始运行测试了');
    </script>
</body>
</html>