/**
 * DynamicTooltip 集成测试
 * 验证 useDictionary Hook 与 Tooltip 组件的完整集成
 */

import { render, waitFor, fireEvent } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";
// React import removed - not needed with new JSX transform
import { DynamicTooltip } from "../DynamicTooltip";
import { dictionaryService } from "@dictionary/dictionary.service";
import { TooltipProps } from "@tooltip/Tooltip";

// Mock dictionary service
vi.mock("@dictionary/dictionary.service", () => ({
  dictionaryService: {
    getWord: vi.fn(),
  },
}));

const mockDictionaryService = dictionaryService as any;

describe("DynamicTooltip Integration Tests", () => {
  const mockTooltipData: TooltipProps = {
    word: "hello",
    phonetic: {
      us: "/həˈloʊ/",
      uk: "/həˈləʊ/",
    },
    explain: [
      {
        pos: "noun",
        definitions: [
          {
            definition: "A greeting or expression of goodwill.",
            chinese: "问候或善意的表达",
            chinese_short: "问候",
          },
        ],
      },
      {
        pos: "verb",
        definitions: [
          {
            definition: "To greet someone by saying hello.",
            chinese: "通过说你好来问候某人",
            chinese_short: "问候",
          },
        ],
      },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockDictionaryService.getWord.mockResolvedValue(mockTooltipData);
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe("基础集成测试", () => {
    it("应该显示加载状态然后显示数据", async () => {
      const { container } = render(<DynamicTooltip word="hello" />);

      // DynamicTooltip在loading时不显示skeleton，只在有数据时才显示tooltip
      // 等待数据加载完成
      await waitFor(() => {
        expect(container.querySelector(".lu-tooltip")).toBeTruthy();
      });

      // 验证数据已正确显示
      expect(mockDictionaryService.getWord).toHaveBeenCalledWith("hello", {
        fresh: false,
        skipCache: false,
        timeout: 3000, // 更新为实际的默认值
      });
    });

    it("应该正确渲染词典数据结构", async () => {
      const { container } = render(<DynamicTooltip word="hello" />);

      await waitFor(() => {
        expect(container.querySelector(".lu-tooltip")).toBeTruthy();
      });

      // 验证词性显示
      const posElements = container.querySelectorAll(".lu-pos");
      expect(posElements).toHaveLength(2); // noun 和 verb

      // 验证中文简短释义
      const chineseElements = container.querySelectorAll(".lu-chinese-short");
      expect(chineseElements).toHaveLength(2);
      expect(chineseElements[0].textContent).toBe("问候");
      expect(chineseElements[1].textContent).toBe("问候");
    });

    it("应该处理错误状态", async () => {
      const error = new Error("Network error");
      mockDictionaryService.getWord.mockRejectedValue(error);

      const { container } = render(<DynamicTooltip word="test" />);

      // DynamicTooltip在错误时返回null，不显示任何内容
      await waitFor(() => {
        expect(container.firstChild).toBeNull();
      });

      expect(mockDictionaryService.getWord).toHaveBeenCalledWith("test", {
        fresh: false,
        skipCache: false,
        timeout: 3000,
      });
    });

    it("应该支持重试功能", async () => {
      const error = new Error("Network error");
      
      // 首次请求失败
      mockDictionaryService.getWord.mockRejectedValueOnce(error);

      const { container, rerender } = render(<DynamicTooltip word="failing-word" />);

      // 等待第一次调用完成（失败）
      await waitFor(() => {
        expect(mockDictionaryService.getWord).toHaveBeenCalled();
      });

      // 第一次调用失败，DynamicTooltip返回null
      expect(container.firstChild).toBeNull();

      // 清除mock并设置成功响应
      mockDictionaryService.getWord.mockClear();
      mockDictionaryService.getWord.mockResolvedValue(mockTooltipData);

      // 使用不同的单词来避免缓存，相当于重试
      rerender(<DynamicTooltip word="success-word" />);

      // 验证重试后显示正确数据
      await waitFor(() => {
        expect(container.querySelector(".lu-tooltip")).toBeTruthy();
      }, { timeout: 3000 });

      expect(mockDictionaryService.getWord).toHaveBeenCalledWith("success-word", {
        fresh: false,
        skipCache: false,
        timeout: 3000,
      });
    });
  });

  describe("配置选项测试", () => {
    it("应该支持 fresh 选项", async () => {
      render(
        <DynamicTooltip word="hello" dictionaryOptions={{ fresh: true }} />
      );

      await waitFor(() => {
        expect(mockDictionaryService.getWord).toHaveBeenCalledWith("hello", {
          fresh: true,
          skipCache: false,
          timeout: 3000, // 更新为实际的默认值
        });
      });
    });

    it("应该支持 skipCache 选项", async () => {
      render(
        <DynamicTooltip word="hello" dictionaryOptions={{ skipCache: true }} />
      );

      await waitFor(() => {
        expect(mockDictionaryService.getWord).toHaveBeenCalledWith("hello", {
          fresh: false,
          skipCache: true,
          timeout: 3000, // 更新为实际的默认值
        });
      });
    });

    it("应该支持自定义超时时间", async () => {
      render(
        <DynamicTooltip word="hello" dictionaryOptions={{ timeout: 5000 }} />
      );

      await waitFor(() => {
        expect(mockDictionaryService.getWord).toHaveBeenCalledWith("hello", {
          fresh: false,
          skipCache: false,
          timeout: 5000,
        });
      });
    });
  });

  describe("主题和样式测试", () => {
    it("应该支持 dark 主题", async () => {
      const { container } = render(
        <DynamicTooltip word="hello" theme="dark" />
      );

      await waitFor(() => {
        const tooltip = container.querySelector(".lu-tooltip");
        expect(tooltip).toBeTruthy();
        expect(tooltip?.getAttribute("data-theme")).toBe("dark");
      });
    });

    it("应该支持 light 主题", async () => {
      const { container } = render(
        <DynamicTooltip word="hello" theme="light" />
      );

      await waitFor(() => {
        const tooltip = container.querySelector(".lu-tooltip");
        expect(tooltip).toBeTruthy();
        expect(tooltip?.getAttribute("data-theme")).toBe("light");
      });
    });
  });

  describe("fallback 行为测试", () => {
    it("应该在空单词时返回null", () => {
      const { container } = render(
        <DynamicTooltip
          word=""
          fallbackContent={<span>No word provided</span>}
        />
      );

      expect(container.firstChild).toBeNull();
      expect(mockDictionaryService.getWord).not.toHaveBeenCalled();
    });

    it("应该在无数据时返回null", async () => {
      mockDictionaryService.getWord.mockResolvedValue(null);

      const { container } = render(
        <DynamicTooltip
          word="unknown"
          fallbackContent={<span>Word not found</span>}
        />
      );

      await waitFor(() => {
        expect(container.firstChild).toBeNull();
      });
    });
  });

  describe("数据格式验证测试", () => {
    it("应该正确处理完整的词典数据格式", async () => {
      const complexData: TooltipProps = {
        word: "test",
        phonetic: { us: "/test/", uk: "/test/" },
        explain: [
          {
            pos: "noun",
            definitions: [
              {
                definition: "A procedure intended to establish quality",
                chinese: "测试，考验，试验的过程或方法",
                chinese_short: "测试",
              },
              {
                definition: "An examination for medical purposes",
                chinese: "医学检查或化验",
                chinese_short: "检查",
              },
            ],
          },
          {
            pos: "verb",
            definitions: [
              {
                definition: "To check quality or performance",
                chinese: "测试，检验某物的质量或性能",
                chinese_short: "测试",
              },
            ],
          },
          {
            pos: "adjective",
            definitions: [
              {
                definition: "Used as a trial",
                chinese: "试验的，测试的",
                chinese_short: "试验",
              },
            ],
          },
        ],
      };

      mockDictionaryService.getWord.mockResolvedValue(complexData);

      const { container } = render(<DynamicTooltip word="test" />);

      await waitFor(() => {
        expect(container.querySelector(".lu-tooltip")).toBeTruthy();
      });

      // 验证所有词性都被渲染
      const posElements = container.querySelectorAll(".lu-pos");
      expect(posElements).toHaveLength(3); // noun, verb, adjective

      // 验证词性简写
      expect(posElements[0].textContent).toBe("n.");
      expect(posElements[1].textContent).toBe("v.");
      expect(posElements[2].textContent).toBe("adj.");

      // 验证所有定义都被渲染
      const chineseElements = container.querySelectorAll(".lu-chinese-short");
      expect(chineseElements).toHaveLength(4); // 2 + 1 + 1 definitions
    });

    it("应该处理只有音标没有释义的数据", async () => {
      const phoneticOnlyData: TooltipProps = {
        word: "hello",
        phonetic: { us: "/həˈloʊ/", uk: "/həˈləʊ/" },
        explain: [],
      };

      mockDictionaryService.getWord.mockResolvedValue(phoneticOnlyData);

      const { container } = render(<DynamicTooltip word="hello" />);

      await waitFor(() => {
        expect(container.querySelector(".lu-tooltip")).toBeTruthy();
      });

      // 应该显示默认的例子结构
      expect(container.textContent).toContain("示例");
    });
  });

  describe("性能测试", () => {
    it("应该避免重复请求相同单词", async () => {
      const { rerender, container } = render(<DynamicTooltip word="hello" />);

      // 重新渲染相同单词
      rerender(<DynamicTooltip word="hello" />);
      rerender(<DynamicTooltip word="hello" />);

      await waitFor(() => {
        expect(container.querySelector(".lu-tooltip")).toBeTruthy();
      });

      // 由于 Hook 内部的去重逻辑，应该只调用一次
      expect(mockDictionaryService.getWord).toHaveBeenCalledTimes(1);
    });

    it("应该取消过时的请求", async () => {
      const { rerender } = render(<DynamicTooltip word="hello" />);

      // 快速更改单词
      rerender(<DynamicTooltip word="world" />);
      rerender(<DynamicTooltip word="test" />);

      await waitFor(() => {
        // 最终应该显示最后一个单词的数据
        expect(mockDictionaryService.getWord).toHaveBeenLastCalledWith(
          "test",
          expect.any(Object)
        );
      });
    });
  });
});
