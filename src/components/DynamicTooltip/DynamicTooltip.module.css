/**
 * DynamicTooltip 组件样式
 * 包括骨架屏、错误状态和动态加载效果
 */

/* CSS 变量定义 */
:root {
  --skeleton-bg: #444;
  --skeleton-highlight: #666;
  --error-bg: rgba(220, 38, 38, 0.9);
  --error-border: rgba(220, 38, 38, 0.6);
  --error-text: #ffffff;
}

/* === 骨架屏样式 === */
.lu-tooltip-skeleton {
  backdrop-filter: blur(14px) saturate(160%);
  -webkit-backdrop-filter: blur(14px) saturate(160%);
  background: rgba(30, 30, 30, 0.45);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  padding: 4px 12px;
  display: inline-block;
  white-space: nowrap;
  font-size: 0.88em;
  line-height: 1.45;
}

.lu-tooltip-skeleton[data-theme="light"] {
  background: rgba(240, 240, 240, 0.6);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.lu-skeleton-text {
  display: inline-block;
  height: 16px;
  background: var(--skeleton-bg);
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.lu-skeleton-text::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    var(--skeleton-highlight),
    transparent
  );
  animation: skeleton-loading 1.5s infinite;
}

.lu-skeleton-animated .lu-skeleton-text::after {
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 骨架屏尺寸变体 */
.lu-skeleton-short {
  width: 40px;
}

.lu-skeleton-medium {
  width: 80px;
}

.lu-skeleton-long {
  width: 120px;
}

.lu-skeleton-word-text {
  width: 60px;
}

.lu-skeleton-phonetic-text {
  width: 80px;
}

.lu-skeleton-definition-text {
  width: 100px;
}

.lu-skeleton-pos {
  width: 30px;
  height: 14px;
}

/* 骨架屏布局变体 */
.lu-tooltip-skeleton[data-variant="full"] {
  display: block;
  width: 250px;
  padding: 10px;
  white-space: normal;
}

.lu-skeleton-header {
  margin-bottom: 8px;
}

.lu-skeleton-word {
  margin-bottom: 4px;
}

.lu-skeleton-phonetic {
  margin-bottom: 8px;
}

.lu-skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.lu-skeleton-definition {
  display: flex;
  align-items: center;
  gap: 8px;
}

.lu-skeleton-separator {
  margin: 0 4px;
  color: rgba(255, 255, 255, 0.3);
}

/* === 错误状态样式 === */
.lu-tooltip-error {
  backdrop-filter: blur(14px) saturate(160%);
  -webkit-backdrop-filter: blur(14px) saturate(160%);
  background: var(--error-bg);
  border: 1px solid var(--error-border);
  border-radius: 8px;
  padding: 4px 12px;
  display: inline-block;
  font-size: 0.88em;
  line-height: 1.45;
  color: var(--error-text);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", 
               "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", 
               "Helvetica Neue", Helvetica, Arial, sans-serif;
  user-select: none;
}

.lu-error-minimal {
  white-space: nowrap;
}

.lu-error-standard {
  white-space: nowrap;
  max-width: 200px;
}

.lu-error-detailed {
  display: block;
  white-space: normal;
  max-width: 300px;
  padding: 8px 12px;
}

.lu-error-icon {
  margin-right: 4px;
  font-size: 1.1em;
}

.lu-error-word {
  font-weight: 500;
  margin-left: 4px;
}

.lu-error-message {
  color: var(--error-text);
}

.lu-error-retry-btn {
  background: transparent;
  border: 1px solid var(--error-text);
  color: var(--error-text);
  padding: 2px 8px;
  border-radius: 3px;
  cursor: pointer;
  margin-left: 8px;
  font-size: 12px;
  transition: all 0.15s ease;
}

.lu-error-retry-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.lu-error-retry-btn:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

/* 详细错误状态布局 */
.lu-error-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 600;
}

.lu-error-title {
  margin-left: 4px;
}

.lu-error-content {
  margin: 8px 0;
}

.lu-error-word-info,
.lu-error-details {
  margin: 4px 0;
  font-size: 0.9em;
}

.lu-error-label {
  font-weight: 500;
  margin-right: 6px;
  opacity: 0.9;
}

.lu-error-text,
.lu-error-detail-text {
  opacity: 0.95;
}

.lu-error-actions {
  margin-top: 10px;
  text-align: right;
}

.lu-error-actions .lu-error-retry-btn {
  margin-left: 0;
  padding: 4px 12px;
}

/* === 回退内容样式 === */
.lu-tooltip.lu-fallback {
  opacity: 0.8;
  font-style: italic;
  border-style: dashed;
}

/* === 主题变体 === */
.lu-tooltip-skeleton[data-theme="light"],
.lu-tooltip-error[data-theme="light"] {
  background: rgba(240, 240, 240, 0.9);
  border-color: rgba(0, 0, 0, 0.2);
  color: #333;
}

.lu-tooltip-error[data-theme="light"] {
  background: rgba(220, 38, 38, 0.1);
  border-color: rgba(220, 38, 38, 0.3);
  color: #dc2626;
}

.lu-tooltip-error[data-theme="light"] .lu-error-retry-btn {
  border-color: #dc2626;
  color: #dc2626;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
  .lu-tooltip-skeleton,
  .lu-tooltip-error {
    font-size: 0.8em;
    padding: 3px 10px;
  }
  
  .lu-tooltip-skeleton[data-variant="full"] {
    width: 200px;
    padding: 8px;
  }
  
  .lu-error-detailed {
    max-width: 250px;
    padding: 6px 10px;
  }
  
  .lu-skeleton-text {
    height: 14px;
  }
}

/* === 减少动画效果模式 === */
@media (prefers-reduced-motion: reduce) {
  .lu-skeleton-text::after {
    animation: none;
  }
  
  .lu-error-retry-btn {
    transition: none;
  }
}

/* === 高对比度模式 === */
@media (prefers-contrast: high) {
  .lu-tooltip-skeleton {
    background: rgba(0, 0, 0, 0.9);
    border-color: rgba(255, 255, 255, 0.9);
  }
  
  .lu-tooltip-error {
    background: rgba(220, 38, 38, 0.95);
    border-color: rgba(255, 255, 255, 0.9);
  }
  
  .lu-skeleton-text {
    background: #666;
  }
}

/* === 打印样式 === */
@media print {
  .lu-tooltip-skeleton,
  .lu-tooltip-error {
    background: white !important;
    border: 1px solid black !important;
    color: black !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }
  
  .lu-skeleton-text {
    background: #ccc !important;
  }
  
  .lu-skeleton-text::after {
    display: none;
  }
  
  .lu-error-retry-btn {
    border-color: black !important;
    color: black !important;
  }
}