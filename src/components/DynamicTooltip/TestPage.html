<!DOCTYPE html>
<html>
<head>
    <title>Interactive Tooltip Test</title>
    <style>
        body {
            padding: 20px;
            font-family: system-ui, sans-serif;
        }
        .test-area {
            padding: 20px;
            border: 2px dashed #ccc;
            margin: 20px 0;
        }
        /* 复制交互样式用于测试 */
        .lu-chinese-short.interactive {
            position: relative;
            padding: 2px 4px;
            margin: 0 1px;
            border-radius: 3px;
            transition: all 0.15s ease;
            border: 1px solid transparent;
            display: inline-block;
            cursor: pointer;
            user-select: none;
        }
        .lu-chinese-short.interactive:hover {
            background-color: rgba(59, 130, 246, 0.15);
            border-color: rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        }
    </style>
</head>
<body>
    <h1>Interactive Tooltip Test</h1>
    
    <div class="test-area">
        <h3>测试中文释义独立高亮和点击：</h3>
        <p>
            <span class="lu-pos">n.</span>
            <span class="lu-chinese-short interactive" onclick="handleClick('noun', '问候')">问候</span>
            <span class="lu-chinese-short interactive" onclick="handleClick('noun', '欢迎')">欢迎</span>
            <span class="lu-separator"> </span>
            <span class="lu-pos">v.</span>
            <span class="lu-chinese-short interactive" onclick="handleClick('verb', '招呼')">招呼</span>
        </p>
    </div>

    <div id="log">
        <h3>点击日志：</h3>
        <ul id="logList"></ul>
    </div>

    <script>
        function handleClick(pos, chinese) {
            console.log('Clicked:', pos, chinese);
            
            const logList = document.getElementById('logList');
            const li = document.createElement('li');
            li.textContent = `点击了 ${pos}.${chinese} - ${new Date().toLocaleTimeString()}`;
            logList.appendChild(li);
            
            // 测试localStorage
            const prefs = JSON.parse(localStorage.getItem('lucid-word-preferences') || '{}');
            if (!prefs.hello) prefs.hello = {};
            const key = `${pos}.${chinese}`;
            prefs.hello[key] = (prefs.hello[key] || 0) + 1;
            localStorage.setItem('lucid-word-preferences', JSON.stringify(prefs));
            
            console.log('Updated preferences:', prefs);
        }
        
        // 显示当前偏好
        function showPreferences() {
            const prefs = JSON.parse(localStorage.getItem('lucid-word-preferences') || '{}');
            console.log('Current preferences:', prefs);
        }
        
        showPreferences();
    </script>
</body>
</html>