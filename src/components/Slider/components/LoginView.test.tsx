/**
 * LoginView Component Tests
 * 测试优化后的登录组件
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { LoginView } from './LoginView';

// Mock useAuth hook
vi.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    login: vi.fn(),
    loading: false,
    error: null
  })
}));

describe('LoginView Component', () => {
  const mockProps = {
    onLogin: vi.fn(),
    onShowRegister: vi.fn(),
    onBack: vi.fn(),
    onForgotPassword: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with Chinese localization', () => {
    render(<LoginView {...mockProps} />);
    
    // 检查中文标题
    expect(screen.getByText('欢迎回来')).toBeInTheDocument();
    expect(screen.getByText('请先登录账号，以便使用 Lucid 的完整功能')).toBeInTheDocument();
    
    // 检查中文按钮文本
    expect(screen.getByText('继续使用 Google')).toBeInTheDocument();
    expect(screen.getByText('继续使用 GitHub')).toBeInTheDocument();
    expect(screen.getByText('使用邮箱登录')).toBeInTheDocument();
    expect(screen.getByText('忘记密码？')).toBeInTheDocument();
    expect(screen.getByText('注册账号')).toBeInTheDocument();
  });

  it('renders back button when onBack is provided', () => {
    render(<LoginView {...mockProps} />);
    
    const backButton = screen.getByText('返回');
    expect(backButton).toBeInTheDocument();
    
    fireEvent.click(backButton);
    expect(mockProps.onBack).toHaveBeenCalledTimes(1);
  });

  it('does not render back button when onBack is not provided', () => {
    const propsWithoutBack = { ...mockProps, onBack: undefined };
    render(<LoginView {...propsWithoutBack} />);
    
    expect(screen.queryByText('返回')).not.toBeInTheDocument();
  });

  it('renders input fields with Chinese placeholders', () => {
    render(<LoginView {...mockProps} />);
    
    expect(screen.getByPlaceholderText('邮箱')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('密码')).toBeInTheDocument();
  });

  it('calls onShowRegister when register button is clicked', () => {
    render(<LoginView {...mockProps} />);
    
    const registerButton = screen.getByText('注册账号');
    fireEvent.click(registerButton);
    
    expect(mockProps.onShowRegister).toHaveBeenCalledTimes(1);
  });

  it('calls onForgotPassword when forgot password button is clicked', () => {
    render(<LoginView {...mockProps} />);
    
    const forgotPasswordButton = screen.getByText('忘记密码？');
    fireEvent.click(forgotPasswordButton);
    
    expect(mockProps.onForgotPassword).toHaveBeenCalledTimes(1);
  });

  it('handles email and password input', () => {
    render(<LoginView {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('邮箱') as HTMLInputElement;
    const passwordInput = screen.getByPlaceholderText('密码') as HTMLInputElement;
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    
    expect(emailInput.value).toBe('<EMAIL>');
    expect(passwordInput.value).toBe('password123');
  });

  // Note: Loading state test would require more complex mocking setup
  // For now, we focus on UI rendering and interaction tests
});
