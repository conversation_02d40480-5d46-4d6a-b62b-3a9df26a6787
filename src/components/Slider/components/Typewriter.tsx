/**
 * Typewriter Component - 1:1 复刻自设计文件
 * 打字机效果组件，用于单词详情的动画显示
 */

import React, { useState, useEffect } from 'react';

interface TypewriterProps {
  text: string;
  speed?: number; // 打字速度（毫秒）
}

export const Typewriter: React.FC<TypewriterProps> = ({ text, speed = 20 }) => {
  const [displayedText, setDisplayedText] = useState('');

  useEffect(() => {
    setDisplayedText('');
    let i = 0;
    const intervalId = setInterval(() => {
      if (i < text.length) {
        setDisplayedText((prev) => prev + text.charAt(i));
        i++;
      } else {
        clearInterval(intervalId);
      }
    }, speed);
    
    return () => clearInterval(intervalId);
  }, [text, speed]);

  return <span>{displayedText}</span>;
};