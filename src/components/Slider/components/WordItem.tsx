/**
 * WordItem Component - 1:1 复刻自设计文件
 * 单词条目组件，支持展开、收藏、掌握等功能
 */

import React, { useState } from 'react';
// import styles from '../Slider.module.css'; // Commented out for direct CSS classes
import { ExpandIcon, CheckIcon, HeartIcon } from '../icons';
import { Typewriter } from './Typewriter';

// 复制设计文件中的数据类型
interface WordData {
  id: number;
  word: string;
  explain: {
    pos: string;
    definitions: {
      definition: string;
      chinese: string;
      chinese_short: string;
    }[];
  }[];
  wordFormats?: {
    name: string;
    form: string;
  }[];
  phonetic: {
    us: string;
    uk: string;
  };
}

interface WordItemProps {
  data: WordData;
}

export const WordItem: React.FC<WordItemProps> = ({ data }) => {
  const [isExpanded, setExpanded] = useState(false);
  const [isMastered, setMastered] = useState(false);
  const [isFavorite, setFavorite] = useState(false);

  const handleHeaderClick = (e: React.MouseEvent) => {
    // 如果点击的是操作按钮区域，不触发展开/折叠
    if ((e.target as HTMLElement).closest('.lu-word-actions')) {
      return;
    }
    setExpanded(!isExpanded);
  };

  const toggleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    setFavorite(!isFavorite);
  };

  const toggleMastered = (e: React.MouseEvent) => {
    e.stopPropagation();
    setMastered(!isMastered);
  };

  return (
    <div className={`lu-card lu-word-item ${isExpanded ? 'expanded' : ''}`}>
      <div className="lu-word-item-header" onClick={handleHeaderClick}>
        <div className="lu-word-text-wrapper">
          <ExpandIcon className="lu-expand-icon" />
          <div className="lu-word-text">{data.word}</div>
        </div>
        <div className="lu-word-actions">
          <button
            className={`lu-word-action master ${isMastered ? 'active' : ''}`}
            title="掌握"
            onClick={toggleMastered}
          >
            <CheckIcon />
          </button>
          <button
            className={`lu-word-action favorite ${isFavorite ? 'active' : ''}`}
            title="收藏"
            onClick={toggleFavorite}
          >
            <HeartIcon filled={isFavorite} />
          </button>
        </div>
      </div>
      
      <div className="lu-word-details">
        {/* 音标 */}
        <div className="lu-word-detail-item lu-phonetic-group">
          <strong>音标</strong>
          <div>
            <span className="lu-phonetic-item">
              <span className="lu-phonetic-label">US</span>{' '}
              <span className="lu-phonetic">{data.phonetic.us}</span>
            </span>
            <span className="lu-phonetic-item">
              <span className="lu-phonetic-label">UK</span>{' '}
              <span className="lu-phonetic">{data.phonetic.uk}</span>
            </span>
          </div>
        </div>

        {/* 释义 */}
        {data.explain.map((exp, index) => (
          <div key={index} className="lu-word-detail-item lu-pos-section">
            <strong>{exp.pos}</strong>
            <div className="lu-definitions-list">
              {exp.definitions.map((def, defIndex) => (
                <div key={defIndex} className="lu-definition-item">
                  <div className="lu-definition-cn">{def.chinese}</div>
                  <div className="lu-definition-en">
                    {isExpanded && <Typewriter text={def.definition} />}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* 单词形态 */}
        {data.wordFormats && data.wordFormats.length > 0 && (
          <div className="lu-word-detail-item">
            <strong>单词形态</strong>
            <div className="lu-word-forms-grid">
              {data.wordFormats.map((format, index) => (
                <React.Fragment key={index}>
                  <span className="lu-word-form-name">{format.name}</span>
                  <span className="lu-word-form-value">{format.form}</span>
                </React.Fragment>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};