/**
 * LoginView Demo - 展示优化后的登录组件
 * 用于开发和测试界面效果
 */

import React, { useState } from 'react';
import { LoginView } from './LoginView';

// Mock useAuth hook for demo
const mockUseAuth = () => ({
  login: async (email: string, password: string) => {
    console.log('Demo login attempt:', { email, password });
    // 模拟登录延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { success: true };
  },
  loading: false,
  error: null
});

// 临时替换 useAuth hook
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: mockUseAuth
}));

export const LoginViewDemo: React.FC = () => {
  const [currentView, setCurrentView] = useState<'login' | 'success'>('login');

  const handleLogin = () => {
    console.log('Login successful!');
    setCurrentView('success');
  };

  const handleShowRegister = () => {
    console.log('Show register view');
    alert('注册功能演示 - 将跳转到注册页面');
  };

  const handleBack = () => {
    console.log('Back to previous view');
    alert('返回上一页面');
  };

  const handleForgotPassword = () => {
    console.log('Forgot password');
    alert('忘记密码功能演示 - 将发送重置邮件');
  };

  if (currentView === 'success') {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        backgroundColor: '#111113',
        color: '#e2e2e2',
        fontFamily: 'Inter, sans-serif'
      }}>
        <div style={{ textAlign: 'center' }}>
          <h2>登录成功！</h2>
          <p>欢迎使用 Lucid</p>
          <button
            onClick={() => setCurrentView('login')}
            style={{
              padding: '12px 24px',
              backgroundColor: '#f97316',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              marginTop: '16px'
            }}
          >
            返回登录页面
          </button>
        </div>
      </div>
    );
  }

  return (
    <LoginView
      onLogin={handleLogin}
      onShowRegister={handleShowRegister}
      onBack={handleBack}
      onForgotPassword={handleForgotPassword}
    />
  );
};

export default LoginViewDemo;
