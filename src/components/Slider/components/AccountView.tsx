/**
 * AccountView Component - 1:1 复刻自设计文件
 * 账户页面组件 - 集成真实用户信息
 */

import React from 'react';
// import styles from '../Slider.module.css'; // Commented out for direct CSS classes
import { SettingItem } from './SettingItem';
import type { User } from '../../../types/auth';

interface AccountViewProps {
  onLogout: () => void;
  user: User | null;
}

export const AccountView: React.FC<AccountViewProps> = ({ onLogout, user }) => {
  // 格式化日期显示
  const formatDate = (dateString?: string) => {
    if (!dateString) return '未知';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch {
      return '未知';
    }
  };

  // 获取会员状态显示
  const getMembershipDisplay = () => {
    // 这里可以根据用户数据中的会员信息来显示
    // 暂时使用默认值，后续可以根据后端API返回的用户信息调整
    return 'Early Bird';
  };

  // 获取到期时间显示
  const getExpirationDate = () => {
    // 这里可以根据用户数据中的会员到期时间来显示
    // 暂时使用默认值，后续可以根据后端API返回的用户信息调整
    return '2024.12.31';
  };
  return (
    <div id="view-account" className="lu-view lu-account-view active">
      <div className="lu-card lu-settings-card">
        <div className="lu-setting-item">
          <div className="lu-setting-title">邮箱</div>
          <span className="lu-setting-value">
            {user?.email || '未知邮箱'}
          </span>
        </div>

        {user?.name && (
          <div className="lu-setting-item">
            <div className="lu-setting-title">姓名</div>
            <span className="lu-setting-value">{user.name}</span>
          </div>
        )}

        <div className="lu-setting-item">
          <div className="lu-setting-title">会员</div>
          <span className="lu-membership-tag early-bird">
            {getMembershipDisplay()}
          </span>
        </div>

        <div className="lu-setting-item">
          <div className="lu-setting-title">到期时间</div>
          <span className="lu-setting-value">
            {getExpirationDate()}
          </span>
        </div>

        <div className="lu-setting-item">
          <div className="lu-setting-title">注册时间</div>
          <span className="lu-setting-value">
            {formatDate(user?.createdAt)}
          </span>
        </div>

        <SettingItem title="订阅管理" hasArrow={true} />
        <SettingItem title="删除账号" hasArrow={true} />
      </div>

      <button className="lu-logout-button" onClick={onLogout}>
        退出账号
      </button>
    </div>
  );
};