/**
 * SettingItem Component - 1:1 复刻自设计文件
 * 设置页面的单项配置组件
 */

import React from 'react';
// import styles from '../Slider.module.css'; // Commented out for direct CSS classes
import { ArrowRightIcon } from '../icons';

interface SettingItemProps {
  icon?: React.ReactElement;
  title: string;
  value?: string | React.ReactNode;
  tag?: string;
  children?: React.ReactNode;
  hasArrow?: boolean;
  onClick?: () => void;
}

export const SettingItem: React.FC<SettingItemProps> = ({
  icon = null,
  title,
  value = null,
  tag = null,
  children = null,
  hasArrow = true,
  onClick = () => {},
}) => {
  return (
    <div className="lu-setting-item" onClick={onClick}>
      {icon && (
        <div className="lu-setting-icon">
          {React.cloneElement(icon as React.ReactElement<any>, {})}
        </div>
      )}
      <div className="lu-setting-title">
        {title}{' '}
        {tag && <span className="lu-new-tag">{tag}</span>}
      </div>
      {value && <span className="lu-setting-value">{value}</span>}
      {children}
      {hasArrow && !children && <ArrowRightIcon className="lu-setting-arrow" />}
    </div>
  );
};