/**
 * LoginView Component - 1:1 复刻自设计文件
 * 登录界面组件 - 集成真实认证服务
 */

import React, { useState, useCallback } from 'react';
// import styles from '../Slider.module.css'; // Commented out for direct CSS classes
import { GitHubIcon, GoogleIcon, AppleIcon, MailIcon } from '../icons';
import { useAuth } from '../../../hooks/useAuth';

interface LoginViewProps {
  onLogin: () => void;
  onShowRegister: () => void;
}

export const LoginView: React.FC<LoginViewProps> = ({ onLogin, onShowRegister }) => {
  const { login, loading, error } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // 处理邮箱登录
  const handleEmailLogin = useCallback(async () => {
    if (!email.trim()) {
      alert('请输入邮箱地址');
      return;
    }

    if (!password.trim()) {
      alert('请输入密码');
      return;
    }

    try {
      const result = await login(email.trim(), password);
      if (result.success) {
        onLogin(); // 通知父组件登录成功
      } else {
        alert(result.error || '登录失败');
      }
    } catch (error) {
      console.error('[LoginView] Login error:', error);
      alert('登录过程中发生错误');
    }
  }, [email, password, login, onLogin]);



  // 处理社交登录（暂时显示提示）
  const handleSocialLogin = useCallback((provider: string) => {
    alert(`${provider} 登录功能即将上线`);
  }, []);

  return (
    <div className="lu-login-container">
      <div className="lu-login-box">
        <div
          className="lu-brand"
          style={{ justifyContent: 'center', marginBottom: '32px' }}
        >
          <div className="lu-logo">L</div>
          <div className="lu-title">Welcome to Lucid</div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="lu-error-message" style={{
            color: '#ef4444',
            textAlign: 'center',
            marginBottom: '16px',
            fontSize: '14px'
          }}>
            {error}
          </div>
        )}

        <div className="lu-social-login-group">
          <button
            className="lu-social-login-btn github"
            onClick={() => handleSocialLogin('GitHub')}
            disabled={loading}
          >
            <GitHubIcon /> Sign in with GitHub
          </button>
          <button
            className="lu-social-login-btn google"
            onClick={() => handleSocialLogin('Google')}
            disabled={loading}
          >
            <GoogleIcon /> Sign in with Google
          </button>
        
        </div>

        <div className="lu-divider">
          <span>OR</span>
        </div>

        <div className="lu-email-form">
          <div className="lu-email-input-wrapper">
            <MailIcon />
            <input
              type="email"
              placeholder="Enter your email"
              className="lu-email-input"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
          </div>

          {/* 密码输入框 */}
          <div className="lu-email-input-wrapper" style={{ marginTop: '12px' }}>
            <input
              type="password"
              placeholder="Enter your password"
              className="lu-email-input"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleEmailLogin();
                }
              }}
            />
          </div>

          <button
            className="lu-send-code-btn"
            onClick={handleEmailLogin}
            disabled={loading}
            style={{
              opacity: loading ? 0.6 : 1,
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? '处理中...' : 'Sign In'}
          </button>

          {/* 注册链接 */}
          <div style={{ textAlign: 'center', marginTop: '16px' }}>
            <span style={{ color: '#666', fontSize: '14px' }}>
              Don't have an account?{' '}
              <button
                onClick={onShowRegister}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#007AFF',
                  cursor: 'pointer',
                  textDecoration: 'underline',
                  fontSize: '14px'
                }}
              >
                Sign up
              </button>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};