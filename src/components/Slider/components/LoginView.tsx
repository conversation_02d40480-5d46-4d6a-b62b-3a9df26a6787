/**
 * LoginView Component - 1:1 复刻自设计文件
 * 登录界面组件 - 集成真实认证服务
 */

import React, { useState, useCallback } from 'react';
// import styles from '../Slider.module.css'; // Commented out for direct CSS classes
import { GoogleIcon, GitHubIcon, MailIcon, LockIcon, BackIcon } from '../icons';
import { useAuth } from '../../../hooks/useAuth';

interface LoginViewProps {
  onLogin: () => void;
  onShowRegister: () => void;
  onBack?: () => void;
  onForgotPassword?: () => void;
}

export const LoginView: React.FC<LoginViewProps> = ({
  onLogin,
  onShowRegister,
  onBack,
  onForgotPassword
}) => {
  const { login, loading, error } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // 处理邮箱登录
  const handleEmailLogin = useCallback(async () => {
    if (!email.trim()) {
      alert('请输入邮箱地址');
      return;
    }

    if (!password.trim()) {
      alert('请输入密码');
      return;
    }

    try {
      const result = await login(email.trim(), password);
      if (result.success) {
        onLogin(); // 通知父组件登录成功
      }
      // 错误消息会通过 useAuth hook 的 error 状态自动显示在界面上
    } catch (error) {
      console.error('[LoginView] Login error:', error);
      // 不再使用 alert，错误会通过状态管理显示
    }
  }, [email, password, login, onLogin]);



  // 处理社交登录（暂时显示提示）
  const handleSocialLogin = useCallback((provider: string) => {
    alert(`${provider} 登录功能即将上线`);
  }, []);

  return (
    <div className="lu-login-container">
      <div className="lu-login-box">
        {/* 返回按钮 */}
        {onBack && (
          <button
            onClick={onBack}
            style={{
              position: 'absolute',
              top: '20px',
              left: '20px',
              background: 'none',
              border: 'none',
              color: '#e2e2e2',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              fontSize: '14px',
              padding: '8px'
            }}
          >
            <BackIcon />
            返回
          </button>
        )}

        {/* 标题区域 */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <h1 style={{
            color: '#e2e2e2',
            fontSize: '24px',
            fontWeight: '300',
            margin: '0 0 8px 0',
            fontFamily: '"Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
          }}>
            欢迎回来
          </h1>
          <p style={{
            color: '#9ca3af',
            fontSize: '14px',
            margin: '0',
            lineHeight: '1.5',
            fontWeight: '200',
            fontFamily: '"Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
          }}>
            请先登录账号，以便使用 Lucid 的完整功能
          </p>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="lu-error-message" style={{
            color: '#ef4444',
            textAlign: 'center',
            marginBottom: '16px',
            fontSize: '14px',
            fontWeight: '200',
            fontFamily: '"Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
          }}>
            {error}
          </div>
        )}

        <div className="lu-social-login-group">
          <button
            className="lu-social-login-btn google"
            onClick={() => handleSocialLogin('Google')}
            disabled={loading}
          >
            <GoogleIcon /> 继续使用 Google
          </button>
          <button
            className="lu-social-login-btn github"
            onClick={() => handleSocialLogin('GitHub')}
            disabled={loading}
          >
            <GitHubIcon /> 继续使用 GitHub
          </button>
        </div>

        <div className="lu-email-form">
          <div className="lu-email-input-wrapper">
            <MailIcon />
            <input
              type="email"
              placeholder="邮箱"
              className="lu-email-input"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
          </div>

          {/* 密码输入框 */}
          <div className="lu-email-input-wrapper" style={{ marginTop: '12px' }}>
            <LockIcon />
            <input
              type="password"
              placeholder="密码"
              className="lu-email-input"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleEmailLogin();
                }
              }}
            />
          </div>

          <button
            className="lu-send-code-btn"
            onClick={handleEmailLogin}
            disabled={loading}
            style={{
              opacity: loading ? 0.6 : 1,
              cursor: loading ? 'not-allowed' : 'pointer',
              marginTop: '16px'
            }}
          >
            {loading ? '登录中...' : '使用邮箱登录'}
          </button>

          {/* 底部链接 */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: '16px'
          }}>
            <button
              onClick={onForgotPassword}
              style={{
                background: 'none',
                border: 'none',
                color: '#9ca3af',
                cursor: 'pointer',
                fontSize: '14px',
                textDecoration: 'none'
              }}
            >
              忘记密码？
            </button>
            <button
              onClick={onShowRegister}
              style={{
                background: 'none',
                border: 'none',
                color: '#f97316',
                cursor: 'pointer',
                fontSize: '14px',
                textDecoration: 'none'
              }}
            >
              注册账号
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};