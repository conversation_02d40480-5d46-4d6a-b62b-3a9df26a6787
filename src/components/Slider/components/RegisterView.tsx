/**
 * RegisterView Component - 注册界面组件
 * 与 LoginView 保持一致的设计风格
 */

import React, { useState, useCallback } from 'react';
import { GoogleIcon, AppleIcon, MailIcon, LockIcon, BackIcon } from '../icons';
import { useAuth } from '../../../hooks/useAuth';

interface RegisterViewProps {
  onRegister: () => void;
  onShowLogin: () => void;
  onBack?: () => void;
}

export const RegisterView: React.FC<RegisterViewProps> = ({ 
  onRegister, 
  onShowLogin,
  onBack 
}) => {
  const { register, loading, error } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // 处理邮箱注册
  const handleEmailRegister = useCallback(async () => {
    if (!email.trim()) {
      alert('请输入邮箱地址');
      return;
    }

    if (!password.trim()) {
      alert('请输入密码');
      return;
    }

    if (password !== confirmPassword) {
      alert('两次输入的密码不一致');
      return;
    }

    if (password.length < 6) {
      alert('密码长度至少6位');
      return;
    }

    try {
      const result = await register(email.trim(), password);
      if (result.success) {
        onRegister(); // 通知父组件注册成功
      } else {
        alert(result.error || '注册失败');
      }
    } catch (error) {
      console.error('[RegisterView] Register error:', error);
      alert('注册过程中发生错误');
    }
  }, [email, password, confirmPassword, register, onRegister]);

  // 处理社交注册（暂时显示提示）
  const handleSocialRegister = useCallback((provider: string) => {
    alert(`${provider} 注册功能即将上线`);
  }, []);

  return (
    <div className="lu-login-container">
      <div className="lu-login-box">
        {/* 返回按钮 */}
        {onBack && (
          <button
            onClick={onBack}
            style={{
              position: 'absolute',
              top: '20px',
              left: '20px',
              background: 'none',
              border: 'none',
              color: '#e2e2e2',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              fontSize: '14px',
              padding: '8px'
            }}
          >
            <BackIcon />
            返回
          </button>
        )}

        {/* 标题区域 */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <h1 style={{ 
            color: '#e2e2e2', 
            fontSize: '24px', 
            fontWeight: '600', 
            margin: '0 0 8px 0' 
          }}>
            创建账号
          </h1>
          <p style={{ 
            color: '#9ca3af', 
            fontSize: '14px', 
            margin: '0',
            lineHeight: '1.5'
          }}>
            注册 Trancy 账号，开启您的学习之旅
          </p>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="lu-error-message" style={{
            color: '#ef4444',
            textAlign: 'center',
            marginBottom: '16px',
            fontSize: '14px'
          }}>
            {error}
          </div>
        )}

        <div className="lu-social-login-group">
          <button
            className="lu-social-login-btn google"
            onClick={() => handleSocialRegister('Google')}
            disabled={loading}
          >
            <GoogleIcon /> 使用 Google 注册
          </button>
          <button
            className="lu-social-login-btn apple"
            onClick={() => handleSocialRegister('Apple')}
            disabled={loading}
          >
            <AppleIcon /> 使用 Apple 注册
          </button>
        </div>

        <div className="lu-email-form">
          <div className="lu-email-input-wrapper">
            <MailIcon />
            <input
              type="email"
              placeholder="邮箱"
              className="lu-email-input"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
          </div>

          {/* 密码输入框 */}
          <div className="lu-email-input-wrapper" style={{ marginTop: '12px' }}>
            <LockIcon />
            <input
              type="password"
              placeholder="密码（至少6位）"
              className="lu-email-input"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
            />
          </div>

          {/* 确认密码输入框 */}
          <div className="lu-email-input-wrapper" style={{ marginTop: '12px' }}>
            <LockIcon />
            <input
              type="password"
              placeholder="确认密码"
              className="lu-email-input"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              disabled={loading}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleEmailRegister();
                }
              }}
            />
          </div>

          <button
            className="lu-send-code-btn"
            onClick={handleEmailRegister}
            disabled={loading}
            style={{
              opacity: loading ? 0.6 : 1,
              cursor: loading ? 'not-allowed' : 'pointer',
              marginTop: '16px'
            }}
          >
            {loading ? '注册中...' : '创建账号'}
          </button>

          {/* 底部链接 */}
          <div style={{ 
            textAlign: 'center',
            marginTop: '16px' 
          }}>
            <span style={{ color: '#9ca3af', fontSize: '14px' }}>
              已有账号？{' '}
              <button
                onClick={onShowLogin}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#f97316',
                  cursor: 'pointer',
                  fontSize: '14px',
                  textDecoration: 'none'
                }}
              >
                立即登录
              </button>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
