import React, { useState, useCallback } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { User } from '../../../types/auth';

// 图标组件
const GitHubIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
  </svg>
);

const GoogleIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24">
    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
  </svg>
);

const MailIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
    <polyline points="22,6 12,13 2,6"/>
  </svg>
);

interface RegisterViewProps {
  onRegister: () => void;
  onShowLogin: () => void;
}

export const RegisterView: React.FC<RegisterViewProps> = ({ onRegister, onShowLogin }) => {
  const { register, loading, error } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // 处理邮箱注册
  const handleEmailRegister = useCallback(async () => {
    if (!email.trim()) {
      alert('请输入邮箱地址');
      return;
    }

    if (!password.trim()) {
      alert('请输入密码');
      return;
    }

    try {
      const result = await register(email.trim(), password);
      if (result.success) {
        onRegister(); // 通知父组件注册成功
      } else {
        alert(result.error || '注册失败');
      }
    } catch (error) {
      console.error('[RegisterView] Register error:', error);
      alert('注册过程中发生错误');
    }
  }, [email, password, register, onRegister]);

  // 处理社交登录（暂时显示提示）
  const handleSocialLogin = useCallback((provider: string) => {
    alert(`${provider} 注册即将上线，敬请期待！`);
  }, []);

  return (
    <div className="lu-login-view">
      <div className="lu-login-container">
        <div className="lu-login-header">
          <h2>Welcome to Lucid</h2>
          <p>Create your account</p>
        </div>

        {/* 错误提示 */}
        {error && (
          <div style={{ 
            color: '#ff4444', 
            fontSize: '14px', 
            textAlign: 'center', 
            marginBottom: '16px',
            padding: '8px',
            backgroundColor: '#fff5f5',
            borderRadius: '4px',
            border: '1px solid #ffebee'
          }}>
            {error}
          </div>
        )}

        <div className="lu-social-login">
          <button
            className="lu-social-login-btn github"
            onClick={() => handleSocialLogin('GitHub')}
            disabled={loading}
          >
            <GitHubIcon /> Sign up with GitHub
          </button>
          
          <button
            className="lu-social-login-btn google"
            onClick={() => handleSocialLogin('Google')}
            disabled={loading}
          >
            <GoogleIcon /> Sign up with Google
          </button>
        </div>

        <div className="lu-divider">
          <span>OR</span>
        </div>

        <div className="lu-email-form">
          <div className="lu-email-input-wrapper">
            <MailIcon />
            <input
              type="email"
              placeholder="Enter your email"
              className="lu-email-input"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
          </div>

          {/* 密码输入框 */}
          <div className="lu-email-input-wrapper" style={{ marginTop: '12px' }}>
            <input
              type="password"
              placeholder="Enter your password"
              className="lu-email-input"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleEmailRegister();
                }
              }}
            />
          </div>

          <button
            className="lu-send-code-btn"
            onClick={handleEmailRegister}
            disabled={loading}
            style={{
              opacity: loading ? 0.6 : 1,
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? '处理中...' : 'Sign Up'}
          </button>

          {/* 登录链接 */}
          <div style={{ textAlign: 'center', marginTop: '16px' }}>
            <span style={{ color: '#666', fontSize: '14px' }}>
              Already have an account?{' '}
              <button
                onClick={onShowLogin}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#007AFF',
                  cursor: 'pointer',
                  textDecoration: 'underline',
                  fontSize: '14px'
                }}
              >
                Sign in
              </button>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterView;
