/**
 * RegisterView Component Tests
 * 测试注册组件功能
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { RegisterView } from './RegisterView';

// Mock useAuth hook
vi.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    register: vi.fn(),
    loading: false,
    error: null
  })
}));

describe('RegisterView Component', () => {
  const mockProps = {
    onRegister: vi.fn(),
    onShowLogin: vi.fn(),
    onBack: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with Chinese localization', () => {
    render(<RegisterView {...mockProps} />);
    
    // 检查中文标题
    expect(screen.getByText('创建账号')).toBeInTheDocument();
    expect(screen.getByText('注册 Trancy 账号，开启您的学习之旅')).toBeInTheDocument();
    
    // 检查中文按钮文本
    expect(screen.getByText('使用 Google 注册')).toBeInTheDocument();
    expect(screen.getByText('使用 Apple 注册')).toBeInTheDocument();
    expect(screen.getByText('创建账号')).toBeInTheDocument();
    expect(screen.getByText('立即登录')).toBeInTheDocument();
  });

  it('renders back button when onBack is provided', () => {
    render(<RegisterView {...mockProps} />);
    
    const backButton = screen.getByText('返回');
    expect(backButton).toBeInTheDocument();
    
    fireEvent.click(backButton);
    expect(mockProps.onBack).toHaveBeenCalledTimes(1);
  });

  it('does not render back button when onBack is not provided', () => {
    const propsWithoutBack = { ...mockProps, onBack: undefined };
    render(<RegisterView {...propsWithoutBack} />);
    
    expect(screen.queryByText('返回')).not.toBeInTheDocument();
  });

  it('renders input fields with Chinese placeholders', () => {
    render(<RegisterView {...mockProps} />);
    
    expect(screen.getByPlaceholderText('邮箱')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('密码（至少6位）')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('确认密码')).toBeInTheDocument();
  });

  it('calls onShowLogin when login button is clicked', () => {
    render(<RegisterView {...mockProps} />);
    
    const loginButton = screen.getByText('立即登录');
    fireEvent.click(loginButton);
    
    expect(mockProps.onShowLogin).toHaveBeenCalledTimes(1);
  });

  it('handles email and password input', () => {
    render(<RegisterView {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('邮箱') as HTMLInputElement;
    const passwordInput = screen.getByPlaceholderText('密码（至少6位）') as HTMLInputElement;
    const confirmPasswordInput = screen.getByPlaceholderText('确认密码') as HTMLInputElement;
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.change(confirmPasswordInput, { target: { value: 'password123' } });
    
    expect(emailInput.value).toBe('<EMAIL>');
    expect(passwordInput.value).toBe('password123');
    expect(confirmPasswordInput.value).toBe('password123');
  });

  it('shows validation error for empty email', () => {
    // Mock window.alert
    const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {});
    
    render(<RegisterView {...mockProps} />);
    
    const registerButton = screen.getByText('创建账号');
    fireEvent.click(registerButton);
    
    expect(alertSpy).toHaveBeenCalledWith('请输入邮箱地址');
    
    alertSpy.mockRestore();
  });

  it('shows validation error for password mismatch', () => {
    // Mock window.alert
    const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {});
    
    render(<RegisterView {...mockProps} />);
    
    const emailInput = screen.getByPlaceholderText('邮箱');
    const passwordInput = screen.getByPlaceholderText('密码（至少6位）');
    const confirmPasswordInput = screen.getByPlaceholderText('确认密码');
    const registerButton = screen.getByText('创建账号');
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.change(confirmPasswordInput, { target: { value: 'different' } });
    fireEvent.click(registerButton);
    
    expect(alertSpy).toHaveBeenCalledWith('两次输入的密码不一致');
    
    alertSpy.mockRestore();
  });
});
