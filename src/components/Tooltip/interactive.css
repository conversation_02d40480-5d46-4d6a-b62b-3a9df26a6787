/* 交互式Tooltip样式 */

.lu-chinese-short.interactive {
  position: relative;
  padding: 2px 4px;
  margin: 0 1px;
  border-radius: 3px;
  transition: all 0.15s ease;
  border: 1px solid transparent;
  display: inline-block;
  cursor: pointer;
  user-select: none;
}

/* 所有中文释义的默认hover效果 - 文字变白 */
.lu-chinese-short:hover {
  color: var(--text-white, #ffffff) !important;
}

/* 交互式中文释义的特殊hover效果 - 背景变白，覆盖默认效果 */
.lu-chinese-short.interactive:hover {
  background-color: white !important;
  color: #333 !important;
}

.lu-chinese-short.interactive:active {
  background-color: white !important;
  color: #333 !important;
}