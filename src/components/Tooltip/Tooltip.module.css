/**
 * Tooltip 样式
 * 基于 .claude/design/tooltip.html 的毛玻璃效果设计
 * 使用固定类名，与设计稿保持一致
 */

/* CSS 变量定义 - 在 Shadow DOM 内部 */
:root {
  --glass-bg: rgba(30, 30, 30, 0.45);
  --glass-border: rgba(255, 255, 255, 0.15);
  --text-gray: #dadada;
  --text-white: #ffffff;
}

/* 主要的 Tooltip 容器 - 固定类名 */
.lu-tooltip {
  /* 毛玻璃效果 */
  backdrop-filter: blur(14px) saturate(160%);
  -webkit-backdrop-filter: blur(14px) saturate(160%);

  /* 背景和边框 */
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 8px;

  /* 布局和间距 */
  padding: 4px 12px;
  display: inline-block;
  white-space: nowrap;

  /* 文本样式 */
  font-size: 0.88em;
  line-height: 1.45;
  color: var(--text-gray);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial,
    sans-serif;

  /* 动画效果 */
  transition: color 0.15s ease-out;

  /* 阴影效果 */
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.45);

  /* 防止文本选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 移除全局tooltip hover效果，改为单独元素hover */

/* 中文释义hover效果 */
.lu-chinese-short:hover {
  color: var(--text-white) !important;
}

/* 词性样式 */
.lu-pos {
  font-weight: 500;
  color: inherit;
  margin-right: 2px;
}

/* 中文简短释义样式 */
.lu-chinese-short {
  font-size: 0.9em;
  color: inherit;
  margin-right: 3px;
}

.lu-chinese-short:hover {
  color: var(--text-white) !important;
}

/* 分隔符样式 */
.lu-separator {
  margin: 0 4px;
  opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lu-tooltip {
    font-size: 0.8em;
    padding: 3px 10px;
  }

  .lu-pos {
    margin-right: 1px;
  }

  .lu-chinese-short {
    margin-right: 2px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .lu-tooltip {
    --glass-bg: rgba(0, 0, 0, 0.8);
    --glass-border: rgba(255, 255, 255, 0.8);
    --text-gray: #ffffff;
  }
}

/* 减少动画效果模式 */
@media (prefers-reduced-motion: reduce) {
  .lu-tooltip {
    transition: none;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .lu-tooltip {
    --glass-bg: rgba(30, 30, 30, 0.6);
    --glass-border: rgba(255, 255, 255, 0.2);
    --text-gray: #e0e0e0;
  }
}

/* 亮色主题支持 */
@media (prefers-color-scheme: light) {
  .lu-tooltip {
    --glass-bg: rgba(240, 240, 240, 0.6);
    --glass-border: rgba(0, 0, 0, 0.1);
    --text-gray: #333333;
    --text-white: #000000;
  }
}

/* 打印样式 */
@media print {
  .lu-tooltip {
    background: white !important;
    border: 1px solid black !important;
    color: black !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
  }

  .lu-word,
  .lu-definition {
    color: black !important;
  }
}

/* 聚焦样式（可访问性） */
.lu-tooltip:focus {
  outline: 2px solid var(--glass-border);
  outline-offset: 2px;
}

/* 不同状态的样式变体 */
.lu-tooltip.loading {
  opacity: 0.7;
  pointer-events: none;
}

.lu-tooltip.error {
  --glass-bg: rgba(220, 38, 38, 0.2);
  --glass-border: rgba(220, 38, 38, 0.3);
}

.lu-tooltip.success {
  --glass-bg: rgba(34, 197, 94, 0.2);
  --glass-border: rgba(34, 197, 94, 0.3);
}
