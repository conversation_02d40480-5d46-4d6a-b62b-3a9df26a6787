/**
 * 翻译系统设计令牌
 * 定义翻译功能的CSS变量和主题
 */

:root {
  /* 基础颜色 */
  --lu-origin-color: #111827;
  --lu-trans-color: #6b7280;
  --lu-trans-opacity: 0.6; /* 调整为0.6透明度 */
  
  /* 间距 - 减少xs值以缩短翻译与原文的距离 */
  --lu-spacing-xs: 0.125rem;
  --lu-spacing-sm: 0.5rem;
  --lu-spacing-md: 0.75rem;
  --lu-spacing-lg: 1rem;
  
  /* 字体 */
  --lu-font-size-sm: 0.875rem;
  --lu-font-size-base: 0.95em; /* 相对于父元素 */
  --lu-font-size-lg: 1rem;
  
  /* 行高 */
  --lu-line-height-tight: 1.4;
  --lu-line-height-normal: 1.6;
  --lu-line-height-relaxed: 1.8;
  
  /* 圆角 */
  --lu-border-radius: 0.25rem;
  --lu-border-radius-lg: 0.5rem;
  
  /* 阴影 */
  --lu-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --lu-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  
  /* 过渡 */
  --lu-transition-fast: 150ms ease-in-out;
  --lu-transition-normal: 250ms ease-in-out;
  --lu-transition-slow: 350ms ease-in-out;
  
  /* 焦点颜色 */
  --lu-focus-color: #3b82f6;
  --lu-focus-ring: 0 0 0 2px rgba(59, 130, 246, 0.5);
  
  /* 边框 */
  --lu-border-color: #e5e7eb;
  --lu-border-color-hover: #d1d5db;
}

/* 深色主题 */
html.dark {
  --lu-origin-color: #f9fafb;
  --lu-trans-color: #9ca3af;
  --lu-border-color: #374151;
  --lu-border-color-hover: #4b5563;
  --lu-focus-color: #60a5fa;
}

/* 高对比度主题 */
@media (prefers-contrast: high) {
  :root {
    --lu-trans-opacity: 1;
    --lu-trans-color: #000000;
  }
  
  html.dark {
    --lu-trans-color: #ffffff;
  }
}

/* 减动画主题 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --lu-transition-fast: 0ms;
    --lu-transition-normal: 0ms;
    --lu-transition-slow: 0ms;
  }
}

/* 小屏幕适配 */
@media (max-width: 768px) {
  :root {
    --lu-font-size-base: 0.9em;
    --lu-spacing-xs: 0.1rem;
    --lu-spacing-sm: 0.375rem;
    --lu-spacing-md: 0.625rem;
  }
}

/* 大屏幕适配 */
@media (min-width: 1200px) {
  :root {
    --lu-font-size-base: 1em;
    --lu-line-height-normal: 1.7;
  }
}

/* 打印样式 */
@media print {
  :root {
    --lu-trans-opacity: 0.8;
    --lu-trans-color: #000000;
    --lu-shadow-sm: none;
    --lu-shadow-md: none;
  }
}