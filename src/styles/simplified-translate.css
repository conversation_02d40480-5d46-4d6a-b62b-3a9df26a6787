/**
 * 简化版翻译样式
 * 支持原文/译文/双语模式切换
 */

/* 基础包装器样式 */
.lu-wrapper {
  display: block;
  margin: 0;
  padding: 0;
  border: none;
  background: transparent;
  font-family: inherit;
  line-height: inherit;
}

/* 原文容器 */
.lu-original {
  display: block;
  margin: 0;
  padding: 0;
}

/* 译文容器 */
.lu-translation {
  display: block;
  margin: 4px 0 0 0;
  padding: 8px 12px;
  background-color: rgba(0, 123, 255, 0.08);
  border-left: 3px solid rgba(0, 123, 255, 0.3);
  border-radius: 4px;
  font-size: 0.95em;
  color: #2c3e50;
  line-height: 1.5;
}

/* 模式切换 - 只显示原文 */
[lu-view="origin"] .lu-translation {
  display: none;
}

/* 模式切换 - 只显示译文 */
[lu-view="trans"] .lu-original {
  display: none;
}

[lu-view="trans"] .lu-translation {
  background-color: transparent;
  border: none;
  padding: 0;
  margin: 0;
  font-size: inherit;
}

/* 模式切换 - 双语显示（默认） */
[lu-view="dual"] .lu-original,
[lu-view="dual"] .lu-translation {
  display: block;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .lu-translation {
    background-color: rgba(0, 123, 255, 0.15);
    border-left-color: rgba(0, 123, 255, 0.4);
    color: #e8e8e8;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lu-translation {
    padding: 6px 10px;
    margin-top: 3px;
    font-size: 0.93em;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .lu-translation {
    background-color: rgba(0, 0, 0, 0.05);
    border-left: 2px solid #000;
    color: #000;
  }
  
  @media (prefers-color-scheme: dark) {
    .lu-translation {
      background-color: rgba(255, 255, 255, 0.1);
      border-left-color: #fff;
      color: #fff;
    }
  }
}

/* 加载状态 */
.lu-wrapper.lu-loading .lu-translation {
  opacity: 0.6;
  position: relative;
}

.lu-wrapper.lu-loading .lu-translation::after {
  content: "翻译中...";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.8em;
  color: #666;
}

/* 错误状态 */
.lu-wrapper.lu-error .lu-translation {
  background-color: rgba(220, 53, 69, 0.08);
  border-left-color: rgba(220, 53, 69, 0.3);
  color: #721c24;
}

.lu-wrapper.lu-error .lu-translation::before {
  content: "⚠️ ";
}

/* 动画效果 */
.lu-translation {
  transition: all 0.2s ease-in-out;
}

.lu-wrapper.lu-animate-in .lu-translation {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 调试模式样式 */
.lu-debug .lu-wrapper {
  outline: 2px dashed rgba(255, 193, 7, 0.5);
  background-color: rgba(255, 193, 7, 0.05);
}

.lu-debug .lu-original {
  outline: 1px solid rgba(40, 167, 69, 0.5);
}

.lu-debug .lu-translation {
  outline: 1px solid rgba(0, 123, 255, 0.5);
}