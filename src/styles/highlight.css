/**
 * Lucid Highlight System CSS
 * Dynamic styling for progressive word highlighting
 */

/* Base color variables */
:root {
  --lucid-highlight-start: #f97316; /* orange-500 */
  --lucid-highlight-end: #dc2626; /* red-600 */
  --lucid-current-color: var(--lucid-highlight-start);
}

/* Define custom element and class selector support */
lucid-highlight,
.lucid-highlight {
  display: inline;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

/* Color levels - progressive intensity */
lucid-highlight.lu-level-1,
.lucid-highlight.lu-level-1 {
  --lucid-current-color: color-mix(
    in srgb,
    var(--lucid-highlight-start) 100%,
    var(--lucid-highlight-end) 0%
  );
}

lucid-highlight.lu-level-2,
.lucid-highlight.lu-level-2 {
  --lucid-current-color: color-mix(
    in srgb,
    var(--lucid-highlight-start) 75%,
    var(--lucid-highlight-end) 25%
  );
}

lucid-highlight.lu-level-3,
.lucid-highlight.lu-level-3 {
  --lucid-current-color: color-mix(
    in srgb,
    var(--lucid-highlight-start) 50%,
    var(--lucid-highlight-end) 50%
  );
}

lucid-highlight.lu-level-4,
.lucid-highlight.lu-level-4 {
  --lucid-current-color: color-mix(
    in srgb,
    var(--lucid-highlight-start) 25%,
    var(--lucid-highlight-end) 75%
  );
}

lucid-highlight.lu-level-5,
.lucid-highlight.lu-level-5 {
  --lucid-current-color: color-mix(
    in srgb,
    var(--lucid-highlight-start) 0%,
    var(--lucid-highlight-end) 100%
  );
}

/* Visual effects */
lucid-highlight.lu-gradient,
.lucid-highlight.lu-gradient {
  background: linear-gradient(135deg, var(--lucid-current-color), #ffba8a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

lucid-highlight.lu-underline,
.lucid-highlight.lu-underline {
  text-decoration: underline;
  text-underline-offset: 3px;
  text-decoration-thickness: 2px;
  text-decoration-color: var(--lucid-current-color);
}

lucid-highlight.lu-bold,
.lucid-highlight.lu-bold {
  font-weight: bold;
}

lucid-highlight.lu-shadow,
.lucid-highlight.lu-shadow {
  text-shadow: 1px 1px 2px var(--lucid-current-color);
}

lucid-highlight.lu-glow,
.lucid-highlight.lu-glow {
  text-shadow: 0 0 8px var(--lucid-current-color);
}

lucid-highlight.lu-pulse,
.lucid-highlight.lu-pulse {
  animation: lu-pulse 2s infinite;
}

@keyframes lu-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Dark text adjustments */
lucid-highlight.lu-dark-text,
.lucid-highlight.lu-dark-text {
  --lucid-highlight-start: #fb923c; /* orange-400 - lighter for better contrast */
  --lucid-highlight-end: #ef4444; /* red-500 - lighter for better contrast */
}

/* Hover effects */
lucid-highlight:hover,
.lucid-highlight:hover {
  opacity: 0.8;
  transform: scale(1.02);
}

/* Focus effects for accessibility */
lucid-highlight:focus,
.lucid-highlight:focus {
  outline: 2px solid var(--lucid-current-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  lucid-highlight,
  .lucid-highlight {
    --lucid-highlight-start: #000000;
    --lucid-highlight-end: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  lucid-highlight,
  .lucid-highlight {
    transition: none;
  }

  lucid-highlight.lu-pulse,
  .lucid-highlight.lu-pulse {
    animation: none;
  }
}

/* Print styles */
@media print {
  lucid-highlight,
  .lucid-highlight {
    background: none !important;
    color: inherit !important;
    text-decoration: underline !important;
    text-shadow: none !important;
  }
}
