/**
 * 翻译系统主样式文件
 * 基于 lu- 命名规范的翻译界面样式
 */

/* 导入设计令牌 */
@import "./translate-tokens.css";

/* ==========================================================================
   翻译容器样式
   ========================================================================== */

lu-trans {
  white-space: break-spaces;
  pointer-events: none; /* 防止意外点击获取焦点 */
}

/* 但允许文本选择功能 */
lu-trans * {
  pointer-events: auto;
  user-select: text;
}

/* lu-strategy 基础样式 */
lu-strategy.inline {
  display: inline;
  margin-left: 10px;
}

/* lu-br 换行元素样式 */
lu-strategy lu-br {
  display: block !important;
  height: 10px !important;
  background: rgba(0, 0, 0, 0) !important;
}

lu-strategy.block lu-trans {
  margin-top: 0 !important;
  margin-bottom: 10px !important;
  display: inline-block;
  visibility: visible;
}

/* ===== Inline 策略样式 ===== */

lu-strategy.inline lu-trans {
  visibility: visible;
}

/* ==========================================================================
   特殊状态样式
   ========================================================================== */

/* 加载状态 */
.lu-wrapper.lu-loading .lu-block {
  opacity: 0.5;
  position: relative;
}

.lu-wrapper.lu-loading .lu-block::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: lu-shimmer 1.5s infinite;
}

@keyframes lu-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 错误状态 */
.lu-wrapper.lu-error .lu-block {
  color: #ef4444;
  font-style: italic;
}

.lu-wrapper.lu-error .lu-block::before {
  content: "⚠ ";
  opacity: 0.7;
}

/* 编辑状态 */
.lu-wrapper.lu-editable .lu-block {
  border: 1px dashed var(--lu-border-color);
  padding: var(--lu-spacing-xs);
  border-radius: var(--lu-border-radius);
  cursor: text;
}

.lu-wrapper.lu-editable .lu-block:hover {
  border-color: var(--lu-border-color-hover);
  background-color: rgba(59, 130, 246, 0.05);
}

/* ==========================================================================
   动画和过渡效果
   ========================================================================== */

/* 淡入动画 */
@keyframes lu-fade-in {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: var(--lu-trans-opacity);
    transform: translateY(0);
  }
}

.lu-wrapper.lu-animate-in .lu-block {
  animation: lu-fade-in var(--lu-transition-normal) ease-out;
}

/* 减动画用户的简化版本 */
@media (prefers-reduced-motion: reduce) {
  .lu-wrapper.lu-loading .lu-block::after {
    animation: none;
  }

  .lu-wrapper.lu-animate-in .lu-block {
    animation: none;
  }

  .lu-block {
    transition: none;
  }
}

/* ==========================================================================
   工具类
   ========================================================================== */

/* 隐藏翻译 */
.lu-hidden {
  display: none !important;
}

/* 强制显示 */
.lu-force-show {
  display: block !important;
  opacity: 1 !important;
}

/* 禁用翻译 - 这些类由翻译引擎识别，不需要设置样式 */

/* 调试模式 */
.lu-debug .lu-wrapper {
  outline: 1px dashed #ff6b6b;
}

.lu-debug .lu-block {
  background-color: rgba(255, 107, 107, 0.1);
}

/* ==========================================================================
   侧边注入布局样式 (Beside Injection)
   ========================================================================== */

/* 侧边翻译内容 - 与.lu-block保持完全一致的样式 */
.lu-beside-inline {
  /* 基础样式 - 与.lu-block完全一致 */
  display: inline !important;
  color: var(--lu-trans-color);
  font-size: var(--lu-font-size-base);
  font-family: inherit;
  font-weight: inherit;
  transition: color var(--lu-transition-normal);
  white-space: nowrap;
  vertical-align: baseline;
}

/* hover效果 - 与.lu-block保持一致 */
.lu-beside-inline:hover {
  opacity: 1;
  color: var(--lu-origin-color);
}

/* 所有 beside 注入的内层翻译块都应该是 inline */
.lu-beside-inline .lu-block {
  display: inline !important;
  white-space: nowrap;
  vertical-align: baseline;
}

/* Block注入容器样式 - 确保能撑起父容器高度 */
.lu-block-wrapper {
  display: block !important;
  width: 100%;
  clear: both; /* 清除浮动，确保撑起容器 */
  box-sizing: border-box;
}

/* 所有 block 注入的内层翻译块都应该是 block，并撑起容器高度 */
.lu-block-wrapper .lu-block {
  display: block !important;
  width: 100%;
  min-height: 1.2em; /* 确保有足够高度 */
  line-height: 1.4;
  box-sizing: border-box;
}

/* 标题元素中的侧边翻译 - 特殊字体大小 */
h1 .lu-beside-inline,
h2 .lu-beside-inline,
h3 .lu-beside-inline,
h4 .lu-beside-inline,
h5 .lu-beside-inline,
h6 .lu-beside-inline {
  font-size: 0.9em;
  font-weight: normal;
}
