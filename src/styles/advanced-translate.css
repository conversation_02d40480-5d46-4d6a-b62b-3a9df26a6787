/**
 * 高级翻译样式 - 支持自定义HTML元素
 * 基于用户坚持的 lu-strategy 和 lu-trans 元素设计
 */

/* ===== 自定义元素基础样式 ===== */

/* lu-strategy 基础样式 */
lu-strategy.inline {
  display: inline;
}

/* lu-br 换行元素样式 */
lu-strategy lu-br {
  display: block !important;
  height: 10px !important;
  background: rgba(0, 0, 0, 0) !important;
}

lu-strategy.block lu-trans {
  margin-top: 0 !important;
  margin-bottom: 10px !important;
  display: inline-block;
  visibility: visible;
}

/* ===== Inline 策略样式 ===== */

lu-strategy.inline lu-trans {
  visibility: visible;
  margin-left: 4px !important;
}

/* ===== 状态样式 ===== */

/* 已翻译状态 */
lu-strategy.lu-translated {
  position: relative;
}

lu-strategy.lu-translated::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  width: 4px;
  height: 4px;
  background-color: #28a745;
  border-radius: 50%;
  opacity: 0.7;
}

/* 加载状态 */
lu-strategy.lu-loading lu-trans {
  opacity: 0.4;
  position: relative;
  pointer-events: none;
}

lu-strategy.lu-loading lu-trans::after {
  content: "翻译中...";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.8em;
  color: #666;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 3px;
  white-space: nowrap;
}

/* 错误状态 */
lu-strategy.lu-error lu-trans {
  background-color: rgba(220, 53, 69, 0.08) !important;
  border-left-color: rgba(220, 53, 69, 0.3) !important;
  color: #721c24 !important;
}

lu-strategy.lu-error lu-trans::before {
  content: "⚠️ ";
  color: #dc3545;
}

/* ===== 动画效果 ===== */

lu-trans {
  transition: all 0.2s ease-in-out;
}

lu-strategy.lu-animate-in lu-trans {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 0.8;
    transform: translateY(0);
  }
}

/* ===== 调试模式样式 ===== */

[data-debug="true"] lu-strategy {
  outline: 2px dashed rgba(255, 193, 7, 0.5) !important;
  background-color: rgba(255, 193, 7, 0.05) !important;
}

[data-debug="true"] lu-trans {
  outline: 1px solid rgba(0, 123, 255, 0.5) !important;
}

.lu-debug lu-strategy,
lu-strategy[data-debug="true"] {
  position: relative;
}

.lu-debug lu-strategy::before,
lu-strategy[data-debug="true"]::before {
  content: attr(data-strategy) " #" attr(lu-id);
  position: absolute;
  top: -20px;
  left: 0;
  font-size: 10px;
  color: #ffc107;
  background: rgba(0, 0, 0, 0.7);
  padding: 1px 4px;
  border-radius: 2px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}
