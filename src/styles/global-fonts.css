/**
 * Lucid Extension Global Font Configuration
 * 统一字体设置 - Noto Sans SC with font-weight: 200
 */

/* 字体导入 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100..900&display=swap');

/* 全局字体变量 */
:root {
  --lu-font-family: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --lu-font-weight-thin: 100;
  --lu-font-weight-extralight: 200;
  --lu-font-weight-light: 300;
  --lu-font-weight-normal: 400;
  --lu-font-weight-medium: 500;
  --lu-font-weight-semibold: 600;
  --lu-font-weight-bold: 700;
  --lu-font-weight-extrabold: 800;
  --lu-font-weight-black: 900;
  
  /* 默认字重设置为 200 (extralight) */
  --lu-font-weight-default: var(--lu-font-weight-extralight);
}

/* 全局字体应用 */
* {
  font-family: var(--lu-font-family) !important;
  font-weight: var(--lu-font-weight-default) !important;
}

/* Lucid 组件专用字体设置 */
.lu-slide,
.lu-slide *,
[class^="lu-"],
[class*=" lu-"] {
  font-family: var(--lu-font-family) !important;
  font-weight: var(--lu-font-weight-default) !important;
}

/* 特殊元素的字重覆盖 */
.lu-slide h1,
.lu-slide h2,
.lu-slide h3,
.lu-slide h4,
.lu-slide h5,
.lu-slide h6 {
  font-weight: var(--lu-font-weight-light) !important; /* 标题使用 300 */
}

.lu-slide .lu-logo {
  font-weight: var(--lu-font-weight-bold) !important; /* Logo 使用 700 */
}

.lu-slide .lu-stat-number {
  font-weight: var(--lu-font-weight-semibold) !important; /* 统计数字使用 600 */
}

.lu-slide button {
  font-weight: var(--lu-font-weight-default) !important; /* 按钮使用默认 200 */
}

.lu-slide input,
.lu-slide textarea {
  font-weight: var(--lu-font-weight-default) !important; /* 输入框使用默认 200 */
}

/* 错误消息 */
.lu-error-message {
  font-family: var(--lu-font-family) !important;
  font-weight: var(--lu-font-weight-default) !important;
}

/* 社交登录按钮 */
.lu-social-login-btn {
  font-family: var(--lu-font-family) !important;
  font-weight: var(--lu-font-weight-default) !important;
}

/* 邮箱输入框 */
.lu-email-input {
  font-family: var(--lu-font-family) !important;
  font-weight: var(--lu-font-weight-default) !important;
}

/* 发送验证码按钮 */
.lu-send-code-btn {
  font-family: var(--lu-font-family) !important;
  font-weight: var(--lu-font-weight-default) !important;
}

/* 设置项 */
.lu-setting-item {
  font-family: var(--lu-font-family) !important;
  font-weight: var(--lu-font-weight-default) !important;
}

/* 单词项 */
.lu-word-item {
  font-family: var(--lu-font-family) !important;
  font-weight: var(--lu-font-weight-default) !important;
}

/* 确保 Shadow DOM 内的元素也应用字体 */
:host {
  font-family: var(--lu-font-family) !important;
  font-weight: var(--lu-font-weight-default) !important;
}

:host * {
  font-family: var(--lu-font-family) !important;
  font-weight: var(--lu-font-weight-default) !important;
}

/* 响应式字体大小调整 */
@media (max-width: 768px) {
  .lu-slide {
    font-size: 14px;
  }
  
  .lu-slide h1 {
    font-size: 20px;
  }
  
  .lu-slide h2 {
    font-size: 18px;
  }
}

@media (min-width: 1200px) {
  .lu-slide {
    font-size: 16px;
  }
  
  .lu-slide h1 {
    font-size: 26px;
  }
  
  .lu-slide h2 {
    font-size: 22px;
  }
}

/* 打印样式 */
@media print {
  * {
    font-family: var(--lu-font-family) !important;
    font-weight: var(--lu-font-weight-normal) !important; /* 打印时使用正常字重 */
  }
}
