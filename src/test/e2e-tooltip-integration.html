<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>端到端 Tooltip Hook 集成测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .test-panel {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .test-panel h2 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .test-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .test-controls input,
        .test-controls select,
        .test-controls button {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .test-controls button {
            background: #007acc;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .test-controls button:hover {
            background: #005c99;
        }
        
        .test-area {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            min-height: 100px;
            border: 2px dashed #dee2e6;
            position: relative;
        }
        
        .highlight-text {
            font-size: 18px;
            line-height: 1.6;
        }
        
        .highlight-word {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            padding: 3px 6px;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 3px;
            transition: all 0.3s ease;
            position: relative;
            display: inline-block;
        }
        
        .highlight-word:hover {
            background: linear-gradient(45deg, #ffc107, #ffeb3b);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
        }
        
        .tooltip-display {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            border: 1px solid #e0e0e0;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        
        .status-indicator {
            position: absolute;
            top: 10px;
            right: 15px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .log-panel {
            background: #1a1a1a;
            color: #00ff00;
            border-radius: 12px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #007acc;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 14px;
        }
        
        /* Tooltip 样式 - 重现实际样式 */
        .lu-tooltip {
            backdrop-filter: blur(14px) saturate(160%);
            -webkit-backdrop-filter: blur(14px) saturate(160%);
            background: rgba(30, 30, 30, 0.85);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            padding: 8px 16px;
            display: inline-block;
            white-space: nowrap;
            font-size: 16px;
            line-height: 1.45;
            color: #dadada;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
            box-shadow: 0 4px 32px rgba(0, 0, 0, 0.45);
            user-select: none;
        }
        
        .lu-pos {
            font-weight: 500;
            color: #ffc107;
            margin-right: 6px;
        }
        
        .lu-chinese-short {
            color: #dadada;
            margin-right: 8px;
        }
        
        .lu-separator {
            margin: 0 6px;
            opacity: 0.6;
        }
        
        /* 骨架屏样式 */
        .lu-tooltip-skeleton {
            backdrop-filter: blur(14px) saturate(160%);
            -webkit-backdrop-filter: blur(14px) saturate(160%);
            background: rgba(30, 30, 30, 0.45);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            padding: 8px 16px;
            display: inline-block;
            white-space: nowrap;
            font-size: 16px;
            line-height: 1.45;
        }
        
        .lu-skeleton-text {
            display: inline-block;
            height: 18px;
            background: #666;
            border-radius: 3px;
            position: relative;
            overflow: hidden;
            animation: skeleton-pulse 1.5s ease-in-out infinite;
        }
        
        .lu-skeleton-short { width: 50px; }
        .lu-skeleton-medium { width: 90px; }
        
        @keyframes skeleton-pulse {
            0%, 100% { opacity: 0.4; }
            50% { opacity: 0.8; }
        }
        
        /* 错误样式 */
        .lu-tooltip-error {
            backdrop-filter: blur(14px) saturate(160%);
            -webkit-backdrop-filter: blur(14px) saturate(160%);
            background: rgba(220, 38, 38, 0.9);
            border: 1px solid rgba(220, 38, 38, 0.6);
            border-radius: 8px;
            padding: 8px 16px;
            display: inline-block;
            font-size: 16px;
            line-height: 1.45;
            color: white;
        }
        
        .lu-error-retry-btn {
            background: transparent;
            border: 1px solid white;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 8px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 端到端 Tooltip Hook 集成测试</h1>
            <p>验证 useDictionary Hook 与 Tooltip 组件在真实环境中的完整集成</p>
        </div>
        
        <!-- 基础功能测试 -->
        <div class="test-panel">
            <h2>🎯 基础功能测试</h2>
            <div class="test-controls">
                <input type="text" id="basicTestWord" value="hello" placeholder="输入测试单词">
                <button onclick="testBasicIntegration()">测试基础集成</button>
                <button onclick="testErrorHandling()">测试错误处理</button>
                <button onclick="testDataFormat()">测试数据格式</button>
            </div>
            <div class="tooltip-display" id="basicTestResult">
                <span style="color: #999;">点击上方按钮开始测试...</span>
            </div>
        </div>
        
        <!-- 交互式高亮测试 -->
        <div class="test-panel">
            <h2>🖱️ 交互式高亮测试</h2>
            <p style="margin-bottom: 15px; color: #666;">鼠标悬停在高亮单词上查看动态 Tooltip：</p>
            <div class="test-area">
                <div class="highlight-text">
                    This is a <span class="highlight-word" data-word="test">test</span> sentence with 
                    <span class="highlight-word" data-word="hello">hello</span> and 
                    <span class="highlight-word" data-word="example">example</span> words that should 
                    show dynamic tooltips when you <span class="highlight-word" data-word="hover">hover</span> over them.
                    Try <span class="highlight-word" data-word="beautiful">beautiful</span> and 
                    <span class="highlight-word" data-word="amazing">amazing</span> words too!
                </div>
            </div>
            <div class="tooltip-display" id="hoverTooltipResult">
                <span style="color: #999;">悬停在上方高亮单词上查看效果...</span>
            </div>
        </div>
        
        <!-- 性能测试 -->
        <div class="test-panel">
            <h2>⚡ 性能测试</h2>
            <div class="test-controls">
                <select id="performanceTestType">
                    <option value="sequential">顺序加载测试</option>
                    <option value="concurrent">并发加载测试</option>
                    <option value="cache">缓存性能测试</option>
                </select>
                <button onclick="runPerformanceTest()">运行性能测试</button>
                <button onclick="clearPerformanceMetrics()">清除指标</button>
            </div>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="avgLoadTime">0</div>
                    <div class="metric-label">平均加载时间 (ms)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="cacheHitRate">0</div>
                    <div class="metric-label">缓存命中率 (%)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="totalRequests">0</div>
                    <div class="metric-label">总请求数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="errorRate">0</div>
                    <div class="metric-label">错误率 (%)</div>
                </div>
            </div>
        </div>
        
        <!-- 日志面板 -->
        <div class="test-panel">
            <h2>📋 实时日志</h2>
            <div class="test-controls">
                <button onclick="clearLogs()">清除日志</button>
                <button onclick="exportTestReport()">导出测试报告</button>
            </div>
            <div class="log-panel" id="logPanel">
                <div>🚀 端到端集成测试环境已就绪</div>
                <div>📌 可以开始运行各项测试</div>
                <div>💡 提示：所有交互都会记录在此日志中</div>
            </div>
        </div>
    </div>
    
    <script>
        // 全局测试状态
        const testState = {
            metrics: {
                totalRequests: 0,
                cacheHits: 0,
                totalLoadTime: 0,
                errors: 0
            },
            logs: [],
            mockService: null
        };
        
        // 模拟数据
        const mockDictionaryData = {
            hello: {
                word: 'hello',
                phonetic: { us: '/həˈloʊ/', uk: '/həˈləʊ/' },
                explain: [
                    {
                        pos: 'noun',
                        definitions: [{
                            definition: 'A greeting or expression of goodwill.',
                            chinese: '问候或善意的表达',
                            chinese_short: '问候'
                        }]
                    },
                    {
                        pos: 'verb',
                        definitions: [{
                            definition: 'To greet someone by saying hello.',
                            chinese: '通过说你好来问候某人',
                            chinese_short: '问候'
                        }]
                    }
                ]
            },
            test: {
                word: 'test',
                phonetic: { us: '/test/', uk: '/test/' },
                explain: [
                    {
                        pos: 'noun',
                        definitions: [{
                            definition: 'A procedure to establish quality.',
                            chinese: '测试，考验，试验的过程',
                            chinese_short: '测试'
                        }]
                    },
                    {
                        pos: 'verb',
                        definitions: [{
                            definition: 'To check quality or performance.',
                            chinese: '测试，检验某物的质量',
                            chinese_short: '测试'
                        }]
                    }
                ]
            },
            example: {
                word: 'example',
                phonetic: { us: '/ɪɡˈzæmpəl/', uk: '/ɪɡˈzɑːmpəl/' },
                explain: [
                    {
                        pos: 'noun',
                        definitions: [{
                            definition: 'A thing characteristic of its kind.',
                            chinese: '例子，实例，范例',
                            chinese_short: '例子'
                        }]
                    }
                ]
            },
            beautiful: {
                word: 'beautiful',
                phonetic: { us: '/ˈbjuːtɪfəl/', uk: '/ˈbjuːtɪfʊl/' },
                explain: [
                    {
                        pos: 'adjective',
                        definitions: [{
                            definition: 'Pleasing the senses or mind aesthetically.',
                            chinese: '美丽的，漂亮的，悦目的',
                            chinese_short: '美丽'
                        }]
                    }
                ]
            },
            amazing: {
                word: 'amazing',
                phonetic: { us: '/əˈmeɪzɪŋ/', uk: '/əˈmeɪzɪŋ/' },
                explain: [
                    {
                        pos: 'adjective',
                        definitions: [{
                            definition: 'Causing great surprise or wonder.',
                            chinese: '令人惊奇的，了不起的',
                            chinese_short: '惊奇'
                        }]
                    }
                ]
            },
            hover: {
                word: 'hover',
                phonetic: { us: '/ˈhʌvər/', uk: '/ˈhɒvə/' },
                explain: [
                    {
                        pos: 'verb',
                        definitions: [{
                            definition: 'To remain in one place in the air.',
                            chinese: '悬停，盘旋，徘徊',
                            chinese_short: '悬停'
                        }]
                    }
                ]
            }
        };
        
        // 模拟词典服务
        class MockDictionaryService {
            constructor() {
                this.cache = new Map();
                this.requestHistory = [];
            }
            
            async getWord(word, options = {}) {
                const startTime = Date.now();
                testState.metrics.totalRequests++;
                
                log(`🔄 请求单词: ${word}`, 'info');
                
                // 模拟缓存检查
                if (!options.fresh && this.cache.has(word)) {
                    testState.metrics.cacheHits++;
                    const cached = this.cache.get(word);
                    log(`✅ 缓存命中: ${word}`, 'success');
                    return cached;
                }
                
                // 模拟网络延迟
                const delay = Math.random() * 300 + 100; // 100-400ms
                await new Promise(resolve => setTimeout(resolve, delay));
                
                // 模拟错误
                if (word === 'error-word' || Math.random() < 0.05) {
                    testState.metrics.errors++;
                    const error = new Error(`Failed to fetch "${word}"`);
                    log(`❌ 请求失败: ${word} - ${error.message}`, 'error');
                    throw error;
                }
                
                const data = mockDictionaryData[word];
                if (data) {
                    this.cache.set(word, data);
                    const loadTime = Date.now() - startTime;
                    testState.metrics.totalLoadTime += loadTime;
                    log(`✅ 数据获取成功: ${word} (${loadTime}ms)`, 'success');
                    return data;
                } else {
                    log(`⚠️ 未找到单词: ${word}`, 'warning');
                    return null;
                }
            }
        }
        
        // 创建服务实例
        testState.mockService = new MockDictionaryService();
        
        // 模拟 useDictionary Hook
        async function useDictionary(word, options = {}) {
            try {
                const data = await testState.mockService.getWord(word, options);
                return {
                    data,
                    loading: false,
                    error: null,
                    isValidating: false
                };
            } catch (error) {
                return {
                    data: null,
                    loading: false,
                    error,
                    isValidating: false
                };
            }
        }
        
        // Tooltip 渲染函数
        function renderTooltip(data) {
            if (!data || !data.explain || data.explain.length === 0) {
                return '<span class="lu-tooltip">No data available</span>';
            }

            const parts = [];
            data.explain.forEach((item, index) => {
                const posShort = item.pos === 'noun' ? 'n.' :
                                item.pos === 'verb' ? 'v.' :
                                item.pos === 'adjective' ? 'adj.' :
                                item.pos === 'adverb' ? 'adv.' :
                                item.pos.substring(0, 3) + '.';

                parts.push(`<span class="lu-pos">${posShort}</span>`);

                item.definitions.forEach(def => {
                    parts.push(`<span class="lu-chinese-short">${def.chinese_short}</span>`);
                });

                if (index < data.explain.length - 1) {
                    parts.push('<span class="lu-separator"> </span>');
                }
            });

            return `<span class="lu-tooltip">${parts.join('')}</span>`;
        }
        
        function renderSkeleton() {
            return `
                <span class="lu-tooltip-skeleton">
                    <span class="lu-skeleton-text lu-skeleton-short"></span>
                    <span class="lu-separator"> </span>
                    <span class="lu-skeleton-text lu-skeleton-medium"></span>
                </span>
            `;
        }
        
        function renderError(error) {
            return `
                <span class="lu-tooltip-error">
                    ⚠ ${error.message}
                    <button class="lu-error-retry-btn" onclick="retryLastTest()">重试</button>
                </span>
            `;
        }
        
        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testState.logs.push({ timestamp, message, type });
            
            const logPanel = document.getElementById('logPanel');
            const div = document.createElement('div');
            div.textContent = logEntry;
            div.style.color = type === 'error' ? '#ff6b6b' : 
                             type === 'success' ? '#51cf66' :
                             type === 'warning' ? '#ffd43b' : '#00ff00';
            logPanel.appendChild(div);
            logPanel.scrollTop = logPanel.scrollHeight;
        }
        
        // 更新性能指标
        function updateMetrics() {
            const avgLoadTime = testState.metrics.totalRequests > 0 ? 
                Math.round(testState.metrics.totalLoadTime / (testState.metrics.totalRequests - testState.metrics.cacheHits)) : 0;
            const cacheHitRate = testState.metrics.totalRequests > 0 ? 
                Math.round((testState.metrics.cacheHits / testState.metrics.totalRequests) * 100) : 0;
            const errorRate = testState.metrics.totalRequests > 0 ? 
                Math.round((testState.metrics.errors / testState.metrics.totalRequests) * 100) : 0;
            
            document.getElementById('avgLoadTime').textContent = avgLoadTime;
            document.getElementById('cacheHitRate').textContent = cacheHitRate;
            document.getElementById('totalRequests').textContent = testState.metrics.totalRequests;
            document.getElementById('errorRate').textContent = errorRate;
        }
        
        // 测试函数
        async function testBasicIntegration() {
            const word = document.getElementById('basicTestWord').value.trim();
            const resultEl = document.getElementById('basicTestResult');
            
            if (!word) {
                alert('请输入要测试的单词');
                return;
            }
            
            log(`🧪 开始基础集成测试: ${word}`, 'info');
            
            // 显示加载状态
            resultEl.innerHTML = renderSkeleton() + '<div class="status-indicator status-loading">加载中</div>';
            
            try {
                const result = await useDictionary(word);
                
                if (result.data) {
                    const tooltipHtml = renderTooltip(result.data);
                    resultEl.innerHTML = tooltipHtml + '<div class="status-indicator status-success">成功</div>';
                    log(`✅ 基础集成测试通过: ${word}`, 'success');
                } else {
                    resultEl.innerHTML = `<span style="color: #666;">未找到 "${word}" 的定义</span><div class="status-indicator status-error">无数据</div>`;
                    log(`⚠️ 未找到单词定义: ${word}`, 'warning');
                }
            } catch (error) {
                resultEl.innerHTML = renderError(error) + '<div class="status-indicator status-error">错误</div>';
                log(`❌ 基础集成测试失败: ${error.message}`, 'error');
            }
            
            updateMetrics();
        }
        
        async function testErrorHandling() {
            const resultEl = document.getElementById('basicTestResult');
            
            log('🧪 开始错误处理测试', 'info');
            
            resultEl.innerHTML = renderSkeleton() + '<div class="status-indicator status-loading">测试错误</div>';
            
            try {
                const result = await useDictionary('error-word');
                
                if (result.error) {
                    const errorHtml = renderError(result.error);
                    resultEl.innerHTML = errorHtml + '<div class="status-indicator status-success">错误处理正常</div>';
                    log('✅ 错误处理测试通过', 'success');
                } else {
                    resultEl.innerHTML = '<span style="color: red;">错误处理测试失败：期望错误但未收到</span>';
                    log('❌ 错误处理测试失败：期望错误但未收到', 'error');
                }
            } catch (error) {
                resultEl.innerHTML = renderError(error) + '<div class="status-indicator status-success">错误处理正常</div>';
                log('✅ 错误处理测试通过（异常捕获）', 'success');
            }
            
            updateMetrics();
        }
        
        async function testDataFormat() {
            const word = document.getElementById('basicTestWord').value.trim() || 'hello';
            const resultEl = document.getElementById('basicTestResult');
            
            log(`🧪 开始数据格式验证测试: ${word}`, 'info');
            
            resultEl.innerHTML = renderSkeleton() + '<div class="status-indicator status-loading">验证格式</div>';
            
            try {
                const result = await useDictionary(word);
                
                if (result.data) {
                    const isValidFormat = 
                        typeof result.data.word === 'string' &&
                        Array.isArray(result.data.explain) &&
                        result.data.explain.every(item => 
                            typeof item.pos === 'string' &&
                            Array.isArray(item.definitions) &&
                            item.definitions.every(def => 
                                typeof def.definition === 'string' &&
                                typeof def.chinese === 'string' &&
                                typeof def.chinese_short === 'string'
                            )
                        );
                    
                    if (isValidFormat) {
                        const tooltipHtml = renderTooltip(result.data);
                        const formatInfo = `
                            <div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-radius: 8px; font-size: 12px;">
                                <strong>数据格式验证通过：</strong><br>
                                • 单词: "${result.data.word}"<br>
                                • 词性数量: ${result.data.explain.length}<br>
                                • 定义总数: ${result.data.explain.reduce((acc, item) => acc + item.definitions.length, 0)}<br>
                                • TooltipProps 格式: ✅
                            </div>
                        `;
                        resultEl.innerHTML = tooltipHtml + formatInfo + '<div class="status-indicator status-success">格式正确</div>';
                        log('✅ 数据格式验证通过', 'success');
                    } else {
                        resultEl.innerHTML = '<span style="color: red;">数据格式验证失败</span><div class="status-indicator status-error">格式错误</div>';
                        log('❌ 数据格式验证失败', 'error');
                    }
                } else {
                    resultEl.innerHTML = '<span style="color: #666;">无数据可验证</span><div class="status-indicator status-error">无数据</div>';
                    log('⚠️ 数据格式验证：无数据', 'warning');
                }
            } catch (error) {
                resultEl.innerHTML = renderError(error) + '<div class="status-indicator status-error">测试失败</div>';
                log(`❌ 数据格式验证失败: ${error.message}`, 'error');
            }
            
            updateMetrics();
        }
        
        // 性能测试
        async function runPerformanceTest() {
            const testType = document.getElementById('performanceTestType').value;
            log(`🚀 开始性能测试: ${testType}`, 'info');
            
            switch (testType) {
                case 'sequential':
                    await runSequentialTest();
                    break;
                case 'concurrent':
                    await runConcurrentTest();
                    break;
                case 'cache':
                    await runCacheTest();
                    break;
            }
            
            updateMetrics();
        }
        
        async function runSequentialTest() {
            const words = ['hello', 'test', 'example', 'beautiful', 'amazing'];
            log('📝 顺序加载测试开始', 'info');
            
            for (const word of words) {
                const startTime = Date.now();
                await useDictionary(word);
                const loadTime = Date.now() - startTime;
                log(`⏱️ ${word}: ${loadTime}ms`, 'info');
            }
            
            log('✅ 顺序加载测试完成', 'success');
        }
        
        async function runConcurrentTest() {
            const words = ['hello', 'test', 'example', 'beautiful', 'amazing'];
            log('📝 并发加载测试开始', 'info');
            
            const startTime = Date.now();
            const promises = words.map(word => useDictionary(word));
            await Promise.all(promises);
            const totalTime = Date.now() - startTime;
            
            log(`⏱️ 并发加载 ${words.length} 个单词: ${totalTime}ms`, 'info');
            log('✅ 并发加载测试完成', 'success');
        }
        
        async function runCacheTest() {
            const word = 'hello';
            log('📝 缓存性能测试开始', 'info');
            
            // 首次加载
            const startTime1 = Date.now();
            await useDictionary(word);
            const firstLoadTime = Date.now() - startTime1;
            
            // 缓存加载
            const startTime2 = Date.now();
            await useDictionary(word);
            const cacheLoadTime = Date.now() - startTime2;
            
            log(`⏱️ 首次加载: ${firstLoadTime}ms`, 'info');
            log(`⏱️ 缓存加载: ${cacheLoadTime}ms`, 'info');
            log(`📈 性能提升: ${Math.round((firstLoadTime / cacheLoadTime) * 100) / 100}x`, 'success');
            log('✅ 缓存性能测试完成', 'success');
        }
        
        function clearPerformanceMetrics() {
            testState.metrics = {
                totalRequests: 0,
                cacheHits: 0,
                totalLoadTime: 0,
                errors: 0
            };
            updateMetrics();
            log('🧹 性能指标已清除', 'info');
        }
        
        function clearLogs() {
            testState.logs = [];
            document.getElementById('logPanel').innerHTML = '<div>📋 日志已清除</div>';
        }
        
        function exportTestReport() {
            const report = {
                timestamp: new Date().toISOString(),
                metrics: testState.metrics,
                logs: testState.logs
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `tooltip-integration-test-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('📄 测试报告已导出', 'success');
        }
        
        // 初始化交互式高亮
        document.addEventListener('DOMContentLoaded', function() {
            const highlightWords = document.querySelectorAll('.highlight-word');
            const tooltipDisplay = document.getElementById('hoverTooltipResult');
            
            highlightWords.forEach(word => {
                let hoverTimeout;
                
                word.addEventListener('mouseenter', function() {
                    const wordText = this.getAttribute('data-word');
                    tooltipDisplay.innerHTML = renderSkeleton() + '<div class="status-indicator status-loading">加载中</div>';
                    
                    hoverTimeout = setTimeout(async () => {
                        try {
                            const result = await useDictionary(wordText);
                            if (result.data) {
                                const tooltipHtml = renderTooltip(result.data);
                                tooltipDisplay.innerHTML = tooltipHtml + '<div class="status-indicator status-success">悬停成功</div>';
                                log(`🖱️ 悬停获取: ${wordText}`, 'success');
                            } else {
                                tooltipDisplay.innerHTML = `<span style="color: #666;">未找到 "${wordText}" 的定义</span>`;
                                log(`⚠️ 悬停未找到: ${wordText}`, 'warning');
                            }
                        } catch (error) {
                            tooltipDisplay.innerHTML = renderError(error);
                            log(`❌ 悬停错误: ${wordText} - ${error.message}`, 'error');
                        }
                        updateMetrics();
                    }, 200);
                });
                
                word.addEventListener('mouseleave', function() {
                    if (hoverTimeout) {
                        clearTimeout(hoverTimeout);
                    }
                    tooltipDisplay.innerHTML = '<span style="color: #999;">悬停在上方高亮单词上查看效果...</span>';
                });
            });
        });
        
        // 初始化
        log('🌟 端到端集成测试页面初始化完成', 'success');
        log('💡 所有功能已就绪，可以开始测试', 'info');
    </script>
</body>
</html>