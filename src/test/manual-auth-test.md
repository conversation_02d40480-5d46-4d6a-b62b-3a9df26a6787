# 认证集成手动测试指南

## 测试目标
验证UI组件与认证服务的集成是否正常工作

## 测试环境准备

1. **启动开发环境**
   ```bash
   npm run dev
   ```

2. **加载扩展到浏览器**
   - 打开Chrome扩展管理页面 (chrome://extensions/)
   - 启用开发者模式
   - 点击"加载已解压的扩展程序"
   - 选择项目的 `.output/chrome-mv3` 目录

## 测试用例

### 1. 登录界面测试

**测试步骤：**
1. 右键点击页面，选择"Toggle Lucid Slider"或使用快捷键 `Ctrl+Shift+S`
2. 确认显示登录界面（因为初始状态为未登录）
3. 检查登录界面元素：
   - [ ] 显示"Welcome to Lucid"标题
   - [ ] 显示GitHub、Google、Apple登录按钮
   - [ ] 显示邮箱输入框和"Send Code"按钮

**预期结果：**
- 登录界面正常显示
- 所有UI元素正确渲染

### 2. 邮箱登录测试

**测试步骤：**
1. 在邮箱输入框中输入测试邮箱：`<EMAIL>`
2. 点击"Send Code"按钮
3. 确认界面切换到密码输入模式
4. 输入测试密码：`password123`
5. 点击"Sign In"按钮

**预期结果：**
- [ ] 界面正确切换到密码输入模式
- [ ] 点击登录按钮时显示"处理中..."状态
- [ ] 如果后端服务未启动，应显示网络错误提示
- [ ] 如果后端服务正常，应根据认证结果显示相应反馈

### 3. 社交登录测试

**测试步骤：**
1. 点击GitHub登录按钮
2. 点击Google登录按钮
3. 点击Apple登录按钮

**预期结果：**
- [ ] 每个按钮点击时显示"即将上线"提示
- [ ] 按钮在加载状态时正确禁用

### 4. 认证状态管理测试

**测试步骤：**
1. 检查AuthManager初始状态
2. 尝试登录操作
3. 观察UI状态变化

**预期结果：**
- [ ] 初始状态为未认证
- [ ] 登录过程中loading状态正确更新
- [ ] 错误状态正确显示和清除

### 5. 用户信息显示测试

**前提条件：** 需要成功登录

**测试步骤：**
1. 成功登录后，导航到账户页面
2. 检查用户信息显示

**预期结果：**
- [ ] 显示真实的用户邮箱
- [ ] 显示用户姓名（如果有）
- [ ] 显示注册时间
- [ ] 显示会员状态和到期时间

### 6. 登出功能测试

**前提条件：** 需要处于登录状态

**测试步骤：**
1. 在账户页面点击"退出账号"按钮
2. 观察界面变化

**预期结果：**
- [ ] 成功登出后返回登录界面
- [ ] 本地存储的认证数据被清除
- [ ] UI状态正确重置

## 错误处理测试

### 1. 网络错误测试
- 断开网络连接后尝试登录
- 预期显示网络错误提示

### 2. 无效凭据测试
- 使用错误的邮箱/密码组合
- 预期显示认证失败提示

### 3. 服务器错误测试
- 后端服务不可用时的错误处理
- 预期显示友好的错误信息

## 性能测试

### 1. 响应时间测试
- 登录操作的响应时间应在合理范围内
- UI状态更新应该流畅

### 2. 内存泄漏测试
- 多次打开/关闭Slider
- 多次登录/登出操作
- 检查是否有内存泄漏

## 测试结果记录

| 测试用例 | 状态 | 备注 |
|---------|------|------|
| 登录界面显示 | ⏳ | 待测试 |
| 邮箱登录流程 | ⏳ | 待测试 |
| 社交登录提示 | ⏳ | 待测试 |
| 认证状态管理 | ⏳ | 待测试 |
| 用户信息显示 | ⏳ | 待测试 |
| 登出功能 | ⏳ | 待测试 |
| 错误处理 | ⏳ | 待测试 |
| 性能表现 | ⏳ | 待测试 |

## 已知问题

1. **后端服务依赖**: 完整的登录测试需要后端服务运行
2. **社交登录**: 当前只显示提示，实际功能需要后续实现
3. **会员信息**: 当前使用默认值，需要后端API支持

## 下一步计划

1. 完成手动测试验证
2. 修复发现的问题
3. 优化用户体验
4. 准备生产环境部署
