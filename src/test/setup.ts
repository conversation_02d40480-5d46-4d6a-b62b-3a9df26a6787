import '@testing-library/jest-dom';
import { beforeAll, afterEach, afterAll, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { handlers } from './mocks/handlers';

// MSW server setup
export const server = setupServer(...handlers);

// Mock browser extension APIs - configurable for tests
const mockBrowser = {
  storage: {
    local: {
      get: vi.fn().mockImplementation((key) => {
        // Return proper config structure based on the key requested
        if (key === 'lucid-translate-config' || (Array.isArray(key) && key.includes('lucid-translate-config'))) {
          return Promise.resolve({
            'lucid-translate-config': {
              engines: {
                google: {
                  enabled: true,
                  priority: 1,
                  apis: [
                    { name: 'google-translate-api-1', enabled: true, url: 'https://translate-pa.googleapis.com/v1/translateHtml' },
                    { name: 'google-translate-api-2', enabled: true, url: 'https://translate.googleapis.com/translate_a/t' }
                  ]
                },
                microsoft: {
                  enabled: true,
                  priority: 2,
                  apis: [
                    { name: 'azure-translator', enabled: true, url: 'https://api.cognitive.microsofttranslator.com/translate' }
                  ]
                }
              }
            }
          });
        }
        // For other keys, return empty result
        return Promise.resolve({});
      }),
      set: vi.fn().mockResolvedValue(undefined),
      remove: vi.fn().mockResolvedValue(undefined),
      clear: vi.fn().mockResolvedValue(undefined)
    }
  },
  runtime: {
    getURL: vi.fn((path: string) => `chrome-extension://test-id/${path}`),
    sendMessage: vi.fn().mockImplementation((message: any, callback?: (response: any) => void) => {
      // Use setTimeout to simulate async behavior and ensure callback is called
      setTimeout(() => {
        if (callback) {
          // Handle new TRANSLATE_REQUEST format from engines
          if (message.type === 'TRANSLATE_REQUEST' && message.payload) {
            const { url, options } = message.payload;
            
            // Mock Google Translate API responses
            if (url.includes('translate-pa.googleapis.com/v1/translateHtml')) {
              // Parse request body to get texts
              let texts = ['Hello', 'World']; // Default fallback
              try {
                if (options.body) {
                  const parsed = JSON.parse(options.body);
                  if (Array.isArray(parsed) && Array.isArray(parsed[0])) {
                    texts = parsed[0];
                  }
                }
              } catch (e) {
                // Use default texts
              }
              
              const translations = texts.map(text => [
                getMockTranslation(text),
                text,
                null,
                null,
                1
              ]);
              
              callback({
                success: true,
                data: JSON.stringify([
                  [translations],
                  'te_lib'
                ]),
                status: 200
              });
              return;
            }
            
            if (url.includes('translate.googleapis.com/translate_a/t')) {
              // Handle form data format
              let texts = ['Hello', 'World']; // Default fallback
              try {
                if (options.body) {
                  const formData = new URLSearchParams(options.body);
                  texts = formData.getAll('q');
                }
              } catch (e) {
                // Use default texts
              }
              
              const translations = texts.map(text => [
                getMockTranslation(text),
                text,
                null,
                null,
                1
              ]);
              
              callback({
                success: true,
                data: JSON.stringify([translations]),
                status: 200
              });
              return;
            }
            
            // Mock Microsoft API responses
            if (url.includes('cognitive.microsofttranslator.com')) {
              callback({
                success: true,
                data: JSON.stringify([{
                  translations: [{
                    text: '翻译: test',
                    to: 'zh-Hans'
                  }]
                }]),
                status: 200
              });
              return;
            }
            
            if (url.includes('edge.microsoft.com/translate/auth')) {
              callback({
                success: true,
                data: 'mock-microsoft-auth-token-12345',
                status: 200
              });
              return;
            }
          }
          
          // Legacy format support for backward compatibility
          if (message.action === 'translate' && message.url) {
            // (keep existing logic for backward compatibility)
            // ... existing code ...
          }
          
          // Default mock response
          callback({ success: true, data: 'mock response' });
        }
      }, 10); // Very small delay to simulate async behavior
      
      // For Promise compatibility, also return a Promise (though engines use callback)
      return Promise.resolve({ success: true, data: 'mock response' });
    }),
    id: 'test-extension-id',
    lastError: null
  }
};

// Ensure browser is accessible in the global scope for typeof checks
if (typeof (globalThis as any).browser === 'undefined') {
  // Set as a global variable that can be accessed by typeof browser
  (globalThis as any).browser = mockBrowser;
  
  // Also set it on global/window for broader compatibility  
  Object.defineProperty(globalThis, 'browser', {
    value: mockBrowser,
    configurable: true,
    writable: true
  });
}

// Mock chrome API (fallback for browser API)
Object.defineProperty(global, 'chrome', {
  value: (globalThis as any).browser,
  configurable: true,
  writable: true
});

// Mock DOM APIs that might be missing in test environment
Object.defineProperty(global, 'ResizeObserver', {
  value: class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
});

// Helper function to provide mock translations
function getMockTranslation(text: string): string {
  const translations: Record<string, string> = {
    'Hello, World!': '你好，世界！',
    'How are you today?': '你今天好吗？',
    'This is a test sentence.': '这是一个测试句子。',
    'Good morning, everyone!': '大家早上好！',
    'Hello': '你好',
    'World': '世界',
    'test': '测试',
    'Hello, World': '你好，世界'
  };

  return translations[text] || `翻译: ${text}`;
}

// Mock CustomEvent for JSDOM compatibility
if (typeof CustomEvent === 'undefined') {
  global.CustomEvent = class CustomEvent extends Event {
    detail: any;

    constructor(type: string, options: CustomEventInit = {}) {
      super(type, options);
      this.detail = options.detail;
    }
  } as any;
}

// Mock fetch for tests that don't use MSW
if (typeof global.fetch === 'undefined') {
  global.fetch = vi.fn().mockImplementation(async (url: string, options?: RequestInit) => {
    // Mock Google Translate API responses
    if (url.includes('translate-pa.googleapis.com')) {
      return {
        ok: true,
        status: 200,
        json: () => Promise.resolve([
          [[[['翻译: test', 'test', null, null, 1]]]],
          'te_lib'
        ]),
        text: () => Promise.resolve(JSON.stringify([
          [[[['翻译: test', 'test', null, null, 1]]]],
          'te_lib'
        ])),
      };
    }
    
    if (url.includes('translate.googleapis.com')) {
      return {
        ok: true,
        status: 200,
        json: () => Promise.resolve([[[['翻译: test', 'test', null, null, 1]]]]),
        text: () => Promise.resolve(JSON.stringify([[[['翻译: test', 'test', null, null, 1]]]])),
      };
    }
    
    // Mock Microsoft API responses
    if (url.includes('cognitive.microsofttranslator.com')) {
      return {
        ok: true,
        status: 200,
        json: () => Promise.resolve([{
          translations: [{
            text: '翻译: test',
            to: 'zh-Hans'
          }]
        }]),
        text: () => Promise.resolve(JSON.stringify([{
          translations: [{
            text: '翻译: test',
            to: 'zh-Hans'
          }]
        }])),
      };
    }
    
    if (url.includes('edge.microsoft.com/translate/auth')) {
      return {
        ok: true,
        status: 200,
        json: () => Promise.resolve('mock-microsoft-auth-token-12345'),
        text: () => Promise.resolve('mock-microsoft-auth-token-12345'),
      };
    }
    
    // Default response
    return {
      ok: true,
      status: 200,
      json: () => Promise.resolve({}),
      text: () => Promise.resolve(''),
    };
  });
}

// 智能控制台过滤系统 - 减少测试期间的日志噪声
const originalConsole = {
  log: console.log,
  warn: console.warn,
  error: console.error,
  info: console.info,
  debug: console.debug
};

// 定义需要保留的日志模式
const ALLOWED_LOG_PATTERNS = [
  // 测试框架相关
  /VITEST|TEST|FAIL|PASS|SKIP/i,
  // 测试结果统计
  /Tests?\s+\d+|Start at|Duration/i,
  /Test Files/i,
  // MSW相关重要信息（仅保留错误）
  /\[MSW\].*error/i,
];

const ALLOWED_WARN_PATTERNS = [
  // React 测试警告
  /act\(/i,
  /Warning:/i,
  // React组件相关警告
  /React/i,
  // 测试特定警告
  /deprecated|unsafe/i,
];

const ALLOWED_ERROR_PATTERNS = [
  // 保留所有错误信息用于调试
  /.*/
];

// 智能过滤函数
function shouldAllowLog(message: string, patterns: RegExp[]): boolean {
  return patterns.some(pattern => pattern.test(message));
}

// Setup and teardown
beforeAll(() => {
  server.listen({
    onUnhandledRequest: 'bypass' // 静默处理未处理的请求，减少测试日志输出
  });
  
  // 设置智能console过滤
  console.log = vi.fn((...args) => {
    const message = args.join(' ');
    if (shouldAllowLog(message, ALLOWED_LOG_PATTERNS)) {
      originalConsole.log(...args);
    }
  });
  
  console.warn = vi.fn((...args) => {
    const message = args.join(' ');
    if (shouldAllowLog(message, ALLOWED_WARN_PATTERNS)) {
      originalConsole.warn(...args);
    }
  });
  
  console.error = vi.fn((...args) => {
    const message = args.join(' ');
    if (shouldAllowLog(message, ALLOWED_ERROR_PATTERNS)) {
      originalConsole.error(...args);
    }
  });
  
  // 完全静默info和debug日志
  console.info = vi.fn();
  console.debug = vi.fn();
});

afterEach(() => {
  cleanup();
  server.resetHandlers();
  // Reset all mocks including browser API mocks
  vi.clearAllMocks();
});

afterAll(() => {
  server.close();
  
  // 恢复原始console方法
  console.log = originalConsole.log;
  console.warn = originalConsole.warn;
  console.error = originalConsole.error;
  console.info = originalConsole.info;
  console.debug = originalConsole.debug;
});