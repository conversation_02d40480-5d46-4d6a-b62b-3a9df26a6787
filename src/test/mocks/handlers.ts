import { http, HttpResponse } from 'msw';

// Mock dictionary API responses
export const mockDictionaryData = {
  'hello': {
    word: 'hello',
    phonetic: '/həˈləʊ/',
    phonetics: [
      {
        text: '/həˈləʊ/',
        audio: 'https://api.dictionaryapi.dev/media/pronunciations/en/hello-us.mp3'
      }
    ],
    meanings: [
      {
        partOfSpeech: 'noun',
        definitions: [
          {
            definition: 'A greeting or expression of goodwill.',
            example: 'She gave me a warm hello.',
            synonyms: ['greeting', 'salutation']
          }
        ]
      },
      {
        partOfSpeech: 'verb',
        definitions: [
          {
            definition: 'To greet someone by saying hello.',
            example: 'I helloed to my neighbor.',
            synonyms: ['greet', 'salute']
          }
        ]
      }
    ]
  },
  'test': {
    word: 'test',
    phonetic: '/tɛst/',
    phonetics: [
      {
        text: '/tɛst/',
        audio: 'https://api.dictionaryapi.dev/media/pronunciations/en/test-us.mp3'
      }
    ],
    meanings: [
      {
        partOfSpeech: 'noun',
        definitions: [
          {
            definition: 'A procedure intended to establish the quality, performance, or reliability of something.',
            example: 'The test results were positive.',
            synonyms: ['examination', 'trial']
          }
        ]
      },
      {
        partOfSpeech: 'verb',
        definitions: [
          {
            definition: 'To take measures to check the quality, performance, or reliability of something.',
            example: 'We need to test the new software.',
            synonyms: ['examine', 'try']
          }
        ]
      }
    ]
  }
};

export const handlers = [
  // Dictionary API mock
  http.get('http://localhost:4000/api/dictionary/en/:word', ({ params }) => {
    const word = params.word as string;
    const mockData = mockDictionaryData[word as keyof typeof mockDictionaryData];

    if (mockData) {
      return HttpResponse.json(mockData);
    }

    return HttpResponse.json(
      { error: 'Word not found' },
      { status: 404 }
    );
  }),

  // Handle fresh parameter
  http.get('http://localhost:4000/api/dictionary/en/:word', ({ params, request }) => {
    const url = new URL(request.url);
    const fresh = url.searchParams.get('fresh');
    const word = params.word as string;

    // Simulate cache behavior
    if (fresh === 'true') {
      // Force fresh data
      console.log(`Mock API: Fetching fresh data for "${word}"`);
    }

    const mockData = mockDictionaryData[word as keyof typeof mockDictionaryData];

    if (mockData) {
      return HttpResponse.json(mockData);
    }

    return HttpResponse.json(
      { error: 'Word not found' },
      { status: 404 }
    );
  }),

  // Error simulation for testing
  http.get('http://localhost:4000/api/dictionary/en/error', () => {
    return HttpResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }),

  // Timeout simulation
  http.get('http://localhost:4000/api/dictionary/en/timeout', () => {
    return new Promise(() => {
      // Never resolve to simulate timeout
    });
  }),

  // Microsoft Translate API - Authentication endpoint
  http.get('https://edge.microsoft.com/translate/auth', () => {
    return HttpResponse.text('mock-microsoft-auth-token-12345');
  }),

  // Microsoft Translate API - Translation endpoint
  http.post('https://api.cognitive.microsofttranslator.com/translate', async ({ request }) => {
    try {
      const body = await request.json() as Array<{ Text: string }>;
      
      // Mock translation responses
      const translations = body.map(item => ({
        translations: [
          {
            text: getMockTranslation(item.Text),
            to: 'zh-Hans'
          }
        ]
      }));

      return HttpResponse.json(translations);
    } catch (error) {
      return HttpResponse.json({ error: 'Invalid request format' }, { status: 400 });
    }
  }),

  // Google Translate API 1 - translateHtml endpoint (JSON format)
  http.post('https://translate-pa.googleapis.com/v1/translateHtml', async ({ request }) => {
    try {
      // Clone the request to avoid "Body has already been used" error
      const clonedRequest = request.clone();
      const body = await clonedRequest.json() as [string[], string, string, string] | any;
      
      let texts: string[];
      // Expected format: [['Hello', 'World'], 'en', 'zh', 'te_lib']
      if (Array.isArray(body) && Array.isArray(body[0])) {
        // Format: [texts[], from, to, format]
        texts = body[0];
      } else if (Array.isArray(body)) {
        // Fallback: assume first element is text array
        texts = Array.isArray(body[0]) ? body[0] : [body[0]];
      } else {
        texts = [body];
      }
      
      // Mock protobuf-style response format: [[translations]]
      const translations = texts.map(text => [
        getMockTranslation(text),
        text,
        null,
        null,
        1
      ]);

      return HttpResponse.json([
        [translations],
        'te_lib'
      ]);
    } catch (error) {
      console.error('Google API 1 handler error:', error);
      return HttpResponse.json({ error: 'Invalid request format' }, { status: 400 });
    }
  }),

  // Google Translate API 2 - translate_a/t endpoint (form data)
  http.post('https://translate.googleapis.com/translate_a/t', async ({ request }) => {
    try {
      const formData = await request.formData();
      const texts = formData.getAll('q') as string[];
      
      // Filter out empty strings and zero-width spaces
      const validTexts = texts.filter(text => text && text.trim() && text !== '​');
      
      // Mock simplified response format
      const translations = validTexts.map(text => [
        getMockTranslation(text),
        text,
        null,
        null,
        1
      ]);

      return HttpResponse.text(JSON.stringify([translations]));
    } catch (error) {
      return HttpResponse.json({ error: 'Invalid request format' }, { status: 400 });
    }
  }),

  // Google Translate API - single endpoint (query params)
  http.get('https://translate.googleapis.com/translate_a/single', ({ request }) => {
    const url = new URL(request.url);
    const text = url.searchParams.get('q');
    
    if (!text) {
      return HttpResponse.json({ error: 'No text provided' }, { status: 400 });
    }

    const translation = getMockTranslation(text);
    return HttpResponse.text(JSON.stringify([[[translation, text, null, null, 1]]]));
  }),

  // Catch-all for any other Google Translate URLs to prevent network requests
  http.post('https://translate.googleapis.com/*', () => {
    return HttpResponse.json([[[['Mock Translation', 'Original Text', null, null, 1]]]]);
  }),

  http.get('https://translate.googleapis.com/*', ({ request }) => {
    const url = new URL(request.url);
    const text = url.searchParams.get('q') || 'Default text';
    const translation = getMockTranslation(text);
    return HttpResponse.text(JSON.stringify([[[translation, text, null, null, 1]]]));
  }),

  // Catch-all for translate-pa.googleapis.com
  http.post('https://translate-pa.googleapis.com/*', async ({ request }) => {
    try {
      const body = await request.json();
      return HttpResponse.json([
        [[[getMockTranslation('Mock text'), 'Mock text', null, null, 1]]],
        'te_lib'
      ]);
    } catch {
      return HttpResponse.json([
        [[[getMockTranslation('Mock text'), 'Mock text', null, null, 1]]],
        'te_lib'
      ]);
    }
  })
];

// Helper function to provide mock translations
function getMockTranslation(text: string): string {
  const translations: Record<string, string> = {
    'Hello, World!': '你好，世界！',
    'How are you today?': '你今天好吗？',
    'This is a test sentence.': '这是一个测试句子。',
    'Good morning, everyone!': '大家早上好！',
    'Hello': '你好',
    'World': '世界',
    'test': '测试',
    'Hello, World': '你好，世界'
  };

  return translations[text] || `翻译: ${text}`;
}