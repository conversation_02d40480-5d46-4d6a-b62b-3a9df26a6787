/**
 * 安全检查器 - 轻量化版本
 * 修复了过度拦截问题，专注实际安全威胁
 */

import { TranslationConfig, TranslationErrorType, TranslationError } from '../types';
import { TextProcessor } from '../utils/text-utils';

export class SecurityChecker {
  private config: TranslationConfig['security'];

  // 修复后的恶意内容检测规则
  private readonly MALICIOUS_PATTERNS = [
    // JavaScript协议
    /javascript:/i,
    // 事件处理器 - 修复：要求前面有空格或在开头，避免误判data属性
    /(^|\s)on\w+\s*=/i,
    // 脚本标签
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    // 样式注入
    /<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi,
    // 危险数据URI
    /data:\s*text\/html/i,
    /data:\s*[^;]*;base64.*script/i,
    // 其他危险协议
    /vbscript:|livescript:|mocha:|tcl:/i,
    // CSS表达式注入
    /expression\s*\(/i,
    // 内联框架
    /<iframe\b[^>]*>/gi,
    // 对象和嵌入标签
    /<(object|embed)\b[^>]*>/gi,
    // 表单标签
    /<form\b[^>]*>/gi,
    // Meta刷新重定向
    /<meta\b[^>]*http-equiv\s*=\s*["']?refresh["']?/gi
  ];

  constructor(config: TranslationConfig['security']) {
    this.config = config;
  }

  /**
   * 验证单个文本内容
   */
  validateContent(text: string, format: 'text' | 'html' = 'text'): boolean {
    try {
      // 基本输入检查
      if (!text || typeof text !== 'string') {
        return false;
      }

      // 长度检查 - 使用调整后的限制
      if (text.length > this.config.maxLength) {
        return false;
      }

      // 检查恶意内容
      if (this.hasMaliciousContent(text)) {
        return false;
      }

      // HTML格式的额外检查
      if (format === 'html') {
        return this.validateHtmlContent(text);
      }

      return true;
    } catch (error) {
      console.error('Security validation error:', error);
      return false;
    }
  }

  /**
   * 批量验证内容
   */
  validateMultiple(texts: string[], format: 'text' | 'html' = 'text'): boolean[] {
    return texts.map(text => this.validateContent(text, format));
  }

  /**
   * 过滤安全的文本
   */
  filterSafe(texts: string[], format: 'text' | 'html' = 'text'): string[] {
    return texts.filter(text => this.validateContent(text, format));
  }

  /**
   * 清理文本内容
   */
  sanitizeText(text: string): string {
    if (!text || typeof text !== 'string') {
      return '';
    }

    return TextProcessor.cleanText(text)
      // 移除HTML标签（如果不是HTML格式）
      .replace(/<[^>]*>/g, '')
      // 转义特殊字符
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      // 移除控制字符
      .replace(/[\x00-\x1F\x7F]/g, '');
  }

  /**
   * 创建安全错误
   */
  createSecurityError(reason: string, recoverable: boolean = false): TranslationError {
    return new TranslationError(
      TranslationErrorType.SECURITY_BLOCKED,
      `Security check failed: ${reason}`,
      recoverable
    );
  }

  /**
   * 检查是否包含恶意内容
   */
  private hasMaliciousContent(text: string): boolean {
    // 检查明确的恶意模式
    for (const pattern of this.MALICIOUS_PATTERNS) {
      if (pattern.test(text)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 验证HTML内容
   */
  private validateHtmlContent(html: string): boolean {
    // 检查HTML标签是否在白名单中
    const htmlTagMatches = html.match(/<[^>]*>/g);
    if (htmlTagMatches) {
      for (const tagMatch of htmlTagMatches) {
        if (!this.isSafeHtmlTag(tagMatch)) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * 检查HTML标签是否安全
   */
  private isSafeHtmlTag(tagMatch: string): boolean {
    // 提取标签名
    const tagNameMatch = tagMatch.match(/<\/?(\w+)/);
    if (!tagNameMatch) {
      return false;
    }

    const tagName = tagNameMatch[1].toLowerCase();

    // 检查是否在白名单中
    if (!this.config.allowedTags.includes(tagName)) {
      return false;
    }

    // 检查是否包含危险属性 - 修复data属性误判
    const dangerousAttributes = /(^|\s)on\w+|javascript:|vbscript:/i;
    if (dangerousAttributes.test(tagMatch)) {
      return false;
    }

    return true;
  }

  /**
   * 获取安全检查统计
   */
  getStats(): {
    configuredMaxLength: number;
    allowedTagsCount: number;
    strictMode: boolean;
  } {
    return {
      configuredMaxLength: this.config.maxLength,
      allowedTagsCount: this.config.allowedTags.length,
      strictMode: this.config.strictMode
    };
  }
}

/**
 * 内容安全策略辅助工具
 */
export class CSPHelper {
  /**
   * 检查内容是否符合CSP
   */
  static isCSPCompliant(content: string): boolean {
    // 检查内联脚本
    if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(content)) {
      return false;
    }

    // 检查事件处理器 - 使用修复后的正则
    if (/(^|\s)on\w+\s*=/i.test(content)) {
      return false;
    }

    return true;
  }

  /**
   * 生成基本CSP策略
   */
  static generateCSP(): string {
    return [
      "default-src 'self'",
      "script-src 'self'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data:",
      "font-src 'self'",
      "connect-src 'self'",
      "object-src 'none'",
      "frame-src 'none'"
    ].join('; ');
  }
}

/**
 * 快速安全检查函数 - 用于性能敏感场景
 */
export function quickSecurityCheck(text: string, maxLength: number = 15000): boolean {
  // 快速长度检查
  if (!text || text.length > maxLength) {
    return false;
  }

  // 快速恶意内容检查
  if (text.includes('javascript:') || 
      text.includes('<script') || 
      /(^|\s)on\w+\s*=/.test(text)) {
    return false;
  }

  return true;
}