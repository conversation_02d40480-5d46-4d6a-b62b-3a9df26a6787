/**
 * DOM元素分析器
 * 负责DOM树遍历和基础元素分析
 * 
 * 核心职责：
 * 1. 高效的DOM树遍历（使用TreeWalker）
 * 2. 基础元素过滤（目标选择器、排除选择器）
 * 3. 简单的翻译状态检查
 * 4. 文本内容提取（委派给TextProcessor）
 * 
 * 不负责：
 * - 复杂的父子冲突解决（由pipeline scanner处理）
 * - 调试可视化（由ExclusionManager和ExclusionDebugger处理）
 */

import { TextProcessor } from '../utils/text-utils';
import { DOMUtils } from '../utils/dom-utils';
import { TextFilter } from '../utils/text-filter';
// debugContent 导入已移除，使用手动 console.log
import { LinkInfo } from '../types';

/**
 * 可翻译节点信息
 */
export interface TranslatableElement {
  /** DOM元素 */
  element: HTMLElement;
  /** 提取的文本内容 */
  text: string;
  /** HTML内容（如果包含链接等结构） */
  htmlContent?: string;
  /** 是否包含HTML结构 */
  hasHtmlStructure: boolean;
  /** 链接信息数组 */
  links: LinkInfo[];
  /** 在页面中的位置索引 */
  position: number;
  /** 优先级（数字越小优先级越高） */
  priority: number;
  /** 节点类型 */
  nodeType: string;
}

/**
 * 分析统计信息
 */
export interface AnalysisStats {
  /** 总扫描节点数 */
  totalScanned: number;
  /** 匹配选择器的节点数 */
  matchedSelectors: number;
  /** 被排除的节点数 */
  excluded: number;
  /** 最终可翻译节点数 */
  translatable: number;
  /** 各类型节点统计 */
  byType: Record<string, number>;
  /** 分析耗时（毫秒） */
  analysisTime: number;
}

/**
 * DOM元素分析器类
 * 提供低级别的DOM遍历和元素分析功能
 */
export class DomElementAnalyzer {
  /** 调试工具实例 */
  // debug 属性已移除，使用手动 console.log
  
  /** 目标选择器 - 优先级从高到低 */
  private readonly targetSelectors = [
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6', // 标题（最高优先级）
    'p', // 段落
    'a', // 链接元素（重要：应该被翻译的内联内容）
    'span', // 内联文本容器（重要：用于处理父子冲突，如 div > span 结构）
    'div', // div元素（用于测试header等区域的翻译）
    'li', // 列表项
    'td', 'th', // 表格单元格
    'blockquote', // 引用
    'figcaption', // 图片说明
    '[data-translatable]' // 显式标记的元素
  ];

  /** 排除选择器 */
  private readonly excludeSelectors = [
    // 代码相关
    'code', 'pre', 'script', 'style', 'kbd', 'samp', 'var',
    // JSON 数据脚本（特别排除）
    'script[type="application/json"]', 'script[type="application/ld+json"]',
    // 其他数据脚本
    'script[type*="json"]', 'script[data-catalog-id]',
    // 表单控件
    'input', 'textarea', 'select', 'option', 'button',
    // 媒体和交互
    'svg', 'canvas', 'video', 'audio', 'iframe',
    // 导航和元数据（移除header以支持标题区域翻译）
    'nav', 'footer', 'aside', 'noscript',
    // 排除标记
    '.lu-skip', '.notranslate', '[data-no-translate]', '[translate="no"]',
    // 已翻译内容
    '.lu-wrapper', '.lu-block', 'lu-trans',
    // 编辑器相关
    '[contenteditable="true"]', '.editor', '.monaco-editor'
  ].join(', ');

  /**
   * 分析页面中的可翻译元素
   * @param rootElement 根元素，默认为document.body
   * @returns 可翻译元素数组和统计信息
   */
  analyzeElements(rootElement: Element = document.body): {
    elements: TranslatableElement[];
    stats: AnalysisStats;
  } {
    const startTime = performance.now();
    const stats: AnalysisStats = {
      totalScanned: 0,
      matchedSelectors: 0,
      excluded: 0,
      translatable: 0,
      byType: {},
      analysisTime: 0
    };

    const elements: TranslatableElement[] = [];
    let position = 0;

    // 验证rootElement
    if (!rootElement || !(rootElement instanceof Element)) {
      console.log('⚠️ [dom-analyzer|WARN] Invalid rootElement provided, using document.documentElement as fallback', {
        rootElement,
        fallback: document.documentElement
      });
      rootElement = document.documentElement || document.body;

      // 如果仍然无效，返回空结果
      if (!rootElement) {
        console.log('❌ [dom-analyzer|ERROR] No valid root element found, cannot analyze page');
        stats.analysisTime = performance.now() - startTime;
        return { elements: [], stats };
      }
    }

    // 使用TreeWalker进行高效遍历
    const walker = document.createTreeWalker(
      rootElement,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node: Element) => {
          stats.totalScanned++;

          // 快速排除不匹配的节点
          if (!this.matchesTargetSelectors(node as HTMLElement)) {
            return NodeFilter.FILTER_SKIP;
          }

          stats.matchedSelectors++;

          // 检查是否被基础规则排除
          if (this.isExcludedByBasicRules(node as HTMLElement)) {
            stats.excluded++;
            return NodeFilter.FILTER_SKIP;
          }

          return NodeFilter.FILTER_ACCEPT;
        }
      }
    );

    // 遍历所有匹配的节点
    let currentNode: Element | null;
    while ((currentNode = walker.nextNode() as Element)) {
      const htmlElement = currentNode as HTMLElement;
      const elementInfo = this.analyzeElement(htmlElement, position++);

      if (elementInfo) {
        elements.push(elementInfo);
        stats.translatable++;

        // 统计节点类型
        const nodeType = elementInfo.nodeType;
        stats.byType[nodeType] = (stats.byType[nodeType] || 0) + 1;
      }
    }

    // 按优先级和位置排序
    elements.sort((a, b) => {
      if (a.priority !== b.priority) {
        return a.priority - b.priority; // 优先级排序
      }
      return a.position - b.position; // 位置排序
    });

    stats.analysisTime = performance.now() - startTime;

    // 输出分析统计（仅开发环境）
    if (process.env.NODE_ENV === 'development') {
      this.logAnalysisStats(stats);
    }

    return { elements, stats };
  }

  /**
   * 检查元素是否匹配目标选择器
   */
  private matchesTargetSelectors(element: HTMLElement): boolean {
    return this.targetSelectors.some(selector => {
      try {
        return element.matches(selector);
      } catch {
        return false;
      }
    });
  }

  /**
   * 检查元素是否被基础规则排除
   * 🚀 性能优化版本：增强早期过滤，减少昂贵操作
   */
  private isExcludedByBasicRules(element: HTMLElement): boolean {
    try {
      // 🚀 优化1：最快的属性检查 - 排除原因标记（优先级最高）
      const exclusionReason = element.getAttribute('data-exclusion-reason');
      if (exclusionReason === 'parentExcluded' || exclusionReason === 'excludeSelector' || 
          exclusionReason === 'hidden' || exclusionReason === 'alreadyTranslated') {
        return true;
      }

      // 🚀 优化2：快速翻译状态检查 - 避免重复处理
      if (element.hasAttribute('data-lu-translated') || 
          element.hasAttribute('data-lu-processing') ||
          element.hasAttribute('data-lu-failed')) {
        return true;
      }

      // 🚀 优化3：快速类名检查 - 避免昂贵的matches调用
      const className = element.className || '';
      const quickExcludeClasses = [
        'lu-wrapper', 'lu-block', 'lu-inline', 'lu-strategy', 'lu-trans',
        'notranslate', 'no-translate', 'skiptranslate',
        'translation-result', 'translated-content'
      ];
      
      if (quickExcludeClasses.some(cls => className.includes(cls))) {
        return true;
      }

      // 🚀 优化4：快速标签名检查 - 避免正则表达式
      const tagName = element.tagName.toLowerCase();
      const quickExcludeTags = [
        'script', 'style', 'noscript', 'meta', 'link',
        'code', 'pre', 'kbd', 'samp', 'var',
        'input', 'textarea', 'select', 'option', 'button',
        'svg', 'canvas', 'video', 'audio', 'iframe'
      ];
      
      if (quickExcludeTags.includes(tagName)) {
        return true;
      }

      // 🚀 优化5：快速基本隐藏检查 - 避免getComputedStyle
      if (element.hasAttribute('hidden') || 
          element.getAttribute('aria-hidden') === 'true' ||
          element.style.display === 'none' ||
          element.style.visibility === 'hidden') {
        return true;
      }

      // 🚀 优化6：快速文本内容检查 - 提前过滤空内容
      const textContent = element.textContent || '';
      const trimmedText = textContent.trim();
      
      // 跳过空元素
      if (trimmedText.length === 0) {
        return true;
      }
      
      // 跳过过短文本
      if (trimmedText.length < 2) {
        return true;
      }
      
      // 跳过纯符号内容
      if (/^[\d\s\-_.,;:!?()[\]{}'"`~@#$%^&*+=<>/\\|]*$/.test(trimmedText)) {
        return true;
      }

      // 🚀 优化7：仅在快速检查都通过后，才进行昂贵的选择器匹配
      if (element.matches(this.excludeSelectors)) {
        return true;
      }

      // 🚀 优化8：仅在必要时检查父元素（单层检查）
      const parentElement = element.parentElement;
      if (parentElement && parentElement !== document.body) {
        // 快速父元素类名检查
        const parentClassName = parentElement.className || '';
        if (quickExcludeClasses.some(cls => parentClassName.includes(cls))) {
          return true;
        }
        
        // 如果父元素快速检查通过，才进行选择器匹配
        if (parentElement.matches(this.excludeSelectors)) {
          return true;
        }
      }

      // 🚀 优化9：最后才进行最昂贵的closest查找
      if (element.closest('.lu-wrapper, .lu-block, .lu-inline')) {
        return true;
      }

      // 🚀 优化10：最后进行复合样式检查（仅在必要时）
      if (DOMUtils.isElementTrulyHidden(element)) {
        return true;
      }

      return false;
    } catch (error) {
      // 区分可恢复错误和严重错误
      if (error instanceof TypeError || error instanceof ReferenceError) {
        console.error('🚨 [dom-analyzer|CRITICAL] 严重错误，需要调试', {
          tagName: element.tagName,
          error: error.message,
          stack: error.stack
        });
        throw error; // 严重错误需要抛出
      }

      // 可恢复错误：DOM访问异常、样式计算失败等
      if (error instanceof DOMException || 
          (error as Error).message?.includes('getComputedStyle') ||
          (error as Error).message?.includes('getAttribute')) {
        console.warn('🚀 [dom-analyzer|RECOVERABLE] DOM访问异常，安全排除元素', {
          tagName: element.tagName,
          error: (error as Error).message
        });
        return true;
      }

      // 其他未知错误，记录详细信息但继续执行
      console.warn('🚀 [dom-analyzer|UNKNOWN] 未知错误，保守排除元素', {
        tagName: element.tagName,
        error: error instanceof Error ? error.message : String(error),
        errorType: error instanceof Error ? error.constructor.name : typeof error
      });
      return true;
    }
  }

  /**
   * 分析单个元素并提取信息
   * 🔧 优化版本：统一宽松模式应用，增强文本清理
   */
  analyzeElement(element: HTMLElement, position: number): TranslatableElement | null {
    // 使用TextProcessor提取文本和结构信息
    const extractionResult = TextProcessor.extractText(element);

    // 🔧 统一宽松模式：翻译适用性检查使用宽松模式
    if (!extractionResult.text || !TextFilter.isTranslatable(extractionResult.text, true)) {
      return null;
    }

    const nodeType = element.tagName.toLowerCase();
    const priority = this.getElementPriority(nodeType);

    // 🔧 文本清理：在宽松模式检查通过后，进行标准化清理
    const cleanedText = TextFilter.cleanText(extractionResult.text);

    return {
      element,
      text: cleanedText,
      htmlContent: extractionResult.htmlContent,
      hasHtmlStructure: extractionResult.hasHtmlStructure,
      links: extractionResult.links,
      position,
      priority,
      nodeType
    };
  }

  /**
   * 获取元素优先级
   * 数字越小优先级越高
   */
  private getElementPriority(nodeType: string): number {
    const priorityMap: Record<string, number> = {
      h1: 1,
      h2: 2,
      h3: 3,
      h4: 4,
      h5: 5,
      h6: 6,
      p: 10,
      a: 12,        // 链接元素 - 高优先级，因为通常包含具体的可翻译内容
      span: 13,     // 内联文本容器 - 高优先级，通常比父容器更具体
      li: 15,
      blockquote: 20,
      figcaption: 25,
      td: 30,
      th: 30,
      div: 40
    };

    return priorityMap[nodeType] || 50;
  }

  /**
   * 检查特定元素是否可翻译
   */
  isElementTranslatable(element: HTMLElement): boolean {
    if (!this.matchesTargetSelectors(element)) {
      return false;
    }

    if (this.isExcludedByBasicRules(element)) {
      return false;
    }

    const extractionResult = TextProcessor.extractText(element);
    return extractionResult.text ? TextFilter.isTranslatable(extractionResult.text) : false;
  }

  /**
   * 获取元素在文档中的唯一标识
   */
  getElementSignature(element: HTMLElement): string {
    const tagName = element.tagName.toLowerCase();
    const extractionResult = TextProcessor.extractText(element);
    const textContent = extractionResult.text.slice(0, 50);
    const position = Array.from(document.querySelectorAll(tagName)).indexOf(element);

    return `${tagName}:${position}:${textContent.replace(/\s+/g, '_')}`;
  }

  /**
   * 输出分析统计信息（开发环境）
   */
  private logAnalysisStats(stats: AnalysisStats): void {
    console.group('🔍 DOM Element Analyzer Statistics');
    console.log(`⏱️ Analysis time: ${stats.analysisTime.toFixed(2)}ms`);
    console.log(`📊 Total scanned: ${stats.totalScanned}`);
    console.log(`🎯 Matched selectors: ${stats.matchedSelectors}`);
    console.log(`❌ Excluded: ${stats.excluded}`);
    console.log(`✅ Translatable: ${stats.translatable}`);

    if (Object.keys(stats.byType).length > 0) {
      console.log('📋 By element type:');
      Object.entries(stats.byType)
        .sort(([, a], [, b]) => b - a)
        .forEach(([type, count]) => {
          console.log(`  ${type}: ${count}`);
        });
    }
    console.groupEnd();
  }
}