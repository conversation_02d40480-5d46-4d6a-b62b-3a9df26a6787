/**
 * 自定义HTML元素定义
 * 实现 lu-strategy 和 lu-trans 元素
 */

declare global {
  interface HTMLElementTagNameMap {
    'lu-strategy': LuStrategyElement;
    'lu-trans': LuTransElement;
  }
}

/**
 * lu-strategy 元素 - 简化版本
 * 翻译策略容器，支持 inline 和 block 模式
 */
export class LuStrategyElement extends HTMLElement {
  connectedCallback(): void {
    // 设置策略数据属性（用于CSS选择器）
    if (this.classList.contains('inline')) {
      this.setAttribute('data-strategy', 'inline');
    } else if (this.classList.contains('block')) {
      this.setAttribute('data-strategy', 'block');
    }

    // 设置翻译状态
    if (this.getAttribute('data-lu') === 'ok') {
      this.classList.add('lu-translated');
    }
  }

  /**
   * 获取策略类型
   */
  getStrategyType(): 'inline' | 'block' | 'unknown' {
    if (this.classList.contains('inline')) return 'inline';
    if (this.classList.contains('block')) return 'block';
    return 'unknown';
  }

  /**
   * 获取翻译ID
   */
  getTranslationId(): string | null {
    return this.getAttribute('lu-id');
  }

  /**
   * 检查是否已翻译
   */
  isTranslated(): boolean {
    return this.getAttribute('data-lu') === 'ok';
  }
}

/**
 * lu-trans 元素 - 简化版本
 * 翻译文本容器
 */
export class LuTransElement extends HTMLElement {
  connectedCallback(): void {
    // 设置语义化属性
    this.setAttribute('role', 'text');
    this.setAttribute('aria-label', '翻译文本');

    // 根据父元素策略设置数据属性
    const parent = this.parentElement;
    if (parent instanceof LuStrategyElement) {
      const strategyType = parent.getStrategyType();
      this.setAttribute('data-parent-strategy', strategyType);
    }
  }

  /**
   * 设置翻译文本
   */
  setTranslationText(text: string): void {
    this.textContent = text;
  }

  /**
   * 获取翻译文本
   */
  getTranslationText(): string {
    return this.textContent || '';
  }
}

/**
 * 注册自定义元素 - 简化版本
 */
export function registerCustomElements(): void {
  try {
    // 检查自定义元素支持
    if (!checkCustomElementsSupport()) {
      console.warn('Custom elements not supported in this environment');
      return;
    }

    // 注册 lu-strategy
    if (!customElements.get('lu-strategy')) {
      customElements.define('lu-strategy', LuStrategyElement);
    }

    // 注册 lu-trans
    if (!customElements.get('lu-trans')) {
      customElements.define('lu-trans', LuTransElement);
    }

    // 注册 lu-br（简单的换行元素）
    if (!customElements.get('lu-br')) {
      customElements.define('lu-br', class extends HTMLElement {});
    }
    
  } catch (error) {
    console.error('Failed to register custom elements:', error);
    throw error;
  }
}

/**
 * 检查浏览器是否支持自定义元素
 */
export function checkCustomElementsSupport(): boolean {
  return typeof window !== 'undefined' && 
         'customElements' in window && 
         window.customElements !== null && 
         typeof window.customElements.define === 'function' &&
         typeof window.customElements.get === 'function';
}

/**
 * 生成唯一的翻译ID
 */
export function generateTranslationId(): string {
  // 使用时间戳和随机数生成唯一ID
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `lu-${timestamp}-${random}`;
}

/**
 * 工具函数：创建 lu-strategy 元素 - 简化版本
 */
export function createStrategyElement(
  strategy: 'inline' | 'block',
  translationId?: string
): LuStrategyElement {
  const element = document.createElement('lu-strategy') as LuStrategyElement;
  element.className = strategy;
  element.setAttribute('lu-id', translationId || generateTranslationId());
  element.setAttribute('data-lu', 'ok');
  return element;
}

/**
 * 工具函数：创建 lu-trans 元素 - 简化版本
 */
export function createTransElement(translationText: string): LuTransElement {
  const element = document.createElement('lu-trans') as LuTransElement;
  element.textContent = translationText;
  return element;
}