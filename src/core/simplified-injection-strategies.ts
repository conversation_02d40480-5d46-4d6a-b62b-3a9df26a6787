/**
 * 简化的注入策略系统
 * 
 * 只支持三种核心策略：
 * - Block: 块级翻译，在元素后插入翻译块
 * - Inline: 内联翻译，直接替换文本内容  
 * - Skip: 跳过翻译，不处理该元素
 */

export enum SimplifiedInjectionStrategy {
  BLOCK = 'block',
  INLINE = 'inline', 
  SKIP = 'skip'
}

export interface StrategyParams {
  element: HTMLElement;
  translation: string;
  language: string;
  format: 'text' | 'html';
  enableAccessibility: boolean;
}

export interface StrategyResult {
  success: boolean;
  injectedElement?: HTMLElement;
  error?: string;
}

/**
 * 基础策略抽象类
 */
export abstract class BaseStrategy {
  abstract execute(params: StrategyParams): Promise<StrategyResult>;
  
  protected generateTranslationId(): string {
    return `lu-trans-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 内联策略：直接替换元素文本
 */
export class InlineStrategy extends BaseStrategy {
  async execute(params: StrategyParams): Promise<StrategyResult> {
    try {
      const { element, translation, format } = params;
      
      // 保存原始文本
      const originalText = element.textContent || '';
      element.setAttribute('data-lu-original-text', originalText);
      element.setAttribute('data-lu-translated', 'true');
      element.setAttribute('data-lu-strategy', 'inline');
      
      // 替换文本
      if (format === 'html') {
        element.innerHTML = translation;
      } else {
        element.textContent = translation;
      }

      return {
        success: true,
        injectedElement: element
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }
}

/**
 * 块级策略：在元素后插入翻译块
 */
export class BlockStrategy extends BaseStrategy {
  async execute(params: StrategyParams): Promise<StrategyResult> {
    try {
      const { element, translation, language, format, enableAccessibility } = params;
      
      // 创建翻译元素
      const translationElement = document.createElement('lu-trans');
      const translationId = this.generateTranslationId();
      
      translationElement.setAttribute('data-lu-translation-id', translationId);
      translationElement.setAttribute('data-lu-strategy', 'block');
      translationElement.setAttribute('lang', language);
      
      if (format === 'html') {
        translationElement.innerHTML = translation;
      } else {
        translationElement.textContent = translation;
      }

      // 添加无障碍支持
      if (enableAccessibility) {
        translationElement.setAttribute('role', 'region');
        translationElement.setAttribute('aria-label', 'Translation');
      }

      // 插入到元素后面
      element.parentNode?.insertBefore(translationElement, element.nextSibling);
      
      // 标记原始元素
      element.setAttribute('data-lu-translated', 'true');
      element.setAttribute('data-lu-strategy', 'block');
      element.setAttribute('data-lu-translation-id', translationId);

      return {
        success: true,
        injectedElement: translationElement
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }
}

/**
 * 跳过策略：不处理元素
 */
export class SkipStrategy extends BaseStrategy {
  async execute(params: StrategyParams): Promise<StrategyResult> {
    // 跳过策略不做任何处理，只标记为已处理
    params.element.setAttribute('data-lu-strategy', 'skip');
    
    return {
      success: true
    };
  }
}

/**
 * 简化的策略工厂
 */
export class SimplifiedStrategyFactory {
  private static strategies = new Map<SimplifiedInjectionStrategy, BaseStrategy>([
    [SimplifiedInjectionStrategy.INLINE, new InlineStrategy()],
    [SimplifiedInjectionStrategy.BLOCK, new BlockStrategy()],
    [SimplifiedInjectionStrategy.SKIP, new SkipStrategy()]
  ]);

  /**
   * 获取策略执行器
   */
  static getStrategy(strategyType: SimplifiedInjectionStrategy): BaseStrategy {
    const strategy = this.strategies.get(strategyType);
    if (!strategy) {
      throw new Error(`Unknown injection strategy: ${strategyType}`);
    }
    return strategy;
  }

  /**
   * 获取所有可用策略
   */
  static getAvailableStrategies(): SimplifiedInjectionStrategy[] {
    return Array.from(this.strategies.keys());
  }

  /**
   * 执行策略
   */
  static async executeStrategy(
    strategyType: SimplifiedInjectionStrategy,
    params: StrategyParams
  ): Promise<StrategyResult> {
    const strategy = this.getStrategy(strategyType);
    return await strategy.execute(params);
  }

  /**
   * 验证策略类型
   */
  static isValidStrategy(strategy: string): strategy is SimplifiedInjectionStrategy {
    return Object.values(SimplifiedInjectionStrategy).includes(strategy as SimplifiedInjectionStrategy);
  }
}