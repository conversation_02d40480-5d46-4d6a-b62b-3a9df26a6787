/**
 * Core Module Exports
 * 核心模块导出 - 简化架构
 */

// === 简化的核心组件 ===
export { SimplifiedAdvancedDOMInjector } from './simplified-advanced-injector';
export { 
  SimplifiedInjectionStrategy,
  InlineStrategy,
  BlockStrategy,
  SkipStrategy
} from './simplified-injection-strategies';

// === 原有核心组件（保持兼容性） ===
export { ConfigLoader } from './config-loader';
export { AdvancedConfigParser } from './advanced-config-parser';
export { AdvancedStrategyMatcher } from './advanced-strategy-matcher';
export { SecurityChecker, quickSecurityCheck } from './security-checker';
export { registerCustomElements, checkCustomElementsSupport } from './custom-elements';

// === 简化注入策略系统 ===
// 旧版策略系统已迁移到简化版本

// === 规则引擎 ===
export { 
  InjectionRuleEngine,
  getInjectionRuleEngine,
  setInjectionRuleEngine,
  resetInjectionRuleEngine
} from './injection-rules';

// === 配置管理 ===
export { 
  RuleConfigManager,
  getRuleConfigManager,
  setRuleConfigManager
} from './rule-config-manager';

// === 类型定义 ===
export type { 
  SimplifiedInjectionOptions,
  SimplifiedInjectionResult 
} from './simplified-advanced-injector';

export type {
  StrategyParams,
  StrategyResult
} from './simplified-injection-strategies';