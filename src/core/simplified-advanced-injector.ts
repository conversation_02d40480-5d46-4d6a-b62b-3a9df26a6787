import { ConfigLoader } from './config-loader';
import { AdvancedConfigParser } from './advanced-config-parser';
import { AdvancedStrategyMatcher } from './advanced-strategy-matcher';
// SimplifiedStrategyFactory import removed - not used in this file
import { registerCustomElements } from './custom-elements';
import { quickSecurityCheck } from './security-checker';
// debugTranslation 导入已移除，使用手动 console.log

/**
 * 注入选项接口
 */
export interface SimplifiedInjectionOptions {
  language: string;
  format: 'text' | 'html';
  enableAccessibility: boolean;
  useRuleEngine: boolean;
  debug: boolean;
}

/**
 * 注入结果接口
 */
export interface SimplifiedInjectionResult {
  success: boolean;
  strategy: string;
  injectedElement?: HTMLElement;
  error?: string;
  duration: number;
}

/**
 * 简化的高级DOM注入器
 * 
 * 核心特性：
 * - 支持4种策略：Block、Inline、Skip、Clone Replace
 * - 基于 advanced-injection-rules.json 配置
 * - 移除继承体系，单一类实现
 * - 专注于核心注入逻辑
 * - 🚀 性能优化：配置缓存机制，避免重复解析
 */
export class SimplifiedAdvancedDOMInjector {
  private configParser: AdvancedConfigParser;
  private strategyMatcher: AdvancedStrategyMatcher;
  private cachedConfig: any = null; // 🚀 添加配置缓存
  private configCacheTimestamp: number = 0; // 🚀 缓存时间戳
  private configCacheTTL: number = 5 * 60 * 1000; // 🚀 缓存TTL：5分钟
  private configVersion: string = ''; // 🚀 配置版本标识
  private configLoadPromise: Promise<any> | null = null; // 避免并发重复加载
  private stats = {
    totalInjections: 0,
    successful: 0,
    failed: 0,
    byStrategy: {
      block: 0,
      inline: 0,
      skip: 0,
      clone_replace: 0
    }
  };
  private customElementsRegistered = false;

  constructor(
    private configLoader: ConfigLoader,
    private debug: boolean = false
  ) {
    this.configParser = new AdvancedConfigParser();
    this.strategyMatcher = new AdvancedStrategyMatcher();
    this.logDebug('SimplifiedAdvancedDOMInjector initialized with config caching');
  }

  /**
   * 🚀 预加载配置 - 在批量翻译开始前调用，避免重复解析
   */
  async preloadConfig(): Promise<void> {
    // 🚀 检查缓存是否仍然有效
    if (this.isCacheValid()) {
      return; // 配置已缓存且有效
    }

    if (this.configLoadPromise) {
      await this.configLoadPromise; // 等待正在进行的加载
      return;
    }

    this.configLoadPromise = this.loadAndCacheConfig();
    await this.configLoadPromise;
    this.configLoadPromise = null;
  }

  /**
   * 🚀 检查缓存是否有效
   * @private
   */
  private isCacheValid(): boolean {
    if (!this.cachedConfig) {
      return false;
    }

    // 检查TTL是否过期
    const now = Date.now();
    if (now - this.configCacheTimestamp > this.configCacheTTL) {
      if (this.debug) {
        console.log('🧹 [injector|CACHE] 配置缓存已过期，需要重新加载');
      }
      return false;
    }

    return true;
  }

  /**
   * 加载并缓存配置
   * @private
   */
  private async loadAndCacheConfig(): Promise<void> {
    try {
      if (this.debug) {
        console.log('🔧 [injector|CONFIG] 开始一次性加载配置文件...');
      }

      const config = await this.configLoader.loadAdvancedConfig();
      
      // 🚀 生成配置版本标识（基于配置内容的哈希）
      const configVersion = this.generateConfigVersion(config);
      
      // 🚀 更新缓存和时间戳
      this.cachedConfig = config;
      this.configCacheTimestamp = Date.now();
      this.configVersion = configVersion;
      this.strategyMatcher.loadConfig(config);

      if (this.debug) {
        console.log('✅ [injector|CONFIG] 配置文件加载并缓存完成', {
          rulesCount: config?.rules?.length || 0,
          strategiesCount: Object.keys(config?.strategies || {}).length,
          version: configVersion,
          timestamp: new Date(this.configCacheTimestamp).toISOString()
        });
      }
    } catch (error) {
      this.cachedConfig = null;
      this.configCacheTimestamp = 0;
      this.configVersion = '';
      if (this.debug) {
        console.error('❌ [injector|CONFIG] 配置文件加载失败:', error);
      }
      throw error;
    }
  }

  /**
   * 🚀 生成配置版本标识
   * @private
   */
  private generateConfigVersion(config: any): string {
    try {
      // 使用配置内容生成简单的版本标识
      const configStr = JSON.stringify(config);
      let hash = 0;
      for (let i = 0; i < configStr.length; i++) {
        const char = configStr.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
      }
      return Math.abs(hash).toString(16);
    } catch {
      return Date.now().toString(16); // 回退到时间戳
    }
  }

  /**
   * 清除配置缓存 - 用于配置更新后重新加载
   */
  clearConfigCache(): void {
    this.cachedConfig = null;
    this.configCacheTimestamp = 0;
    this.configVersion = '';
    this.configLoadPromise = null;
    if (this.debug) {
      console.log('🧹 [injector|CONFIG] 配置缓存已清除');
    }
  }

  /**
   * 🚀 获取配置缓存信息 - 用于调试和监控
   */
  getCacheInfo(): {
    hasCache: boolean;
    version: string;
    age: number;
    ttl: number;
    isValid: boolean;
  } {
    const now = Date.now();
    return {
      hasCache: !!this.cachedConfig,
      version: this.configVersion,
      age: this.configCacheTimestamp ? now - this.configCacheTimestamp : 0,
      ttl: this.configCacheTTL,
      isValid: this.isCacheValid()
    };
  }

  /**
   * 注入翻译到元素 - 🚀 性能优化版本：使用缓存的配置
   */
  async injectTranslation(
    element: HTMLElement,
    translation: string,
    options: SimplifiedInjectionOptions
  ): Promise<SimplifiedInjectionResult> {
    const startTime = performance.now();
    this.stats.totalInjections++;

    try {
      // 1. 确保自定义元素已注册
      this.ensureCustomElements();

      // 2. 安全检查
      if (!this.performSecurityCheck(element, translation)) {
        throw new Error('Security check failed');
      }

      // 3. 检查是否已翻译
      if (this.hasExistingTranslation(element)) {
        this.logDebug('Element already translated, skipping', { element: element.tagName });
        return {
          success: true,
          strategy: 'already-translated',
          duration: performance.now() - startTime
        };
      }

      // 🚀 4. 使用缓存的配置或预加载配置
      if (!this.isCacheValid()) {
        // 如果配置缓存无效，则进行预加载
        await this.preloadConfig();
      }

      // 5. 直接使用已缓存的配置进行策略匹配
      const matchResult = this.strategyMatcher.matchStrategy(element);

      this.logDebug('🎯 Strategy matched using cached config', { 
        element: `${element.tagName.toLowerCase()}${element.className ? '.' + element.className : ''}`,
        strategy: matchResult.strategy,
        rule: matchResult.rule?.id,
        priority: matchResult.rule?.priority,
        configCacheHit: !!this.cachedConfig,
        currentUrlDomain: this.sanitizeUrlForLogging(window.location.href),
        textContent: element.textContent?.substring(0, 50) + '...'
      });

      // 6. 执行注入策略
      const injectionResult = await this.executeStrategy(
        matchResult.strategy, 
        element, 
        translation, 
        options
      );

      // 7. 更新统计
      const duration = performance.now() - startTime;
      this.updateStats(injectionResult.success, matchResult.strategy);

      return {
        ...injectionResult,
        duration
      };

    } catch (error) {
      const duration = performance.now() - startTime;
      this.stats.failed++;
      
      this.logDebug('Injection failed', { 
        error: (error as Error).message,
        element: element.tagName,
        duration: `${duration.toFixed(2)}ms`
      });

      return {
        success: false,
        strategy: 'error',
        error: (error as Error).message,
        duration
      };
    }
  }

  /**
   * 清除所有翻译
   */
  async clearAllTranslations(): Promise<void> {
    // 移除所有翻译元素
    const translatedElements = document.querySelectorAll('[data-lu-translated="true"]');
    translatedElements.forEach(element => {
      this.removeTranslation(element as HTMLElement);
    });

    // 移除所有自定义翻译元素
    const customElements = document.querySelectorAll('lu-trans, lu-strategy');
    customElements.forEach(element => element.remove());

    this.logDebug('All translations cleared');
  }

  /**
   * 移除单个元素的翻译
   */
  removeTranslation(element: HTMLElement): void {
    // 获取翻译ID
    const translationId = element.getAttribute('data-lu-translation-id');
    
    // 移除元素上的翻译标记
    element.removeAttribute('data-lu-translated');
    element.removeAttribute('data-lu-strategy');
    element.removeAttribute('data-lu-translation-id');

    if (translationId) {
      // 查找并移除相关的翻译元素（包括克隆的元素和翻译容器）
      const relatedElements = document.querySelectorAll(`[data-lu-translation-id="${translationId}"], [lu-id="${translationId}"]`);
      relatedElements.forEach(el => {
        if (el !== element) {
          el.remove();
        }
      });

      // 特别处理clone_replace策略：移除克隆的同级元素
      if (element.parentElement) {
        const siblings = Array.from(element.parentElement.children);
        siblings.forEach(sibling => {
          if (sibling !== element && 
              (sibling.getAttribute('data-lu-translation-id') === translationId ||
               sibling.querySelector(`[lu-id="${translationId}"]`) ||
               sibling.querySelector('.clone_replace'))) {
            sibling.remove();
          }
        });
      }
    }

    // 清理元素内部的翻译内容（兼容旧的内部注入方式）
    const internalTranslations = element.querySelectorAll('lu-trans, lu-strategy, .lu-wrapper, .lu-block, .lu-inline');
    internalTranslations.forEach(el => el.remove());

    this.logDebug('Translation removed', { 
      element: element.tagName,
      translationId: translationId,
      removedElements: internalTranslations.length
    });
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalInjections > 0 
        ? (this.stats.successful / this.stats.totalInjections) * 100 
        : 0
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalInjections: 0,
      successful: 0,
      failed: 0,
      byStrategy: {
        block: 0,
        inline: 0,
        skip: 0,
        clone_replace: 0
      }
    };
    this.logDebug('Stats reset');
  }

  // === 私有方法 ===

  private ensureCustomElements(): void {
    if (!this.customElementsRegistered) {
      try {
        registerCustomElements();
        this.customElementsRegistered = true;
        this.logDebug('Custom elements registered');
      } catch (error) {
        this.logDebug('Custom elements registration failed, continuing without custom elements', { error: (error as Error).message });
        // 设置为已注册以避免重复尝试
        this.customElementsRegistered = true;
      }
    }
  }

  private performSecurityCheck(element: HTMLElement, translation: string): boolean {
    try {
      return quickSecurityCheck(translation);
    } catch (error) {
      this.logDebug('Security check failed', { error: (error as Error).message });
      return false;
    }
  }

  private hasExistingTranslation(element: HTMLElement): boolean {
    return element.hasAttribute('data-lu-translated') ||
           element.hasAttribute('data-lu-strategy') ||
           element.querySelector('lu-trans, lu-strategy') !== null;
  }

  private async executeStrategy(
    strategy: string,
    element: HTMLElement,
    translation: string,
    options: SimplifiedInjectionOptions
  ): Promise<Omit<SimplifiedInjectionResult, 'duration'>> {
    
    // 根据策略执行不同的注入逻辑
    switch (strategy.toLowerCase()) {
      case 'skip':
        this.logDebug('Skipping element per strategy', { element: element.tagName });
        return {
          success: true,
          strategy: 'skip'
        };

      case 'inline':
        return await this.executeInlineStrategy(element, translation, options);

      case 'block':
        return await this.executeBlockStrategy(element, translation, options);

      case 'clone_replace':
        return await this.executeCloneReplaceStrategy(element, translation, options);

      default:
        // 默认使用 block 策略
        this.logDebug('Unknown strategy, using block as fallback', { strategy });
        return await this.executeBlockStrategy(element, translation, options);
    }
  }

  private async executeInlineStrategy(
    element: HTMLElement,
    translation: string,
    options: SimplifiedInjectionOptions
  ): Promise<Omit<SimplifiedInjectionResult, 'duration'>> {
    try {
      const translationId = this.generateTranslationId();
      const originalText = element.textContent || '';
      
      // 创建 lu-strategy 包装容器
      const strategyElement = this.customElementsRegistered 
        ? document.createElement('lu-strategy')
        : document.createElement('span');
      strategyElement.className = 'inline';
      strategyElement.setAttribute('lu-id', translationId);
      strategyElement.setAttribute('data-lu', 'ok');
      
      // 创建 lu-trans 翻译元素
      const translationElement = this.customElementsRegistered 
        ? document.createElement('lu-trans')
        : document.createElement('span');
      translationElement.style.opacity = '0.8';
      
      if (options.format === 'html') {
        translationElement.innerHTML = translation;
      } else {
        translationElement.textContent = translation;
      }



      // 将翻译元素放入策略容器
      strategyElement.appendChild(translationElement);
      
      // 双语翻译：保留原文本，在后面添加翻译容器
      element.appendChild(strategyElement);

      this.logDebug('Inline injection completed', { element: element.tagName });
      
      return {
        success: true,
        strategy: 'inline',
        injectedElement: strategyElement
      };
    } catch (error) {
      return {
        success: false,
        strategy: 'inline',
        error: (error as Error).message
      };
    }
  }

  private async executeBlockStrategy(
    element: HTMLElement,
    translation: string,
    options: SimplifiedInjectionOptions
  ): Promise<Omit<SimplifiedInjectionResult, 'duration'>> {
    try {
      const translationId = this.generateTranslationId();
      
      // 创建 lu-strategy 包装容器
      const strategyElement = this.customElementsRegistered 
        ? document.createElement('lu-strategy')
        : document.createElement('div');
      strategyElement.className = 'block';
      strategyElement.setAttribute('lu-id', translationId);
      strategyElement.setAttribute('data-lu', 'ok');
      
      // 创建 lu-trans 翻译元素
      const translationElement = this.customElementsRegistered 
        ? document.createElement('lu-trans')
        : document.createElement('div');
      translationElement.style.opacity = '0.8';
      
      if (options.format === 'html') {
        translationElement.innerHTML = translation;
      } else {
        translationElement.textContent = translation;
      }



      // 将翻译元素放入策略容器
      strategyElement.appendChild(translationElement);
      
      // 将策略容器作为子元素插入到原元素内部
      element.appendChild(strategyElement);

      this.logDebug('Block injection completed', { element: element.tagName });
      
      return {
        success: true,
        strategy: 'block',
        injectedElement: strategyElement
      };
    } catch (error) {
      return {
        success: false,
        strategy: 'block',
        error: (error as Error).message
      };
    }
  }

  private async executeCloneReplaceStrategy(
    element: HTMLElement,
    translation: string,
    options: SimplifiedInjectionOptions
  ): Promise<Omit<SimplifiedInjectionResult, 'duration'>> {
    try {
      const translationId = this.generateTranslationId();
      
      // 1. 标记原始元素
      element.setAttribute('data-lu-translated', 'true');
      element.setAttribute('data-lu-strategy', 'clone_replace');
      element.setAttribute('data-lu-translation-id', translationId);
      
      // 2. 克隆原始元素
      const clonedElement = element.cloneNode(true) as HTMLElement;
      
      // 3. 清空克隆元素的文本内容，准备替换为翻译
      this.clearTextNodes(clonedElement);
      
      // 4. 创建翻译容器
      const strategyElement = this.customElementsRegistered 
        ? document.createElement('lu-strategy')
        : document.createElement('div');
      strategyElement.className = 'clone_replace';
      strategyElement.setAttribute('lu-id', translationId);
      strategyElement.setAttribute('data-lu', 'ok');
      
      // 5. 创建翻译文本元素
      const translationElement = this.customElementsRegistered 
        ? document.createElement('lu-trans')
        : document.createElement('span');
      translationElement.style.opacity = '0.8';
      
      if (options.format === 'html') {
        translationElement.innerHTML = translation;
      } else {
        translationElement.textContent = translation;
      }
      
      // 6. 将翻译内容放入策略容器，再放入克隆的元素
      strategyElement.appendChild(translationElement);
      clonedElement.appendChild(strategyElement);
      
      // 7. 将克隆的翻译元素插入为原始元素的下一个兄弟节点
      if (element.parentElement) {
        element.parentElement.insertBefore(clonedElement, element.nextSibling);
      } else {
        // 如果没有父元素，尝试插入到文档中
        document.body.appendChild(clonedElement);
      }

      this.logDebug('Clone replace injection completed', { 
        element: element.tagName,
        clonedElement: clonedElement.tagName,
        translationLength: translation.length,
        hasParent: !!element.parentElement
      });
      
      return {
        success: true,
        strategy: 'clone_replace',
        injectedElement: clonedElement
      };
    } catch (error) {
      return {
        success: false,
        strategy: 'clone_replace',
        error: (error as Error).message
      };
    }
  }

  private updateStats(success: boolean, strategy: string): void {
    if (success) {
      this.stats.successful++;
      
      // 更新策略统计
      const strategyKey = strategy.toLowerCase() as keyof typeof this.stats.byStrategy;
      if (strategyKey in this.stats.byStrategy) {
        this.stats.byStrategy[strategyKey]++;
      }
    } else {
      this.stats.failed++;
    }
  }

  private generateTranslationId(): string {
    return `lu-trans-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private getTextNodes(element: HTMLElement): Text[] {
    const textNodes: Text[] = [];
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          // 只处理直接的文本节点，避免处理已有的翻译内容
          if (node.parentElement && 
              (node.parentElement.classList.contains('lu-trans') ||
               node.parentElement.classList.contains('lu-strategy') ||
               node.parentElement.hasAttribute('data-lu'))) {
            return NodeFilter.FILTER_REJECT;
          }
          return node.textContent?.trim() ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
        }
      }
    );

    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node as Text);
    }
    
    return textNodes;
  }

  private clearTextNodes(element: HTMLElement): void {
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          // 只清空直接的文本节点，避免清空已有的翻译内容
          if (node.parentElement && 
              (node.parentElement.classList.contains('lu-trans') ||
               node.parentElement.classList.contains('lu-strategy') ||
               node.parentElement.hasAttribute('data-lu'))) {
            return NodeFilter.FILTER_REJECT;
          }
          return node.textContent?.trim() ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
        }
      }
    );

    const textNodes: Text[] = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node as Text);
    }
    
    // 清空所有文本节点
    textNodes.forEach(textNode => {
      textNode.textContent = '';
    });
  }

  private logDebug(message: string, data?: any): void {
    if (!this.debug) return;
    
    if (data) {
      console.log('🔧 [simplified-injector|DEBUG] ' + message, data);
    } else {
      console.log('🔧 [simplified-injector|DEBUG] ' + message);
    }
  }

  /**
   * 脱敏URL用于日志输出 - 增强安全性
   * @private
   */
  private sanitizeUrlForLogging(url: string): string {
    try {
      const urlObj = new URL(url);
      // 只显示协议和域名顶级部分，完全隐藏子域名、路径、查询参数和哈希
      const domainParts = urlObj.hostname.split('.');
      const topLevelDomain = domainParts.length >= 2 
        ? domainParts.slice(-2).join('.') 
        : urlObj.hostname;
      return `${urlObj.protocol}//${topLevelDomain}/***`;
    } catch {
      // 完全脱敏无效URL
      return '***';
    }
  }
}