/**
 * 注入规则引擎
 * 根据元素特征和用户配置，智能选择注入策略
 */

import { ConfigLoader } from './config-loader';

/**
 * 注入策略类型
 */
export enum InjectionStrategy {
  /** 行内注入 - 在元素内容后面追加，适合短文本、链接等 */
  INLINE = 'inline',
  /** 块级注入 - 在元素下方添加，适合段落、标题等 */
  BLOCK = 'block',
  /** 侧边注入 - 在元素旁边显示，适合特殊布局需求 */
  BESIDE = 'beside',
  /** 覆盖注入 - 替换原内容，适合特殊场景 */
  OVERLAY = 'overlay',
  /** 隐藏保持注入 - 保持原元素的隐藏状态，翻译也隐藏 */
  HIDDEN_PRESERVE = 'hidden-preserve',
  /** 克隆替换注入 - 隐藏原元素，创建包含原文+译文的新容器 */
  CLONE_REPLACE = 'clone_replace',
  /** 不注入 - 跳过该元素 */
  SKIP = 'skip'
}

/**
 * 元素特征分析结果
 */
export interface ElementContext {
  /** 标签名 */
  tagName: string;
  /** CSS类名列表 */
  classNames: string[];
  /** 元素ID */
  id?: string;
  /** 文本长度 */
  textLength: number;
  /** 是否为内联元素 */
  isInline: boolean;
  /** 是否为块级元素 */
  isBlock: boolean;
  /** 父元素信息 */
  parent: {
    tagName: string;
    className: string;
  };
  /** 子元素数量 */
  childrenCount: number;
  /** 是否包含链接 */
  hasLinks: boolean;
  /** 是否包含HTML结构 */
  hasHtmlStructure: boolean;
  /** 计算样式信息 */
  computedStyle: {
    display: string;
    position: string;
    float: string;
  };
  /** 元素在视口中的位置信息 */
  boundingRect: {
    width: number;
    height: number;
    top: number;
    left: number;
  };
  /** 原始DOM元素引用 - 用于自定义函数访问 */
  element: HTMLElement;
}

/**
 * 注入规则条件
 */
export interface RuleCondition {
  /** 标签名匹配（支持数组和正则） */
  tagName?: string | string[] | RegExp;
  /** CSS类名匹配 */
  className?: string | string[] | RegExp;
  /** 文本长度范围 */
  textLength?: {
    min?: number;
    max?: number;
  };
  /** 显示类型匹配 */
  displayType?: ('inline' | 'block' | 'inline-block' | 'flex' | 'grid')[];
  /** 父元素条件 */
  parent?: {
    tagName?: string | string[];
    className?: string | string[];
  };
  /** 子元素条件 */
  children?: {
    count?: {
      min?: number;
      max?: number;
    };
    hasLinks?: boolean;
    hasHtmlStructure?: boolean;
  };
  /** 自定义条件函数 */
  custom?: (context: ElementContext) => boolean;
}

/**
 * 注入规则定义
 */
export interface InjectionRule {
  /** 规则ID */
  id: string;
  /** 规则名称 */
  name: string;
  /** 规则描述 */
  description: string;
  /** 匹配条件 */
  condition: RuleCondition;
  /** 注入策略 */
  strategy: InjectionStrategy;
  /** 规则优先级（数字越小优先级越高） */
  priority: number;
  /** 是否启用 */
  enabled: boolean;
  /** 额外配置选项 */
  options?: {
    /** 自定义CSS类 */
    customClasses?: string[];
    /** 动画效果 */
    animation?: 'fade' | 'slide' | 'none';
    /** 延迟显示时间(ms) */
    delay?: number;
  };
}

/**
 * 预定义规则集合
 */
export const DEFAULT_INJECTION_RULES: InjectionRule[] = [
  {
    id: 'inline-short-text',
    name: '短文本行内注入',
    description: '对于短文本（少于50字符）使用行内注入',
    condition: {
      textLength: { max: 50 },
      displayType: ['inline', 'inline-block']
    },
    strategy: InjectionStrategy.INLINE,
    priority: 1,
    enabled: true,
    options: {
      animation: 'fade',
      delay: 0
    }
  },
  {
    id: 'inline-links',
    name: '链接行内注入',
    description: '链接元素使用行内注入，保持流式布局',
    condition: {
      tagName: 'a',
      textLength: { max: 100 }
    },
    strategy: InjectionStrategy.INLINE,
    priority: 2,
    enabled: true
  },
  {
    id: 'inline-spans',
    name: 'Span元素行内注入',
    description: 'span元素优先使用行内注入',
    condition: {
      tagName: 'span',
      displayType: ['inline', 'inline-block'],
      textLength: { max: 200 }
    },
    strategy: InjectionStrategy.INLINE,
    priority: 3,
    enabled: true
  },
  {
    id: 'block-headings',
    name: '标题块级注入',
    description: '所有标题元素使用块级注入',
    condition: {
      tagName: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']
    },
    strategy: InjectionStrategy.BLOCK,
    priority: 10,
    enabled: true,
    options: {
      animation: 'slide',
      customClasses: ['lu-heading-translation']
    }
  },
  {
    id: 'block-paragraphs',
    name: '段落块级注入',
    description: '段落元素使用块级注入',
    condition: {
      tagName: 'p',
      textLength: { min: 10 }
    },
    strategy: InjectionStrategy.BLOCK,
    priority: 11,
    enabled: true
  },
  {
    id: 'block-list-items',
    name: '列表项块级注入',
    description: '列表项使用块级注入',
    condition: {
      tagName: 'li'
    },
    strategy: InjectionStrategy.BLOCK,
    priority: 12,
    enabled: true
  },
  {
    id: 'block-long-divs',
    name: '长内容Div块级注入',
    description: '包含较长文本的div使用块级注入',
    condition: {
      tagName: 'div',
      textLength: { min: 100 },
      displayType: ['block', 'flex']
    },
    strategy: InjectionStrategy.BLOCK,
    priority: 13,
    enabled: true
  },
  {
    id: 'inline-navigation',
    name: '导航菜单行内注入',
    description: '导航菜单项使用行内注入',
    condition: {
      parent: {
        tagName: ['nav', 'menu'],
        className: ['nav', 'menu', 'navigation']
      },
      textLength: { max: 80 }
    },
    strategy: InjectionStrategy.INLINE,
    priority: 5,
    enabled: true
  },
  {
    id: 'block-table-cells',
    name: '表格单元格块级注入',
    description: '表格单元格使用块级注入',
    condition: {
      tagName: ['td', 'th'],
      textLength: { min: 20 }
    },
    strategy: InjectionStrategy.BLOCK,
    priority: 14,
    enabled: true
  },
  {
    id: 'skip-code-elements',
    name: '跳过代码元素',
    description: '跳过代码相关元素的翻译',
    condition: {
      tagName: ['code', 'pre', 'kbd', 'samp', 'var']
    },
    strategy: InjectionStrategy.SKIP,
    priority: 0,
    enabled: true
  },
  {
    id: 'custom-brand-inline',
    name: '品牌标识行内注入',
    description: '品牌相关元素使用行内注入',
    condition: {
      custom: (context) => {
        // 检测品牌相关的类名或属性
        const brandKeywords = ['brand', 'logo', 'site-name', 'site-title'];
        return brandKeywords.some(keyword =>
          context.classNames.some(cls => cls.includes(keyword)) ||
          context.id?.includes(keyword)
        );
      }
    },
    strategy: InjectionStrategy.INLINE,
    priority: 4,
    enabled: true
  }
];

/**
 * 规则匹配引擎
 */
export class InjectionRuleEngine {
  private rules: InjectionRule[] = [];

  constructor(initialRules: InjectionRule[] = DEFAULT_INJECTION_RULES) {
    this.rules = [...initialRules].sort((a, b) => a.priority - b.priority);
  }

  /**
   * 添加规则
   */
  addRule(rule: InjectionRule): void {
    this.rules.push(rule);
    this.rules.sort((a, b) => a.priority - b.priority);
  }

  /**
   * 移除规则
   */
  removeRule(ruleId: string): boolean {
    const index = this.rules.findIndex(rule => rule.id === ruleId);
    if (index !== -1) {
      this.rules.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 更新规则
   */
  updateRule(ruleId: string, updates: Partial<InjectionRule>): boolean {
    const rule = this.rules.find(rule => rule.id === ruleId);
    if (rule) {
      Object.assign(rule, updates);
      if (updates.priority !== undefined) {
        this.rules.sort((a, b) => a.priority - b.priority);
      }
      return true;
    }
    return false;
  }

  /**
   * 启用/禁用规则
   */
  toggleRule(ruleId: string, enabled?: boolean): boolean {
    const rule = this.rules.find(rule => rule.id === ruleId);
    if (rule) {
      rule.enabled = enabled !== undefined ? enabled : !rule.enabled;
      return true;
    }
    return false;
  }

  /**
   * 获取所有规则
   */
  getRules(): InjectionRule[] {
    return [...this.rules];
  }

  /**
   * 获取启用的规则
   */
  getEnabledRules(): InjectionRule[] {
    return this.rules.filter(rule => rule.enabled);
  }

  /**
   * 分析元素上下文
   */
  analyzeElement(element: HTMLElement): ElementContext {
    const computedStyle = window.getComputedStyle(element);
    const rect = element.getBoundingClientRect();

    return {
      tagName: element.tagName.toLowerCase(),
      classNames: Array.from(element.classList),
      id: element.id || undefined,
      textLength: this.getTextLength(element),
      isInline: this.isInlineElement(element),
      isBlock: this.isBlockElement(element),
      parent: {
        tagName: element.parentElement?.tagName.toLowerCase() || '',
        className: element.parentElement?.className || ''
      },
      childrenCount: element.children.length,
      hasLinks: element.querySelectorAll('a').length > 0,
      hasHtmlStructure: this.hasHtmlStructure(element),
      computedStyle: {
        display: computedStyle.display,
        position: computedStyle.position,
        float: computedStyle.float
      },
      boundingRect: {
        width: rect.width,
        height: rect.height,
        top: rect.top,
        left: rect.left
      },
      element: element
    };
  }

  /**
   * 匹配注入策略
   */
  matchStrategy(element: HTMLElement): {
    strategy: InjectionStrategy;
    rule: InjectionRule | null;
    context: ElementContext;
  } {
    const context = this.analyzeElement(element);
    const enabledRules = this.getEnabledRules();

    for (const rule of enabledRules) {
      if (this.matchesCondition(context, rule.condition)) {
        return {
          strategy: rule.strategy,
          rule,
          context
        };
      }
    }

    // 默认策略：根据元素类型选择
    const defaultStrategy = context.isInline && context.textLength < 100
      ? InjectionStrategy.INLINE
      : InjectionStrategy.BLOCK;

    return {
      strategy: defaultStrategy,
      rule: null,
      context
    };
  }

  /**
   * 检查条件是否匹配
   */
  private matchesCondition(context: ElementContext, condition: RuleCondition): boolean {
    // 标签名匹配
    if (condition.tagName) {
      if (!this.matchesStringPattern(context.tagName, condition.tagName)) {
        return false;
      }
    }

    // 类名匹配
    if (condition.className) {
      const hasMatchingClass = context.classNames.some(className =>
        this.matchesStringPattern(className, condition.className!)
      );
      if (!hasMatchingClass) {
        return false;
      }
    }

    // 文本长度匹配
    if (condition.textLength) {
      const { min, max } = condition.textLength;
      if (min !== undefined && context.textLength < min) return false;
      if (max !== undefined && context.textLength > max) return false;
    }

    // 显示类型匹配
    if (condition.displayType) {
      if (!condition.displayType.includes(context.computedStyle.display as any)) {
        return false;
      }
    }

    // 父元素条件匹配
    if (condition.parent) {
      if (condition.parent.tagName) {
        if (!this.matchesStringPattern(context.parent.tagName, condition.parent.tagName)) {
          return false;
        }
      }
      if (condition.parent.className) {
        if (!this.matchesStringPattern(context.parent.className, condition.parent.className)) {
          return false;
        }
      }
    }

    // 子元素条件匹配
    if (condition.children) {
      const { count, hasLinks, hasHtmlStructure } = condition.children;

      if (count) {
        if (count.min !== undefined && context.childrenCount < count.min) return false;
        if (count.max !== undefined && context.childrenCount > count.max) return false;
      }

      if (hasLinks !== undefined && context.hasLinks !== hasLinks) return false;
      if (hasHtmlStructure !== undefined && context.hasHtmlStructure !== hasHtmlStructure) return false;
    }

    // 自定义条件匹配
    if (condition.custom) {
      return condition.custom(context);
    }

    return true;
  }

  /**
   * 字符串模式匹配
   */
  private matchesStringPattern(
    value: string,
    pattern: string | string[] | RegExp
  ): boolean {
    if (typeof pattern === 'string') {
      return value === pattern;
    }

    if (Array.isArray(pattern)) {
      return pattern.includes(value);
    }

    if (pattern instanceof RegExp) {
      return pattern.test(value);
    }

    return false;
  }

  /**
   * 获取元素文本长度
   */
  private getTextLength(element: HTMLElement): number {
    return element.textContent?.trim().length || 0;
  }

  /**
   * 判断是否为内联元素
   */
  private isInlineElement(element: HTMLElement): boolean {
    const display = window.getComputedStyle(element).display;
    return ['inline', 'inline-block', 'inline-flex'].includes(display);
  }

  /**
   * 判断是否为块级元素
   */
  private isBlockElement(element: HTMLElement): boolean {
    const display = window.getComputedStyle(element).display;
    return ['block', 'flex', 'grid'].includes(display);
  }

  /**
   * 检查是否包含HTML结构
   */
  private hasHtmlStructure(element: HTMLElement): boolean {
    return element.children.length > 0 ||
      element.querySelectorAll('a, strong, em, b, i, span').length > 0;
  }

  /**
   * 重置为默认规则
   */
  resetToDefaults(): void {
    this.rules = [...DEFAULT_INJECTION_RULES].sort((a, b) => a.priority - b.priority);
  }

  /**
   * 导出规则配置
   */
  exportRules(): string {
    return JSON.stringify(this.rules, null, 2);
  }

  /**
   * 导入规则配置
   */
  importRules(rulesJson: string): boolean {
    try {
      const importedRules = JSON.parse(rulesJson);
      if (Array.isArray(importedRules)) {
        this.rules = importedRules.sort((a, b) => a.priority - b.priority);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to import rules:', error);
      return false;
    }
  }

  /**
   * 获取规则统计信息
   */
  getStats(): {
    totalRules: number;
    enabledRules: number;
    disabledRules: number;
    strategyCounts: Record<InjectionStrategy, number>;
  } {
    const enabledRules = this.getEnabledRules();
    const strategyCounts = {} as Record<InjectionStrategy, number>;

    // 初始化计数
    Object.values(InjectionStrategy).forEach(strategy => {
      strategyCounts[strategy] = 0;
    });

    // 统计每种策略的使用次数
    enabledRules.forEach(rule => {
      strategyCounts[rule.strategy]++;
    });

    return {
      totalRules: this.rules.length,
      enabledRules: enabledRules.length,
      disabledRules: this.rules.length - enabledRules.length,
      strategyCounts
    };
  }
}

/**
 * 全局规则引擎实例
 */
let globalRuleEngine: InjectionRuleEngine | null = null;

/**
 * 获取全局规则引擎实例
 */
export function getInjectionRuleEngine(): InjectionRuleEngine {
  if (!globalRuleEngine) {
    try {
      // 尝试从JSON配置文件加载规则
      const configLoader = new ConfigLoader();
      const rulesFromConfig = configLoader.loadRules();
      globalRuleEngine = new InjectionRuleEngine(rulesFromConfig);
      console.log('✅ Loaded injection rules from config file:', rulesFromConfig.length, 'rules');
    } catch (error) {
      // 如果加载失败，使用默认规则
      console.warn('⚠️ Failed to load rules from config, using defaults:', error);
      globalRuleEngine = new InjectionRuleEngine();
    }
  }
  return globalRuleEngine;
}

/**
 * 设置全局规则引擎实例
 */
export function setInjectionRuleEngine(engine: InjectionRuleEngine): void {
  globalRuleEngine = engine;
}

/**
 * 重置规则引擎（强制重新加载配置）
 */
export function resetInjectionRuleEngine(): void {
  globalRuleEngine = null;
  console.log('🔄 规则引擎缓存已重置，下次调用将重新加载配置');
}
