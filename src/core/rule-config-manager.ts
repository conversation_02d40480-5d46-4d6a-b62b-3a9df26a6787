/**
 * 注入规则配置管理器
 * 提供规则的持久化存储、验证、导入导出等功能
 */

import { 
  InjectionRule, 
  InjectionStrategy, 
  RuleCondition,
  DEFAULT_INJECTION_RULES,
  InjectionRuleEngine 
} from './injection-rules';

/**
 * 配置验证结果
 */
export interface ValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
}

/**
 * 规则模板
 */
export interface RuleTemplate {
  /** 模板ID */
  id: string;
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  description: string;
  /** 模板类别 */
  category: 'common' | 'advanced' | 'custom';
  /** 规则模板 */
  template: Partial<InjectionRule>;
}

/**
 * 配置预设
 */
export interface ConfigPreset {
  /** 预设ID */
  id: string;
  /** 预设名称 */
  name: string;
  /** 预设描述 */
  description: string;
  /** 预设规则集 */
  rules: InjectionRule[];
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
}

/**
 * 预定义规则模板
 */
export const RULE_TEMPLATES: RuleTemplate[] = [
  {
    id: 'inline-short',
    name: '短文本行内注入',
    description: '为短文本创建行内注入规则',
    category: 'common',
    template: {
      strategy: InjectionStrategy.INLINE,
      condition: {
        textLength: { max: 50 }
      },
      priority: 5,
      enabled: true
    }
  },
  {
    id: 'block-heading',
    name: '标题块级注入',
    description: '为标题元素创建块级注入规则',
    category: 'common',
    template: {
      strategy: InjectionStrategy.BLOCK,
      condition: {
        tagName: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']
      },
      priority: 10,
      enabled: true
    }
  },
  {
    id: 'inline-link',
    name: '链接行内注入',
    description: '为链接元素创建行内注入规则',
    category: 'common',
    template: {
      strategy: InjectionStrategy.INLINE,
      condition: {
        tagName: 'a',
        textLength: { max: 100 }
      },
      priority: 3,
      enabled: true
    }
  },
  {
    id: 'skip-code',
    name: '跳过代码元素',
    description: '为代码相关元素创建跳过规则',
    category: 'common',
    template: {
      strategy: InjectionStrategy.SKIP,
      condition: {
        tagName: ['code', 'pre', 'kbd', 'samp', 'var']
      },
      priority: 0,
      enabled: true
    }
  },
  {
    id: 'beside-tooltip',
    name: '提示框侧边注入',
    description: '为提示框元素创建侧边注入规则',
    category: 'advanced',
    template: {
      strategy: InjectionStrategy.BESIDE,
      condition: {
        className: ['tooltip', 'popup', 'dropdown']
      },
      priority: 2,
      enabled: true,
      options: {
        animation: 'fade',
        delay: 200
      }
    }
  },
  {
    id: 'overlay-hover',
    name: '悬停覆盖注入',
    description: '为特定元素创建悬停覆盖规则',
    category: 'advanced',
    template: {
      strategy: InjectionStrategy.OVERLAY,
      condition: {
        textLength: { min: 10, max: 200 }
      },
      priority: 8,
      enabled: false,
      options: {
        animation: 'fade',
        delay: 500
      }
    }
  }
];

/**
 * 默认配置预设
 */
export const DEFAULT_CONFIG_PRESETS: ConfigPreset[] = [
  {
    id: 'default',
    name: '默认配置',
    description: '系统默认的注入规则配置',
    rules: DEFAULT_INJECTION_RULES,
    createdAt: Date.now(),
    updatedAt: Date.now()
  },
  {
    id: 'minimal',
    name: '简化配置',
    description: '只包含基本的行内和块级注入规则',
    rules: [
      {
        id: 'minimal-inline',
        name: '基础行内注入',
        description: '短文本和链接使用行内注入',
        condition: {
          textLength: { max: 80 },
          displayType: ['inline', 'inline-block']
        },
        strategy: InjectionStrategy.INLINE,
        priority: 1,
        enabled: true
      },
      {
        id: 'minimal-block',
        name: '基础块级注入',
        description: '段落和标题使用块级注入',
        condition: {
          tagName: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li']
        },
        strategy: InjectionStrategy.BLOCK,
        priority: 10,
        enabled: true
      }
    ],
    createdAt: Date.now(),
    updatedAt: Date.now()
  },
  {
    id: 'advanced',
    name: '高级配置',
    description: '包含所有注入策略的完整配置',
    rules: [
      ...DEFAULT_INJECTION_RULES,
      {
        id: 'advanced-beside',
        name: '侧边注入示例',
        description: '为特殊元素使用侧边注入',
        condition: {
          custom: (context) => {
            return context.boundingRect.width > 600 && 
                   context.textLength > 200 &&
                   context.parent.tagName === 'main';
          }
        },
        strategy: InjectionStrategy.BESIDE,
        priority: 5,
        enabled: false
      }
    ],
    createdAt: Date.now(),
    updatedAt: Date.now()
  }
];

/**
 * 规则配置管理器
 */
export class RuleConfigManager {
  private storageKey = 'lucid-injection-rules';
  private presetsKey = 'lucid-rule-presets';
  private currentConfigKey = 'lucid-current-config';

  /**
   * 加载规则配置
   */
  async loadRules(): Promise<InjectionRule[]> {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const rules = JSON.parse(stored);
        if (Array.isArray(rules)) {
          return rules;
        }
      }
    } catch (error) {
      console.warn('Failed to load rules from storage:', error);
    }
    
    // 返回默认规则
    return [...DEFAULT_INJECTION_RULES];
  }

  /**
   * 保存规则配置
   */
  async saveRules(rules: InjectionRule[]): Promise<boolean> {
    try {
      const validation = this.validateRules(rules);
      if (!validation.isValid) {
        console.error('Cannot save invalid rules:', validation.errors);
        return false;
      }

      localStorage.setItem(this.storageKey, JSON.stringify(rules));
      return true;
    } catch (error) {
      console.error('Failed to save rules:', error);
      return false;
    }
  }

  /**
   * 验证规则配置
   */
  validateRules(rules: InjectionRule[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!Array.isArray(rules)) {
      errors.push('Rules must be an array');
      return { isValid: false, errors, warnings };
    }

    // 检查规则ID唯一性
    const ruleIds = new Set<string>();
    const duplicateIds: string[] = [];

    rules.forEach((rule, index) => {
      // 基本字段验证
      if (!rule.id || typeof rule.id !== 'string') {
        errors.push(`Rule ${index}: Invalid or missing ID`);
      } else if (ruleIds.has(rule.id)) {
        duplicateIds.push(rule.id);
      } else {
        ruleIds.add(rule.id);
      }

      if (!rule.name || typeof rule.name !== 'string') {
        errors.push(`Rule ${rule.id || index}: Invalid or missing name`);
      }

      if (!Object.values(InjectionStrategy).includes(rule.strategy)) {
        errors.push(`Rule ${rule.id || index}: Invalid strategy ${rule.strategy}`);
      }

      if (typeof rule.priority !== 'number') {
        errors.push(`Rule ${rule.id || index}: Priority must be a number`);
      }

      if (typeof rule.enabled !== 'boolean') {
        errors.push(`Rule ${rule.id || index}: Enabled must be a boolean`);
      }

      // 条件验证
      if (rule.condition) {
        this.validateCondition(rule.condition, rule.id || index.toString(), errors, warnings);
      }

      // 优先级检查
      if (rule.priority < 0 || rule.priority > 100) {
        warnings.push(`Rule ${rule.id}: Priority ${rule.priority} is outside recommended range (0-100)`);
      }
    });

    // 报告重复ID
    if (duplicateIds.length > 0) {
      errors.push(`Duplicate rule IDs found: ${duplicateIds.join(', ')}`);
    }

    // 检查是否有启用的规则
    const enabledRules = rules.filter(r => r.enabled);
    if (enabledRules.length === 0) {
      warnings.push('No enabled rules found');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证规则条件
   */
  private validateCondition(
    condition: RuleCondition, 
    ruleId: string, 
    errors: string[], 
    warnings: string[]
  ): void {
    // 验证标签名
    if (condition.tagName) {
      if (typeof condition.tagName === 'string') {
        if (!condition.tagName.trim()) {
          errors.push(`Rule ${ruleId}: Empty tagName string`);
        }
      } else if (Array.isArray(condition.tagName)) {
        if (condition.tagName.length === 0) {
          warnings.push(`Rule ${ruleId}: Empty tagName array`);
        }
      } else if (!(condition.tagName instanceof RegExp)) {
        errors.push(`Rule ${ruleId}: tagName must be string, array, or RegExp`);
      }
    }

    // 验证文本长度
    if (condition.textLength) {
      const { min, max } = condition.textLength;
      if (min !== undefined && (typeof min !== 'number' || min < 0)) {
        errors.push(`Rule ${ruleId}: textLength.min must be a non-negative number`);
      }
      if (max !== undefined && (typeof max !== 'number' || max < 0)) {
        errors.push(`Rule ${ruleId}: textLength.max must be a non-negative number`);
      }
      if (min !== undefined && max !== undefined && min > max) {
        errors.push(`Rule ${ruleId}: textLength.min cannot be greater than max`);
      }
    }

    // 验证显示类型
    if (condition.displayType) {
      const validDisplayTypes = ['inline', 'block', 'inline-block', 'flex', 'grid'];
      const invalidTypes = condition.displayType.filter(type => !validDisplayTypes.includes(type));
      if (invalidTypes.length > 0) {
        errors.push(`Rule ${ruleId}: Invalid display types: ${invalidTypes.join(', ')}`);
      }
    }

    // 验证自定义函数
    if (condition.custom && typeof condition.custom !== 'function') {
      errors.push(`Rule ${ruleId}: custom condition must be a function`);
    }
  }

  /**
   * 创建新规则
   */
  createRule(template?: RuleTemplate): InjectionRule {
    const baseRule: InjectionRule = {
      id: this.generateRuleId(),
      name: 'New Rule',
      description: 'Description for new rule',
      condition: {},
      strategy: InjectionStrategy.BLOCK,
      priority: 50,
      enabled: true
    };

    if (template) {
      return {
        ...baseRule,
        ...template.template,
        id: this.generateRuleId(),
        name: template.name,
        description: template.description
      };
    }

    return baseRule;
  }

  /**
   * 生成唯一规则ID
   */
  private generateRuleId(): string {
    return `rule-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取规则模板
   */
  getRuleTemplates(): RuleTemplate[] {
    return [...RULE_TEMPLATES];
  }

  /**
   * 导出规则配置
   */
  async exportRules(rules: InjectionRule[]): Promise<string> {
    const exportData = {
      version: '1.0',
      exportedAt: new Date().toISOString(),
      rules: rules,
      metadata: {
        totalRules: rules.length,
        enabledRules: rules.filter(r => r.enabled).length,
        strategies: this.getStrategyUsage(rules)
      }
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 导入规则配置
   */
  async importRules(jsonData: string): Promise<{
    success: boolean;
    rules?: InjectionRule[];
    error?: string;
    warnings?: string[];
  }> {
    try {
      const data = JSON.parse(jsonData);
      
      // 检查格式
      if (!data.rules || !Array.isArray(data.rules)) {
        return {
          success: false,
          error: 'Invalid format: missing rules array'
        };
      }

      // 验证规则
      const validation = this.validateRules(data.rules);
      
      if (!validation.isValid) {
        return {
          success: false,
          error: `Validation failed: ${validation.errors.join(', ')}`
        };
      }

      return {
        success: true,
        rules: data.rules,
        warnings: validation.warnings
      };

    } catch (error) {
      return {
        success: false,
        error: `Parse error: ${(error as Error).message}`
      };
    }
  }

  /**
   * 获取策略使用统计
   */
  private getStrategyUsage(rules: InjectionRule[]): Record<string, number> {
    const usage: Record<string, number> = {};
    
    rules.forEach(rule => {
      usage[rule.strategy] = (usage[rule.strategy] || 0) + 1;
    });

    return usage;
  }

  /**
   * 保存配置预设
   */
  async savePreset(preset: ConfigPreset): Promise<boolean> {
    try {
      const presets = await this.loadPresets();
      const existingIndex = presets.findIndex(p => p.id === preset.id);
      
      if (existingIndex >= 0) {
        presets[existingIndex] = { ...preset, updatedAt: Date.now() };
      } else {
        presets.push(preset);
      }

      localStorage.setItem(this.presetsKey, JSON.stringify(presets));
      return true;
    } catch (error) {
      console.error('Failed to save preset:', error);
      return false;
    }
  }

  /**
   * 加载配置预设
   */
  async loadPresets(): Promise<ConfigPreset[]> {
    try {
      const stored = localStorage.getItem(this.presetsKey);
      if (stored) {
        const presets = JSON.parse(stored);
        if (Array.isArray(presets)) {
          return presets;
        }
      }
    } catch (error) {
      console.warn('Failed to load presets:', error);
    }

    // 返回默认预设
    return [...DEFAULT_CONFIG_PRESETS];
  }

  /**
   * 删除配置预设
   */
  async deletePreset(presetId: string): Promise<boolean> {
    try {
      const presets = await this.loadPresets();
      const filteredPresets = presets.filter(p => p.id !== presetId);
      
      if (filteredPresets.length !== presets.length) {
        localStorage.setItem(this.presetsKey, JSON.stringify(filteredPresets));
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to delete preset:', error);
      return false;
    }
  }

  /**
   * 应用配置预设
   */
  async applyPreset(presetId: string): Promise<InjectionRule[] | null> {
    try {
      const presets = await this.loadPresets();
      const preset = presets.find(p => p.id === presetId);
      
      if (preset) {
        await this.saveRules(preset.rules);
        localStorage.setItem(this.currentConfigKey, presetId);
        return preset.rules;
      }
      
      return null;
    } catch (error) {
      console.error('Failed to apply preset:', error);
      return null;
    }
  }

  /**
   * 获取当前配置
   */
  getCurrentConfig(): string | null {
    return localStorage.getItem(this.currentConfigKey);
  }

  /**
   * 重置为默认配置
   */
  async resetToDefaults(): Promise<boolean> {
    try {
      await this.saveRules(DEFAULT_INJECTION_RULES);
      localStorage.setItem(this.currentConfigKey, 'default');
      return true;
    } catch (error) {
      console.error('Failed to reset to defaults:', error);
      return false;
    }
  }

  /**
   * 清除所有配置
   */
  async clearAllConfig(): Promise<void> {
    localStorage.removeItem(this.storageKey);
    localStorage.removeItem(this.presetsKey);
    localStorage.removeItem(this.currentConfigKey);
  }

  /**
   * 获取配置统计
   */
  async getConfigStats(): Promise<{
    totalRules: number;
    enabledRules: number;
    disabledRules: number;
    strategyCounts: Record<InjectionStrategy, number>;
    totalPresets: number;
    currentConfig: string | null;
  }> {
    const rules = await this.loadRules();
    const presets = await this.loadPresets();
    const currentConfig = this.getCurrentConfig();

    const strategyCounts = {} as Record<InjectionStrategy, number>;
    Object.values(InjectionStrategy).forEach(strategy => {
      strategyCounts[strategy] = 0;
    });

    rules.forEach(rule => {
      strategyCounts[rule.strategy]++;
    });

    return {
      totalRules: rules.length,
      enabledRules: rules.filter(r => r.enabled).length,
      disabledRules: rules.filter(r => !r.enabled).length,
      strategyCounts,
      totalPresets: presets.length,
      currentConfig
    };
  }
}

/**
 * 全局配置管理器实例
 */
let globalConfigManager: RuleConfigManager | null = null;

/**
 * 获取全局配置管理器
 */
export function getRuleConfigManager(): RuleConfigManager {
  if (!globalConfigManager) {
    globalConfigManager = new RuleConfigManager();
  }
  return globalConfigManager;
}

/**
 * 设置全局配置管理器
 */
export function setRuleConfigManager(manager: RuleConfigManager): void {
  globalConfigManager = manager;
}