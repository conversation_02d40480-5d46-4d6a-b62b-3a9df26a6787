/**
 * 配置文件加载器
 * 负责从配置文件加载注入规则并转换为系统可用格式
 */

import {
  InjectionRule,
  InjectionStrategy,
  RuleCondition,
  ElementContext
} from './injection-rules';
import injectionRulesConfig from '../config/advanced-injection-rules.json';

/**
 * 配置文件格式接口
 */
interface ConfigFile {
  version: string;
  description: string;
  updatedAt: string;
  rules: ConfigRule[];
  customFunctions: Record<string, string>;
  settings: ConfigSettings;
}

/**
 * 配置文件中的规则格式
 */
interface ConfigRule {
  id: string;
  name: string;
  description: string;
  conditions: ConfigCondition[];
  strategy: string;
  priority: number;
  enabled: boolean;
  options?: {
    animation?: string;
    delay?: number;
    customClasses?: string[];
  };
}

/**
 * 配置文件中的条件格式
 */
interface ConfigCondition {
  tagName?: string | string[];
  className?: string | string[];
  nestedSelector?: string | string[];
  textLength?: {
    min?: number;
    max?: number;
  };
  displayType?: string[];
  parent?: {
    tagName?: string | string[];
    className?: string | string[];
  };
  children?: {
    count?: {
      min?: number;
      max?: number;
    };
    hasLinks?: boolean;
    hasHtmlStructure?: boolean;
  };
  customFunction?: string;
  comment?: string;
}

/**
 * 配置设置
 */
interface ConfigSettings {
  defaultStrategy: string;
  enableAutoFallback: boolean;
  performanceMode: 'fast' | 'balanced' | 'accurate';
  debugMode: boolean;
}

/**
 * 自定义函数映射
 */
const CUSTOM_FUNCTIONS: Record<string, (context: ElementContext) => boolean> = {
  brandDetection: (context: ElementContext) => {
    // 检测品牌相关的类名或属性
    const brandKeywords = ['brand', 'logo', 'site-name', 'site-title'];
    return brandKeywords.some(keyword =>
      context.classNames.some(cls => cls.includes(keyword)) ||
      context.id?.includes(keyword)
    );
  },

  isNewsCardHeadline: (context: ElementContext) => {
    // 能到达这里说明已经通过了基础的 article a 过滤
    // 现在进行更详细的 Google News 新闻卡片判断

    const element = context.element as HTMLAnchorElement;
    const parentArticle = element.closest('article');

    if (!parentArticle) {
      return false;
    }

    // 1. 检查是否有图片（新闻卡片特征）
    const hasImage = parentArticle.querySelector('figure, img');
    if (!hasImage) {
      return false;
    }

    // 2. 检查是否是卡片中文本最多的链接（主标题特征）
    const allLinks = Array.from(parentArticle.querySelectorAll('a'));
    if (allLinks.length === 0) {
      return false;
    }

    const linkWithMostText = allLinks.reduce((longest, link) =>
      (link.textContent?.length || 0) > (longest.textContent?.length || 0) ? link : longest
    );

    // 3. 检查是否有覆盖层链接（Google News 特征）
    // 更新检测逻辑：检查是否有多个链接且有隐藏的覆盖链接
    const hasOverlayLink = parentArticle.querySelector(
      'a[aria-hidden="true"], a[tabindex="-1"], a[style*="absolute"], a[style*="cover"]'
    );

    // 4. 检查文本长度是否合理（20-200字符）
    const textLength = element.textContent?.trim().length || 0;
    if (textLength < 20 || textLength > 200) {
      return false;
    }

    // 5. 确保有多个链接（覆盖链接 + 文本链接的组合）
    if (allLinks.length < 2) {
      return false;
    }

    // 只有当前元素是文本最多的链接，且存在覆盖层结构时才匹配
    return element === linkWithMostText && !!hasOverlayLink;
  }
};

/**
 * 配置加载器类
 */
export class ConfigLoader {
  private config: ConfigFile;

  constructor() {
    this.config = injectionRulesConfig as ConfigFile;
  }

  /**
   * 加载并转换规则
   */
  loadRules(): InjectionRule[] {
    return this.config.rules.map(configRule => this.convertRule(configRule));
  }

  /**
   * 加载高级配置文件（用于简化架构）
   */
  async loadAdvancedConfig(): Promise<any> {
    try {
      // 直接返回原始配置，供AdvancedConfigParser处理
      return this.config;
    } catch (error) {
      console.error('Failed to load advanced config:', error);
      throw new Error(`Config loading failed: ${(error as Error).message}`);
    }
  }

  /**
   * 获取配置设置
   */
  getSettings(): ConfigSettings {
    return { ...this.config.settings };
  }

  /**
   * 获取配置版本信息
   */
  getVersionInfo(): {
    version: string;
    description: string;
    updatedAt: string;
  } {
    return {
      version: this.config.version,
      description: this.config.description,
      updatedAt: this.config.updatedAt
    };
  }

  /**
   * 转换配置规则为系统规则
   */
  private convertRule(configRule: ConfigRule): InjectionRule {
    // 将多个条件合并为一个条件（现在支持OR逻辑）
    // 但为了保持向后兼容，我们先处理第一个条件
    const mainCondition = configRule.conditions && configRule.conditions.length > 0
      ? configRule.conditions[0]
      : {};

    return {
      id: configRule.id,
      name: configRule.name,
      description: configRule.description,
      condition: this.convertCondition(mainCondition),
      strategy: this.convertStrategy(configRule.strategy),
      priority: configRule.priority,
      enabled: configRule.enabled,
      options: configRule.options as any
    };
  }

  /**
   * 转换条件配置
   */
  private convertCondition(configCondition: ConfigCondition): RuleCondition {
    const condition: RuleCondition = {};

    // 转换基本条件
    if (configCondition.tagName) {
      condition.tagName = configCondition.tagName;
    }

    if (configCondition.className) {
      condition.className = configCondition.className;
    }

    if (configCondition.textLength) {
      condition.textLength = configCondition.textLength;
    }

    if (configCondition.displayType) {
      condition.displayType = configCondition.displayType as any[];
    }

    // 转换嵌套选择器 - 将其转换为父子关系
    if (configCondition.nestedSelector) {
      const selectors = Array.isArray(configCondition.nestedSelector)
        ? configCondition.nestedSelector
        : [configCondition.nestedSelector];

      // 对于 "article a" 这样的选择器，我们将其转换为父子关系
      for (const selector of selectors) {
        const parts = selector.trim().split(/\s+/);
        if (parts.length === 2) {
          const [parentTag, childTag] = parts;
          condition.parent = {
            tagName: [parentTag]
          };
          condition.tagName = [childTag];
          break; // 只处理第一个匹配的选择器
        }
      }
    }

    // 转换父元素条件
    if (configCondition.parent) {
      condition.parent = {
        tagName: configCondition.parent.tagName,
        className: configCondition.parent.className
      };
    }

    // 转换子元素条件
    if (configCondition.children) {
      condition.children = configCondition.children;
    }

    // 转换自定义函数
    if (configCondition.customFunction) {
      const customFn = CUSTOM_FUNCTIONS[configCondition.customFunction];
      if (customFn) {
        condition.custom = customFn;
      } else {
        console.warn(`Unknown custom function: ${configCondition.customFunction}`);
      }
    }

    return condition;
  }

  /**
   * 转换策略字符串为枚举
   */
  private convertStrategy(strategyString: string): InjectionStrategy {
    const strategyMap: Record<string, InjectionStrategy> = {
      'inline': InjectionStrategy.INLINE,
      'block': InjectionStrategy.BLOCK,
      'beside': InjectionStrategy.BESIDE,
      'overlay': InjectionStrategy.OVERLAY,
      'hidden-preserve': InjectionStrategy.HIDDEN_PRESERVE,
      'clone_replace': InjectionStrategy.CLONE_REPLACE,
      'skip': InjectionStrategy.SKIP
    };

    const strategy = strategyMap[strategyString.toLowerCase()];
    if (!strategy) {
      console.warn(`Unknown strategy: ${strategyString}, falling back to BLOCK`);
      return InjectionStrategy.BLOCK;
    }

    return strategy;
  }

  /**
   * 验证配置文件格式
   */
  validateConfig(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查版本
    if (!this.config.version) {
      errors.push('Missing version field');
    }

    // 检查规则数组
    if (!Array.isArray(this.config.rules)) {
      errors.push('Rules must be an array');
      return { isValid: false, errors, warnings };
    }

    // 检查每个规则
    this.config.rules.forEach((rule, index) => {
      if (!rule.id) {
        errors.push(`Rule ${index}: Missing id`);
      }

      if (!rule.strategy) {
        errors.push(`Rule ${rule.id || index}: Missing strategy`);
      }

      if (typeof rule.priority !== 'number') {
        errors.push(`Rule ${rule.id || index}: Priority must be a number`);
      }

      // 检查自定义函数是否存在
      if (rule.conditions) {
        rule.conditions.forEach((condition: ConfigCondition, conditionIndex: number) => {
          if (condition.customFunction &&
            !CUSTOM_FUNCTIONS[condition.customFunction]) {
            warnings.push(`Rule ${rule.id} condition ${conditionIndex}: Custom function '${condition.customFunction}' not found`);
          }
        });
      }
    });

    // 检查重复ID
    const ruleIds = this.config.rules.map(r => r.id);
    const duplicateIds = ruleIds.filter((id, index) => ruleIds.indexOf(id) !== index);
    if (duplicateIds.length > 0) {
      errors.push(`Duplicate rule IDs: ${duplicateIds.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 热重载配置（开发模式）
   */
  async reloadConfig(): Promise<boolean> {
    try {
      // 在实际环境中，这里可以重新读取配置文件
      // 现在只是验证当前配置
      const validation = this.validateConfig();
      if (validation.isValid) {
        console.log('Configuration reloaded successfully');
        if (validation.warnings.length > 0) {
          console.warn('Configuration warnings:', validation.warnings);
        }
        return true;
      } else {
        console.error('Configuration reload failed:', validation.errors);
        return false;
      }
    } catch (error) {
      console.error('Failed to reload configuration:', error);
      return false;
    }
  }

  /**
   * 获取启用的规则数量
   */
  getEnabledRulesCount(): number {
    return this.config.rules.filter(rule => rule.enabled).length;
  }

  /**
   * 获取策略分布统计
   */
  getStrategyDistribution(): Record<string, number> {
    const distribution: Record<string, number> = {};

    this.config.rules.forEach(rule => {
      if (rule.enabled) {
        distribution[rule.strategy] = (distribution[rule.strategy] || 0) + 1;
      }
    });

    return distribution;
  }

  /**
   * 注册自定义函数
   */
  static registerCustomFunction(
    name: string,
    fn: (context: ElementContext) => boolean
  ): void {
    CUSTOM_FUNCTIONS[name] = fn;
  }

  /**
   * 获取所有可用的自定义函数
   */
  static getAvailableCustomFunctions(): string[] {
    return Object.keys(CUSTOM_FUNCTIONS);
  }
}

/**
 * 全局配置加载器实例
 */
let globalConfigLoader: ConfigLoader | null = null;

/**
 * 获取全局配置加载器
 */
export function getConfigLoader(): ConfigLoader {
  if (!globalConfigLoader) {
    globalConfigLoader = new ConfigLoader();
  }
  return globalConfigLoader;
}

/**
 * 重置配置加载器（主要用于测试）
 */
export function resetConfigLoader(): void {
  globalConfigLoader = null;
}