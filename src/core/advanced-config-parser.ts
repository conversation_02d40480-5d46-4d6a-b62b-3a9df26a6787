/**
 * 高级配置解析器
 * 支持用户定义的复杂配置语法，包括嵌套选择器和条件组合
 */

export interface StrategyCondition {
  /** 简单标签匹配 */
  tagName?: string[];
  /** 嵌套选择器匹配，如 "a div", "button span" */
  nestedSelector?: string | string[];
  /** 类名匹配 */
  className?: string[];
  /** 文本长度限制 */
  textLength?: {
    min?: number;
    max?: number;
  };
  /** URL条件匹配 */
  url?: string[];
  /** 自定义函数名称 */
  customFunction?: string;
  /** 规则说明（仅用于文档） */
  comment?: string;
}

export interface StrategyRule {
  id: string;
  name: string;
  description: string;
  conditions: StrategyCondition[];
  strategy: 'inline' | 'block' | 'skip' | 'clone_replace';
  priority: number;
  enabled: boolean;
  options?: {
    animation?: string;
    delay?: number;
    customClasses?: string[];
  };
}

export interface StrategyConfig {
  rules: StrategyRule[];
}

export class AdvancedConfigParser {
  private debug = true;

  /**
   * 解析配置文件并验证
   */
  parseConfig(configData: any): StrategyRule[] {
    this.log('开始解析配置文件...');

    if (!configData) {
      throw new Error('Configuration data is required');
    }

    if (!configData.rules || !Array.isArray(configData.rules)) {
      throw new Error('Invalid config: rules must be an array');
    }

    const strategies = configData.rules
      .filter((rule: any) => {
        if (rule.enabled === false) {
          this.log(`跳过已禁用的规则: ${rule.id}`);
          return false;
        }
        return true;
      })
      .map((rule: any, index: number) => {
        try {
          return this.parseStrategy(rule);
        } catch (error) {
          console.error(`解析规则 #${index} 失败:`, error);
          throw new Error(`Failed to parse rule at index ${index}: ${(error as Error).message}`);
        }
      })
      .sort((a: any, b: any) => b.priority - a.priority); // 按优先级降序排序

    this.log(`成功解析 ${strategies.length} 个策略规则`);
    
    // 验证优先级唯一性
    this.validatePriorities(strategies);
    
    return strategies;
  }

  /**
   * 解析单个策略规则
   */
  private parseStrategy(strategyData: any): StrategyRule {
    // 验证必需字段
    if (!strategyData.id) {
      throw new Error('Strategy id is required');
    }

    if (!strategyData.strategy) {
      throw new Error(`Strategy '${strategyData.id}' must have a strategy field`);
    }

    // 验证策略类型
    const validStrategies = ['inline', 'block', 'skip', 'clone_replace'];
    if (!validStrategies.includes(strategyData.strategy)) {
      throw new Error(`Invalid strategy '${strategyData.strategy}' in rule '${strategyData.id}'. Must be one of: ${validStrategies.join(', ')}`);
    }

    const rule: StrategyRule = {
      id: strategyData.id,
      name: strategyData.name || strategyData.id,
      description: strategyData.description || '',
      conditions: this.parseConditions(strategyData.conditions || []),
      strategy: strategyData.strategy,
      priority: this.parsePriority(strategyData.priority),
      enabled: strategyData.enabled !== false,
      options: strategyData.options
    };

    this.log(`解析策略: ${rule.id} (priority: ${rule.priority}, strategy: ${rule.strategy})`);
    
    return rule;
  }

  /**
   * 解析条件数组
   */
  private parseConditions(conditionsData: any[]): StrategyCondition[] {
    if (!Array.isArray(conditionsData)) {
      // 兼容单个条件对象的情况
      conditionsData = [conditionsData];
    }

    return conditionsData.map((condition, index) => {
      try {
        return this.parseCondition(condition);
      } catch (error) {
        throw new Error(`Failed to parse condition at index ${index}: ${(error as Error).message}`);
      }
    });
  }

  /**
   * 解析单个条件
   */
  private parseCondition(conditionData: any): StrategyCondition {
    const condition: StrategyCondition = {};

    // 解析 tagName
    if (conditionData.tagName !== undefined) {
      condition.tagName = this.normalizeStringArray(conditionData.tagName, 'tagName');
      
      // 转换为小写以便匹配
      condition.tagName = condition.tagName.map(tag => tag.toLowerCase());
    }

    // 解析 nestedSelector
    if (conditionData.nestedSelector !== undefined) {
      if (typeof conditionData.nestedSelector === 'string') {
        condition.nestedSelector = conditionData.nestedSelector.trim();
      } else if (Array.isArray(conditionData.nestedSelector)) {
        condition.nestedSelector = conditionData.nestedSelector
          .map((selector: any) => typeof selector === 'string' ? selector.trim() : '')
          .filter((selector: any) => selector.length > 0);
      } else {
        throw new Error('nestedSelector must be a string or array of strings');
      }
    }

    // 解析 className
    if (conditionData.className !== undefined) {
      condition.className = this.normalizeStringArray(conditionData.className, 'className');
    }

    // 解析 URL 条件
    if (conditionData.url !== undefined) {
      condition.url = this.normalizeStringArray(conditionData.url, 'url');
      
      // 验证URL格式
      for (const url of condition.url) {
        if (!this.isValidUrlPattern(url)) {
          console.warn(`⚠️ [translation|WARN] Invalid URL pattern: ${url}`);
        }
      }
    }

    // 解析 textLength
    if (conditionData.textLength !== undefined) {
      if (typeof conditionData.textLength !== 'object' || conditionData.textLength === null) {
        throw new Error('textLength must be an object with min/max properties');
      }

      condition.textLength = {};

      if (conditionData.textLength.min !== undefined) {
        const min = Number(conditionData.textLength.min);
        if (isNaN(min) || min < 0) {
          throw new Error('textLength.min must be a non-negative number');
        }
        condition.textLength.min = min;
      }

      if (conditionData.textLength.max !== undefined) {
        const max = Number(conditionData.textLength.max);
        if (isNaN(max) || max < 0) {
          throw new Error('textLength.max must be a non-negative number');
        }
        condition.textLength.max = max;
      }

      // 验证 min <= max
      if (condition.textLength.min !== undefined && 
          condition.textLength.max !== undefined && 
          condition.textLength.min > condition.textLength.max) {
        throw new Error('textLength.min cannot be greater than textLength.max');
      }
    }

    // 解析 customFunction
    if (conditionData.customFunction !== undefined) {
      if (typeof conditionData.customFunction !== 'string' || conditionData.customFunction.trim() === '') {
        throw new Error('customFunction must be a non-empty string');
      }
      condition.customFunction = conditionData.customFunction.trim();
    }

    // 保存注释
    if (conditionData.comment) {
      condition.comment = String(conditionData.comment);
    }

    return condition;
  }

  /**
   * 规范化字符串数组
   */
  private normalizeStringArray(value: any, fieldName: string): string[] {
    if (Array.isArray(value)) {
      return value.map(item => {
        if (typeof item !== 'string') {
          throw new Error(`${fieldName} array must contain only strings, got: ${typeof item}`);
        }
        return item.trim();
      }).filter(item => item.length > 0);
    }
    
    if (typeof value === 'string') {
      const trimmed = value.trim();
      return trimmed.length > 0 ? [trimmed] : [];
    }
    
    throw new Error(`${fieldName} must be a string or array of strings`);
  }

  /**
   * 解析优先级
   */
  private parsePriority(priority: any): number {
    if (priority === undefined || priority === null) {
      return 1; // 默认优先级
    }

    const numPriority = Number(priority);
    if (isNaN(numPriority)) {
      throw new Error('Priority must be a number');
    }

    if (numPriority < 0 || numPriority > 1000) {
      throw new Error('Priority must be between 0 and 1000');
    }

    return numPriority;
  }

  /**
   * 验证优先级唯一性
   */
  private validatePriorities(strategies: StrategyRule[]): void {
    const priorityMap = new Map<number, string[]>();

    strategies.forEach(strategy => {
      const existing = priorityMap.get(strategy.priority) || [];
      existing.push(strategy.id);
      priorityMap.set(strategy.priority, existing);
    });

    // 检查重复优先级（警告但不阻止）
    priorityMap.forEach((strategyIds, priority) => {
      if (strategyIds.length > 1) {
        console.warn(`警告: 多个策略具有相同优先级 ${priority}:`, strategyIds);
      }
    });
  }

  /**
   * 验证完整配置
   */
  validateConfig(strategies: StrategyRule[]): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查是否有默认策略
    const hasDefault = strategies.some(s => s.conditions.length === 0 || 
      s.conditions.every(c => Object.keys(c).length === 0 || (Object.keys(c).length === 1 && c.comment)));
    
    if (!hasDefault) {
      warnings.push('建议添加一个默认策略（空条件）作为最后的回退');
    }

    // 检查跳过策略是否有最高优先级
    const skipStrategies = strategies.filter(s => s.strategy === 'skip');
    if (skipStrategies.length > 0) {
      const maxPriority = Math.max(...strategies.map(s => s.priority));
      const highestSkipPriority = Math.max(...skipStrategies.map(s => s.priority));
      
      if (highestSkipPriority < maxPriority) {
        warnings.push('跳过策略通常应该具有最高优先级以确保正确排除');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 调试日志
   */
  private log(message: string, ...args: any[]): void {
    if (this.debug) {
      console.log(`[AdvancedConfigParser] ${message}`, ...args);
    }
  }

  /**
   * 设置调试模式
   */
  setDebug(enabled: boolean): void {
    this.debug = enabled;
  }

  /**
   * 验证URL模式是否有效
   */
  private isValidUrlPattern(pattern: string): boolean {
    if (!pattern || typeof pattern !== 'string') {
      return false;
    }

    // 相对路径模式
    if (pattern.startsWith('/')) {
      return pattern.length > 1;
    }

    // 通配符模式
    if (pattern.includes('*')) {
      // 简单验证：确保通配符不在协议部分
      if (pattern.startsWith('*')) {
        return false;
      }
      return true;
    }

    // 完整URL模式
    try {
      new URL(pattern);
      return true;
    } catch {
      // 如果不是有效URL，检查是否为域名模式
      return /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(pattern);
    }
  }
}