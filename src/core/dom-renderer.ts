/**
 * DOM渲染器 - 轻量化版本
 * 专注性能和实用性，避免过度工程化
 */

import {
  RenderOperation,
  TranslationResult,
  TranslationConfig,
  LinkInfo,
  TranslationErrorType,
  TranslationError
} from '../types';
import { DOMUtils } from '../utils/dom-utils';
import { processDOMBatch } from '../utils/batch';
import { TextProcessor } from '../utils/text-utils';
import { DOMBatchProcessor, type DOMOperation } from '../utils/dom-batch-processor';
import { ParallelBatchProcessor } from '../utils/parallel-batch-processor';

export class DOMRenderer {
  private config: TranslationConfig['rendering'];
  private domBatchProcessor?: DOMBatchProcessor;
  private parallelProcessor?: ParallelBatchProcessor;
  private useParallelProcessing: boolean;
  private stats = {
    totalRenders: 0,
    successfulRenders: 0,
    failedRenders: 0,
    averageTime: 0,
    totalTime: 0
  };

  constructor(config: TranslationConfig['rendering']) {
    this.config = config;

    // 根据配置选择处理模式
    this.useParallelProcessing = this.shouldUseParallelProcessing();

    if (this.useParallelProcessing) {
      // 初始化并行批处理器
      this.parallelProcessor = new ParallelBatchProcessor({
        workerCount: 4,
        loadBalanceStrategy: 'priority-based',
        maxWaitTime: 100,
        enableStats: true,
        processorOptions: {
          batchSize: 20,
          debounceTime: 6,
          maxWaitTime: 40,
          enableProfiling: true
        }
      });
    } else {
      // 初始化单个DOM批处理器
      this.domBatchProcessor = new DOMBatchProcessor({
        batchSize: 30,
        debounceTime: 8,
        maxWaitTime: 50,
        enableProfiling: true
      });
    }
  }

  /**
   * 判断是否使用并行处理
   */
  private shouldUseParallelProcessing(): boolean {
    // 根据设备性能和环境决定
    const isHighPerformanceDevice = navigator.hardwareConcurrency >= 4;
    const hasEnoughMemory = (navigator as any).deviceMemory >= 4 || true; // GB，默认假设足够
    // 注意：rendering配置中可能没有debug属性，使用默认值
    const isProductionMode = true; // 默认启用生产模式

    return isHighPerformanceDevice && hasEnoughMemory && isProductionMode;
  }

  /**
   * 渲染单个翻译
   */
  async render(
    element: HTMLElement,
    translatedText: string,
    originalText: string,
    options: {
      format?: 'text' | 'html';
      mode?: 'append' | 'replace';
      links?: LinkInfo[];
    } = {}
  ): Promise<TranslationResult> {
    const startTime = performance.now();
    this.stats.totalRenders++;

    try {
      const {
        format = 'text',
        mode = this.config.defaultMode,
        links = []
      } = options;

      // 检查元素有效性
      if (!this.isValidTarget(element)) {
        throw new TranslationError(
          TranslationErrorType.RENDERING_FAILED,
          'Invalid target element'
        );
      }

      // 检查是否已翻译
      if (DOMUtils.isTranslated(element)) {
        if (mode === 'replace') {
          this.clearPreviousTranslation(element);
        } else {
          throw new TranslationError(
            TranslationErrorType.RENDERING_FAILED,
            'Element already translated'
          );
        }
      }

      // 创建翻译元素
      const translationElement = this.createTranslationElement(
        translatedText,
        format,
        links
      );

      // 执行渲染 - 使用批处理优化
      await this.performOptimizedRender(element, translationElement, mode);

      // 标记为已翻译
      DOMUtils.markAsTranslated(element);

      // 更新统计
      const duration = performance.now() - startTime;
      this.updateStats(duration, true);

      return {
        success: true,
        element,
        originalText,
        translatedText,
        duration
      };

    } catch (error) {
      const duration = performance.now() - startTime;
      this.updateStats(duration, false);

      return {
        success: false,
        element,
        originalText,
        error: (error as Error).message,
        duration
      };
    }
  }

  /**
   * 批量渲染 - 优化版本使用DOM批处理器
   */
  async renderBatch(operations: RenderOperation[]): Promise<TranslationResult[]> {
    const results: TranslationResult[] = [];
    const batchOperations: DOMOperation[] = [];

    // 预处理所有渲染操作为DOM操作
    for (const op of operations) {
      try {
        const translationElement = this.createTranslationElement(
          op.content,
          op.format || 'text',
          []
        );

        // 添加到批处理队列
        batchOperations.push({
          type: 'modify',
          element: op.element,
          data: {
            modify: (element: HTMLElement) => {
              if (op.mode === 'replace') {
                this.clearPreviousTranslation(element);
              }
              element.appendChild(translationElement);
              DOMUtils.markAsTranslated(element);
            }
          },
          priority: 1,
          id: `render-${op.element.tagName}-${Date.now()}`
        });

        results.push({
          success: true,
          element: op.element,
          originalText: op.element.textContent || '',
          translatedText: op.content,
          duration: 0 // 批量处理中单个操作时间设为0
        });

      } catch (error) {
        results.push({
          success: false,
          element: op.element,
          originalText: op.element.textContent || '',
          error: (error as Error).message,
          duration: 0
        });
      }
    }

    // 执行批量DOM操作 - 选择适当的处理器
    if (this.useParallelProcessing && this.parallelProcessor) {
      this.parallelProcessor.addOperations(batchOperations);
      await this.parallelProcessor.flush();
    } else if (this.domBatchProcessor) {
      this.domBatchProcessor.addOperations(batchOperations);
      await this.domBatchProcessor.flush();
    }

    return results;
  }

  /**
   * 清除翻译
   */
  clearTranslation(element: HTMLElement): boolean {
    try {
      DOMUtils.clearTranslationMarks(element);
      return true;
    } catch (error) {
      console.error('Failed to clear translation:', error);
      return false;
    }
  }

  /**
   * 清除所有翻译 - 优化版本使用批处理
   */
  clearAllTranslations(): number {
    const translatedElements = document.querySelectorAll('[data-lu-translated]');
    const batchOperations: DOMOperation[] = [];

    translatedElements.forEach((element, index) => {
      batchOperations.push({
        type: 'modify',
        element: element as HTMLElement,
        data: {
          modify: (el: HTMLElement) => {
            DOMUtils.clearTranslationMarks(el);
          }
        },
        priority: 0,
        id: `clear-${index}`
      });
    });

    // 批量清除 - 选择适当的处理器
    if (batchOperations.length > 0) {
      if (this.useParallelProcessing && this.parallelProcessor) {
        this.parallelProcessor.addOperations(batchOperations);
      } else if (this.domBatchProcessor) {
        this.domBatchProcessor.addOperations(batchOperations);
      }
      // 不等待完成，让批处理器自然处理
    }

    return batchOperations.length;
  }

  /**
   * 清除所有翻译 (立即执行版本)
   */
  async clearAllTranslationsImmediate(): Promise<number> {
    const translatedElements = document.querySelectorAll('[data-lu-translated]');
    const batchOperations: DOMOperation[] = [];

    translatedElements.forEach((element, index) => {
      batchOperations.push({
        type: 'modify',
        element: element as HTMLElement,
        data: {
          modify: (el: HTMLElement) => {
            DOMUtils.clearTranslationMarks(el);
          }
        },
        priority: 3, // 高优先级立即执行
        id: `clear-immediate-${index}`
      });
    });

    // 批量清除并等待完成 - 选择适当的处理器
    if (batchOperations.length > 0) {
      if (this.useParallelProcessing && this.parallelProcessor) {
        this.parallelProcessor.addOperations(batchOperations);
        await this.parallelProcessor.flush();
      } else if (this.domBatchProcessor) {
        this.domBatchProcessor.addOperations(batchOperations);
        await this.domBatchProcessor.flush();
      }
    }

    return batchOperations.length;
  }

  /**
   * 检查目标元素是否有效
   */
  private isValidTarget(element: HTMLElement): boolean {
    return element &&
      element.isConnected &&
      DOMUtils.isElementVisible(element);
  }

  /**
   * 清除之前的翻译
   */
  private clearPreviousTranslation(element: HTMLElement): void {
    const wrappers = element.querySelectorAll('.lu-wrapper');
    wrappers.forEach(wrapper => wrapper.remove());
    DOMUtils.clearTranslationMarks(element);
  }

  /**
   * 创建翻译元素
   */
  private createTranslationElement(
    translatedText: string,
    format: 'text' | 'html',
    links: LinkInfo[]
  ): HTMLElement {
    // 创建包装器
    const wrapper = DOMUtils.createElement('div', {
      'class': 'notranslate lu-wrapper',
      'lang': 'zh-CN'
    });

    // 创建翻译内容块
    const translationBlock = this.createContentBlock(translatedText, format, links);
    wrapper.appendChild(translationBlock);

    return wrapper;
  }

  /**
   * 创建内容块 - 安全版本，避免XSS攻击
   */
  private createContentBlock(
    content: string,
    format: 'text' | 'html',
    links: LinkInfo[]
  ): HTMLElement {
    const block = DOMUtils.createElement('font', {
      'class': 'notranslate lu-block lu-weak'
    });

    if (format === 'html' && this.config.preserveLinks && links.length > 0) {
      // 🔒 安全修复: 使用安全的DOM操作而不是innerHTML
      this.safelyRebuildLinksInDOM(block, content, links);
    } else {
      // 文本格式：直接设置文本内容 (安全)
      block.textContent = content;
    }

    return block;
  }

  /**
   * 🔒 安全的DOM链接重建 - 防止XSS攻击
   */
  private safelyRebuildLinksInDOM(container: HTMLElement, content: string, links: LinkInfo[]): void {
    try {
      // 首先设置纯文本内容
      container.textContent = content;

      // 如果没有链接，直接返回
      if (!links.length) {
        return;
      }

      // 逐个安全地创建链接元素
      links.forEach(link => {
        const { href, text, attributes } = link;

        // 安全检查URL
        if (!DOMUtils.isValidUrl(href) || !text.trim()) {
          return;
        }

        // 查找文本在容器中的位置
        const textContent = container.textContent || '';
        const linkIndex = textContent.indexOf(text);

        if (linkIndex === -1) {
          return; // 文本不存在，跳过
        }

        // 创建安全的链接元素
        const linkElement = this.createSafeLinkElement(href, text, attributes);

        // 安全地替换文本节点
        this.safelyReplaceTextWithLink(container, text, linkElement, linkIndex);
      });

    } catch (error) {
      // 安全回退：如果链接重建失败，保持纯文本
      console.warn('Safe link rebuilding failed, falling back to text:', error);
      container.textContent = content;
    }
  }

  /**
   * 创建安全的链接元素
   */
  private createSafeLinkElement(href: string, text: string, attributes: Record<string, string> = {}): HTMLAnchorElement {
    const link = document.createElement('a');

    // 设置安全的href
    link.href = href;
    link.textContent = text; // 使用textContent避免HTML注入

    // 安全属性
    link.target = '_blank';
    link.rel = 'noopener nofollow';

    // 过滤并设置安全属性
    const safeAttributes = this.filterSafeAttributes(attributes);
    Object.entries(safeAttributes).forEach(([key, value]) => {
      if (this.isSafeAttribute(key)) {
        link.setAttribute(key, value);
      }
    });

    return link;
  }

  /**
   * 安全地在文本节点中替换链接
   */
  private safelyReplaceTextWithLink(
    container: HTMLElement,
    text: string,
    linkElement: HTMLAnchorElement,
    textIndex: number
  ): void {
    const walker = document.createTreeWalker(
      container,
      NodeFilter.SHOW_TEXT,
      null
    );

    let currentIndex = 0;
    let textNode: Text | null = null;

    // 找到包含目标文本的文本节点
    while (walker.nextNode()) {
      const node = walker.currentNode as Text;
      const nodeText = node.textContent || '';

      if (currentIndex <= textIndex && textIndex < currentIndex + nodeText.length) {
        textNode = node;
        break;
      }
      currentIndex += nodeText.length;
    }

    if (!textNode) {
      return; // 未找到文本节点
    }

    const nodeText = textNode.textContent || '';
    const relativeIndex = textIndex - currentIndex;
    const textInNode = nodeText.substring(relativeIndex, relativeIndex + text.length);

    if (textInNode === text) {
      // 分割文本节点并插入链接
      const beforeText = nodeText.substring(0, relativeIndex);
      const afterText = nodeText.substring(relativeIndex + text.length);

      // 创建新的文本节点
      const beforeNode = beforeText ? document.createTextNode(beforeText) : null;
      const afterNode = afterText ? document.createTextNode(afterText) : null;

      // 替换原始节点
      const parent = textNode.parentNode;
      if (parent) {
        if (beforeNode) parent.insertBefore(beforeNode, textNode);
        parent.insertBefore(linkElement, textNode);
        if (afterNode) parent.insertBefore(afterNode, textNode);
        parent.removeChild(textNode);
      }
    }
  }

  /**
   * 检查属性是否安全
   */
  private isSafeAttribute(attributeName: string): boolean {
    const safeAttributes = new Set([
      'href', 'target', 'rel', 'title', 'class', 'id', 'data-*'
    ]);

    return safeAttributes.has(attributeName) || attributeName.startsWith('data-');
  }

  /**
   * 在内容中重建链接 (已弃用 - 保留向后兼容)
   * @deprecated 使用 safelyRebuildLinksInDOM 替代
   */
  private rebuildLinksInContent(content: string, links: LinkInfo[]): string {
    console.warn('rebuildLinksInContent is deprecated. Use safelyRebuildLinksInDOM instead.');

    let rebuiltContent = TextProcessor.escapeHtml(content);

    // 为每个链接尝试重建
    links.forEach(link => {
      const { href, text, attributes } = link;

      // 检查URL安全性
      if (!DOMUtils.isValidUrl(href)) {
        return;
      }

      // 创建安全的链接HTML
      const safeHref = TextProcessor.escapeHtml(href);
      const safeText = TextProcessor.escapeHtml(text);

      // 构建属性字符串
      const safeAttributes = {
        href: safeHref,
        target: '_blank',
        rel: 'noopener nofollow',
        ...this.filterSafeAttributes(attributes)
      };

      const attrString = Object.entries(safeAttributes)
        .map(([key, value]) => `${key}="${TextProcessor.escapeHtml(value)}"`)
        .join(' ');

      const linkHtml = `<a ${attrString}>${safeText}</a>`;

      // 尝试替换文本
      if (rebuiltContent.includes(safeText)) {
        rebuiltContent = rebuiltContent.replace(safeText, linkHtml);
      }
    });

    return rebuiltContent;
  }

  /**
   * 过滤安全的属性
   */
  private filterSafeAttributes(attributes: Record<string, string>): Record<string, string> {
    const safeAttrs: Record<string, string> = {};
    const allowedAttrs = ['title', 'id', 'class'];

    allowedAttrs.forEach(attr => {
      if (attributes[attr] && DOMUtils.isValidAttribute(attr, attributes[attr])) {
        safeAttrs[attr] = attributes[attr];
      }
    });

    return safeAttrs;
  }

  /**
   * 执行优化渲染操作 - 使用DOM批处理器
   */
  private async performOptimizedRender(
    target: HTMLElement,
    translationElement: HTMLElement,
    mode: 'append' | 'replace'
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const operation: DOMOperation = {
          type: 'modify',
          element: target,
          data: {
            modify: (element: HTMLElement) => {
              if (mode === 'replace') {
                this.clearPreviousTranslation(element);
              }
              element.appendChild(translationElement);

              // 验证渲染成功
              if (!translationElement.isConnected) {
                throw new Error('Failed to attach translation element');
              }
            }
          },
          priority: 2, // 单个渲染比批量渲染优先级稍高
          id: `single-render-${target.tagName}-${Date.now()}`
        };

        // 根据处理模式选择批处理器
        if (this.useParallelProcessing && this.parallelProcessor) {
          this.parallelProcessor.addOperation(operation);
          this.parallelProcessor.flush()
            .then(() => resolve())
            .catch(error => reject(error));
        } else if (this.domBatchProcessor) {
          this.domBatchProcessor.addOperation(operation);
          this.domBatchProcessor.flush()
            .then(() => resolve())
            .catch(error => reject(error));
        } else {
          reject(new Error('No batch processor available'));
        }

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 执行渲染操作 (保留原版本以便向后兼容)
   * @deprecated 使用 performOptimizedRender 替代
   */
  private async performRender(
    target: HTMLElement,
    translationElement: HTMLElement,
    mode: 'append' | 'replace'
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const operation = () => {
          if (mode === 'replace') {
            // 替换模式：清除现有内容后添加
            this.clearPreviousTranslation(target);
          }

          target.appendChild(translationElement);

          // 验证渲染成功
          if (!translationElement.isConnected) {
            throw new Error('Failed to attach translation element');
          }
        };

        // 使用批量DOM操作优化性能
        processDOMBatch([operation], { useRequestAnimationFrame: true })
          .then(() => resolve())
          .catch(error => reject(error));

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 按目标分组操作
   */
  private groupOperationsByTarget(operations: RenderOperation[]): RenderOperation[][] {
    const groups = new Map<HTMLElement, RenderOperation[]>();

    operations.forEach(op => {
      if (!groups.has(op.element)) {
        groups.set(op.element, []);
      }
      groups.get(op.element)!.push(op);
    });

    return Array.from(groups.values());
  }

  /**
   * 渲染分组
   */
  private async renderGroup(operations: RenderOperation[]): Promise<TranslationResult[]> {
    const results: TranslationResult[] = [];

    for (const op of operations) {
      const result = await this.render(
        op.element,
        op.content,
        op.element.textContent || '',
        {
          format: op.format,
          mode: op.mode
        }
      );
      results.push(result);
    }

    return results;
  }

  /**
   * 更新统计信息
   */
  private updateStats(duration: number, success: boolean): void {
    if (success) {
      this.stats.successfulRenders++;
    } else {
      this.stats.failedRenders++;
    }

    this.stats.totalTime += duration;
    this.stats.averageTime = this.stats.totalTime / this.stats.totalRenders;
  }

  /**
   * 获取渲染统计 - 包含批处理器统计
   */
  getStats(): typeof this.stats & {
    batchProcessor?: ReturnType<DOMBatchProcessor['getStats']>;
    parallelProcessor?: ReturnType<ParallelBatchProcessor['getStats']>;
    processingMode: 'single' | 'parallel';
  } {
    const baseStats = {
      ...this.stats,
      processingMode: this.useParallelProcessing ? 'parallel' as const : 'single' as const
    };

    if (this.useParallelProcessing && this.parallelProcessor) {
      return {
        ...baseStats,
        parallelProcessor: this.parallelProcessor.getStats()
      };
    } else if (this.domBatchProcessor) {
      return {
        ...baseStats,
        batchProcessor: this.domBatchProcessor.getStats()
      };
    } else {
      return baseStats;
    }
  }

  /**
   * 重置统计
   */
  resetStats(): void {
    this.stats = {
      totalRenders: 0,
      successfulRenders: 0,
      failedRenders: 0,
      averageTime: 0,
      totalTime: 0
    };

    if (this.useParallelProcessing && this.parallelProcessor) {
      this.parallelProcessor.resetStats();
    } else if (this.domBatchProcessor) {
      this.domBatchProcessor.resetStats();
    }
  }

  /**
   * 销毁渲染器和清理资源
   */
  destroy(): void {
    if (this.useParallelProcessing && this.parallelProcessor) {
      this.parallelProcessor.destroy();
    } else if (this.domBatchProcessor) {
      this.domBatchProcessor.destroy();
    }
    this.resetStats();
  }

  /**
   * 获取处理模式信息
   */
  getProcessingInfo(): {
    mode: 'single' | 'parallel';
    reason: string;
    workerCount?: number;
    strategy?: string;
  } {
    if (this.useParallelProcessing && this.parallelProcessor) {
      const parallelStats = this.parallelProcessor.getStats();
      return {
        mode: 'parallel',
        reason: '高性能设备，启用并行处理以提升性能',
        workerCount: parallelStats.totalProcessors,
        strategy: this.parallelProcessor.getStrategyInfo()
      };
    } else {
      return {
        mode: 'single',
        reason: '设备性能限制或调试模式，使用单线程处理确保稳定性'
      };
    }
  }
}