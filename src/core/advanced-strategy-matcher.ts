/**
 * 高级策略匹配引擎
 * 支持复杂的选择器匹配和条件组合
 */

import { AdvancedConfigParser, StrategyRule, StrategyCondition } from './advanced-config-parser';
import { UrlMatcher } from '../utils/url-matcher';
// debug 导入已移除，使用手动 console.log

export interface MatchResult {
  strategy: 'inline' | 'block' | 'skip' | 'clone_replace';
  rule: StrategyRule | null;
  matchedCondition?: StrategyCondition;
  reason?: string;
}

export class AdvancedStrategyMatcher {
  private configParser = new AdvancedConfigParser();
  private strategies: StrategyRule[] = [];
  private debug = true;

  constructor() {
    this.log('Advanced Strategy Matcher initialized');
  }

  /**
   * 加载配置
   */
  loadConfig(configData: any): void {
    try {
      this.strategies = this.configParser.parseConfig(configData);

      // 验证配置
      const validation = this.configParser.validateConfig(this.strategies);

      if (validation.warnings.length > 0) {
        validation.warnings.forEach(warning => console.log('⚠️ [translation|WARN] ' + `[配置警告] ${warning}`));
      }

      if (!validation.valid) {
        validation.errors.forEach(error => console.log('❌ [translation|ERROR] ' + `[配置错误] ${error}`));
        throw new Error('Configuration validation failed');
      }

      this.log(`成功加载 ${this.strategies.length} 个策略规则`);



    } catch (error) {
      console.log('❌ [translation|ERROR] ' + 'Failed to load config:', error);
      throw error;
    }
  }

  /**
   * 为元素匹配最佳策略 - 优化版本
   */
  matchStrategy(element: HTMLElement): MatchResult {
    const textContent = element.textContent?.trim() || '';
    const textLength = textContent.length;

    // 按优先级顺序检查策略（strategies已经按优先级排序）
    for (const rule of this.strategies) {
      const matchResult = this.matchesRule(element, rule, textLength);

      if (matchResult.matched) {
        const result = {
          strategy: rule.strategy,
          rule,
          matchedCondition: rule.conditions[matchResult.conditionIndex],
          reason: `Matched rule '${rule.id}'`
        };

        // 为排除规则添加详细日志
        if (rule.strategy === 'skip') {
          this.log(`🚫 SKIP规则匹配: ${rule.id}`, {
            element: `${element.tagName.toLowerCase()}${element.className ? '.' + element.className : ''}`,
            rule: rule.id,
            condition: rule.conditions[matchResult.conditionIndex],
            reason: result.reason
          });
        }

        return result;
      }
    }

    // 没有匹配任何规则，使用默认block策略
    return {
      strategy: 'block',
      rule: null,
      reason: 'Using default block strategy'
    };
  }

  /**
   * 检查元素是否匹配指定规则
   */
  private matchesRule(element: HTMLElement, rule: StrategyRule, textLength: number): { matched: boolean; conditionIndex: number } {
    // 规则必须匹配其中任意一个条件
    for (let i = 0; i < rule.conditions.length; i++) {
      const condition = rule.conditions[i];

      if (this.matchesCondition(element, condition, textLength)) {
        return { matched: true, conditionIndex: i };
      }
    }

    return { matched: false, conditionIndex: -1 };
  }

  /**
   * 检查元素是否匹配指定条件 - 优化版本
   */
  private matchesCondition(element: HTMLElement, condition: StrategyCondition, textLength: number): boolean {
    // 空条件（默认策略）- 快速返回
    if (Object.keys(condition).length === 0 ||
      (Object.keys(condition).length === 1 && condition.comment)) {
      return true;
    }

    // 0. 优先检查URL条件 - 只有在正确的页面上才需要执行其他检查
    if (condition.url && condition.url.length > 0) {
      const currentUrl = window.location.href;
      const hasMatchingUrl = UrlMatcher.matchesAny(currentUrl, condition.url);
      
      if (this.debug) {
        this.log(`🌐 URL条件检查`, {
          currentUrlDomain: this.sanitizeUrlForLogging(currentUrl),
          requiredUrls: condition.url,
          hasMatchingUrl,
          element: `${element.tagName.toLowerCase()}${element.className ? '.' + element.className : ''}`
        });
      }
      
      if (!hasMatchingUrl) {
        if (this.debug) {
          this.log(`❌ URL条件不匹配，跳过后续检查`, {
            currentUrlDomain: this.sanitizeUrlForLogging(currentUrl),
            requiredUrls: condition.url,
            element: `${element.tagName.toLowerCase()}${element.className ? '.' + element.className : ''}`
          });
        }
        return false;
      } else {
        if (this.debug) {
          this.log(`✅ URL条件匹配，继续检查其他条件`, {
            currentUrlDomain: this.sanitizeUrlForLogging(currentUrl),
            requiredUrls: condition.url,
            element: `${element.tagName.toLowerCase()}${element.className ? '.' + element.className : ''}`
          });
        }
      }
    }

    // 1. 优先检查嵌套选择器 - 这是最重要的过滤条件
    if (condition.nestedSelector) {
      const selectors = Array.isArray(condition.nestedSelector)
        ? condition.nestedSelector
        : [condition.nestedSelector];

      const hasMatchingSelector = selectors.some(selector =>
        this.matchesNestedSelector(element, selector)
      );

      if (this.debug) {
        this.log(`🔍 嵌套选择器匹配检查`, {
          element: `${element.tagName.toLowerCase()}${element.className ? '.' + element.className : ''}`,
          selectors,
          hasMatchingSelector,
          elementPath: this.getElementPath(element),
          textContent: element.textContent?.substring(0, 50) + '...'
        });
      }

      if (!hasMatchingSelector) {
        // 嵌套选择器不匹配，直接返回false，避免无意义的后续检查
        if (this.debug) {
          this.log(`❌ 嵌套选择器不匹配，跳过后续检查`, {
            element: `${element.tagName.toLowerCase()}${element.className ? '.' + element.className : ''}`,
            requiredSelectors: selectors
          });
        }
        return false;
      }
    }

    // 2. 检查文本长度（快速检查）
    if (condition.textLength) {
      const { min = 0, max = Infinity } = condition.textLength;
      if (textLength < min || textLength > max) {
        if (this.debug) {
          this.log(`❌ 文本长度不匹配: ${textLength}, 要求: ${min}-${max}`, {
            element: `${element.tagName.toLowerCase()}${element.className ? '.' + element.className : ''}`,
            textContent: element.textContent?.substring(0, 50) + '...'
          });
        }
        return false; // 快速失败
      }
    }

    // 3. 检查标签匹配（支持祖先元素检查）
    let tagMatched = true; // 默认为 true，只有明确不匹配时才为 false
    if (condition.tagName && condition.tagName.length > 0) {
      const elementTag = element.tagName.toLowerCase();

      // 首先检查当前元素
      const directMatch = condition.tagName.includes(elementTag);

      if (directMatch) {
        tagMatched = true;
      } else {
        // 对于排除性规则（如 header、footer 等），检查祖先元素
        // 这些标签通常用于排除其内部的所有子元素
        const exclusionTags = ['header', 'footer', 'nav', 'aside', 'code', 'pre', 'script', 'style'];
        const hasExclusionTag = condition.tagName.some(tag => exclusionTags.includes(tag));

        if (hasExclusionTag) {
          // 向上检查祖先元素是否包含排除标签
          let ancestor = element.parentElement;
          let depth = 0;
          const maxDepth = 10; // 限制搜索深度以提升性能

          if (this.debug) {
            this.log(`🔍 检查排除标签祖先: ${elementTag}`, {
              targetTags: condition.tagName,
              element: `${elementTag}${element.className ? '.' + element.className : ''}`
            });
          }

          let ancestorMatched = false;
          while (ancestor && depth < maxDepth) {
            const ancestorTag = ancestor.tagName.toLowerCase();
            if (condition.tagName.includes(ancestorTag)) {
              if (this.debug) {
                this.log(`✅ 找到排除祖先: ${ancestorTag} (深度: ${depth})`, {
                  element: `${elementTag}${element.className ? '.' + element.className : ''}`,
                  ancestor: `${ancestorTag}${ancestor.className ? '.' + ancestor.className : ''}`,
                  depth
                });
              }
              ancestorMatched = true;
              break;
            }
            ancestor = ancestor.parentElement;
            depth++;
          }

          if (this.debug && !ancestorMatched) {
            this.log(`❌ 未找到排除祖先 (搜索深度: ${depth})`, {
              element: `${elementTag}${element.className ? '.' + element.className : ''}`,
              targetTags: condition.tagName
            });
          }

          tagMatched = ancestorMatched;
        } else {
          // 对于非排除性规则，只检查当前元素
          tagMatched = false;
        }
      }
    }

    // 如果标签不匹配，直接返回 false
    if (!tagMatched) {
      return false;
    }

    // 4. 检查类名匹配
    if (condition.className && condition.className.length > 0) {
      const elementClasses = Array.from(element.classList);
      const hasMatchingClass = condition.className.some(reqClass =>
        elementClasses.some(elClass => elClass.includes(reqClass))
      );

      if (!hasMatchingClass) {
        return false;
      }
    }

    // 所有条件都匹配
    return true;
  }

  /**
   * 匹配嵌套选择器：支持 "a div", "button span" 等 - 优化版本
   */
  private matchesNestedSelector(element: HTMLElement, selector: string): boolean {
    try {
      const cleanSelector = selector.trim();
      if (!cleanSelector) return false;

      // 处理空格分隔的选择器
      const parts = cleanSelector.split(/\s+/).filter(part => part.length > 0);

      if (parts.length === 1) {
        // 简单选择器
        return element.matches(parts[0]);
      }

      if (parts.length === 2) {
        // 父子选择器 "a div" - 最常用的情况
        const [parentSelector, childSelector] = parts;

        // 快速检查：当前元素是否匹配子选择器
        if (!element.matches(childSelector)) {
          return false;
        }

        // 向上遍历查找匹配的父元素（限制深度提升性能）
        let parent = element.parentElement;
        let depth = 0;

        while (parent && depth < 8) { // 减少搜索深度
          if (parent.matches(parentSelector)) {
            return true;
          }
          parent = parent.parentElement;
          depth++;
        }

        return false;
      }

      if (parts.length === 3) {
        // 三级选择器 "nav li a"
        const [grandParentSelector, parentSelector, childSelector] = parts;

        if (!element.matches(childSelector)) {
          return false;
        }

        // 查找匹配的父元素和祖父元素
        let parent = element.parentElement;
        let depth = 0;

        while (parent && depth < 6) { // 限制深度
          if (parent.matches(parentSelector)) {
            // 找到匹配的父元素，查找祖父元素
            let grandParent = parent.parentElement;
            let grandDepth = 0;

            while (grandParent && grandDepth < 4) { // 进一步限制深度
              if (grandParent.matches(grandParentSelector)) {
                return true;
              }
              grandParent = grandParent.parentElement;
              grandDepth++;
            }
          }
          parent = parent.parentElement;
          depth++;
        }

        return false;
      }

      if (parts.length === 4) {
        // 四级选择器 "article div div span" - 使用优化的closest查找
        return this.matchesFourLevelSelector(element, parts);
      }

      // 不支持更复杂的选择器
      return false;

    } catch (error) {
      // 静默失败，避免控制台噪音
      return false;
    }
  }

  /**
   * 获取策略统计信息
   */
  getStrategyStats(): {
    totalRules: number;
    byStrategy: Record<string, number>;
    byPriority: Array<{ priority: number; rules: string[] }>;
    hasDefault: boolean;
  } {
    const byStrategy: Record<string, number> = {};
    const priorities = new Map<number, string[]>();

    this.strategies.forEach(rule => {
      // 按策略统计
      byStrategy[rule.strategy] = (byStrategy[rule.strategy] || 0) + 1;

      // 按优先级统计
      const existing = priorities.get(rule.priority) || [];
      existing.push(rule.id);
      priorities.set(rule.priority, existing);
    });

    const byPriority = Array.from(priorities.entries())
      .map(([priority, rules]) => ({ priority, rules }))
      .sort((a, b) => b.priority - a.priority);

    // 检查是否有默认策略
    const hasDefault = this.strategies.some(rule =>
      rule.conditions.length === 0 ||
      rule.conditions.every(c => Object.keys(c).length === 0 || (Object.keys(c).length === 1 && c.comment))
    );

    return {
      totalRules: this.strategies.length,
      byStrategy,
      byPriority,
      hasDefault
    };
  }


  /**
   * 调试日志
   */
  private log(message: string, ...args: any[]): void {
    if (this.debug) {
      console.log('🔧 [translation|DEBUG] ' + message, ...args);
    }
  }

  /**
   * 设置调试模式
   */
  setDebug(enabled: boolean): void {
    this.debug = enabled;
    this.configParser.setDebug(enabled);
  }

  /**
   * 获取当前加载的策略规则
   */
  getStrategies(): StrategyRule[] {
    return [...this.strategies];
  }

  /**
   * 清除所有策略
   */
  clearStrategies(): void {
    this.strategies = [];
    this.log('已清除所有策略规则');
  }

  /**
   * 获取元素的DOM路径用于调试
   * @private
   */
  private getElementPath(element: HTMLElement): string {
    const path = [];
    let current = element;
    
    while (current && current.tagName) {
      let selector = current.tagName.toLowerCase();
      if (current.className) {
        selector += '.' + current.className.split(' ').join('.');
      }
      path.unshift(selector);
      current = current.parentElement as HTMLElement;
      
      // 限制路径长度避免过长
      if (path.length > 5) break;
    }
    
    return path.join(' > ');
  }

  /**
   * 脱敏URL用于日志输出 - 增强安全性
   * @private
   */
  private sanitizeUrlForLogging(url: string): string {
    try {
      const urlObj = new URL(url);
      // 只显示协议和域名顶级部分，完全隐藏子域名、路径、查询参数和哈希
      const domainParts = urlObj.hostname.split('.');
      const topLevelDomain = domainParts.length >= 2 
        ? domainParts.slice(-2).join('.') 
        : urlObj.hostname;
      return `${urlObj.protocol}//${topLevelDomain}/***`;
    } catch {
      // 完全脱敏无效URL
      return '***';
    }
  }

  /**
   * 优化的四级选择器匹配 - 使用closest避免三重循环
   * @private
   */
  private matchesFourLevelSelector(element: HTMLElement, parts: string[]): boolean {
    const [greatGrandParentSelector, grandParentSelector, parentSelector, childSelector] = parts;
    
    // 首先检查当前元素是否匹配子选择器
    if (!element.matches(childSelector)) {
      return false;
    }

    try {
      // 🔧 修复：向上逐级查找，避免closest匹配到自己
      
      // 查找直接父元素匹配 parentSelector
      let parentEl = element.parentElement;
      while (parentEl && !parentEl.matches(parentSelector)) {
        parentEl = parentEl.parentElement;
      }
      if (!parentEl) {
        return false;
      }

      // 查找祖父元素匹配 grandParentSelector
      let grandParentEl = parentEl.parentElement;
      while (grandParentEl && !grandParentEl.matches(grandParentSelector)) {
        grandParentEl = grandParentEl.parentElement;
      }
      if (!grandParentEl) {
        return false;
      }

      // 查找曾祖父元素匹配 greatGrandParentSelector
      let greatGrandParentEl = grandParentEl.parentElement;
      while (greatGrandParentEl && !greatGrandParentEl.matches(greatGrandParentSelector)) {
        greatGrandParentEl = greatGrandParentEl.parentElement;
      }
      if (!greatGrandParentEl) {
        return false;
      }

      // 验证层级关系：确保找到的元素确实是按照预期的层级结构
      return this.validateHierarchy(element, parentEl, grandParentEl, greatGrandParentEl);
      
    } catch (error) {
      if (this.debug) {
        this.log('四级选择器匹配出错', { 
          error: (error as Error).message,
          parts 
        });
      }
      return false;
    }
  }

  /**
   * 验证DOM层级关系
   * @private
   */
  private validateHierarchy(
    child: HTMLElement, 
    parent: HTMLElement, 
    grandParent: HTMLElement, 
    greatGrandParent: HTMLElement
  ): boolean {
    // 确保层级关系正确：child < parent < grandParent < greatGrandParent
    return (
      parent.contains(child) &&
      grandParent.contains(parent) && 
      greatGrandParent.contains(grandParent) &&
      child !== parent &&
      parent !== grandParent &&
      grandParent !== greatGrandParent
    );
  }
}