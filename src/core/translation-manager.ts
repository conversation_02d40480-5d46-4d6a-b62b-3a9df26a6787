/**
 * 翻译管理器 - 轻量化版本
 * 整合翻译编排逻辑，避免过度拆分
 */

import { 
  TranslationOptions,
  TranslationResult,
  BatchTranslationResult,
  TextExtraction,
  TranslationConfig,
  TranslationStats,
  TranslationErrorType,
  TranslationError
} from '../types';
import { SecurityChecker } from './security-checker';
import { DOMRenderer } from './dom-renderer';
import { SimpleCache, CacheKeyGenerator } from '../utils/cache';
import { TextProcessor } from '../utils/text-utils';
import { TextFilter } from '../utils/text-filter';
import { DOMUtils } from '../utils/dom-utils';
import { processBatch, withRetry } from '../utils/batch';
import { getMockTranslator } from '../features/translate/mock-translate-service-refactored';
import { ErrorBoundary, ResourceManager } from '../utils/error-boundary';
import { getGlobalRequestPool, type RequestPool } from '../utils/request-pool';

export class TranslationManager {
  private security: SecurityChecker;
  private renderer: DOMRenderer;
  private cache: SimpleCache;
  private config: TranslationConfig;
  private requestPool: RequestPool;
  
  // 🔒 错误边界和资源管理
  private errorBoundary: ErrorBoundary;
  private resourceManager: ResourceManager;
  
  // 统计信息
  private stats: TranslationStats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    cacheHitRate: 0,
    securityBlocked: 0
  };

  private totalTime = 0;

  constructor(config: TranslationConfig) {
    this.config = config;
    this.security = new SecurityChecker(config.security);
    this.renderer = new DOMRenderer(config.rendering);
    this.cache = new SimpleCache(config.cache.maxSize, config.cache.ttl);
    this.requestPool = getGlobalRequestPool();
    
    // 🔒 初始化错误边界和资源管理
    this.errorBoundary = new ErrorBoundary({
      name: 'TranslationManager',
      logErrors: config.debug,
      onError: (error, context) => {
        this.stats.failedRequests++;
        if (config.debug) {
          console.error(`翻译管理器错误 [${context}]:`, error);
        }
      },
      cleanup: () => this.cleanupResources(),
      maxRetries: 2,
      retryDelay: 500
    });

    this.resourceManager = new ResourceManager();
    
    // 注册缓存清理
    this.resourceManager.register('cache', () => {
      if (this.cache && typeof this.cache.clear === 'function') {
        this.cache.clear();
      }
    });

    // 注册请求池清理
    this.resourceManager.register('requestPool', () => {
      if (this.requestPool && typeof this.requestPool.clearAllRequests === 'function') {
        this.requestPool.clearAllRequests();
      }
    });
  }

  /**
   * 翻译单个元素 - 使用错误边界保护
   */
  async translateElement(
    element: HTMLElement, 
    options: TranslationOptions = {}
  ): Promise<TranslationResult> {
    // 🔒 使用错误边界安全执行
    const result = await this.errorBoundary.safeExecute(
      () => this.performTranslateElement(element, options),
      `translateElement:${element.tagName}`
    );

    if (!result.success) {
      return {
        success: false,
        element,
        originalText: element.textContent || '',
        error: result.error?.message || 'Translation failed',
        duration: 0
      };
    }

    return result.result!;
  }

  /**
   * 执行单个元素翻译的具体逻辑
   */
  private async performTranslateElement(
    element: HTMLElement, 
    options: TranslationOptions = {}
  ): Promise<TranslationResult> {
    const startTime = performance.now();
    this.stats.totalRequests++;

    try {
      // 1. 提取文本
      const extraction = TextProcessor.extractText(element);
      
      const lenientMode = options.securityLevel === 'lenient';
      if (!extraction.text || !TextFilter.isTranslatable(extraction.text, lenientMode)) {
        throw new TranslationError(
          TranslationErrorType.VALIDATION_FAILED,
          'Text is not translatable'
        );
      }

      // 2. 安全检查
      const format = options.format || 'text';
      if (!this.security.validateContent(extraction.text, format)) {
        this.stats.securityBlocked++;
        throw this.security.createSecurityError('Content blocked by security check');
      }

      // 3. 缓存检查
      const cacheKey = CacheKeyGenerator.forText(extraction.text, { format });
      let translatedText = this.config.cache.enabled ? this.cache.get(cacheKey) : null;
      
      if (translatedText) {
        // 缓存命中，直接渲染
        const result = await this.renderer.render(
          element, 
          translatedText, 
          extraction.text,
          {
            format,
            mode: options.renderMode || this.config.rendering.defaultMode,
            links: extraction.links
          }
        );

        this.updateStats(performance.now() - startTime, true, true);
        return result;
      }

      // 4. 执行翻译
      translatedText = await this.performTranslation(extraction.text, options);

      // 5. 缓存结果
      if (this.config.cache.enabled) {
        this.cache.set(cacheKey, translatedText);
      }

      // 6. 渲染到DOM
      const result = await this.renderer.render(
        element, 
        translatedText, 
        extraction.text,
        {
          format,
          mode: options.renderMode || this.config.rendering.defaultMode,
          links: extraction.links
        }
      );

      this.updateStats(performance.now() - startTime, true, false);
      return result;

    } catch (error) {
      this.updateStats(performance.now() - startTime, false, false);
      
      return {
        success: false,
        element,
        originalText: element.textContent || '',
        error: (error as Error).message,
        duration: performance.now() - startTime
      };
    }
  }

  /**
   * 批量翻译元素
   */
  async translateElements(
    elements: HTMLElement[], 
    options: TranslationOptions = {}
  ): Promise<BatchTranslationResult> {
    const startTime = performance.now();
    
    if (!elements || elements.length === 0) {
      return {
        totalCount: 0,
        successCount: 0,
        failureCount: 0,
        results: [],
        totalDuration: 0
      };
    }

    // 1. 批量提取文本
    const extractions = this.extractTextsFromElements(elements, options);
    
    // 2. 安全过滤
    const safeExtractions = this.filterSafeExtractions(extractions, options.format || 'text');
    
    // 3. 缓存检查
    const { cached, uncached } = await this.separateCachedTranslations(safeExtractions, options);
    
    // 4. 批量翻译未缓存的
    const newTranslations = await this.translateBatch(
      uncached.map(e => e.text), 
      options
    );

    // 5. 合并结果
    const allTranslations = this.mergeTranslationResults(cached, uncached, newTranslations);
    
    // 6. 批量渲染
    const results = await this.renderBatchTranslations(allTranslations, options);

    const totalDuration = performance.now() - startTime;
    
    return {
      totalCount: elements.length,
      successCount: results.filter(r => r.success).length,
      failureCount: results.filter(r => !r.success).length,
      results,
      totalDuration
    };
  }

  /**
   * 自动翻译页面
   */
  async translatePage(options: TranslationOptions = {}): Promise<BatchTranslationResult> {
    // 查找可翻译的元素
    const elements = DOMUtils.findTranslatableElements();
    
    // 过滤已翻译的元素
    const untranslatedElements = elements.filter(el => !DOMUtils.isTranslated(el));
    
    this.log(`Found ${untranslatedElements.length} translatable elements`);
    
    return this.translateElements(untranslatedElements, options);
  }

  /**
   * 清除页面翻译
   */
  clearPageTranslations(): number {
    const clearedCount = this.renderer.clearAllTranslations();
    this.log(`Cleared ${clearedCount} translations`);
    return clearedCount;
  }

  /**
   * 提取元素文本
   */
  private extractTextsFromElements(elements: HTMLElement[], options: TranslationOptions = {}): TextExtraction[] {
    const lenientMode = options.securityLevel === 'lenient';
    return elements
      .map(element => {
        try {
          return TextProcessor.extractText(element);
        } catch (error) {
          this.log(`Failed to extract text from element: ${error}`);
          return null;
        }
      })
      .filter((extraction): extraction is TextExtraction => 
        extraction !== null && 
        TextFilter.isTranslatable(extraction.text, lenientMode)
      );
  }

  /**
   * 过滤安全的提取结果
   */
  private filterSafeExtractions(
    extractions: TextExtraction[], 
    format: 'text' | 'html'
  ): TextExtraction[] {
    return extractions.filter(extraction => {
      const isValid = this.security.validateContent(extraction.text, format);
      if (!isValid) {
        this.stats.securityBlocked++;
      }
      return isValid;
    });
  }

  /**
   * 分离缓存和未缓存的翻译
   */
  private async separateCachedTranslations(
    extractions: TextExtraction[],
    options: TranslationOptions
  ): Promise<{
    cached: Array<{ extraction: TextExtraction; translation: string }>;
    uncached: TextExtraction[];
  }> {
    const cached: Array<{ extraction: TextExtraction; translation: string }> = [];
    const uncached: TextExtraction[] = [];

    if (!this.config.cache.enabled) {
      return { cached, uncached: extractions };
    }

    extractions.forEach(extraction => {
      const cacheKey = CacheKeyGenerator.forText(extraction.text, { 
        format: options.format || 'text' 
      });
      const cachedTranslation = this.cache.get(cacheKey);
      
      if (cachedTranslation) {
        cached.push({ extraction, translation: cachedTranslation });
      } else {
        uncached.push(extraction);
      }
    });

    return { cached, uncached };
  }

  /**
   * 批量翻译文本
   */
  private async translateBatch(texts: string[], options: TranslationOptions): Promise<string[]> {
    if (texts.length === 0) {
      return [];
    }

    // 使用批处理和重试机制
    return processBatch(
      texts,
      async (text: string) => withRetry(
        () => this.performTranslation(text, options),
        {
          maxRetries: 3,
          baseDelay: 1000,
          shouldRetry: (error) => {
            // 只重试网络错误，不重试安全错误
            return !error.message.includes('security');
          }
        }
      ),
      {
        batchSize: this.config.performance.batchSize,
        concurrency: this.config.performance.concurrency
      }
    );
  }

  /**
   * 执行单个翻译 - 使用请求池优化
   */
  private async performTranslation(text: string, options: TranslationOptions): Promise<string> {
    const requestKey = {
      method: 'translate',
      params: JSON.stringify({
        text,
        format: options.format || 'text',
        renderMode: options.renderMode || 'append'
      })
    };

    try {
      // 使用请求池执行翻译，自动处理去重和并发控制
      const result = await this.requestPool.execute(requestKey, async () => {
        // 这里使用Mock翻译服务，实际项目中可以替换为真实的翻译服务
        const mockTranslator = getMockTranslator();

        const translationResult = await mockTranslator.translateText(text, { to: 'zh' });
        
        // 如果返回的是对象，提取文本；如果是字符串，直接返回
        return typeof translationResult === 'string' ? translationResult : translationResult.translations?.[0] || text;
      });

      return result;
    } catch (error) {
      throw new TranslationError(
        TranslationErrorType.TRANSLATION_FAILED,
        `Translation failed: ${(error as Error).message}`
      );
    }
  }

  /**
   * 合并翻译结果
   */
  private mergeTranslationResults(
    cached: Array<{ extraction: TextExtraction; translation: string }>,
    uncached: TextExtraction[],
    newTranslations: string[]
  ): Array<{ extraction: TextExtraction; translation: string }> {
    const allResults = [...cached];
    
    uncached.forEach((extraction, index) => {
      if (index < newTranslations.length) {
        allResults.push({
          extraction,
          translation: newTranslations[index]
        });

        // 缓存新翻译
        if (this.config.cache.enabled) {
          const cacheKey = CacheKeyGenerator.forText(extraction.text);
          this.cache.set(cacheKey, newTranslations[index]);
        }
      }
    });

    return allResults;
  }

  /**
   * 批量渲染翻译
   */
  private async renderBatchTranslations(
    translations: Array<{ extraction: TextExtraction; translation: string }>,
    options: TranslationOptions
  ): Promise<TranslationResult[]> {
    const renderPromises = translations.map(({ extraction, translation }) =>
      this.renderer.render(
        extraction.element,
        translation,
        extraction.text,
        {
          format: options.format || 'text',
          mode: options.renderMode || this.config.rendering.defaultMode,
          links: extraction.links
        }
      )
    );

    return Promise.all(renderPromises);
  }

  /**
   * 更新统计信息
   */
  private updateStats(duration: number, success: boolean, fromCache: boolean): void {
    if (success) {
      this.stats.successfulRequests++;
    } else {
      this.stats.failedRequests++;
    }

    this.totalTime += duration;
    this.stats.averageResponseTime = this.totalTime / this.stats.totalRequests;

    // 更新缓存命中率
    if (fromCache) {
      this.stats.cacheHitRate = (this.stats.cacheHitRate * (this.stats.totalRequests - 1) + 1) / this.stats.totalRequests;
    } else {
      this.stats.cacheHitRate = (this.stats.cacheHitRate * (this.stats.totalRequests - 1)) / this.stats.totalRequests;
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): TranslationStats & {
    cacheStats: ReturnType<SimpleCache['getStats']>;
    rendererStats: ReturnType<DOMRenderer['getStats']>;
    securityStats: ReturnType<SecurityChecker['getStats']>;
    requestPoolStats: ReturnType<RequestPool['getStats']>;
  } {
    return {
      ...this.stats,
      cacheStats: this.cache.getStats(),
      rendererStats: this.renderer.getStats(),
      securityStats: this.security.getStats(),
      requestPoolStats: this.requestPool.getStats()
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      securityBlocked: 0
    };
    this.totalTime = 0;
    this.renderer.resetStats();
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * 调试日志
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      console.log(`[TranslationManager] ${message}`, data || '');
    }
  }

  /**
   * 🔒 清理资源 - 防止内存泄漏
   */
  private cleanupResources(): void {
    try {
      // 清理缓存
      if (this.cache && typeof this.cache.clear === 'function') {
        this.cache.clear();
      }

      // 重置统计信息
      this.resetStats();

      this.log('资源清理完成');
    } catch (error) {
      console.error('清理翻译管理器资源时发生错误:', error);
    }
  }

  /**
   * 🔒 销毁翻译管理器
   */
  destroy(): void {
    if (this.errorBoundary && !this.errorBoundary.destroyed) {
      this.errorBoundary.destroy();
    }

    if (this.resourceManager) {
      this.resourceManager.destroy();
    }

    this.log('翻译管理器已销毁');
  }

  /**
   * 📊 获取资源使用情况
   */
  getResourceStats(): {
    translation: ReturnType<TranslationManager['getStats']>;
    resources: ReturnType<ResourceManager['getStats']>;
    errorBoundary: { destroyed: boolean };
  } {
    return {
      translation: this.getStats(),
      resources: this.resourceManager?.getStats() || { resources: 0, timers: 0, intervals: 0, eventListeners: 0 },
      errorBoundary: { destroyed: this.errorBoundary?.destroyed || true }
    };
  }
}