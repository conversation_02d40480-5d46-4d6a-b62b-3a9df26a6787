/**
 * 懒加载优化管理器
 * 使用IntersectionObserver实现大页面性能优化
 */

// debug 导入已移除，使用手动 console.log

/**
 * 懒加载配置
 */
export interface LazyLoadConfig {
  /** 根边距，扩大检测区域 */
  rootMargin?: string;
  /** 可见比例阈值 */
  threshold?: number | number[];
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 批处理大小 */
  batchSize?: number;
  /** 处理延迟（毫秒） */
  processingDelay?: number;
  /** 调试模式 */
  debug?: boolean;
  /** 🚀 批量处理累积延迟（毫秒）- 收集多个元素后等待更多元素的时间 */
  batchAccumulationDelay?: number;
  /** 🚀 是否启用批量处理模式 */
  enableBatchProcessing?: boolean;
}

/**
 * 懒加载项目
 */
export interface LazyLoadItem {
  /** 唯一标识 */
  id: string;
  /** 目标元素 */
  element: HTMLElement;
  /** 处理函数 */
  handler: (element: HTMLElement) => Promise<void>;
  /** 优先级 */
  priority?: number;
  /** 是否只处理一次 */
  once?: boolean;
  /** 元数据 */
  metadata?: Record<string, any>;
}

/**
 * 批量懒加载项目 - 支持批量回调的优化版本
 */
export interface BatchLazyLoadItem {
  /** 唯一标识 */
  id: string;
  /** 目标元素 */
  element: HTMLElement;
  /** 优先级 */
  priority?: number;
  /** 是否只处理一次 */
  once?: boolean;
  /** 元数据 */
  metadata?: Record<string, any>;
}

/**
 * 批量处理回调函数
 */
export type BatchProcessHandler = (items: BatchLazyLoadItem[]) => Promise<void>;

/**
 * 可见性变化事件
 */
export interface VisibilityChangeEvent {
  /** 项目ID */
  id: string;
  /** 目标元素 */
  element: HTMLElement;
  /** 是否可见 */
  isVisible: boolean;
  /** 可见比例 */
  intersectionRatio: number;
  /** 边界矩形 */
  boundingRect: DOMRectReadOnly;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 懒加载统计信息
 */
export interface LazyLoadStats {
  /** 观察中的元素数量 */
  observedElements: number;
  /** 已处理的元素数量 */
  processedElements: number;
  /** 当前可见的元素数量 */
  visibleElements: number;
  /** 处理成功数量 */
  successfulProcessing: number;
  /** 处理失败数量 */
  failedProcessing: number;
  /** 平均处理时间 */
  averageProcessingTime: number;
  /** 内存使用估算 */
  memoryUsage: number;
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
  /** 观察器创建时间 */
  observerCreationTime: number;
  /** 最后更新时间 */
  lastUpdateTime: number;
  /** 总处理时间 */
  totalProcessingTime: number;
  /** 处理的元素数量 */
  processedCount: number;
  /** FPS监控 */
  averageFPS: number;
  /** 内存监控 */
  memoryStats: {
    used: number;
    total: number;
    limit: number;
  };
}

/**
 * 懒加载管理器
 */
export class LazyLoadManager {
  private observer: IntersectionObserver | null = null;
  private config: Required<LazyLoadConfig>;
  private items = new Map<string, LazyLoadItem>();
  private batchItems = new Map<string, BatchLazyLoadItem>(); // 🚀 新增：批量处理项目
  private visibleItems = new Set<string>();
  private processingQueue: LazyLoadItem[] = [];
  private batchProcessingQueue: BatchLazyLoadItem[] = []; // 🚀 新增：批量处理队列
  private isProcessing = false;
  private isBatchProcessing = false; // 🚀 新增：批量处理状态
  private stats: LazyLoadStats;
  private performanceMetrics: PerformanceMetrics;
  private totalProcessingTime = 0;
  private rafId?: number;
  private batchTimeoutId?: number; // 🚀 新增：批量处理超时ID
  private lastTriggerTime = new Map<string, number>(); // 防抖：记录每个元素的最后触发时间
  
  // 🚀 新增：批量处理回调
  private batchProcessHandler?: BatchProcessHandler;

  constructor(config: LazyLoadConfig = {}) {
    this.config = {
      rootMargin: '50px',
      threshold: 0.1, // 🔧 简化：只在10%可见时触发，避免频繁事件
      enablePerformanceMonitoring: true,
      batchSize: 5,
      processingDelay: 16, // ~60fps
      debug: false,
      // 🚀 新增批量处理配置
      batchAccumulationDelay: 150, // 150ms累积延迟，收集更多可见元素
      enableBatchProcessing: true, // 默认启用批量处理
      ...config
    };

    this.stats = this.initializeStats();
    this.performanceMetrics = this.initializePerformanceMetrics();

    this.createObserver();
    
    if (this.config.enablePerformanceMonitoring) {
      this.startPerformanceMonitoring();
    }

    this.log('LazyLoadManager initialized', { config: this.config });
  }

  /**
   * 🚀 设置批量处理回调
   */
  setBatchProcessHandler(handler: BatchProcessHandler): void {
    this.batchProcessHandler = handler;
    this.log('Batch process handler set', { 
      enabled: !!handler, 
      batchProcessingEnabled: this.config.enableBatchProcessing 
    });
  }

  /**
   * 🚀 批量添加懒加载项目 - 支持批量回调的优化版本
   */
  observeBatch(items: BatchLazyLoadItem[]): void {
    if (!this.config.enableBatchProcessing || !this.batchProcessHandler) {
      // 回退到传统单个处理方式
      items.forEach(item => {
        this.observe({
          ...item,
          handler: async () => {
            // 空处理器，因为我们没有批量处理回调
            this.log(`No batch handler available for item ${item.id}`);
          }
        });
      });
      return;
    }

    items.forEach(item => {
      if (this.batchItems.has(item.id)) {
        this.log(`Batch item ${item.id} already being observed`);
        return;
      }

      this.batchItems.set(item.id, {
        priority: 1,
        once: true,
        ...item
      });

      if (this.observer) {
        this.observer.observe(item.element);
        this.log(`Started observing batch item ${item.id}`, { element: item.element.tagName });
      }
    });

    this.updateStats();
  }

  /**
   * 添加懒加载项目
   */
  observe(item: LazyLoadItem): void {
    if (this.items.has(item.id)) {
      this.log(`Item ${item.id} already being observed`);
      return;
    }

    this.items.set(item.id, {
      priority: 1,
      once: true,
      ...item
    });

    if (this.observer) {
      this.observer.observe(item.element);
      this.updateStats();
      this.log(`Started observing item ${item.id}`, { element: item.element.tagName });
    }
  }

  /**
   * 批量添加项目
   */
  observeMultiple(items: LazyLoadItem[]): void {
    items.forEach(item => this.observe(item));
  }

  /**
   * 停止观察项目
   */
  unobserve(id: string): void {
    const item = this.items.get(id);
    if (!item) return;

    if (this.observer) {
      this.observer.unobserve(item.element);
    }

    this.items.delete(id);
    this.visibleItems.delete(id);
    this.updateStats();
    this.log(`Stopped observing item ${id}`);
  }

  /**
   * 停止观察所有项目
   */
  unobserveAll(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
    this.items.clear();
    this.visibleItems.clear();
    this.processingQueue = [];
    this.updateStats();
    this.log('Stopped observing all items');
  }

  /**
   * 强制处理可见元素
   */
  async processVisible(): Promise<void> {
    const visibleItems = Array.from(this.visibleItems)
      .map(id => this.items.get(id))
      .filter((item): item is LazyLoadItem => item !== undefined);

    await this.processBatch(visibleItems);
  }

  /**
   * 获取统计信息
   */
  getStats(): LazyLoadStats {
    return {
      ...this.stats,
      observedElements: this.items.size,
      visibleElements: this.visibleItems.size,
      averageProcessingTime: this.stats.processedElements > 0 
        ? this.totalProcessingTime / this.stats.processedElements 
        : 0
    };
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): PerformanceMetrics {
    if (this.config.enablePerformanceMonitoring) {
      this.updateMemoryStats();
    }
    return { ...this.performanceMetrics };
  }

  /**
   * 清理资源
   */
  destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }

    // 🚀 清理批量处理超时
    if (this.batchTimeoutId) {
      clearTimeout(this.batchTimeoutId);
      this.batchTimeoutId = undefined;
    }

    // 清理RAF
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = undefined;
    }

    this.items.clear();
    this.batchItems.clear(); // 🚀 清理批量处理项目
    this.visibleItems.clear();
    this.processingQueue = [];
    this.batchProcessingQueue = []; // 🚀 清理批量处理队列
    this.lastTriggerTime.clear(); // 🔧 修复：清理防抖时间记录
    this.isProcessing = false;
    this.isBatchProcessing = false; // 🚀 重置批量处理状态
    this.batchProcessHandler = undefined; // 🚀 清理批量处理回调

    this.log('LazyLoadManager destroyed');
  }

  /**
   * 创建IntersectionObserver
   */
  private createObserver(): void {
    if (typeof IntersectionObserver === 'undefined') {
      console.warn('IntersectionObserver not supported');
      return;
    }

    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      {
        rootMargin: this.config.rootMargin,
        threshold: this.config.threshold
      }
    );

    this.performanceMetrics.observerCreationTime = performance.now();
    this.log('IntersectionObserver created');
  }

  /**
   * 处理可见性变化 - 🚀 优化版本：支持批量处理累积
   */
  private handleIntersection(entries: IntersectionObserverEntry[]): void {
    const startTime = performance.now();
    const visibilityEvents: VisibilityChangeEvent[] = [];
    let batchItemsVisible: BatchLazyLoadItem[] = [];

    for (const entry of entries) {
      // 首先检查是否是批量处理项目
      const batchItem = this.findBatchItemByElement(entry.target as HTMLElement);
      if (batchItem) {
        const isVisible = entry.isIntersecting;
        const wasVisible = this.visibleItems.has(batchItem.id);
        const now = Date.now();
        const lastTrigger = this.lastTriggerTime.get(batchItem.id) || 0;
        
        // 🔧 防抖：避免300ms内重复触发同一元素
        if (now - lastTrigger < 300) {
          continue;
        }

        // 更新可见性状态
        if (isVisible && !wasVisible) {
          this.visibleItems.add(batchItem.id);
          this.lastTriggerTime.set(batchItem.id, now);
          batchItemsVisible.push(batchItem);
        } else if (!isVisible && wasVisible) {
          this.visibleItems.delete(batchItem.id);
          this.lastTriggerTime.set(batchItem.id, now);
        }

        // 创建可见性事件
        visibilityEvents.push({
          id: batchItem.id,
          element: batchItem.element,
          isVisible,
          intersectionRatio: entry.intersectionRatio,
          boundingRect: entry.boundingClientRect,
          timestamp: entry.time || Date.now()
        });

        this.log(`Batch visibility changed for ${batchItem.id}`, { 
          isVisible, 
          ratio: entry.intersectionRatio.toFixed(2) 
        });
        continue;
      }

      // 传统单个处理项目
      const item = this.findItemByElement(entry.target as HTMLElement);
      if (!item) continue;

      const isVisible = entry.isIntersecting;
      const wasVisible = this.visibleItems.has(item.id);
      const now = Date.now();
      const lastTrigger = this.lastTriggerTime.get(item.id) || 0;
      
      // 🔧 防抖：避免300ms内重复触发同一元素
      if (now - lastTrigger < 300) {
        continue;
      }

      // 更新可见性状态
      if (isVisible && !wasVisible) {
        this.visibleItems.add(item.id);
        this.lastTriggerTime.set(item.id, now);
        this.queueForProcessing(item);
      } else if (!isVisible && wasVisible) {
        this.visibleItems.delete(item.id);
        this.lastTriggerTime.set(item.id, now);
      }

      // 创建可见性事件
      visibilityEvents.push({
        id: item.id,
        element: item.element,
        isVisible,
        intersectionRatio: entry.intersectionRatio,
        boundingRect: entry.boundingClientRect,
        timestamp: entry.time || Date.now()
      });

      this.log(`Visibility changed for ${item.id}`, { 
        isVisible, 
        ratio: entry.intersectionRatio.toFixed(2) 
      });
    }

    // 🚀 批量处理优化：累积可见的批量项目
    if (batchItemsVisible.length > 0) {
      this.scheduleBatchAccumulation(batchItemsVisible);
    }

    // 触发传统批处理
    if (this.processingQueue.length > 0) {
      this.scheduleBatchProcessing();
    }

    // 更新性能指标
    const duration = performance.now() - startTime;
    this.performanceMetrics.totalProcessingTime += duration;
    this.performanceMetrics.lastUpdateTime = performance.now();

    this.updateStats();
  }

  /**
   * 根据元素查找项目
   */
  private findItemByElement(element: HTMLElement): LazyLoadItem | undefined {
    for (const item of this.items.values()) {
      if (item.element === element) {
        return item;
      }
    }
    return undefined;
  }

  /**
   * 🚀 根据元素查找批量处理项目
   */
  private findBatchItemByElement(element: HTMLElement): BatchLazyLoadItem | undefined {
    for (const item of this.batchItems.values()) {
      if (item.element === element) {
        return item;
      }
    }
    return undefined;
  }

  /**
   * 添加到处理队列
   */
  private queueForProcessing(item: LazyLoadItem): void {
    if (!this.processingQueue.find(queued => queued.id === item.id)) {
      this.processingQueue.push(item);
      
      // 按优先级排序
      this.processingQueue.sort((a, b) => (b.priority || 0) - (a.priority || 0));
    }
  }

  /**
   * 调度批处理
   */
  private scheduleBatchProcessing(): void {
    if (this.isProcessing || this.processingQueue.length === 0) {
      return;
    }

    // 使用requestAnimationFrame优化性能
    this.rafId = requestAnimationFrame(() => {
      this.processBatchFromQueue();
    });
  }

  /**
   * 🚀 调度批量累积处理 - 核心优化：收集多个可见元素后统一处理
   */
  private scheduleBatchAccumulation(newBatchItems: BatchLazyLoadItem[]): void {
    // 将新发现的可见元素加入队列
    this.batchProcessingQueue.push(...newBatchItems);

    // 按优先级排序
    this.batchProcessingQueue.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    // 清除之前的超时
    if (this.batchTimeoutId) {
      clearTimeout(this.batchTimeoutId);
    }

    // 设置累积延迟，等待更多元素变为可见
    this.batchTimeoutId = setTimeout(() => {
      this.executeBatchAccumulation();
    }, this.config.batchAccumulationDelay) as any;

    this.log(`🚀 Scheduled batch accumulation`, {
      newItems: newBatchItems.length,
      queueSize: this.batchProcessingQueue.length,
      delay: `${this.config.batchAccumulationDelay}ms`
    });
  }

  /**
   * 🚀 执行批量累积处理 - 这里是真正的性能优化点
   */
  private async executeBatchAccumulation(): Promise<void> {
    // 🔧 关键修复：清理超时ID防止内存泄漏
    if (this.batchTimeoutId) {
      clearTimeout(this.batchTimeoutId);
      this.batchTimeoutId = undefined;
    }

    if (this.isBatchProcessing || this.batchProcessingQueue.length === 0 || !this.batchProcessHandler) {
      return;
    }

    this.isBatchProcessing = true;
    const batchToProcess = [...this.batchProcessingQueue];
    this.batchProcessingQueue = []; // 清空队列

    const startTime = performance.now();
    
    this.log(`🚀 Executing batch accumulation`, {
      itemCount: batchToProcess.length,
      items: batchToProcess.map(item => ({
        id: item.id,
        element: item.element.tagName,
        priority: item.priority
      }))
    });

    try {
      // 🎯 关键优化：一次性处理所有累积的可见元素
      await this.batchProcessHandler(batchToProcess);

      // 统计成功处理
      this.stats.successfulProcessing += batchToProcess.length;
      this.stats.processedElements += batchToProcess.length;

      // 🔧 修复：清理已处理的元素
      batchToProcess.forEach(item => {
        if (item.once) {
          this.unobserveBatchItem(item.id);
        }
      });

      const duration = performance.now() - startTime;
      this.totalProcessingTime += duration;

      this.log(`✅ Batch accumulation completed`, {
        duration: `${duration.toFixed(2)}ms`,
        processedItems: batchToProcess.length,
        averagePerItem: `${(duration / batchToProcess.length).toFixed(2)}ms`,
        throughput: `${(batchToProcess.length * 1000 / duration).toFixed(1)} items/sec`
      });

    } catch (error) {
      // 🔧 修复：即使处理失败也要清理已处理的元素，防止内存泄漏
      batchToProcess.forEach(item => {
        if (item.once) {
          this.unobserveBatchItem(item.id);
        }
      });

      // 处理失败统计
      this.stats.failedProcessing += batchToProcess.length;
      this.log(`❌ Batch accumulation failed`, { 
        error: (error as Error).message,
        itemCount: batchToProcess.length 
      });

      // 🚀 改进：尝试分批处理作为回退机制
      if (batchToProcess.length > 1) {
        this.log(`🔄 Attempting fallback processing in smaller batches`);
        await this.attemptFallbackProcessing(batchToProcess);
      } else {
        // 如果是单个元素失败，直接抛出错误
        throw error;
      }
    }

    this.isBatchProcessing = false;
  }

  /**
   * 🚀 回退处理机制 - 当批量处理失败时尝试分批处理
   */
  private async attemptFallbackProcessing(failedItems: BatchLazyLoadItem[]): Promise<void> {
    const chunkSize = Math.max(1, Math.floor(failedItems.length / 2));
    const chunks: BatchLazyLoadItem[][] = [];
    
    for (let i = 0; i < failedItems.length; i += chunkSize) {
      chunks.push(failedItems.slice(i, i + chunkSize));
    }

    this.log(`🔄 Fallback processing with ${chunks.length} chunks of size ~${chunkSize}`);

    for (const chunk of chunks) {
      try {
        if (this.batchProcessHandler) {
          await this.batchProcessHandler(chunk);
          this.log(`✅ Fallback chunk processed successfully`, { size: chunk.length });
        }
      } catch (chunkError) {
        this.log(`❌ Fallback chunk failed`, { 
          size: chunk.length,
          error: (chunkError as Error).message 
        });
        // 最后的回退：记录失败但不阻止其他chunks处理
      }
    }
  }

  /**
   * 🚀 停止观察批量处理项目
   */
  private unobserveBatchItem(id: string): void {
    const item = this.batchItems.get(id);
    if (!item) return;

    if (this.observer) {
      this.observer.unobserve(item.element);
    }

    this.batchItems.delete(id);
    this.visibleItems.delete(id);
    this.updateStats();
    this.log(`Stopped observing batch item ${id}`);
  }

  /**
   * 从队列处理批次
   */
  private async processBatchFromQueue(): Promise<void> {
    if (this.isProcessing) return;

    this.isProcessing = true;
    const batch = this.processingQueue.splice(0, this.config.batchSize);
    
    if (batch.length > 0) {
      await this.processBatch(batch);
    }

    this.isProcessing = false;

    // 如果还有待处理项目，继续处理
    if (this.processingQueue.length > 0) {
      setTimeout(() => {
        this.scheduleBatchProcessing();
      }, this.config.processingDelay);
    }
  }

  /**
   * 批处理项目
   */
  private async processBatch(items: LazyLoadItem[]): Promise<void> {
    const startTime = performance.now();
    this.log(`Processing batch of ${items.length} items`);

    const results = await Promise.allSettled(
      items.map(item => this.processItem(item))
    );

    // 统计结果
    results.forEach((result, index) => {
      const item = items[index];
      
      if (result.status === 'fulfilled') {
        this.stats.successfulProcessing++;
        
        // 如果只处理一次，停止观察
        if (item.once) {
          this.unobserve(item.id);
        }
      } else {
        this.stats.failedProcessing++;
        this.log(`Processing failed for item ${item.id}:`, result.reason);
      }
    });

    const duration = performance.now() - startTime;
    this.totalProcessingTime += duration;
    this.stats.processedElements += items.length;

    this.log(`Batch processing completed`, { 
      duration: `${duration.toFixed(2)}ms`,
      items: items.length 
    });
  }

  /**
   * 处理单个项目
   */
  private async processItem(item: LazyLoadItem): Promise<void> {
    const startTime = performance.now();
    
    try {
      await item.handler(item.element);
      
      const duration = performance.now() - startTime;
      this.log(`Item ${item.id} processed successfully`, { 
        duration: `${duration.toFixed(2)}ms` 
      });
    } catch (error) {
      throw new Error(`Failed to process item ${item.id}: ${error}`);
    }
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring(): void {
    let lastTime = performance.now();
    let frameCount = 0;

    const monitorFrame = (currentTime: number) => {
      frameCount++;
      
      if (currentTime - lastTime >= 1000) {
        this.performanceMetrics.averageFPS = frameCount;
        frameCount = 0;
        lastTime = currentTime;
        
        this.updateMemoryStats();
      }

      if (this.config.enablePerformanceMonitoring) {
        requestAnimationFrame(monitorFrame);
      }
    };

    requestAnimationFrame(monitorFrame);
  }

  /**
   * 更新内存统计
   */
  private updateMemoryStats(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.performanceMetrics.memoryStats = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      };
      
      // 简单的内存使用估算
      this.stats.memoryUsage = memory.usedJSHeapSize / (1024 * 1024); // MB
    }
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): LazyLoadStats {
    return {
      observedElements: 0,
      processedElements: 0,
      visibleElements: 0,
      successfulProcessing: 0,
      failedProcessing: 0,
      averageProcessingTime: 0,
      memoryUsage: 0
    };
  }

  /**
   * 初始化性能指标
   */
  private initializePerformanceMetrics(): PerformanceMetrics {
    return {
      observerCreationTime: 0,
      lastUpdateTime: 0,
      totalProcessingTime: 0,
      processedCount: 0,
      averageFPS: 0,
      memoryStats: {
        used: 0,
        total: 0,
        limit: 0
      }
    };
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats.observedElements = this.items.size;
    this.stats.visibleElements = this.visibleItems.size;
  }

  /**
   * 调试日志
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      console.log('🔧 [debug|DEBUG] ' + message, data || '');
    }
  }
}

/**
 * 全局懒加载管理器实例
 */
let globalLazyLoader: LazyLoadManager | null = null;

/**
 * 获取全局懒加载管理器
 */
export function getLazyLoadManager(config?: LazyLoadConfig): LazyLoadManager {
  if (!globalLazyLoader) {
    globalLazyLoader = new LazyLoadManager(config);
  }
  return globalLazyLoader;
}

/**
 * 设置全局懒加载管理器
 */
export function setLazyLoadManager(manager: LazyLoadManager): void {
  if (globalLazyLoader) {
    globalLazyLoader.destroy();
  }
  globalLazyLoader = manager;
}

/**
 * 便捷的懒加载函数
 */
export function lazyLoad(
  elements: HTMLElement | HTMLElement[],
  handler: (element: HTMLElement) => Promise<void>,
  options: {
    priority?: number;
    once?: boolean;
    rootMargin?: string;
    threshold?: number | number[];
  } = {}
): void {
  const manager = getLazyLoadManager({
    rootMargin: options.rootMargin,
    threshold: options.threshold
  });

  const elementsArray = Array.isArray(elements) ? elements : [elements];

  elementsArray.forEach((element, index) => {
    manager.observe({
      id: `lazy-${Date.now()}-${index}`,
      element,
      handler,
      priority: options.priority,
      once: options.once
    });
  });
}