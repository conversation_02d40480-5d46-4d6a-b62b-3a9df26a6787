/**
 * DOM批处理器
 * 优化DOM操作性能，减少重排和重绘
 */

export interface DOMOperation {
  /** 操作类型 */
  type: 'create' | 'modify' | 'remove' | 'style' | 'attribute';
  /** 目标元素 */
  element: HTMLElement;
  /** 操作数据 */
  data: any;
  /** 优先级 */
  priority?: number;
  /** 操作标识 */
  id?: string;
}

export interface BatchProcessorOptions {
  /** 批处理大小 */
  batchSize?: number;
  /** 延迟时间 (ms) */
  debounceTime?: number;
  /** 最大等待时间 (ms) */
  maxWaitTime?: number;
  /** 是否启用性能监控 */
  enableProfiling?: boolean;
}

export class DOMBatchProcessor {
  private pendingOperations: DOMOperation[] = [];
  private isProcessing = false;
  private debounceTimer: number | null = null;
  private maxWaitTimer: number | null = null;
  private options: Required<BatchProcessorOptions>;
  
  // 性能统计
  private stats = {
    totalBatches: 0,
    totalOperations: 0,
    averageBatchSize: 0,
    averageProcessingTime: 0,
    totalProcessingTime: 0
  };

  constructor(options: BatchProcessorOptions = {}) {
    this.options = {
      batchSize: 50,
      debounceTime: 16, // ~60fps
      maxWaitTime: 100,
      enableProfiling: false,
      ...options
    };
  }

  /**
   * 添加DOM操作到批处理队列
   */
  addOperation(operation: DOMOperation): void {
    // 去重：如果有相同元素的相同类型操作，替换旧的
    const existingIndex = this.pendingOperations.findIndex(
      op => op.element === operation.element && 
            op.type === operation.type && 
            op.id === operation.id
    );

    if (existingIndex !== -1) {
      this.pendingOperations[existingIndex] = operation;
    } else {
      this.pendingOperations.push(operation);
    }

    this.scheduleProcessing();
  }

  /**
   * 批量添加操作
   */
  addOperations(operations: DOMOperation[]): void {
    operations.forEach(op => this.addOperation(op));
  }

  /**
   * 安排批处理执行
   */
  private scheduleProcessing(): void {
    // 清除现有的防抖定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // 如果队列已满，立即处理
    if (this.pendingOperations.length >= this.options.batchSize) {
      this.processBatch();
      return;
    }

    // 设置防抖定时器
    this.debounceTimer = window.setTimeout(() => {
      this.processBatch();
    }, this.options.debounceTime);

    // 设置最大等待定时器（如果还没有的话）
    if (!this.maxWaitTimer && this.pendingOperations.length > 0) {
      this.maxWaitTimer = window.setTimeout(() => {
        this.processBatch();
      }, this.options.maxWaitTime);
    }
  }

  /**
   * 处理批次
   */
  private async processBatch(): Promise<void> {
    if (this.isProcessing || this.pendingOperations.length === 0) {
      return;
    }

    this.isProcessing = true;
    
    // 清除定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
    if (this.maxWaitTimer) {
      clearTimeout(this.maxWaitTimer);
      this.maxWaitTimer = null;
    }

    const startTime = performance.now();
    const operations = this.pendingOperations.splice(0);
    
    try {
      await this.executeBatchOperations(operations);
    } catch (error) {
      console.error('DOM批处理执行失败:', error);
    } finally {
      const processingTime = performance.now() - startTime;
      this.updateStats(operations.length, processingTime);
      this.isProcessing = false;

      // 如果还有待处理的操作，继续处理
      if (this.pendingOperations.length > 0) {
        this.scheduleProcessing();
      }
    }
  }

  /**
   * 执行批次操作
   */
  private async executeBatchOperations(operations: DOMOperation[]): Promise<void> {
    if (operations.length === 0) return;

    // 按优先级和类型排序
    const sortedOperations = this.sortOperations(operations);

    // 使用requestAnimationFrame确保在正确的渲染时机执行
    return new Promise<void>((resolve) => {
      requestAnimationFrame(() => {
        // 开始批处理 - 减少重排重绘
        this.startBatchMode();

        try {
          // 分组执行操作
          this.executeOperationGroups(sortedOperations);
        } finally {
          // 结束批处理
          this.endBatchMode();
          resolve();
        }
      });
    });
  }

  /**
   * 排序操作以优化性能
   */
  private sortOperations(operations: DOMOperation[]): DOMOperation[] {
    return operations.sort((a, b) => {
      // 1. 按优先级排序
      const priorityA = a.priority || 0;
      const priorityB = b.priority || 0;
      if (priorityA !== priorityB) {
        return priorityB - priorityA; // 高优先级在前
      }

      // 2. 按操作类型排序：create -> modify -> style -> attribute -> remove
      const typeOrder = { create: 0, modify: 1, style: 2, attribute: 3, remove: 4 };
      const orderA = typeOrder[a.type] || 999;
      const orderB = typeOrder[b.type] || 999;
      return orderA - orderB;
    });
  }

  /**
   * 开始批处理模式
   */
  private startBatchMode(): void {
    // 使用 CSS containment 优化
    document.body.style.contain = 'layout style paint';
  }

  /**
   * 结束批处理模式
   */
  private endBatchMode(): void {
    // 恢复正常模式
    document.body.style.contain = '';
  }

  /**
   * 执行操作组
   */
  private executeOperationGroups(operations: DOMOperation[]): void {
    // 按类型分组
    const groups = this.groupOperationsByType(operations);

    // 按顺序执行各组
    if (groups.create.length > 0) this.executeCreateOperations(groups.create);
    if (groups.modify.length > 0) this.executeModifyOperations(groups.modify);
    if (groups.style.length > 0) this.executeStyleOperations(groups.style);
    if (groups.attribute.length > 0) this.executeAttributeOperations(groups.attribute);
    if (groups.remove.length > 0) this.executeRemoveOperations(groups.remove);
  }

  /**
   * 按类型分组操作
   */
  private groupOperationsByType(operations: DOMOperation[]) {
    const groups = {
      create: [] as DOMOperation[],
      modify: [] as DOMOperation[],
      style: [] as DOMOperation[],
      attribute: [] as DOMOperation[],
      remove: [] as DOMOperation[]
    };

    operations.forEach(op => {
      if (groups[op.type]) {
        groups[op.type].push(op);
      }
    });

    return groups;
  }

  /**
   * 执行创建操作
   */
  private executeCreateOperations(operations: DOMOperation[]): void {
    const fragment = document.createDocumentFragment();
    
    operations.forEach(op => {
      try {
        if (op.data.createElement) {
          const element = op.data.createElement();
          if (element) {
            fragment.appendChild(element);
          }
        }
      } catch (error) {
        console.error('创建元素失败:', error, op);
      }
    });

    // 一次性添加所有元素
    if (fragment.children.length > 0) {
      document.body.appendChild(fragment);
    }
  }

  /**
   * 执行修改操作
   */
  private executeModifyOperations(operations: DOMOperation[]): void {
    operations.forEach(op => {
      try {
        if (op.element && op.data.modify) {
          op.data.modify(op.element);
        }
      } catch (error) {
        console.error('修改元素失败:', error, op);
      }
    });
  }

  /**
   * 执行样式操作
   */
  private executeStyleOperations(operations: DOMOperation[]): void {
    operations.forEach(op => {
      try {
        if (op.element && op.data.styles) {
          Object.assign(op.element.style, op.data.styles);
        }
      } catch (error) {
        console.error('设置样式失败:', error, op);
      }
    });
  }

  /**
   * 执行属性操作
   */
  private executeAttributeOperations(operations: DOMOperation[]): void {
    operations.forEach(op => {
      try {
        if (op.element && op.data.attributes) {
          Object.entries(op.data.attributes).forEach(([key, value]) => {
            op.element.setAttribute(key, String(value));
          });
        }
      } catch (error) {
        console.error('设置属性失败:', error, op);
      }
    });
  }

  /**
   * 执行删除操作
   */
  private executeRemoveOperations(operations: DOMOperation[]): void {
    operations.forEach(op => {
      try {
        if (op.element && op.element.parentNode) {
          op.element.parentNode.removeChild(op.element);
        }
      } catch (error) {
        console.error('删除元素失败:', error, op);
      }
    });
  }

  /**
   * 立即执行所有待处理操作
   */
  async flush(): Promise<void> {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
    if (this.maxWaitTimer) {
      clearTimeout(this.maxWaitTimer);
      this.maxWaitTimer = null;
    }

    await this.processBatch();
  }

  /**
   * 清空队列
   */
  clear(): void {
    this.pendingOperations = [];
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
    if (this.maxWaitTimer) {
      clearTimeout(this.maxWaitTimer);
      this.maxWaitTimer = null;
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(operationCount: number, processingTime: number): void {
    this.stats.totalBatches++;
    this.stats.totalOperations += operationCount;
    this.stats.totalProcessingTime += processingTime;
    
    this.stats.averageBatchSize = this.stats.totalOperations / this.stats.totalBatches;
    this.stats.averageProcessingTime = this.stats.totalProcessingTime / this.stats.totalBatches;

    if (this.options.enableProfiling) {
      console.log(`DOM批处理完成: ${operationCount}个操作, ${processingTime.toFixed(2)}ms`);
    }
  }

  /**
   * 获取性能统计
   */
  getStats() {
    return {
      ...this.stats,
      pendingOperations: this.pendingOperations.length,
      isProcessing: this.isProcessing
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalBatches: 0,
      totalOperations: 0,
      averageBatchSize: 0,
      averageProcessingTime: 0,
      totalProcessingTime: 0
    };
  }

  /**
   * 销毁批处理器
   */
  destroy(): void {
    this.clear();
    this.resetStats();
  }
}