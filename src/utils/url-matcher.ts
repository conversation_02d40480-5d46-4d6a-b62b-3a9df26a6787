/**
 * URL 匹配工具类
 * 支持通配符匹配、完整URL匹配、相对路径匹配等
 */
export class UrlMatcher {
  /**
   * 判断当前URL是否匹配指定的模式
   * @param currentUrl 当前页面URL
   * @param pattern 匹配模式
   * @returns 是否匹配
   */
  static matches(currentUrl: string, pattern: string): boolean {
    if (!currentUrl || !pattern) {
      return false;
    }

    // 完整URL匹配
    if (pattern === currentUrl) {
      return true;
    }

    // 通配符匹配
    if (pattern.includes('*')) {
      return this.matchesWildcard(currentUrl, pattern);
    }

    // 相对路径匹配
    if (pattern.startsWith('/')) {
      try {
        const url = new URL(currentUrl);
        return url.pathname.includes(pattern) || url.pathname.startsWith(pattern);
      } catch (error) {
        console.warn('Invalid URL for relative path matching:', currentUrl);
        return false;
      }
    }

    // 域名匹配
    if (!pattern.includes('/') && !pattern.includes('*')) {
      try {
        const url = new URL(currentUrl);
        return url.hostname.includes(pattern);
      } catch (error) {
        console.warn('Invalid URL for domain matching:', currentUrl);
        return false;
      }
    }

    return false;
  }

  /**
   * 通配符匹配
   * 支持 * 作为任意字符匹配
   * @param url 要匹配的URL
   * @param pattern 包含通配符的模式
   * @returns 是否匹配
   */
  private static matchesWildcard(url: string, pattern: string): boolean {
    try {
      // 安全检查：防止ReDoS攻击
      if (pattern.length > 256) {
        console.warn('URL pattern too long, potential security risk:', pattern.length);
        return false;
      }

      // 限制通配符数量，防止复杂度攻击
      const wildcardCount = (pattern.match(/\*/g) || []).length;
      if (wildcardCount > 5) {
        console.warn('Too many wildcards in pattern, potential ReDoS risk:', wildcardCount);
        return false;
      }

      // 安全的通配符匹配实现
      // 将 * 替换为任意字符匹配，其他字符需要精确匹配
      
      // 先转义所有正则特殊字符（包括 *）
      let regexPattern = pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      
      // 然后将转义后的 \* 替换为 .*?（非贪婪匹配，更安全）
      regexPattern = regexPattern.replace(/\\\*/g, '.*?');
      
      const regex = new RegExp(`^${regexPattern}$`, 'i');
      const result = regex.test(url);
      
      // 调试输出（脱敏处理）
      if (this.debug) {
        const safeUrl = this.sanitizeUrlForLogging(url);
        const safePattern = pattern.length > 50 ? pattern.substring(0, 50) + '...' : pattern;
        console.log(`URL匹配调试: ${safeUrl} vs ${safePattern}`);
        console.log(`  生成的正则: ^${regexPattern}$`);
        console.log(`  匹配结果: ${result}`);
      }
      
      return result;
    } catch (error) {
      console.warn('URL pattern matching error:', error);
      return false;
    }
  }

  /**
   * 脱敏URL用于日志输出
   * @private
   */
  private static sanitizeUrlForLogging(url: string): string {
    try {
      const urlObj = new URL(url);
      // 只显示协议、域名和路径，隐藏查询参数和哈希
      return `${urlObj.protocol}//${urlObj.hostname}${urlObj.pathname}`;
    } catch {
      // 如果不是有效URL，只显示前50个字符
      return url.length > 50 ? url.substring(0, 50) + '...' : url;
    }
  }

  // 调试开关
  private static debug = false;

  /**
   * 批量匹配URL模式
   * @param currentUrl 当前URL
   * @param patterns URL模式数组
   * @returns 是否有任一模式匹配
   */
  static matchesAny(currentUrl: string, patterns: string[]): boolean {
    if (!patterns || patterns.length === 0) {
      return true; // 如果没有URL条件，则认为匹配
    }

    return patterns.some(pattern => this.matches(currentUrl, pattern));
  }

  /**
   * 提取当前页面的URL信息用于调试
   * @returns URL信息对象
   */
  static getCurrentUrlInfo(): {
    href: string;
    hostname: string;
    pathname: string;
    search: string;
    hash: string;
  } {
    const url = new URL(window.location.href);
    return {
      href: url.href,
      hostname: url.hostname,
      pathname: url.pathname,
      search: url.search,
      hash: url.hash
    };
  }
}