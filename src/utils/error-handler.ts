/**
 * 统一错误处理工具
 * 为不同模块提供标准化的错误处理策略
 */

// debug 导入已移除，使用手动 console.log

// 错误类型定义
export enum ErrorCategory {
  NETWORK = 'network',
  STORAGE = 'storage', 
  RUNTIME = 'runtime',
  VALIDATION = 'validation',
  UI = 'ui',
  API = 'api',
  TRANSLATION = 'translation'
}

// 错误严重级别
export enum ErrorSeverity {
  LOW = 'low',        // 不影响核心功能
  MEDIUM = 'medium',  // 影响部分功能
  HIGH = 'high',      // 影响核心功能
  CRITICAL = 'critical' // 系统无法正常工作
}

// 标准化错误接口
export interface StandardError {
  category: ErrorCategory;
  severity: ErrorSeverity;
  message: string;
  context?: any;
  originalError?: Error;
  timestamp: number;
  module: string;
}

// 错误处理器基类
export class ErrorHandler {
  private module: string;
  // debugLogger 属性已移除，使用手动 console.log

  constructor(module: string) {
    this.module = module;
    // getDebugLogger 调用已移除，使用手动 console.log
  }

  // getDebugLogger 方法已移除，使用手动 console.log
  private getModulePrefix(module: string): string {
    switch (module) {
      case 'background': return 'background-service';
      case 'content': return 'content-script';
      case 'ui': return 'ui-manager';
      case 'slider': return 'slider-manager';
      case 'tooltip': return 'tooltip-manager';
      case 'translation': return 'translate-service';
      default: return 'error-handler';
    }
  }

  /**
   * 处理错误的主方法
   */
  handle(
    error: Error | string,
    category: ErrorCategory,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: any
  ): StandardError {
    const standardError: StandardError = {
      category,
      severity,
      message: typeof error === 'string' ? error : error.message,
      context,
      originalError: typeof error === 'string' ? undefined : error,
      timestamp: Date.now(),
      module: this.module
    };

    // 根据严重级别选择日志方法
    this.logError(standardError);

    // 根据错误类型执行特定处理
    this.processError(standardError);

    return standardError;
  }

  /**
   * 记录错误日志
   */
  private logError(error: StandardError): void {
    const logMessage = `[${error.category}] ${error.message}`;
    const prefix = this.getModulePrefix(this.module);
    
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        console.log(`❌ [${prefix}|ERROR] ${logMessage}`, {
          context: error.context,
          originalError: error.originalError
        });
        break;
      case ErrorSeverity.MEDIUM:
        console.log(`⚠️ [${prefix}|WARN] ${logMessage}`, error.context);
        break;
      case ErrorSeverity.LOW:
        console.log(`✅ [${prefix}|INFO] ${logMessage}`, error.context);
        break;
    }
  }

  /**
   * 根据错误类型执行特定处理逻辑
   */
  private processError(error: StandardError): void {
    switch (error.category) {
      case ErrorCategory.STORAGE:
        this.handleStorageError(error);
        break;
      case ErrorCategory.NETWORK:
        this.handleNetworkError(error);
        break;
      case ErrorCategory.VALIDATION:
        this.handleValidationError(error);
        break;
      case ErrorCategory.TRANSLATION:
        this.handleTranslationError(error);
        break;
      // 其他错误类型的默认处理
      default:
        break;
    }
  }

  /**
   * 存储错误处理
   */
  private handleStorageError(error: StandardError): void {
    // 存储错误通常需要回退到其他存储方案
    const prefix = this.getModulePrefix(this.module);
    console.log(`🔧 [${prefix}|DEBUG] Storage error detected, may need fallback strategy`);
  }

  /**
   * 网络错误处理  
   */
  private handleNetworkError(error: StandardError): void {
    // 网络错误可能需要重试或缓存处理
    const prefix = this.getModulePrefix(this.module);
    console.log(`🔧 [${prefix}|DEBUG] Network error detected, consider retry strategy`);
  }

  /**
   * 验证错误处理
   */
  private handleValidationError(error: StandardError): void {
    // 验证错误通常需要用户交互反馈
    const prefix = this.getModulePrefix(this.module);
    console.log(`🔧 [${prefix}|DEBUG] Validation error detected, user feedback may be needed`);
  }

  /**
   * 翻译错误处理
   */
  private handleTranslationError(error: StandardError): void {
    // 翻译错误可能需要回退策略或用户通知
    const prefix = this.getModulePrefix(this.module);
    console.log(`🔧 [${prefix}|DEBUG] Translation error detected, may need fallback or user notification`);
  }

  /**
   * 创建网络错误的快捷方法
   */
  network(error: Error | string, context?: any, severity: ErrorSeverity = ErrorSeverity.MEDIUM): StandardError {
    return this.handle(error, ErrorCategory.NETWORK, severity, context);
  }

  /**
   * 创建存储错误的快捷方法
   */
  storage(error: Error | string, context?: any, severity: ErrorSeverity = ErrorSeverity.MEDIUM): StandardError {
    return this.handle(error, ErrorCategory.STORAGE, severity, context);
  }

  /**
   * 创建运行时错误的快捷方法
   */
  runtime(error: Error | string, context?: any, severity: ErrorSeverity = ErrorSeverity.HIGH): StandardError {
    return this.handle(error, ErrorCategory.RUNTIME, severity, context);
  }

  /**
   * 创建验证错误的快捷方法
   */
  validation(error: Error | string, context?: any, severity: ErrorSeverity = ErrorSeverity.LOW): StandardError {
    return this.handle(error, ErrorCategory.VALIDATION, severity, context);
  }

  /**
   * 创建UI错误的快捷方法
   */
  ui(error: Error | string, context?: any, severity: ErrorSeverity = ErrorSeverity.MEDIUM): StandardError {
    return this.handle(error, ErrorCategory.UI, severity, context);
  }

  /**
   * 创建API错误的快捷方法
   */
  api(error: Error | string, context?: any, severity: ErrorSeverity = ErrorSeverity.MEDIUM): StandardError {
    return this.handle(error, ErrorCategory.API, severity, context);
  }

  /**
   * 创建翻译错误的快捷方法
   */
  translation(error: Error | string, context?: any, severity: ErrorSeverity = ErrorSeverity.MEDIUM): StandardError {
    return this.handle(error, ErrorCategory.TRANSLATION, severity, context);
  }
}

// 为不同模块导出预配置的错误处理器
export const backgroundErrorHandler = new ErrorHandler('background');
export const contentErrorHandler = new ErrorHandler('content');
export const uiErrorHandler = new ErrorHandler('ui');
export const sliderErrorHandler = new ErrorHandler('slider');
export const tooltipErrorHandler = new ErrorHandler('tooltip');
export const translationErrorHandler = new ErrorHandler('translation');

// 通用错误处理器
export const globalErrorHandler = new ErrorHandler('global');

/**
 * 翻译服务专用错误类型
 */
export class TranslationError extends Error {
  public readonly originalText: string;
  public readonly targetLang: string;
  public readonly cause?: Error;

  constructor(message: string, options: {
    originalText: string;
    targetLang: string;
    cause?: Error;
  }) {
    super(message);
    this.name = 'TranslationError';
    this.originalText = options.originalText;
    this.targetLang = options.targetLang;
    this.cause = options.cause;
  }
}

export class NetworkError extends Error {
  constructor(message: string, public readonly cause?: Error) {
    super(message);
    this.name = 'NetworkError';
  }
}

export class RateLimitError extends Error {
  constructor(message: string, public readonly retryAfter?: number) {
    super(message);
    this.name = 'RateLimitError';
  }
}