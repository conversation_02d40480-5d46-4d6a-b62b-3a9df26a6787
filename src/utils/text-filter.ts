/**
 * 文本过滤工具
 * 提供纯函数式的文本验证和过滤功能
 */

/**
 * 文本过滤器集合
 * 用于验证文本是否适合翻译
 */
export const TextFilter = {
  /**
   * 检查是否为纯数字文本
   * @param text 要检查的文本
   * @returns 如果是纯数字返回true
   */
  isPureNumber: (text: string): boolean => {
    return /^\d+$/.test(text.trim());
  },

  /**
   * 检查文本长度是否有效
   * @param text 要检查的文本
   * @param lenientMode 是否使用宽松模式（允许更短的文本）
   * @returns 如果长度在有效范围内返回true
   */
  isValidLength: (text: string, lenientMode: boolean = false): boolean => {
    const trimmed = text.trim();
    const minLength = lenientMode ? 1 : 2; // 宽松模式下允许1字符，普通模式2字符
    return trimmed.length >= minLength && trimmed.length <= 15000;
  },

  /**
   * 检查是否包含有效内容（非纯标点符号/空白）
   * @param text 要检查的文本
   * @returns 如果包含有效内容返回true
   */
  hasValidContent: (text: string): boolean => {
    // 使用Unicode属性检查，排除纯空白、数字、标点符号的文本
    return !/^[\s\d\p{P}]*$/u.test(text.trim());
  },

  /**
   * 检查是否为常见的非翻译内容
   * @param text 要检查的文本
   * @returns 如果是非翻译内容返回true
   */
  isNonTranslatableContent: (text: string): boolean => {
    const trimmed = text.trim().toLowerCase();
    
    // 常见的非翻译模式
    const patterns = [
      /^[\d\s\-\+\.\,\(\)]+$/, // 纯数字、符号组合
      /^[a-z0-9\.\-_@]+$/, // 邮箱、域名、用户名格式
      /^\$[0-9\.\,]+$/, // 价格格式
      /^#[a-f0-9]{3,8}$/i, // 颜色代码
      /^https?:\/\//, // URL
      /^[\w\-\.]+\.(com|org|net|edu|gov)$/i, // 域名
      /^[^\w]*$/ // 纯符号
    ];

    return patterns.some(pattern => pattern.test(trimmed));
  },

  /**
   * 检查文本是否包含代码特征
   * 优化后的版本：减少对HTML翻译内容的误报，增强JSON检测
   * @param text 要检查的文本
   * @returns 如果疑似代码返回true
   */
  isLikelyCode: (text: string): boolean => {
    // 首先检查是否为JSON数据
    if (TextFilter.isJsonData(text)) {
      return true;
    }

    const codePatterns = [
      /^\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*\{?/, // 函数声明
      /^\s*(function|class|const|let|var|if|for|while)\s+/, // 关键字开头
      /[{}();].*[{}();]/, // 多个代码符号
      /^\s*\/\/|^\s*\/\*|^\s*\*/, // 注释
      // 移除过于宽泛的HTML标签检测，只检测明显的代码HTML
      /^\s*<!DOCTYPE|^\s*<html|^\s*<head|^\s*<body/i, // 结构化HTML文档
      /\$\{[^}]+\}/, // 模板字符串
      /[=><]{2,}|[!&|]{2}/ // 操作符
    ];

    // 对于包含常见HTML标签的内容，进行更精确的判断
    if (/<(div|p|span|strong|em|h[1-6])\b/i.test(text)) {
      // 如果只是包含常见HTML标签的翻译内容，不认为是代码
      const hasCodeKeywords = /(function|class|const|let|var|if|for|while|return)\s*[({]/i.test(text);
      if (!hasCodeKeywords) {
        return false;
      }
    }

    return codePatterns.some(pattern => pattern.test(text));
  },

  /**
   * 检查文本是否为JSON数据
   * @param text 要检查的文本
   * @returns 如果是JSON数据返回true
   */
  isJsonData: (text: string): boolean => {
    const trimmed = text.trim();
    
    // 基本的JSON格式检查
    if ((trimmed.startsWith('{') && trimmed.endsWith('}')) ||
        (trimmed.startsWith('[') && trimmed.endsWith(']'))) {
      
      // 检查是否包含JSON特征
      const jsonPatterns = [
        /"[^"]*":\s*"[^"]*"/, // "key": "value"
        /"[^"]*":\s*\d+/, // "key": number
        /"[^"]*":\s*(true|false|null)/, // "key": boolean/null
        /"model":\s*"[^"]*"/, // Django模型特征
        /"pk":\s*\d+/, // 主键特征
        /"fields":\s*\{/ // 字段对象特征
      ];

      return jsonPatterns.some(pattern => pattern.test(trimmed));
    }

    return false;
  },

  /**
   * 清理文本内容，移除多余空白
   * @param text 要清理的文本
   * @returns 清理后的文本
   */
  cleanText: (text: string): string => {
    return text
      .replace(/\s+/g, ' ') // 多个空白字符合并为单个空格
      .replace(/^\s+|\s+$/g, '') // 去除首尾空白
      .replace(/\u00A0/g, ' '); // 将不间断空格转换为普通空格
  },

  /**
   * 综合检查文本是否适合翻译
   * 统一的翻译适用性检查，支持宽松模式
   * @param text 要检查的文本
   * @param lenientMode 是否使用宽松模式（放宽所有过滤条件）
   * @returns 如果适合翻译返回true
   */
  isTranslatable: (text: string, lenientMode: boolean = false): boolean => {
    if (!text) return false;

    const cleaned = TextFilter.cleanText(text);

    // 在宽松模式下，显著放宽过滤条件
    if (lenientMode) {
      // 宽松模式：只进行最基本的检查
      if (cleaned.length < 1 || cleaned.length > 15000) {
        return false;
      }
      
      // 允许HTML内容（常见翻译场景）
      if (/<(div|p|span|strong|em|h[1-6]|ul|ol|li|b|i|u|mark|blockquote)\b/i.test(text)) {
        return true;
      }
      
      // 宽松模式下只排除明显的非文本内容
      const isObviouslyNonText = (
        /^https?:\/\//.test(cleaned) || // URLs
        /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(cleaned) || // 邮箱
        /^#[a-f0-9]{3,8}$/i.test(cleaned) || // 颜色代码
        /^\$[0-9\.\,]+$/.test(cleaned) // 价格
      );
      
      return !isObviouslyNonText;
    }

    // 普通模式：进行标准检查
    const basicChecks = (
      TextFilter.isValidLength(cleaned, false) &&
      TextFilter.hasValidContent(cleaned) &&
      !TextFilter.isPureNumber(cleaned) &&
      !TextFilter.isNonTranslatableContent(cleaned)
    );

    if (!basicChecks) {
      return false;
    }

    // 普通模式下进行代码检测
    return !TextFilter.isLikelyCode(cleaned);
  }
};

/**
 * 文本统计信息
 */
export interface TextStats {
  total: number;
  valid: number;
  filtered: {
    tooShort: number;
    tooLong: number;
    pureNumber: number;
    noContent: number;
    nonTranslatable: number;
    likelyCode: number;
  };
}

/**
 * 批量过滤文本并生成统计信息
 * @param texts 文本数组
 * @param lenientMode 是否使用宽松模式
 * @returns 过滤结果和统计信息
 */
export function filterTextsWithStats(
  texts: string[], 
  lenientMode: boolean = false
): {
  validTexts: string[];
  stats: TextStats;
} {
  const stats: TextStats = {
    total: texts.length,
    valid: 0,
    filtered: {
      tooShort: 0,
      tooLong: 0,
      pureNumber: 0,
      noContent: 0,
      nonTranslatable: 0,
      likelyCode: 0
    }
  };

  const validTexts: string[] = [];

  texts.forEach(text => {
    if (!text) {
      stats.filtered.noContent++;
      return;
    }

    const cleaned = TextFilter.cleanText(text);

    if (!TextFilter.isValidLength(cleaned, lenientMode)) {
      if (cleaned.length < (lenientMode ? 1 : 2)) {
        stats.filtered.tooShort++;
      } else {
        stats.filtered.tooLong++;
      }
      return;
    }

    if (!TextFilter.hasValidContent(cleaned)) {
      stats.filtered.noContent++;
      return;
    }

    if (TextFilter.isPureNumber(cleaned)) {
      stats.filtered.pureNumber++;
      return;
    }

    if (TextFilter.isNonTranslatableContent(cleaned)) {
      stats.filtered.nonTranslatable++;
      return;
    }

    // 在宽松模式下使用更精确的翻译适用性检查
    if (!TextFilter.isTranslatable(cleaned, lenientMode)) {
      stats.filtered.likelyCode++;
      return;
    }

    // 通过所有检查
    validTexts.push(cleaned);
    stats.valid++;
  });

  return { validTexts, stats };
}