/**
 * 错误处理系统使用示例
 * 展示如何正确使用新的错误处理工具
 */

import { 
  errorLogger, 
  TranslationError, 
  StorageError, 
  NetworkError, 
  ApiError, 
  CacheError,
  DomError,
  ConfigError,
  PermissionError,
  TimeoutError,
  ValidationError,
  safeExecute,
  safeExecuteSync,
  ErrorContext 
} from '../utils/error-logger';
import { resourceManager } from '../utils/resource-manager';

/**
 * 示例服务类 - 展示最佳实践
 */
export class ExampleService {
  private timers: number[] = [];

  constructor() {
    // 启用调试模式
    errorLogger.setDebugMode(true);
  }

  /**
   * 示例：手动错误处理代替装饰器（避免TypeScript问题）
   */
  async processData(data: any): Promise<string> {
    const context: ErrorContext = {
      method: 'ExampleService.processData',
      component: 'ExampleService',
      data: { hasData: !!data }
    };

    try {
      // 输入验证
      if (!data) {
        throw new ValidationError('Data is required', 'data', data, ['required']);
      }

      // 模拟处理
      await new Promise(resolve => setTimeout(resolve, 100));
      errorLogger.info('Data processed successfully', context);
      return `Processed: ${JSON.stringify(data)}`;
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      errorLogger.error('Failed to process data', error as Error, context);
      throw new TranslationError('Data processing failed', error as Error, 'PROCESSING_ERROR', context);
    }
  }

  /**
   * 示例：手动错误处理 - 网络请求
   */
  async fetchData(url: string): Promise<any> {
    const context: ErrorContext = {
      method: 'ExampleService.fetchData',
      component: 'ExampleService',
      url
    };

    try {
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new NetworkError(
          `Request failed with status ${response.status}`,
          undefined,
          response.status,
          url,
          context
        );
      }

      const data = await response.json();
      errorLogger.info('Data fetched successfully', context);
      return data;

    } catch (error) {
      if (error instanceof NetworkError) {
        throw error;
      }

      // 网络连接错误
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new NetworkError('Network connection failed', error as Error, undefined, url, context);
      }

      // 其他未知错误
      errorLogger.error('Unexpected error during fetch', error as Error, context);
      throw new ApiError('API request failed', error as Error, undefined, url, undefined, context);
    }
  }

  /**
   * 示例：存储操作错误处理
   */
  async saveToStorage(key: string, value: any): Promise<void> {
    const context: ErrorContext = {
      method: 'ExampleService.saveToStorage',
      component: 'ExampleService',
      data: { key }
    };

    try {
      if (typeof browser === 'undefined' || !browser.storage) {
        throw new PermissionError('Storage API not available', 'storage', undefined, context);
      }

      await browser.storage.local.set({ [key]: value });
      errorLogger.info('Data saved to storage', context);

    } catch (error) {
      if (error instanceof PermissionError) {
        throw error;
      }

      throw new StorageError('Failed to save data to storage', error as Error, context);
    }
  }

  /**
   * 示例：DOM操作错误处理
   */
  updateElement(selector: string, content: string): void {
    const context: ErrorContext = {
      method: 'ExampleService.updateElement',
      component: 'ExampleService',
      data: { selector, content }
    };

    try {
      const element = document.querySelector(selector) as HTMLElement;
      
      if (!element) {
        throw new DomError('Element not found', undefined, undefined, selector, context);
      }

      element.textContent = content;
      errorLogger.debug('Element updated successfully', context);

    } catch (error) {
      if (error instanceof DomError) {
        throw error;
      }

      throw new DomError('Failed to update element', error as Error, undefined, selector, context);
    }
  }

  /**
   * 示例：配置验证错误处理
   */
  validateConfig(config: any): void {
    const context: ErrorContext = {
      method: 'ExampleService.validateConfig',
      component: 'ExampleService',
      data: { config }
    };

    try {
      if (!config.apiKey) {
        throw new ConfigError('API key is required', 'apiKey', config.apiKey, undefined, context);
      }

      if (typeof config.timeout !== 'number' || config.timeout <= 0) {
        throw new ConfigError('Timeout must be a positive number', 'timeout', config.timeout, undefined, context);
      }

      errorLogger.info('Configuration validated successfully', context);

    } catch (error) {
      if (error instanceof ConfigError) {
        throw error;
      }

      throw new ValidationError('Configuration validation failed', undefined, config, ['apiKey', 'timeout'], error as Error, context);
    }
  }

  /**
   * 示例：超时操作错误处理
   */
  async performOperationWithTimeout(operation: () => Promise<any>, timeoutMs: number): Promise<any> {
    const context: ErrorContext = {
      method: 'ExampleService.performOperationWithTimeout',
      component: 'ExampleService',
      data: { timeoutMs }
    };

    return new Promise(async (resolve, reject) => {
      const timeoutId = window.setTimeout(() => {
        reject(new TimeoutError(
          `Operation timed out after ${timeoutMs}ms`,
          'performOperationWithTimeout',
          timeoutMs,
          undefined,
          context
        ));
      }, timeoutMs);

      // 注册定时器到资源管理器
      resourceManager.registerTimer(timeoutId, 'operationTimeout', 'ExampleService');

      try {
        const result = await operation();
        resourceManager.clearTimer(timeoutId);
        resolve(result);
      } catch (error) {
        resourceManager.clearTimer(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * 示例：使用safeExecute辅助函数
   */
  async safeOperation(): Promise<string> {
    return await safeExecute(
      async () => {
        // 可能失败的操作
        const result = await this.fetchData('https://api.example.com/data');
        return result.message;
      },
      'Default message', // fallback值
      'Safe operation failed',
      {
        method: 'ExampleService.safeOperation',
        component: 'ExampleService'
      }
    );
  }

  /**
   * 示例：使用safeExecuteSync辅助函数
   */
  safeSyncOperation(): number {
    return safeExecuteSync(
      () => {
        // 可能失败的同步操作
        const config = JSON.parse(localStorage.getItem('config') || '{}');
        return config.value || 0;
      },
      0, // fallback值
      'Safe sync operation failed',
      {
        method: 'ExampleService.safeSyncOperation',
        component: 'ExampleService'
      }
    );
  }

  /**
   * 示例：资源管理
   */
  startPeriodicTask(): void {
    const intervalId = window.setInterval(() => {
      console.log('Periodic task running...');
    }, 1000);

    // 注册到资源管理器
    resourceManager.registerInterval(intervalId, 'periodicTask', 'ExampleService');
    this.timers.push(intervalId);
  }

  /**
   * 示例：清理资源
   */
  destroy(): void {
    try {
      // 清理特定组件的所有资源
      resourceManager.cleanupComponent('ExampleService');
      
      // 清理本地定时器
      this.timers.forEach(id => clearInterval(id));
      this.timers = [];

      errorLogger.info('ExampleService destroyed successfully', {
        method: 'ExampleService.destroy',
        component: 'ExampleService'
      });
    } catch (error) {
      errorLogger.error('Failed to destroy ExampleService', error as Error, {
        method: 'ExampleService.destroy',
        component: 'ExampleService'
      });
    }
  }
}

/**
 * 使用示例
 */
export async function demonstrateErrorHandling(): Promise<void> {
  const service = new ExampleService();

  try {
    // 1. 成功的操作
    await service.processData({ id: 1, name: 'test' });
    
    // 2. 配置验证
    service.validateConfig({ apiKey: 'test-key', timeout: 5000 });
    
    // 3. 安全操作
    const result = await service.safeOperation();
    console.log('Safe operation result:', result);
    
    // 4. 启动定期任务
    service.startPeriodicTask();
    
    // 5. 检查资源使用情况
    const stats = resourceManager.getStats();
    console.log('Resource stats:', stats);
    
    // 6. 检查可能的内存泄漏
    const leaks = resourceManager.checkForLeaks(60000); // 1分钟
    if (leaks.length > 0) {
      console.warn('Potential memory leaks detected:', leaks);
    }

  } catch (error) {
    // 错误会被自动记录，这里只需要处理业务逻辑
    console.error('Demonstration failed:', error);
    
    if (error instanceof ValidationError) {
      console.log('Validation failed for field:', error.field);
    } else if (error instanceof NetworkError) {
      console.log('Network error with status:', error.statusCode);
    } else if (error instanceof TimeoutError) {
      console.log('Operation timed out after:', error.timeoutMs, 'ms');
    }
  } finally {
    // 清理资源
    service.destroy();
  }
}

// 启动演示（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  demonstrateErrorHandling().catch(console.error);
}