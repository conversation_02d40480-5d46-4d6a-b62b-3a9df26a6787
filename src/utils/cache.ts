/**
 * 简单LRU缓存实现
 * 轻量化版本，专注实用性
 */

import { CacheEntry } from '../types';

export class SimpleCache {
  private cache = new Map<string, CacheEntry>();
  private accessOrder: string[] = [];
  
  constructor(
    private maxSize: number = 1000,
    private ttl: number = 24 * 60 * 60 * 1000 // 24小时
  ) {}

  /**
   * 获取缓存项
   */
  get(key: string): string | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // 检查是否过期
    if (this.isExpired(entry)) {
      this.delete(key);
      return null;
    }

    // 更新访问计数和顺序
    entry.accessCount++;
    this.updateAccessOrder(key);
    
    return entry.value;
  }

  /**
   * 设置缓存项
   */
  set(key: string, value: string): void {
    // 如果已存在，先删除
    if (this.cache.has(key)) {
      this.delete(key);
    }

    // 检查大小限制
    if (this.cache.size >= this.maxSize) {
      this.evictLeastRecentlyUsed();
    }

    // 添加新项
    const entry: CacheEntry = {
      value,
      timestamp: Date.now(),
      accessCount: 1
    };

    this.cache.set(key, entry);
    this.accessOrder.push(key);
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    if (!this.cache.has(key)) {
      return false;
    }

    this.cache.delete(key);
    this.removeFromAccessOrder(key);
    return true;
  }

  /**
   * 批量获取
   */
  getMultiple(keys: string[]): Map<string, string> {
    const results = new Map<string, string>();
    
    keys.forEach(key => {
      const value = this.get(key);
      if (value !== null) {
        results.set(key, value);
      }
    });

    return results;
  }

  /**
   * 批量设置
   */
  setMultiple(entries: Map<string, string>): void {
    entries.forEach((value, key) => {
      this.set(key, value);
    });
  }

  /**
   * 清理过期项
   */
  cleanup(): number {
    let cleanedCount = 0;
    const now = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.ttl) {
        this.delete(key);
        cleanedCount++;
      }
    }

    return cleanedCount;
  }

  /**
   * 获取缓存统计
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    oldestEntry: number;
  } {
    let totalAccess = 0;
    let oldestTimestamp = Date.now();

    for (const entry of this.cache.values()) {
      totalAccess += entry.accessCount;
      oldestTimestamp = Math.min(oldestTimestamp, entry.timestamp);
    }

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: totalAccess > 0 ? (this.cache.size / totalAccess) : 0,
      oldestEntry: Date.now() - oldestTimestamp
    };
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.accessOrder = [];
  }

  /**
   * 检查是否过期
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > this.ttl;
  }

  /**
   * 更新访问顺序
   */
  private updateAccessOrder(key: string): void {
    this.removeFromAccessOrder(key);
    this.accessOrder.push(key);
  }

  /**
   * 从访问顺序中移除
   */
  private removeFromAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  /**
   * 驱逐最久未使用的项
   */
  private evictLeastRecentlyUsed(): void {
    if (this.accessOrder.length === 0) {
      return;
    }

    const lruKey = this.accessOrder[0];
    this.delete(lruKey);
  }
}

/**
 * 缓存键生成工具
 */
export class CacheKeyGenerator {
  /**
   * 为文本生成缓存键
   */
  static forText(text: string, options?: { format?: string; lang?: string }): string {
    const hash = this.simpleHash(text);
    const format = options?.format || 'text';
    const lang = options?.lang || 'zh';
    return `${format}:${lang}:${hash}`;
  }

  /**
   * 简单哈希函数
   */
  private static simpleHash(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转为32位整数
    }
    
    return Math.abs(hash).toString(36);
  }
}