/**
 * 简化的调试工具
 * 用户现在手动使用 console.log 格式: ✅ [module|LEVEL] message
 * 
 * 格式示例:
 * - console.log('✅ [translation-pipeline|STARTUP] 翻译集成已配置')
 * - console.log('🔧 [tooltip-manager|DEBUG] Mouse entered highlight:', word)
 * - console.log('❌ [background-service|ERROR] Failed to process', error)
 * - console.log('⚠️ [storage-service|WARN] Extension context invalidated')
 * 
 * 模块名称应为功能模块名称，而非文件名
 */

// 保留一些类型定义以避免编译错误（向后兼容）
export interface DebugHelper {
  debug: (...args: any[]) => void;
  info: (...args: any[]) => void;
  warn: (...args: any[]) => void;
  error: (...args: any[]) => void;
}

// 创建一个空的debug helper（已不使用）
const createEmptyDebugHelper = (): DebugHelper => ({
  debug: () => {},
  info: () => {},
  warn: () => {},
  error: () => {}
});

// 导出空的debug helper（向后兼容）
export const debug = createEmptyDebugHelper();
export const debugContent = createEmptyDebugHelper();
export const debugBackground = createEmptyDebugHelper();
export const debugUI = createEmptyDebugHelper();
export const debugSlider = createEmptyDebugHelper();
export const debugTooltip = createEmptyDebugHelper();
export const debugTranslation = createEmptyDebugHelper();
export const debugStorage = createEmptyDebugHelper();

// 保留类型定义（向后兼容）
export interface DebugFunctions {
  // 已移除，使用手动 console.log
}

export interface DebugManagerInstances {
  // 已移除，使用手动 console.log
}

// 空的初始化函数（向后兼容）
export function initializeDebugFunctions(_managers: any): DebugFunctions {
  console.log('🔧 [debug-system|INFO] Debug functions have been simplified - use manual console.log');
  return {} as DebugFunctions;
}

// 手动格式化日志指南
export const LOG_FORMAT_GUIDE = {
  SUCCESS: '✅', // 成功操作
  DEBUG: '🔧',   // 调试信息
  ERROR: '❌',   // 错误
  WARNING: '⚠️', // 警告
  INFO: 'ℹ️',    // 一般信息
  CLEANUP: '🧹', // 清理操作
  STARTUP: '🚀', // 启动信息
  TRACE: '🔍'    // 跟踪信息
};

// 使用示例
export const MANUAL_LOG_EXAMPLES = [
  "console.log('✅ [translation-pipeline|STARTUP] 翻译集成已配置')",
  "console.log('🔧 [tooltip-manager|DEBUG] Mouse entered highlight:', word)",
  "console.log('❌ [background-service|ERROR] Failed to process', error)",
  "console.log('⚠️ [storage-service|WARN] Extension context invalidated')",
  "console.log('ℹ️ [slider-manager|INFO] Slider initialized')",
  "console.log('🧹 [ui-manager|DEBUG] Cleanup completed')",
  "console.log('🔍 [dom-analyzer|TRACE] Element analysis result:', data)"
];

console.log('🔧 [debug-system|INFO] Manual logging system active - use format: console.log("✅ [module|LEVEL] message")');