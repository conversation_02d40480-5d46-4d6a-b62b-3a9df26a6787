/**
 * 文本处理工具
 * 统一的文本提取和处理逻辑
 */

import { TextExtraction, LinkInfo } from '../types';
import { DOMUtils } from './dom-utils';

/**
 * 文本处理器
 */
export class TextProcessor {
  private static readonly TRANSLATION_SELECTORS = [
    '.lu-wrapper',
    '.lu-block',
    '.notranslate',
    '[data-lu-translated]',
    '[data-no-translate]'
  ];

  /**
   * 从元素提取文本
   * 🔧 优化版本：确保结构检查和文本提取基于一致的元素状态
   */
  static extractText(element: HTMLElement): TextExtraction {
    // 简化版本：只提取纯文本内容，不保留任何HTML结构信息
    const elementInfo = `${element.tagName}.${element.className || 'no-class'}`;
    
    // 克隆元素以避免修改原始DOM
    const clone = element.cloneNode(true) as HTMLElement;

    // 移除翻译相关的元素
    this.removeTranslationElements(clone);
    
    // 移除隐藏的子元素
    this.removeHiddenElements(clone);

    // 提取纯文本内容
    const text = clone.textContent?.trim() || '';

    // 调试日志（开发环境）
    if (process.env.NODE_ENV === 'development' || (globalThis as any).__LUCID_DEBUG__) {
      console.log(`[TextProcessor] ${elementInfo}: "${text.slice(0, 50)}..."`);
    }

    // 简化返回：只返回必要的文本信息
    return {
      element,
      text,
      hasHtmlStructure: false, // 始终为false，不再保留HTML结构
      htmlContent: undefined,   // 不再需要HTML内容
      links: []                // 不再提取链接信息
    };
  }

  /**
   * 批量提取文本
   */
  static extractMultiple(elements: HTMLElement[]): TextExtraction[] {
    return elements.map(element => this.extractText(element));
  }

  /**
   * 移除翻译相关元素
   */
  static removeTranslationElements(element: HTMLElement): void {
    this.TRANSLATION_SELECTORS.forEach(selector => {
      element.querySelectorAll(selector).forEach(el => el.remove());
    });
  }

  /**
   * 移除隐藏的子元素
   * 🔧 防止容器元素聚合隐藏内容的文本，同时保护重要的内容元素
   */
  static removeHiddenElements(element: HTMLElement): void {
    // 查找所有子元素
    const allChildren = element.querySelectorAll('*');

    for (const child of Array.from(allChildren)) {
      const htmlChild = child as HTMLElement;

      // 🔧 关键修复：保护包含内容的重要元素，避免误删
      const isImportantContentElement = this.isImportantContentElement(htmlChild);
      if (isImportantContentElement) {
        continue; // 跳过重要内容元素的隐藏检查
      }

      // 使用中央化的隐藏检查
      if (DOMUtils.isElementTrulyHidden(htmlChild)) {
        htmlChild.remove();
        continue;
      }

      // 检查是否有隐藏相关的标记（调试模式下的标记）
      if (htmlChild.hasAttribute('data-exclusion-reason')) {
        const reason = htmlChild.getAttribute('data-exclusion-reason');
        if (reason === 'hidden') {
          htmlChild.remove();
          continue;
        }
      }

      // 检查特定的社交分享隐藏元素 - 保留现有逻辑以向后兼容
      if (this.isSocialShareHiddenElement(htmlChild)) {
        htmlChild.remove();
        continue;
      }
    }
  }

  /**
   * 检查是否是重要的内容元素（不应该被隐藏检查移除）
   * 🔧 保护包含文本内容的关键元素，优先排除装饰性元素
   */
  private static isImportantContentElement(element: HTMLElement): boolean {
    const tagName = element.tagName.toLowerCase();
    
    // 🔧 优先排除：明确的装饰性元素，无论标签类型
    const decorativeClasses = ['tombstone', 'spacer', 'divider', 'separator', 'decoration'];
    if (decorativeClasses.some(cls => element.classList.contains(cls))) {
      return false;
    }
    
    // 排除空元素或仅包含空白的元素
    const textContent = element.textContent?.trim() || '';
    if (textContent.length === 0) {
      return false;
    }
    
    // 保护包含文本内容的重要内联元素
    const protectedInlineElements = [
      'a',       // 链接元素 - 最重要
      'strong',  // 加粗文本
      'em',      // 斜体文本
      'b',       // 粗体
      'i',       // 斜体
      'code',    // 代码
      'mark',    // 标记文本
      'small',   // 小字体
      'sub',     // 下标
      'sup'      // 上标
    ];
    
    if (protectedInlineElements.includes(tagName)) {
      return true;
    }
    
    // 对于 span 元素进行特殊处理：只保护明确包含内容的 span
    if (tagName === 'span') {
      // 检查是否有有意义的文本内容（排除纯空白、标点符号）
      const hasSignificantContent = /[a-zA-Z\u4e00-\u9fa5\u0400-\u04FF]/.test(textContent);
      return hasSignificantContent;
    }
    
    return false;
  }

  /**
   * 检查元素是否隐藏
   * @deprecated Use DOMUtils.isElementTrulyHidden() instead
   */
  private static isElementHidden(element: HTMLElement): boolean {
    return DOMUtils.isElementTrulyHidden(element);
  }

  /**
   * 检查是否是社交分享组件中的隐藏元素
   */
  private static isSocialShareHiddenElement(element: HTMLElement): boolean {
    // 检查是否在社交分享对话框中
    const isInShareDialog = element.closest('.uni-social-share__dialog, .social-share-dialog');
    if (!isInShareDialog) {
      return false;
    }

    // 检查对话框是否隐藏
    const dialog = element.closest('.uni-social-share__dialog') as HTMLElement;
    if (dialog) {
      // 检查 aria-expanded 状态
      const trigger = dialog.parentElement?.querySelector('[aria-expanded]');
      if (trigger && trigger.getAttribute('aria-expanded') === 'false') {
        return true;
      }

      // 检查对话框本身的隐藏状态
      const dialogStyle = getComputedStyle(dialog);
      if (dialogStyle.display === 'none' || dialogStyle.visibility === 'hidden') {
        return true;
      }
    }

    return false;
  }

  /**
   * 提取链接信息
   */
  static extractLinks(element: HTMLElement): LinkInfo[] {
    const links: LinkInfo[] = [];
    const linkElements = element.querySelectorAll('a[href]');

    linkElements.forEach(link => {
      const href = link.getAttribute('href');
      const text = link.textContent?.trim();

      if (href && text) {
        const attributes: Record<string, string> = {};

        // 提取所有属性
        for (const attr of link.attributes) {
          attributes[attr.name] = attr.value;
        }

        links.push({
          href,
          text,
          attributes
        });
      }
    });

    return links;
  }

  /**
   * 检查是否包含HTML结构
   */
  static hasHtmlStructure(element: HTMLElement): boolean {
    // 检查是否包含子元素
    const hasChildElements = element.children.length > 0;

    // 检查是否包含常见的结构标签
    const structureTags = ['p', 'div', 'span', 'strong', 'em', 'b', 'i', 'a', 'ul', 'ol', 'li'];
    const hasStructureTags = structureTags.some(tag =>
      element.querySelector(tag) !== null
    );

    return hasChildElements || hasStructureTags;
  }

  /**
   * 检查是否包含复杂HTML结构（需要保持结构的元素）
   * 🔧 修复：包含链接的元素也应该保持HTML结构以正确处理翻译
   */
  static hasComplexHtmlStructure(element: HTMLElement): boolean {
    // 🎯 只有这些元素类型被认为是"复杂结构"，需要保持HTML格式
    const complexStructureSelectors = [
      'table', 'thead', 'tbody', 'tr', 'td', 'th',  // 表格结构
      'ul', 'ol',  // 列表容器（但不包括li）
      'dl', 'dt', 'dd',  // 定义列表
      'form', 'fieldset',  // 表单结构
      'blockquote[cite]',  // 带引用来源的引用
      '[data-preserve-html]'  // 显式标记需要保持HTML的元素
    ];

    // 检查当前元素是否是复杂结构
    const isComplexElement = complexStructureSelectors.some(selector => {
      try {
        return element.matches(selector);
      } catch {
        return false;
      }
    });

    if (isComplexElement) {
      return true;
    }

    // 检查是否包含复杂子结构
    const hasComplexChildren = complexStructureSelectors.some(selector => {
      try {
        return element.querySelector(selector) !== null;
      } catch {
        return false;
      }
    });

    if (hasComplexChildren) {
      return true;
    }

    // 🔧 关键修复：检查是否包含链接或其他内联HTML元素
    // 包含链接的元素需要保持HTML结构以便正确处理链接文本和翻译
    const hasLinks = element.querySelectorAll('a[href]').length > 0;
    const hasInlineElements = element.querySelectorAll('strong, em, b, i, span, code').length > 0;
    
    return hasLinks || hasInlineElements;
  }

  /**
   * 清理文本内容
   */
  static cleanText(text: string): string {
    return text
      // 规范化空白字符
      .replace(/\s+/g, ' ')
      // 移除开头和结尾的空白
      .trim()
      // 移除控制字符
      .replace(/[\x00-\x1F\x7F]/g, '');
  }

  /**
   * 检查文本是否值得翻译
   * @deprecated 使用 TextFilter.isTranslatable() 替代以确保一致性
   */
  static isTranslatable(text: string, lenientMode: boolean = false): boolean {
    // 导入 TextFilter 以确保使用统一的过滤逻辑
    const { TextFilter } = require('./text-filter');
    return TextFilter.isTranslatable(text, lenientMode);
  }

  /**
   * 检测文本语言
   */
  static detectLanguage(text: string): 'zh' | 'en' | 'unknown' {
    // 简单的语言检测
    const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishChars = (text.match(/[a-zA-Z]/g) || []).length;
    const totalChars = text.length;

    if (chineseChars / totalChars > 0.3) {
      return 'zh';
    }

    if (englishChars / totalChars > 0.3) {
      return 'en';
    }

    return 'unknown';
  }

  /**
   * 分割长文本
   */
  static splitLongText(text: string, maxLength: number = 5000): string[] {
    if (text.length <= maxLength) {
      return [text];
    }

    const chunks: string[] = [];
    const sentences = text.split(/[.!?。！？]\s*/);
    let currentChunk = '';

    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length > maxLength) {
        if (currentChunk) {
          chunks.push(currentChunk.trim());
          currentChunk = '';
        }

        // 如果单个句子太长，强制分割
        if (sentence.length > maxLength) {
          const parts = this.forceSplit(sentence, maxLength);
          chunks.push(...parts);
        } else {
          currentChunk = sentence;
        }
      } else {
        currentChunk += (currentChunk ? '. ' : '') + sentence;
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }

    return chunks.filter(chunk => chunk.length > 0);
  }

  /**
   * 强制分割文本
   */
  private static forceSplit(text: string, maxLength: number): string[] {
    const chunks: string[] = [];

    for (let i = 0; i < text.length; i += maxLength) {
      chunks.push(text.slice(i, i + maxLength));
    }

    return chunks;
  }

  /**
   * 合并翻译结果
   */
  static mergeTranslations(chunks: string[]): string {
    return chunks.join(' ').trim();
  }

  /**
   * 转义HTML字符
   */
  static escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 反转义HTML字符
   */
  static unescapeHtml(html: string): string {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || '';
  }
}