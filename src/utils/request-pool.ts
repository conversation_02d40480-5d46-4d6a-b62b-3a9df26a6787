/**
 * 请求池和去重管理器
 * 优化翻译请求性能，避免重复请求和资源浪费
 */

export interface RequestPoolOptions {
  /** 最大并发请求数 */
  maxConcurrent?: number;
  /** 请求去重时间窗口 (ms) */
  dedupeWindow?: number;
  /** 请求超时时间 (ms) */
  timeout?: number;
  /** 连接池大小 */
  poolSize?: number;
  /** 是否启用统计 */
  enableStats?: boolean;
}

export interface RequestStats {
  /** 总请求数 */
  totalRequests: number;
  /** 去重的请求数 */
  deduplicatedRequests: number;
  /** 成功请求数 */
  successfulRequests: number;
  /** 失败请求数 */
  failedRequests: number;
  /** 当前并发数 */
  currentConcurrency: number;
  /** 平均响应时间 */
  averageResponseTime: number;
  /** 去重率 */
  deduplicationRate: number;
}

interface PendingRequest<T> {
  promise: Promise<T>;
  resolve: (value: T) => void;
  reject: (reason: any) => void;
  timestamp: number;
  timeout?: NodeJS.Timeout;
}

interface RequestKey {
  method: string;
  params: string;
}

export class RequestPool {
  private options: Required<RequestPoolOptions>;
  private pendingRequests = new Map<string, PendingRequest<any>>();
  private activeRequests = new Set<Promise<any>>();
  private requestQueue: Array<() => Promise<any>> = [];
  
  // 统计信息
  private stats: RequestStats = {
    totalRequests: 0,
    deduplicatedRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    currentConcurrency: 0,
    averageResponseTime: 0,
    deduplicationRate: 0
  };

  private totalResponseTime = 0;

  constructor(options: RequestPoolOptions = {}) {
    this.options = {
      maxConcurrent: 6,
      dedupeWindow: 5000, // 5秒去重窗口
      timeout: 10000, // 10秒超时
      poolSize: 10,
      enableStats: true,
      ...options
    };
  }

  /**
   * 执行请求（带去重和并发控制）
   */
  async execute<T>(
    requestKey: RequestKey,
    requestFn: () => Promise<T>
  ): Promise<T> {
    const keyString = this.createKeyString(requestKey);
    const startTime = Date.now();

    this.stats.totalRequests++;

    // 检查是否有相同的待处理请求
    const existingRequest = this.pendingRequests.get(keyString);
    if (existingRequest && this.isRequestValid(existingRequest)) {
      this.stats.deduplicatedRequests++;
      this.updateDeduplicationRate();
      return existingRequest.promise;
    }

    // 创建新的请求Promise
    const { promise, resolve, reject } = this.createDeferredPromise<T>();
    
    // 设置超时
    const timeout = setTimeout(() => {
      this.pendingRequests.delete(keyString);
      reject(new Error(`Request timeout after ${this.options.timeout}ms`));
      this.stats.failedRequests++;
    }, this.options.timeout);

    const pendingRequest: PendingRequest<T> = {
      promise,
      resolve,
      reject,
      timestamp: startTime,
      timeout
    };

    this.pendingRequests.set(keyString, pendingRequest);

    // 执行请求
    this.executeWithConcurrencyControl(async () => {
      try {
        const result = await requestFn();
        
        // 清理
        clearTimeout(timeout);
        this.pendingRequests.delete(keyString);
        
        // 更新统计
        this.stats.successfulRequests++;
        this.totalResponseTime += Date.now() - startTime;
        this.updateAverageResponseTime();

        resolve(result);
        return result;
      } catch (error) {
        // 清理
        clearTimeout(timeout);
        this.pendingRequests.delete(keyString);
        
        // 更新统计
        this.stats.failedRequests++;
        
        reject(error);
        throw error;
      }
    });

    return promise;
  }

  /**
   * 批量执行请求
   */
  async executeBatch<T>(
    requests: Array<{
      key: RequestKey;
      requestFn: () => Promise<T>;
    }>
  ): Promise<Array<T | Error>> {
    const batchPromises = requests.map(({ key, requestFn }) =>
      this.execute(key, requestFn).catch(error => error)
    );

    return Promise.all(batchPromises);
  }

  /**
   * 翻译特定的请求执行器
   */
  async translateText(
    text: string,
    options: { from?: string; to?: string; format?: string } = {}
  ): Promise<any> {
    const requestKey: RequestKey = {
      method: 'translate',
      params: JSON.stringify({ text, ...options })
    };

    // 这里需要实际的翻译函数，暂时返回mock结果
    return this.execute(requestKey, async () => {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
      
      if (Math.random() < 0.05) { // 5% 失败率
        throw new Error('Translation service temporarily unavailable');
      }

      return {
        success: true,
        text: `[翻译] ${text}`,
        from: options.from || 'auto',
        to: options.to || 'zh',
        engine: 'mock-pooled'
      };
    });
  }

  /**
   * 创建延迟Promise
   */
  private createDeferredPromise<T>(): {
    promise: Promise<T>;
    resolve: (value: T) => void;
    reject: (reason: any) => void;
  } {
    let resolve!: (value: T) => void;
    let reject!: (reason: any) => void;

    const promise = new Promise<T>((res, rej) => {
      resolve = res;
      reject = rej;
    });

    return { promise, resolve, reject };
  }

  /**
   * 并发控制执行
   */
  private async executeWithConcurrencyControl<T>(
    requestFn: () => Promise<T>
  ): Promise<void> {
    // 如果当前并发数已达上限，加入队列等待
    if (this.activeRequests.size >= this.options.maxConcurrent) {
      await new Promise<void>(resolve => {
        this.requestQueue.push(async () => {
          await this.executeRequest(requestFn);
          resolve();
        });
      });
    } else {
      await this.executeRequest(requestFn);
    }
  }

  /**
   * 执行单个请求
   */
  private async executeRequest<T>(requestFn: () => Promise<T>): Promise<void> {
    const requestPromise = requestFn();
    this.activeRequests.add(requestPromise);
    this.stats.currentConcurrency = this.activeRequests.size;

    try {
      await requestPromise;
    } finally {
      this.activeRequests.delete(requestPromise);
      this.stats.currentConcurrency = this.activeRequests.size;
      
      // 处理队列中的下一个请求
      this.processNextRequest();
    }
  }

  /**
   * 处理队列中的下一个请求
   */
  private processNextRequest(): void {
    if (this.requestQueue.length > 0 && this.activeRequests.size < this.options.maxConcurrent) {
      const nextRequest = this.requestQueue.shift();
      if (nextRequest) {
        nextRequest();
      }
    }
  }

  /**
   * 创建请求键字符串
   */
  private createKeyString(key: RequestKey): string {
    return `${key.method}:${key.params}`;
  }

  /**
   * 检查请求是否仍然有效
   */
  private isRequestValid(request: PendingRequest<any>): boolean {
    const now = Date.now();
    return (now - request.timestamp) < this.options.dedupeWindow;
  }

  /**
   * 更新去重率
   */
  private updateDeduplicationRate(): void {
    if (this.stats.totalRequests > 0) {
      this.stats.deduplicationRate = 
        this.stats.deduplicatedRequests / this.stats.totalRequests;
    }
  }

  /**
   * 更新平均响应时间
   */
  private updateAverageResponseTime(): void {
    if (this.stats.successfulRequests > 0) {
      this.stats.averageResponseTime = 
        this.totalResponseTime / this.stats.successfulRequests;
    }
  }

  /**
   * 清理过期的请求
   */
  cleanupExpiredRequests(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, request] of this.pendingRequests) {
      if (!this.isRequestValid(request)) {
        expiredKeys.push(key);
        if (request.timeout) {
          clearTimeout(request.timeout);
        }
      }
    }

    expiredKeys.forEach(key => {
      this.pendingRequests.delete(key);
    });
  }

  /**
   * 强制清空所有待处理请求
   */
  clearAllRequests(): void {
    // 取消所有待处理请求的超时
    for (const request of this.pendingRequests.values()) {
      if (request.timeout) {
        clearTimeout(request.timeout);
      }
      request.reject(new Error('Request pool cleared'));
    }

    this.pendingRequests.clear();
    this.requestQueue = [];
  }

  /**
   * 获取统计信息
   */
  getStats(): RequestStats {
    this.updateDeduplicationRate();
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalRequests: 0,
      deduplicatedRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      currentConcurrency: this.activeRequests.size,
      averageResponseTime: 0,
      deduplicationRate: 0
    };
    this.totalResponseTime = 0;
  }

  /**
   * 获取当前状态
   */
  getStatus(): {
    pendingRequestsCount: number;
    activeRequestsCount: number;
    queuedRequestsCount: number;
    isHealthy: boolean;
  } {
    return {
      pendingRequestsCount: this.pendingRequests.size,
      activeRequestsCount: this.activeRequests.size,
      queuedRequestsCount: this.requestQueue.length,
      isHealthy: this.activeRequests.size <= this.options.maxConcurrent
    };
  }

  /**
   * 销毁请求池
   */
  destroy(): void {
    this.clearAllRequests();
    this.resetStats();
  }
}

// 全局请求池实例
let globalRequestPool: RequestPool | null = null;

/**
 * 获取全局请求池实例
 */
export function getGlobalRequestPool(): RequestPool {
  if (!globalRequestPool) {
    globalRequestPool = new RequestPool({
      maxConcurrent: 4,
      dedupeWindow: 3000,
      timeout: 8000,
      enableStats: true
    });
  }
  return globalRequestPool;
}

/**
 * 重置全局请求池
 */
export function resetGlobalRequestPool(): void {
  if (globalRequestPool) {
    globalRequestPool.destroy();
    globalRequestPool = null;
  }
}