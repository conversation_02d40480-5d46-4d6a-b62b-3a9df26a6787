/**
 * 错误日志记录工具
 * 提供统一的错误处理和日志记录功能
 */

export interface ErrorContext {
  method?: string;
  component?: string;
  userId?: string;
  data?: any;
  userAgent?: string;
  url?: string;
}

export interface LogLevel {
  ERROR: 'error';
  WARN: 'warn';
  INFO: 'info';
  DEBUG: 'debug';
}

export const LOG_LEVEL: LogLevel = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
};

/**
 * 自定义错误类
 */
export class TranslationError extends Error {
  public readonly code: string;
  public readonly context?: ErrorContext;
  public readonly cause?: Error;
  public readonly timestamp: number;

  constructor(message: string, cause?: Error, code?: string, context?: ErrorContext) {
    super(message);
    this.name = 'TranslationError';
    this.code = code || 'UNKNOWN_ERROR';
    this.context = context;
    this.cause = cause;
    this.timestamp = Date.now();

    // 保持错误堆栈追踪
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, TranslationError);
    }
  }
}

/**
 * 存储相关错误
 */
export class StorageError extends TranslationError {
  constructor(message: string, cause?: Error, context?: ErrorContext) {
    super(message, cause, 'STORAGE_ERROR', context);
    this.name = 'StorageError';
  }
}

/**
 * 网络相关错误
 */
export class NetworkError extends TranslationError {
  public readonly statusCode?: number;
  public readonly url?: string;

  constructor(message: string, cause?: Error, statusCode?: number, url?: string, context?: ErrorContext) {
    super(message, cause, 'NETWORK_ERROR', context);
    this.name = 'NetworkError';
    this.statusCode = statusCode;
    this.url = url;
  }
}

/**
 * API相关错误
 */
export class ApiError extends NetworkError {
  public readonly response?: any;

  constructor(message: string, cause?: Error, statusCode?: number, url?: string, response?: any, context?: ErrorContext) {
    super(message, cause, statusCode, url, context);
    this.name = 'ApiError';
    (this as any).code = 'API_ERROR';
    this.response = response;
  }
}

/**
 * 缓存相关错误
 */
export class CacheError extends TranslationError {
  public readonly operation: 'GET' | 'SET' | 'DELETE' | 'CLEAR';

  constructor(message: string, operation: 'GET' | 'SET' | 'DELETE' | 'CLEAR', cause?: Error, context?: ErrorContext) {
    super(message, cause, 'CACHE_ERROR', context);
    this.name = 'CacheError';
    this.operation = operation;
  }
}

/**
 * DOM操作相关错误
 */
export class DomError extends TranslationError {
  public readonly element?: HTMLElement;
  public readonly selector?: string;

  constructor(message: string, cause?: Error, element?: HTMLElement, selector?: string, context?: ErrorContext) {
    super(message, cause, 'DOM_ERROR', context);
    this.name = 'DomError';
    this.element = element;
    this.selector = selector;
  }
}

/**
 * 配置相关错误
 */
export class ConfigError extends TranslationError {
  public readonly configKey?: string;
  public readonly configValue?: any;

  constructor(message: string, configKey?: string, configValue?: any, cause?: Error, context?: ErrorContext) {
    super(message, cause, 'CONFIG_ERROR', context);
    this.name = 'ConfigError';
    this.configKey = configKey;
    this.configValue = configValue;
  }
}

/**
 * 权限相关错误
 */
export class PermissionError extends TranslationError {
  public readonly permission: string;

  constructor(message: string, permission: string, cause?: Error, context?: ErrorContext) {
    super(message, cause, 'PERMISSION_ERROR', context);
    this.name = 'PermissionError';
    this.permission = permission;
  }
}

/**
 * 超时错误
 */
export class TimeoutError extends TranslationError {
  public readonly timeoutMs: number;
  public readonly operation: string;

  constructor(message: string, operation: string, timeoutMs: number, cause?: Error, context?: ErrorContext) {
    super(message, cause, 'TIMEOUT_ERROR', context);
    this.name = 'TimeoutError';
    this.timeoutMs = timeoutMs;
    this.operation = operation;
  }
}

/**
 * 验证错误
 */
export class ValidationError extends TranslationError {
  public readonly field?: string;
  public readonly value?: any;
  public readonly constraints?: string[];

  constructor(message: string, field?: string, value?: any, constraints?: string[], cause?: Error, context?: ErrorContext) {
    super(message, cause, 'VALIDATION_ERROR', context);
    this.name = 'ValidationError';
    this.field = field;
    this.value = value;
    this.constraints = constraints;
  }
}

/**
 * 错误日志记录器
 */
export class ErrorLogger {
  private static instance: ErrorLogger;
  private debugMode: boolean = false;

  static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger();
    }
    return ErrorLogger.instance;
  }

  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
  }

  /**
   * 记录错误日志
   */
  error(message: string, error?: Error, context?: ErrorContext): void {
    const logData = this.formatLogData('error', message, error, context);
    console.error(`🚨 [ERROR] ${message}`, logData);
    
    // 在生产环境可以发送到监控服务
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoring(logData);
    }
  }

  /**
   * 记录警告日志
   */
  warn(message: string, error?: Error, context?: ErrorContext): void {
    const logData = this.formatLogData('warn', message, error, context);
    console.warn(`⚠️ [WARN] ${message}`, logData);
  }

  /**
   * 记录信息日志
   */
  info(message: string, context?: ErrorContext): void {
    if (this.debugMode) {
      const logData = this.formatLogData('info', message, undefined, context);
      console.info(`ℹ️ [INFO] ${message}`, logData);
    }
  }

  /**
   * 记录调试日志
   */
  debug(message: string, context?: ErrorContext): void {
    if (this.debugMode) {
      const logData = this.formatLogData('debug', message, undefined, context);
      console.debug(`🔍 [DEBUG] ${message}`, logData);
    }
  }

  /**
   * 格式化日志数据
   */
  private formatLogData(level: string, message: string, error?: Error, context?: ErrorContext) {
    const logData: any = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: context || {}
    };

    if (error) {
      logData.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code,
        cause: (error as any).cause?.message
      };
    }

    // 添加浏览器信息
    if (typeof window !== 'undefined') {
      logData.browser = {
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: Date.now()
      };
    }

    return logData;
  }

  /**
   * 发送到监控服务（生产环境）
   */
  private sendToMonitoring(logData: any): void {
    // 这里可以集成监控服务，如 Sentry, LogRocket 等
    // 暂时只是记录到本地存储
    try {
      const logs = JSON.parse(localStorage.getItem('error_logs') || '[]');
      logs.push(logData);
      
      // 只保留最近100条错误日志
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }
      
      localStorage.setItem('error_logs', JSON.stringify(logs));
    } catch (storageError) {
      console.error('Failed to save error log to storage:', storageError);
    }
  }
}

/**
 * 错误处理装饰器
 */
export function handleErrors(
  errorMessage: string,
  context?: ErrorContext,
  rethrow: boolean = true
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const logger = ErrorLogger.getInstance();

    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args);
      } catch (error) {
        const enhancedContext = {
          ...context,
          method: `${target.constructor.name}.${propertyKey}`,
          arguments: args.length > 0 ? 'present' : 'none'
        };

        logger.error(errorMessage, error as Error, enhancedContext);

        if (rethrow) {
          if (error instanceof TranslationError) {
            throw error;
          }
          throw new TranslationError(
            errorMessage,
            error as Error,
            'METHOD_ERROR',
            enhancedContext
          );
        }
      }
    };

    return descriptor;
  };
}

/**
 * 辅助函数：安全执行异步操作
 */
export async function safeExecute<T>(
  operation: () => Promise<T>,
  fallback: T,
  errorMessage: string,
  context?: ErrorContext
): Promise<T> {
  const logger = ErrorLogger.getInstance();
  
  try {
    return await operation();
  } catch (error) {
    logger.error(errorMessage, error as Error, context);
    return fallback;
  }
}

/**
 * 辅助函数：安全执行同步操作
 */
export function safeExecuteSync<T>(
  operation: () => T,
  fallback: T,
  errorMessage: string,
  context?: ErrorContext
): T {
  const logger = ErrorLogger.getInstance();
  
  try {
    return operation();
  } catch (error) {
    logger.error(errorMessage, error as Error, context);
    return fallback;
  }
}

// 导出单例实例
export const errorLogger = ErrorLogger.getInstance();