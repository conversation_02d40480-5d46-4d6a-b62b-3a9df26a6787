/**
 * 错误边界管理器
 * 提供统一的错误处理和资源清理机制
 */

export interface ErrorBoundaryOptions {
  /** 错误处理器名称 */
  name: string;
  /** 是否记录错误到控制台 */
  logErrors?: boolean;
  /** 错误发生时的回调 */
  onError?: (error: Error, context?: string) => void;
  /** 清理资源的回调 */
  cleanup?: () => void;
  /** 重试次数 */
  maxRetries?: number;
  /** 重试延迟 (ms) */
  retryDelay?: number;
}

export class ErrorBoundary {
  private options: Required<ErrorBoundaryOptions>;
  private retryCount = 0;
  private isDestroyed = false;

  constructor(options: ErrorBoundaryOptions) {
    this.options = {
      logErrors: true,
      onError: () => {},
      cleanup: () => {},
      maxRetries: 3,
      retryDelay: 1000,
      ...options
    };
  }

  /**
   * 安全执行异步操作
   */
  async safeExecute<T>(
    operation: () => Promise<T>,
    context?: string
  ): Promise<{ success: boolean; result?: T; error?: Error }> {
    if (this.isDestroyed) {
      return { success: false, error: new Error('ErrorBoundary已销毁') };
    }

    try {
      const result = await operation();
      this.retryCount = 0; // 重置重试计数
      return { success: true, result };

    } catch (error) {
      const normalizedError = error instanceof Error ? error : new Error(String(error));
      
      if (this.options.logErrors) {
        console.error(`[${this.options.name}] 错误:`, normalizedError.message, {
          context,
          retryCount: this.retryCount,
          stack: normalizedError.stack
        });
      }

      // 调用错误处理器
      this.options.onError(normalizedError, context);

      // 检查是否需要重试
      if (this.retryCount < this.options.maxRetries && this.shouldRetry(normalizedError)) {
        this.retryCount++;
        
        if (this.options.logErrors) {
          console.warn(`[${this.options.name}] 准备重试 ${this.retryCount}/${this.options.maxRetries}...`);
        }

        // 延迟后重试
        await this.delay(this.options.retryDelay * this.retryCount);
        return this.safeExecute(operation, context);
      }

      this.retryCount = 0;
      return { success: false, error: normalizedError };
    }
  }

  /**
   * 安全执行同步操作
   */
  safeExecuteSync<T>(
    operation: () => T,
    context?: string
  ): { success: boolean; result?: T; error?: Error } {
    if (this.isDestroyed) {
      return { success: false, error: new Error('ErrorBoundary已销毁') };
    }

    try {
      const result = operation();
      return { success: true, result };

    } catch (error) {
      const normalizedError = error instanceof Error ? error : new Error(String(error));
      
      if (this.options.logErrors) {
        console.error(`[${this.options.name}] 同步错误:`, normalizedError.message, {
          context,
          stack: normalizedError.stack
        });
      }

      this.options.onError(normalizedError, context);
      return { success: false, error: normalizedError };
    }
  }

  /**
   * 创建安全的Promise包装器
   */
  createSafePromise<T>(promise: Promise<T>, context?: string): Promise<{ success: boolean; result?: T; error?: Error }> {
    return this.safeExecute(() => promise, context);
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: Error): boolean {
    // 网络错误通常值得重试
    if (error.message.includes('fetch') || 
        error.message.includes('network') ||
        error.message.includes('timeout')) {
      return true;
    }

    // DOM操作错误通常不值得重试
    if (error.message.includes('DOM') || 
        error.message.includes('Element')) {
      return false;
    }

    // 默认重试
    return true;
  }

  /**
   * 延迟执行
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 销毁错误边界并清理资源
   */
  destroy(): void {
    if (this.isDestroyed) {
      return;
    }

    try {
      this.options.cleanup();
    } catch (error) {
      console.error(`[${this.options.name}] 清理资源时发生错误:`, error);
    }

    this.isDestroyed = true;
  }

  /**
   * 检查是否已销毁
   */
  get destroyed(): boolean {
    return this.isDestroyed;
  }
}

/**
 * 资源管理器
 * 跟踪和清理各种资源
 */
export class ResourceManager {
  private resources = new Map<string, () => void>();
  private timers = new Set<number>();
  private intervals = new Set<number>();
  private eventListeners = new Map<EventTarget, Map<string, EventListener>>();
  private isDestroyed = false;

  /**
   * 注册资源清理函数
   */
  register(name: string, cleanup: () => void): void {
    if (this.isDestroyed) {
      console.warn('ResourceManager已销毁，无法注册新资源');
      return;
    }

    this.resources.set(name, cleanup);
  }

  /**
   * 注册定时器
   */
  registerTimer(timerId: number): void {
    if (this.isDestroyed) {
      clearTimeout(timerId);
      return;
    }

    this.timers.add(timerId);
  }

  /**
   * 注册间隔器
   */
  registerInterval(intervalId: number): void {
    if (this.isDestroyed) {
      clearInterval(intervalId);
      return;
    }

    this.intervals.add(intervalId);
  }

  /**
   * 注册事件监听器
   */
  registerEventListener(
    target: EventTarget,
    event: string,
    listener: EventListener,
    options?: AddEventListenerOptions
  ): void {
    if (this.isDestroyed) {
      return;
    }

    target.addEventListener(event, listener, options);

    if (!this.eventListeners.has(target)) {
      this.eventListeners.set(target, new Map());
    }
    this.eventListeners.get(target)!.set(event, listener);
  }

  /**
   * 移除特定资源
   */
  unregister(name: string): void {
    const cleanup = this.resources.get(name);
    if (cleanup) {
      try {
        cleanup();
      } catch (error) {
        console.error(`清理资源 ${name} 时发生错误:`, error);
      }
      this.resources.delete(name);
    }
  }

  /**
   * 清理所有资源
   */
  destroy(): void {
    if (this.isDestroyed) {
      return;
    }

    // 清理注册的资源
    for (const [name, cleanup] of this.resources) {
      try {
        cleanup();
      } catch (error) {
        console.error(`清理资源 ${name} 时发生错误:`, error);
      }
    }
    this.resources.clear();

    // 清理定时器
    for (const timerId of this.timers) {
      clearTimeout(timerId);
    }
    this.timers.clear();

    // 清理间隔器
    for (const intervalId of this.intervals) {
      clearInterval(intervalId);
    }
    this.intervals.clear();

    // 清理事件监听器
    for (const [target, listeners] of this.eventListeners) {
      for (const [event, listener] of listeners) {
        try {
          target.removeEventListener(event, listener);
        } catch (error) {
          console.error('移除事件监听器时发生错误:', error);
        }
      }
    }
    this.eventListeners.clear();

    this.isDestroyed = true;
  }

  /**
   * 获取资源统计信息
   */
  getStats(): {
    resources: number;
    timers: number;
    intervals: number;
    eventListeners: number;
  } {
    return {
      resources: this.resources.size,
      timers: this.timers.size,
      intervals: this.intervals.size,
      eventListeners: Array.from(this.eventListeners.values())
        .reduce((total, listeners) => total + listeners.size, 0)
    };
  }
}

/**
 * 全局错误边界实例
 */
export const globalErrorBoundary = new ErrorBoundary({
  name: 'GlobalTranslationSystem',
  logErrors: true,
  onError: (error, context) => {
    // 可以在这里添加错误上报逻辑
    console.error('全局翻译系统错误:', error.message, { context });
  }
});

/**
 * 全局资源管理器实例
 */
export const globalResourceManager = new ResourceManager();

// 在页面卸载时清理全局资源
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    globalErrorBoundary.destroy();
    globalResourceManager.destroy();
  });
}