/**
 * Debug test to reproduce the exact issue from enhanced-injector
 */
import { describe, it, expect } from 'vitest';
import { validateContent } from '../security';

describe('Debug Specific Security Issue', () => {
  it('should pass validation for the exact failing text from logs', () => {
    // This is the exact text that was failing according to the user's logs
    const failingText = 'As your days get busier this time of year, new AI-';
    
    console.log('Testing failing text:', failingText);
    console.log('Text length:', failingText.length);
    
    // This is exactly what enhanced-injector.ts line 320 calls
    const result = validateContent(failingText, 'text', 'normal');
    
    console.log('validateContent result:', result);
    console.log('Expected: true (should be valid)');
    
    expect(result).toBe(true);
  });

  it('should pass validation for text with various formats', () => {
    const testText = 'As your days get busier this time of year, new AI-';
    
    // Test all three security levels
    expect(validateContent(testText, 'text', 'strict')).toBe(true);
    expect(validateContent(testText, 'text', 'normal')).toBe(true);
    expect(validateContent(testText, 'text', 'lenient')).toBe(true);
    
    // Test both formats
    expect(validateContent(testText, 'html', 'normal')).toBe(true);
    expect(validateContent(testText, 'text', 'normal')).toBe(true);
  });

  it('should validate content that might contain HTML entities', () => {
    // Test variations that might be causing issues
    const variations = [
      'As your days get busier this time of year, new AI-',
      'As your days get busier this time of year, new AI-powered',
      'As your days get busier this time of year, new AI- tools help',
      'Text with <a href="#test">link</a> inside',
      'Text with &lt;escaped&gt; HTML'
    ];
    
    variations.forEach((text, index) => {
      console.log(`Testing variation ${index + 1}:`, text);
      const result = validateContent(text, 'text', 'normal');
      console.log(`Result ${index + 1}:`, result);
      expect(result).toBe(true);
    });
  });
});