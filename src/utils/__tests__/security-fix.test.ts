/**
 * Test for security fixes - specifically for <a> tag handling
 */
import { describe, it, expect } from 'vitest';
import { hasMaliciousContent, hasMaliciousTextContent, validateContent, isValidAttribute } from '../security';

describe('Security Fixes for <a> Tag', () => {
  describe('hasMaliciousContent function', () => {
    it('should allow safe <a> tags with href attributes', () => {
      const safeContent = '<a href="https://example.com">safe link</a>';
      const result = hasMaliciousContent(safeContent, false);
      expect(result).toBe(false); // Should be safe
    });

    it('should allow <a> tags with anchor links', () => {
      const anchorContent = '<a href="#section1">go to section</a>';
      const result = hasMaliciousContent(anchorContent, false);
      expect(result).toBe(false); // Should be safe
    });

    it('should allow mixed content with safe <a> tags', () => {
      const mixedContent = '<p>This is a paragraph with <a href="https://example.com">a link</a> inside.</p>';
      const result = hasMaliciousContent(mixedContent, false);
      expect(result).toBe(false); // Should be safe
    });

    it('should still block malicious <a> tags with javascript: protocol', () => {
      const maliciousContent = '<a href="javascript:alert(1)">malicious link</a>';
      const result = hasMaliciousContent(maliciousContent, false);
      expect(result).toBe(true); // Should be blocked
    });

    it('should still block dangerous script tags', () => {
      const scriptContent = '<script>alert("xss")</script>';
      const result = hasMaliciousContent(scriptContent, false);
      expect(result).toBe(true); // Should be blocked
    });

    it('should handle empty or null content safely', () => {
      expect(hasMaliciousContent('', false)).toBe(false);
      expect(hasMaliciousContent(null as any, false)).toBe(false);
      expect(hasMaliciousContent(undefined as any, false)).toBe(false);
    });
  });

  describe('validateContent function', () => {
    it('should validate HTML content with safe <a> tags', () => {
      const safeHtml = '<a href="https://example.com">test link</a>';
      const result = validateContent(safeHtml, 'html', 'normal');
      expect(result).toBe(true); // Should pass validation
    });

    it('should validate complex HTML with multiple safe elements', () => {
      const complexHtml = `
        <div>
          <p>Welcome to our site!</p>
          <a href="https://example.com">Visit our homepage</a>
          <strong>Important:</strong> <em>Please read carefully</em>
        </div>
      `;
      const result = validateContent(complexHtml, 'html', 'normal');
      expect(result).toBe(true); // Should pass validation
    });

    it('should reject HTML with malicious script content', () => {
      const maliciousHtml = '<div><script>alert("xss")</script></div>';
      const result = validateContent(maliciousHtml, 'html', 'normal');
      expect(result).toBe(false); // Should fail validation
    });
  });

  describe('href attribute validation', () => {
    it('should validate safe HTTP URLs', () => {
      const result = isValidAttribute('href', 'https://example.com');
      expect(result).toBe(true);
    });

    it('should validate anchor links', () => {
      const result = isValidAttribute('href', '#section1');
      expect(result).toBe(true);
    });

    it('should validate relative URLs', () => {
      const result = isValidAttribute('href', '/path/to/page');
      expect(result).toBe(true);
    });

    it('should validate mailto links', () => {
      const result = isValidAttribute('href', 'mailto:<EMAIL>');
      expect(result).toBe(true);
    });

    it('should reject javascript: protocol', () => {
      const result = isValidAttribute('href', 'javascript:alert(1)');
      expect(result).toBe(false);
    });

    it('should reject vbscript: protocol', () => {
      const result = isValidAttribute('href', 'vbscript:msgbox(1)');
      expect(result).toBe(false);
    });

    it('should reject extremely long URLs', () => {
      const longUrl = 'https://example.com/' + 'a'.repeat(600);
      const result = isValidAttribute('href', longUrl);
      expect(result).toBe(false);
    });
  });
});

/**
 * Test specifically for the malicious content error fix
 */
describe('Malicious Content Error Fix', () => {
  it('should properly handle the case from the logs', () => {
    // This is the exact case that was failing based on the logs
    const contentWithATag = '<p>Some text with <a href="https://example.com">a link</a></p>';
    
    // Test the core function
    const hasMalicious = hasMaliciousContent(contentWithATag, false);
    expect(hasMalicious).toBe(false); // Should not be flagged as malicious
    
    // Test the validation function (this is what's called by enhanced-injector)
    const isValid = validateContent(contentWithATag, 'html', 'normal');
    expect(isValid).toBe(true); // Should pass validation
  });

  it('should demonstrate the fix for the return logic error', () => {
    // Create content that has an anchor tag
    const testContent = '<a href="#test">test</a>';
    
    // In the old broken logic, this would have incorrectly returned false (safe) 
    // when strictMode was false, even if the tag was deemed unsafe
    // Our fix ensures that unsafe tags correctly return true (malicious)
    
    const result = hasMaliciousContent(testContent, false);
    expect(result).toBe(false); // <a> tag should be safe, so false is correct
    
    // Test with actually malicious content
    const maliciousContent = '<script>alert(1)</script>';
    const maliciousResult = hasMaliciousContent(maliciousContent, false);
    expect(maliciousResult).toBe(true); // Script tag should be malicious, so true is correct
  });

  it('should handle the exact text from the error logs', () => {
    // This is the exact text that was failing according to the logs
    const failingText = 'As your days get busier this time of year, new AI-';
    
    // Test as text format (should pass)
    const textValidation = validateContent(failingText, 'text', 'normal');
    expect(textValidation).toBe(true); // Plain text should be valid
    
    // Test as HTML format (should also pass since it's just plain text)
    const htmlValidation = validateContent(failingText, 'html', 'normal');
    expect(htmlValidation).toBe(true); // Plain text should be valid even when processed as HTML
    
    // Test individual functions
    const textContentCheck = hasMaliciousTextContent(failingText);
    expect(textContentCheck).toBe(false); // Should not be flagged as malicious text
    
    const htmlContentCheck = hasMaliciousContent(failingText, false);
    expect(htmlContentCheck).toBe(false); // Should not be flagged as malicious HTML
    
    // Verify no HTML tags are detected
    const htmlTagMatches = failingText.match(/<[^>]*>/g);
    expect(htmlTagMatches).toBeNull(); // Should not contain any HTML tags
  });
});