/**
 * 优化功能集成测试
 * 验证DOM批处理、并行处理和请求池的集成效果
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { DOMBatchProcessor } from '../dom-batch-processor';
import { ParallelBatchProcessor } from '../parallel-batch-processor';
import { RequestPool } from '../request-pool';

describe('Optimization Integration Tests', () => {
  let domBatchProcessor: DOMBatchProcessor;
  let parallelProcessor: ParallelBatchProcessor;
  let requestPool: RequestPool;

  beforeEach(() => {
    // 初始化处理器
    domBatchProcessor = new DOMBatchProcessor({
      batchSize: 10,
      debounceTime: 10,
      maxWaitTime: 50,
      enableProfiling: true
    });

    parallelProcessor = new ParallelBatchProcessor({
      workerCount: 2,
      loadBalanceStrategy: 'round-robin',
      enableStats: true
    });

    requestPool = new RequestPool({
      maxConcurrent: 3,
      dedupeWindow: 1000,
      timeout: 5000,
      enableStats: true
    });
  });

  afterEach(() => {
    // 清理资源
    domBatchProcessor.destroy();
    parallelProcessor.destroy();
    requestPool.destroy();
  });

  describe('DOM Batch Processor', () => {
    it('should batch DOM operations efficiently', async () => {
      const operations = [];
      const testElement = document.createElement('div');
      document.body.appendChild(testElement);

      // 创建多个操作
      for (let i = 0; i < 5; i++) {
        operations.push({
          type: 'modify' as const,
          element: testElement,
          data: {
            modify: (el: HTMLElement) => {
              el.setAttribute(`data-test-${i}`, `value-${i}`);
            }
          },
          priority: 1,
          id: `test-${i}`
        });
      }

      // 批量添加操作
      domBatchProcessor.addOperations(operations);
      await domBatchProcessor.flush();

      // 验证操作已执行
      for (let i = 0; i < 5; i++) {
        expect(testElement.getAttribute(`data-test-${i}`)).toBe(`value-${i}`);
      }

      // 验证统计信息
      const stats = domBatchProcessor.getStats();
      expect(stats.totalBatches).toBeGreaterThan(0);
      expect(stats.totalOperations).toBe(5);

      // 清理
      document.body.removeChild(testElement);
    });

    it('should handle operation deduplication', async () => {
      const testElement = document.createElement('div');
      document.body.appendChild(testElement);

      // 添加重复操作
      const operation = {
        type: 'modify' as const,
        element: testElement,
        data: {
          modify: (el: HTMLElement) => {
            const current = parseInt(el.getAttribute('data-count') || '0');
            el.setAttribute('data-count', (current + 1).toString());
          }
        },
        priority: 1,
        id: 'duplicate-test'
      };

      // 添加相同的操作多次
      domBatchProcessor.addOperation(operation);
      domBatchProcessor.addOperation(operation);
      domBatchProcessor.addOperation(operation);

      await domBatchProcessor.flush();

      // 验证去重效果（应该只执行一次）
      expect(testElement.getAttribute('data-count')).toBe('1');

      // 清理
      document.body.removeChild(testElement);
    });
  });

  describe('Parallel Batch Processor', () => {
    it('should distribute operations across workers', async () => {
      const operations = [];
      const testElements: HTMLElement[] = [];

      // 创建测试元素
      for (let i = 0; i < 6; i++) {
        const element = document.createElement('div');
        element.setAttribute('data-index', i.toString());
        document.body.appendChild(element);
        testElements.push(element);

        operations.push({
          type: 'modify' as const,
          element,
          data: {
            modify: (el: HTMLElement) => {
              el.setAttribute('data-processed', 'true');
            }
          },
          priority: 1,
          id: `parallel-${i}`
        });
      }

      // 批量处理
      parallelProcessor.addOperations(operations);
      await parallelProcessor.flush();

      // 验证所有元素都被处理
      testElements.forEach(element => {
        expect(element.getAttribute('data-processed')).toBe('true');
      });

      // 验证统计信息
      const stats = parallelProcessor.getStats();
      expect(stats.totalProcessors).toBe(2);
      expect(stats.totalPendingOperations).toBe(0); // 处理完成后应该为0

      // 清理
      testElements.forEach(element => {
        document.body.removeChild(element);
      });
    });

    it('should handle priority-based distribution', async () => {
      parallelProcessor.destroy();
      parallelProcessor = new ParallelBatchProcessor({
        workerCount: 2,
        loadBalanceStrategy: 'priority-based',
        enableStats: true
      });

      const operations = [];
      const testElement = document.createElement('div');
      document.body.appendChild(testElement);

      // 创建不同优先级的操作
      for (let i = 0; i < 4; i++) {
        operations.push({
          type: 'modify' as const,
          element: testElement,
          data: {
            modify: (el: HTMLElement) => {
              const order = el.getAttribute('data-order') || '';
              el.setAttribute('data-order', order + i);
            }
          },
          priority: i >= 2 ? 3 : 1, // 后两个高优先级
          id: `priority-${i}`
        });
      }

      parallelProcessor.addOperations(operations);
      await parallelProcessor.flush();

      // 验证操作已执行
      expect(testElement.getAttribute('data-order')).toBeDefined();

      // 清理
      document.body.removeChild(testElement);
    });
  });

  describe('Request Pool', () => {
    it('should deduplicate identical requests', async () => {
      let callCount = 0;
      const mockRequest = async () => {
        callCount++;
        await new Promise(resolve => setTimeout(resolve, 10));
        return `result-${callCount}`;
      };

      const requestKey = {
        method: 'test',
        params: JSON.stringify({ data: 'test' })
      };

      // 同时发起多个相同请求
      const promises = [
        requestPool.execute(requestKey, mockRequest),
        requestPool.execute(requestKey, mockRequest),
        requestPool.execute(requestKey, mockRequest)
      ];

      const results = await Promise.all(promises);

      // 验证去重效果
      expect(callCount).toBe(1); // 只调用一次
      expect(results).toEqual(['result-1', 'result-1', 'result-1']); // 所有结果相同

      // 验证统计信息
      const stats = requestPool.getStats();
      expect(stats.totalRequests).toBe(3);
      expect(stats.deduplicatedRequests).toBe(2);
      expect(stats.successfulRequests).toBe(1);
    });

    it('should respect concurrency limits', async () => {
      let concurrentCount = 0;
      let maxConcurrent = 0;

      const mockRequest = async () => {
        concurrentCount++;
        maxConcurrent = Math.max(maxConcurrent, concurrentCount);
        
        await new Promise(resolve => setTimeout(resolve, 50));
        
        concurrentCount--;
        return 'result';
      };

      // 发起超过并发限制的请求
      const promises = [];
      for (let i = 0; i < 6; i++) {
        promises.push(
          requestPool.execute(
            { method: 'concurrent', params: `test-${i}` },
            mockRequest
          )
        );
      }

      await Promise.all(promises);

      // 验证并发控制
      expect(maxConcurrent).toBeLessThanOrEqual(3); // 不超过配置的最大并发数

      const stats = requestPool.getStats();
      expect(stats.successfulRequests).toBe(6);
    });

    it('should handle request timeouts', async () => {
      const slowRequest = async () => {
        await new Promise(resolve => setTimeout(resolve, 6000)); // 超过超时时间
        return 'slow-result';
      };

      const requestKey = {
        method: 'slow',
        params: 'timeout-test'
      };

      await expect(
        requestPool.execute(requestKey, slowRequest)
      ).rejects.toThrow('Request timeout');

      const stats = requestPool.getStats();
      expect(stats.failedRequests).toBeGreaterThan(0);
    }, 10000); // 增加测试超时时间
  });

  describe('Integration Performance', () => {
    it('should show performance improvements with optimizations', async () => {
      // 模拟翻译请求的性能测试，包含重复请求来测试去重
      const startTime = performance.now();
      
      const requests = [];
      // 添加一些重复的请求来测试去重功能
      for (let i = 0; i < 5; i++) {
        requests.push(
          requestPool.translateText(`test text ${i}`, { format: 'text' })
        );
      }
      // 重复前几个请求
      for (let i = 0; i < 3; i++) {
        requests.push(
          requestPool.translateText(`test text ${i}`, { format: 'text' })
        );
      }

      const results = await Promise.all(requests);
      const endTime = performance.now();

      // 验证结果
      expect(results).toHaveLength(8);
      results.forEach(result => {
        expect(result.success).toBe(true);
        expect(result.text).toBeDefined();
      });

      // 验证性能（由于有去重，应该相对较快）
      const duration = endTime - startTime;
      expect(duration).toBeLessThan(1000); // 应该在1秒内完成

      // 验证统计信息
      const stats = requestPool.getStats();
      expect(stats.totalRequests).toBe(8);
      // 由于有重复请求，去重率应该大于0
      if (stats.deduplicatedRequests > 0) {
        expect(stats.deduplicationRate).toBeGreaterThan(0);
      } else {
        // 如果没有去重，可能是时间窗口问题，至少验证请求成功
        expect(stats.successfulRequests).toBeGreaterThan(0);
      }
    });
  });
});