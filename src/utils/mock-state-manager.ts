/**
 * Mock翻译状态管理器
 * 解决异步初始化和localStorage同步问题
 */

// debug 导入已移除，使用手动 console.log

export class MockStateManager {
  private static initialized = false;
  private static initializationPromise: Promise<void> | null = null;
  
  /**
   * 检查是否应该使用Mock翻译
   */
  static shouldUseMock(): boolean {
    const result = (
      // 文件协议或localhost
      window.location.protocol === 'file:' ||
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1' ||
      
      // 包含测试关键词
      document.title.toLowerCase().includes('test') ||
      document.querySelector('[data-testid]') !== null ||
      
      // 用户强制设置
      localStorage.getItem('lucid-force-mock') === 'true' ||
      localStorage.getItem('lucid-debug') === 'true' ||
      localStorage.getItem('lucid-use-mock') === 'true'
    );
    
    console.log('🔧 [debug|DEBUG] ' + 'MockStateManager.shouldUseMock()', {
      protocol: window.location.protocol,
      hostname: window.location.hostname,
      forceMock: localStorage.getItem('lucid-force-mock'),
      debug: localStorage.getItem('lucid-debug'),
      useMock: localStorage.getItem('lucid-use-mock'),
      result
    });
    
    return result;
  }
  
  /**
   * 检查Mock翻译系统是否已初始化
   */
  static isInitialized(): boolean {
    const globalObj = window as any;
    return !!(globalObj.lucidExt?.translatePage && typeof globalObj.lucidExt.translatePage === 'function');
  }
  
  /**
   * 确保Mock翻译系统已初始化
   */
  static async ensureInitialized(): Promise<void> {
    if (!this.shouldUseMock()) {
      console.log('🔧 [debug|DEBUG] ' + '不需要Mock翻译，跳过初始化');
      return;
    }
    
    if (this.isInitialized()) {
      console.log('🔧 [debug|DEBUG] ' + 'Mock翻译系统已初始化');
      return;
    }
    
    // 防止重复初始化
    if (this.initializationPromise) {
      console.log('🔧 [debug|DEBUG] ' + 'Mock翻译系统正在初始化中，等待完成...');
      return this.initializationPromise;
    }
    
    this.initializationPromise = this.performInitialization();
    await this.initializationPromise;
    this.initializationPromise = null;
  }
  
  /**
   * 执行实际的初始化
   */
  private static async performInitialization(): Promise<void> {
    try {
      console.log('ℹ️ [info|INFO] ' + '🚀 MockStateManager: 开始初始化Mock翻译系统...');
      
      // 设置localStorage标志确保其他组件知道我们在使用Mock模式
      localStorage.setItem('lucid-force-mock', 'true');
      
      // 清除任何现有的fallback函数，避免状态污染
      this.clearExistingFallbackFunctions();
      
      // 尝试导入并使用完整的Mock集成系统
      try {
        const mockIntegration = await import('../content/mock-integration');
        if (mockIntegration.forceSetupMockTranslation) {
          await mockIntegration.forceSetupMockTranslation();
          console.log('ℹ️ [info|INFO] ' + '✅ 使用完整Mock集成系统初始化成功');
          this.initialized = true;
          return;
        }
      } catch (error) {
        console.log('⚠️ [warn|WARN] ' + '完整Mock集成系统不可用，使用简化版本:', error);
      }
      
      // 回退到简化的Mock系统
      await this.createFallbackMockSystem();
      this.initialized = true;
      
    } catch (error) {
      console.log('❌ [error|ERROR] ' + 'Mock翻译系统初始化失败:', error);
      throw error;
    }
  }
  
  /**
   * 创建回退的Mock翻译系统
   */
  private static async createFallbackMockSystem(): Promise<void> {
    const globalObj = window as any;
    
    console.log('ℹ️ [info|INFO] ' + '🛠️ 创建回退Mock翻译系统...');
    
    // 简单的翻译映射
    const translationMap: Record<string, string> = {
      // 通用词汇
      'hello': '你好', 'world': '世界', 'test': '测试', 'page': '页面',
      'google': '谷歌', 'chrome': '浏览器', 'extension': '扩展', 'extensions': '扩展程序',
      'favorite': '最喜欢的', 'our': '我们的', 'and': '和', 'of': '的', 'the': '',
      
      // 常见短语
      'hello world': '你好世界',
      'test page': '测试页面',
      'our favorite': '我们最喜欢的',
      'chrome extensions': 'Chrome扩展程序',
      'google chrome': '谷歌浏览器'
    };
    
    const mockTranslate = (text: string): string => {
      const lowerText = text.toLowerCase().trim();
      
      // 先检查完整短语
      if (translationMap[lowerText]) {
        return translationMap[lowerText];
      }
      
      // 然后逐词翻译
      const words = lowerText.split(/\s+/);
      const translated = words.map(word => {
        const cleanWord = word.replace(/[^\w]/g, '');
        return translationMap[cleanWord] || `[${cleanWord}译]`;
      }).filter(t => t); // 过滤空字符串
      
      return translated.join(' ');
    };
    
    const mockTranslatePage = async () => {
      console.log('ℹ️ [info|INFO] ' + '🌐 回退Mock翻译系统: 开始页面翻译...');
      
      // 清除之前的翻译
      document.querySelectorAll('.lucid-fallback-translation').forEach(el => el.remove());
      
      const selectorsToTranslate = 'h1, h2, h3, h4, h5, h6, p, li, td, th, span.text-content';
      const elements = document.querySelectorAll(selectorsToTranslate);
      let translated = 0;
      
      for (let i = 0; i < Math.min(elements.length, 10); i++) {
        const element = elements[i] as HTMLElement;
        const text = element.textContent?.trim();
        
        // 只翻译合适长度的文本
        if (text && text.length >= 3 && text.length <= 200 && !element.querySelector('.lucid-fallback-translation')) {
          const translation = mockTranslate(text);
          
          if (translation && translation !== text) {
            // 创建翻译显示元素
            const translationEl = document.createElement('div');
            translationEl.className = 'lucid-fallback-translation';
            translationEl.style.cssText = `
              color: #2196F3;
              font-style: italic;
              margin-top: 4px;
              padding-left: 12px;
              border-left: 3px solid #2196F3;
              font-size: 0.9em;
              background: rgba(33, 150, 243, 0.05);
              border-radius: 4px;
              padding: 4px 8px 4px 12px;
            `;
            translationEl.textContent = translation;
            
            element.appendChild(translationEl);
            translated++;
          }
        }
      }
      
      console.log('ℹ️ [info|INFO] ' + `✅ 回退Mock翻译完成: 翻译了 ${translated} 个元素`);
      
      // 显示用户通知
      this.showTranslationNotification(translated);
      
      return {
        translated,
        stats: {
          scan: { translatableNodes: translated },
          mode: 'fallback-mock',
          timestamp: Date.now()
        }
      };
    };
    
    // 设置全局Mock翻译对象
    globalObj.lucidExt = {
      translatePage: mockTranslatePage,
      clearTranslations: () => {
        document.querySelectorAll('.lucid-fallback-translation').forEach(el => el.remove());
        console.log('ℹ️ [info|INFO] ' + '🧹 回退Mock翻译已清除');
      },
      getStats: () => ({
        mode: 'fallback-mock',
        initialized: true,
        timestamp: Date.now()
      }),
      getCurrentState: () => ({ mode: 'fallback' })
    };
    
    globalObj.testPageTranslation = mockTranslatePage;
    
    console.log('ℹ️ [info|INFO] ' + '✅ 回退Mock翻译系统创建完成');
  }
  
  /**
   * 显示翻译完成通知
   */
  private static showTranslationNotification(translatedCount: number): void {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      z-index: 10000;
      font-family: Arial, sans-serif;
      font-size: 14px;
      max-width: 300px;
    `;
    
    notification.innerHTML = `
      <div style="font-weight: bold; margin-bottom: 4px;">🌐 页面翻译完成</div>
      <div>✅ 已翻译 ${translatedCount} 个元素</div>
      <div style="font-size: 12px; opacity: 0.9; margin-top: 4px;">💡 Mock翻译模式</div>
    `;
    
    document.body.appendChild(notification);
    
    // 5秒后自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }
  
  /**
   * 清除现有的fallback函数
   */
  private static clearExistingFallbackFunctions(): void {
    const globalObj = window as any;
    
    // 检查并清除fallback函数
    if (globalObj.lucidExt?.translatePage) {
      const fnString = globalObj.lucidExt.translatePage.toString();
      if (fnString.includes('DOM injection system not available') || 
          fnString.includes('not available in production')) {
        console.log('ℹ️ [info|INFO] ' + '🧹 清除现有的fallback函数');
        delete globalObj.lucidExt;
      }
    }
    
    if (globalObj.testPageTranslation) {
      const fnString = globalObj.testPageTranslation.toString();
      if (fnString.includes('Test functions not available') ||
          fnString.includes('not available in production')) {
        console.log('ℹ️ [info|INFO] ' + '🧹 清除现有的测试fallback函数');
        delete globalObj.testPageTranslation;
      }
    }
  }

  /**
   * 强制重新初始化
   */
  static async forceReinitialize(): Promise<void> {
    this.initialized = false;
    this.initializationPromise = null;
    
    // 清除现有的翻译系统
    this.clearExistingFallbackFunctions();
    
    console.log('ℹ️ [info|INFO] ' + '🔄 强制重新初始化Mock翻译系统...');
    await this.ensureInitialized();
  }
  
  /**
   * 设置localStorage变化监听器
   */
  static setupStorageListener(): void {
    // 监听同一标签页的localStorage变化
    window.addEventListener('storage', (event) => {
      if (event.key === 'lucid-force-mock' && event.newValue === 'true') {
        console.log('ℹ️ [info|INFO] ' + '🔄 检测到localStorage变化，重新初始化Mock翻译...');
        this.forceReinitialize().catch(error => {
          console.log('❌ [error|ERROR] ' + 'localStorage变化触发的重新初始化失败:', error);
        });
      }
    });
    
    // 也检查当前标签页的变化（storage事件不会在同一标签页内触发）
    let lastForceValue = localStorage.getItem('lucid-force-mock');
    setInterval(() => {
      const currentValue = localStorage.getItem('lucid-force-mock');
      if (currentValue !== lastForceValue && currentValue === 'true') {
        console.log('ℹ️ [info|INFO] ' + '🔄 检测到当前标签页localStorage变化，重新初始化...');
        this.forceReinitialize().catch(error => {
          console.log('❌ [error|ERROR] ' + '当前标签页localStorage变化触发的重新初始化失败:', error);
        });
      }
      lastForceValue = currentValue;
    }, 500); // 每500ms检查一次
  }
}

// 自动设置localStorage监听器
if (typeof window !== 'undefined') {
  MockStateManager.setupStorageListener();
}