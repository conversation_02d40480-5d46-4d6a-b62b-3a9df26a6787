/**
 * 资源管理器
 * 统一管理扩展中的所有资源，防止内存泄漏
 */

import { errorLogger, ErrorContext } from './error-logger';

export interface Destroyable {
  destroy(): void | Promise<void>;
}

export interface ResourceMetadata {
  name: string;
  type: 'timer' | 'interval' | 'listener' | 'object' | 'promise';
  created: number;
  component?: string;
}

/**
 * 资源管理器
 * 用于跟踪和清理所有需要销毁的资源
 */
export class ResourceManager {
  private static instance: ResourceManager;
  private resources = new Map<string, { resource: any; metadata: ResourceMetadata }>();
  private timers = new Set<number>();
  private intervals = new Set<number>();
  private listeners = new Map<string, { element: EventTarget; event: string; handler: EventListener }>();
  private objects = new Set<Destroyable>();

  static getInstance(): ResourceManager {
    if (!ResourceManager.instance) {
      ResourceManager.instance = new ResourceManager();
    }
    return ResourceManager.instance;
  }

  /**
   * 注册定时器
   */
  registerTimer(timerId: number, name: string, component?: string): number {
    this.timers.add(timerId);
    this.resources.set(`timer_${timerId}`, {
      resource: timerId,
      metadata: {
        name,
        type: 'timer',
        created: Date.now(),
        component
      }
    });
    return timerId;
  }

  /**
   * 注册间隔定时器
   */
  registerInterval(intervalId: number, name: string, component?: string): number {
    this.intervals.add(intervalId);
    this.resources.set(`interval_${intervalId}`, {
      resource: intervalId,
      metadata: {
        name,
        type: 'interval',
        created: Date.now(),
        component
      }
    });
    return intervalId;
  }

  /**
   * 注册事件监听器
   */
  registerListener(
    element: EventTarget,
    event: string,
    handler: EventListener,
    name: string,
    component?: string
  ): string {
    const key = `${name}_${Date.now()}_${Math.random()}`;
    this.listeners.set(key, { element, event, handler });
    this.resources.set(`listener_${key}`, {
      resource: { element, event, handler },
      metadata: {
        name,
        type: 'listener',
        created: Date.now(),
        component
      }
    });
    return key;
  }

  /**
   * 注册可销毁对象
   */
  registerObject(obj: Destroyable, name: string, component?: string): Destroyable {
    this.objects.add(obj);
    this.resources.set(`object_${name}_${Date.now()}`, {
      resource: obj,
      metadata: {
        name,
        type: 'object',
        created: Date.now(),
        component
      }
    });
    return obj;
  }

  /**
   * 清理定时器
   */
  clearTimer(timerId: number): void {
    if (this.timers.has(timerId)) {
      clearTimeout(timerId);
      this.timers.delete(timerId);
      this.resources.delete(`timer_${timerId}`);
    }
  }

  /**
   * 清理间隔定时器
   */
  clearInterval(intervalId: number): void {
    if (this.intervals.has(intervalId)) {
      clearInterval(intervalId);
      this.intervals.delete(intervalId);
      this.resources.delete(`interval_${intervalId}`);
    }
  }

  /**
   * 移除事件监听器
   */
  removeListener(key: string): void {
    const listener = this.listeners.get(key);
    if (listener) {
      try {
        listener.element.removeEventListener(listener.event, listener.handler);
        this.listeners.delete(key);
        this.resources.delete(`listener_${key}`);
      } catch (error) {
        errorLogger.warn('Failed to remove event listener', error as Error, {
          method: 'ResourceManager.removeListener',
          component: 'ResourceManager',
          data: { key }
        });
      }
    }
  }

  /**
   * 销毁对象
   */
  destroyObject(obj: Destroyable): void {
    if (this.objects.has(obj)) {
      try {
        const result = obj.destroy();
        if (result instanceof Promise) {
          result.catch(error => {
            errorLogger.error('Failed to destroy object asynchronously', error, {
              method: 'ResourceManager.destroyObject',
              component: 'ResourceManager'
            });
          });
        }
        this.objects.delete(obj);
        
        // 从resources中移除对应的条目
        for (const [key, value] of this.resources.entries()) {
          if (value.resource === obj) {
            this.resources.delete(key);
            break;
          }
        }
      } catch (error) {
        errorLogger.error('Failed to destroy object', error as Error, {
          method: 'ResourceManager.destroyObject',
          component: 'ResourceManager'
        });
      }
    }
  }

  /**
   * 清理特定组件的所有资源
   */
  cleanupComponent(componentName: string): void {
    const context: ErrorContext = {
      method: 'ResourceManager.cleanupComponent',
      component: 'ResourceManager',
      data: { componentName }
    };

    try {
      const toCleanup: string[] = [];
      
      for (const [key, value] of this.resources.entries()) {
        if (value.metadata.component === componentName) {
          toCleanup.push(key);
        }
      }

      for (const key of toCleanup) {
        const resource = this.resources.get(key);
        if (!resource) continue;

        try {
          switch (resource.metadata.type) {
            case 'timer':
              this.clearTimer(resource.resource as number);
              break;
            case 'interval':
              this.clearInterval(resource.resource as number);
              break;
            case 'listener':
              const listenerKey = key.replace('listener_', '');
              this.removeListener(listenerKey);
              break;
            case 'object':
              this.destroyObject(resource.resource as Destroyable);
              break;
          }
        } catch (error) {
          errorLogger.warn(`Failed to cleanup resource: ${key}`, error as Error, context);
        }
      }

      errorLogger.info(`Cleaned up ${toCleanup.length} resources for component: ${componentName}`, context);
    } catch (error) {
      errorLogger.error(`Failed to cleanup component: ${componentName}`, error as Error, context);
    }
  }

  /**
   * 清理所有资源
   */
  cleanupAll(): void {
    const context: ErrorContext = {
      method: 'ResourceManager.cleanupAll',
      component: 'ResourceManager'
    };

    try {
      let cleanedCount = 0;

      // 清理所有定时器
      for (const timerId of this.timers) {
        try {
          clearTimeout(timerId);
          cleanedCount++;
        } catch (error) {
          errorLogger.warn(`Failed to clear timer: ${timerId}`, error as Error, context);
        }
      }
      this.timers.clear();

      // 清理所有间隔定时器
      for (const intervalId of this.intervals) {
        try {
          clearInterval(intervalId);
          cleanedCount++;
        } catch (error) {
          errorLogger.warn(`Failed to clear interval: ${intervalId}`, error as Error, context);
        }
      }
      this.intervals.clear();

      // 移除所有事件监听器
      for (const [key, listener] of this.listeners) {
        try {
          listener.element.removeEventListener(listener.event, listener.handler);
          cleanedCount++;
        } catch (error) {
          errorLogger.warn(`Failed to remove listener: ${key}`, error as Error, context);
        }
      }
      this.listeners.clear();

      // 销毁所有对象
      for (const obj of this.objects) {
        try {
          const result = obj.destroy();
          if (result instanceof Promise) {
            result.catch(error => {
              errorLogger.error('Failed to destroy object during cleanup', error, context);
            });
          }
          cleanedCount++;
        } catch (error) {
          errorLogger.warn('Failed to destroy object during cleanup', error as Error, context);
        }
      }
      this.objects.clear();

      // 清理资源记录
      this.resources.clear();

      errorLogger.info(`ResourceManager cleanup completed. Cleaned ${cleanedCount} resources`, context);
    } catch (error) {
      errorLogger.error('Failed to cleanup all resources', error as Error, context);
    }
  }

  /**
   * 获取资源统计信息
   */
  getStats(): {
    timers: number;
    intervals: number;
    listeners: number;
    objects: number;
    total: number;
    oldestResource?: { name: string; age: number; component?: string };
  } {
    let oldestResource: { name: string; age: number; component?: string } | undefined;
    let oldestTime = Date.now();

    for (const [key, value] of this.resources.entries()) {
      if (value.metadata.created < oldestTime) {
        oldestTime = value.metadata.created;
        oldestResource = {
          name: value.metadata.name,
          age: Date.now() - value.metadata.created,
          component: value.metadata.component
        };
      }
    }

    return {
      timers: this.timers.size,
      intervals: this.intervals.size,
      listeners: this.listeners.size,
      objects: this.objects.size,
      total: this.resources.size,
      oldestResource
    };
  }

  /**
   * 检查是否有长时间运行的资源（可能的内存泄漏）
   */
  checkForLeaks(maxAge: number = 5 * 60 * 1000): Array<{
    key: string;
    name: string;
    type: string;
    age: number;
    component?: string;
  }> {
    const now = Date.now();
    const suspiciousResources: Array<{
      key: string;
      name: string;
      type: string;
      age: number;
      component?: string;
    }> = [];

    for (const [key, value] of this.resources.entries()) {
      const age = now - value.metadata.created;
      if (age > maxAge) {
        suspiciousResources.push({
          key,
          name: value.metadata.name,
          type: value.metadata.type,
          age,
          component: value.metadata.component
        });
      }
    }

    if (suspiciousResources.length > 0) {
      errorLogger.warn(`Found ${suspiciousResources.length} potentially leaked resources`, undefined, {
        method: 'ResourceManager.checkForLeaks',
        component: 'ResourceManager',
        data: { suspiciousResources: suspiciousResources.slice(0, 5) } // 只记录前5个
      });
    }

    return suspiciousResources;
  }
}

// 导出单例实例
export const resourceManager = ResourceManager.getInstance();

// 页面卸载时自动清理所有资源
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    resourceManager.cleanupAll();
  });
}