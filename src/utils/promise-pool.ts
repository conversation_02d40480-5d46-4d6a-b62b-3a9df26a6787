/**
 * Promise并发控制池
 * 提供并发数量限制，避免API速率限制和浏览器资源耗尽
 * 支持智能重试、令牌桶限流、优先级队列和动态调整
 */

// debug 导入已移除，使用手动 console.log

/**
 * 任务优先级
 */
export enum TaskPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  CRITICAL = 4
}

/**
 * 并发控制器配置
 */
export interface ConcurrencyConfig {
  /** 最大并发数 */
  maxConcurrent: number;
  /** 每秒请求限制 */
  rateLimitPerSecond?: number;
  /** 请求间隔（毫秒） */
  delayBetweenRequests?: number;
  /** 失败重试次数 */
  retryCount?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 任务超时时间（毫秒） */
  taskTimeout?: number;
  /** 是否启用自适应并发调整 */
  enableAdaptiveConcurrency?: boolean;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * 任务定义
 */
export interface Task<T = any> {
  /** 任务唯一标识 */
  id: string;
  /** 任务优先级 */
  priority?: TaskPriority;
  /** 任务执行函数 */
  execute: () => Promise<T>;
  /** 任务超时时间（覆盖全局配置） */
  timeout?: number;
  /** 重试次数（覆盖全局配置） */
  retryCount?: number;
  /** 任务元数据 */
  metadata?: Record<string, any>;
}

/**
 * 任务执行结果
 */
export interface TaskResult<T> {
  /** 任务ID */
  id: string;
  /** 任务索引 */
  index: number;
  /** 执行结果 */
  result?: T;
  /** 错误信息 */
  error?: Error;
  /** 执行时间（毫秒） */
  duration: number;
  /** 重试次数 */
  retryCount: number;
  /** 任务优先级 */
  priority: TaskPriority;
  /** 开始时间 */
  startTime: number;
  /** 结束时间 */
  endTime: number;
}

/**
 * 并发控制统计信息
 */
export interface ConcurrencyStats {
  /** 总任务数 */
  totalTasks: number;
  /** 已完成任务数 */
  completedTasks: number;
  /** 成功任务数 */
  successfulTasks: number;
  /** 失败任务数 */
  failedTasks: number;
  /** 当前并发数 */
  activeTasks: number;
  /** 平均执行时间 */
  averageExecutionTime: number;
  /** 成功率 */
  successRate: number;
  /** 当前队列长度 */
  queueLength: number;
}

/**
 * 并发控制器类
 */
export class ConcurrencyController {
  private config: Required<ConcurrencyConfig>;
  private taskQueue: Task[] = [];
  private activeTasks = new Map<string, Promise<any>>();
  private rateLimiter?: RateLimiter;
  private stats: ConcurrencyStats;
  private abortController = new AbortController();
  
  private totalExecutionTime = 0;
  private currentConcurrency: number;

  constructor(config: ConcurrencyConfig) {
    this.config = {
      rateLimitPerSecond: config.rateLimitPerSecond || 10,
      delayBetweenRequests: config.delayBetweenRequests || 0,
      retryCount: config.retryCount || 2,
      retryDelay: config.retryDelay || 1000,
      taskTimeout: config.taskTimeout || 30000,
      enableAdaptiveConcurrency: config.enableAdaptiveConcurrency || false,
      debug: config.debug || false,
      ...config
    };

    this.currentConcurrency = this.config.maxConcurrent;
    
    // 初始化速率限制器
    if (this.config.rateLimitPerSecond > 0) {
      this.rateLimiter = new RateLimiter(this.config.rateLimitPerSecond);
    }

    // 初始化统计信息
    this.stats = this.initializeStats();

    this.log('ConcurrencyController initialized', { config: this.config });
  }

  /**
   * 添加任务到队列
   */
  addTask<T>(task: Task<T>): void {
    this.taskQueue.push({
      priority: TaskPriority.NORMAL,
      ...task
    });
    
    // 按优先级排序
    this.taskQueue.sort((a, b) => (b.priority || 0) - (a.priority || 0));
    this.updateStats();
    this.processQueue();
  }

  /**
   * 批量添加任务
   */
  addTasks<T>(tasks: Task<T>[]): void {
    tasks.forEach(task => {
      this.taskQueue.push({
        priority: TaskPriority.NORMAL,
        ...task
      });
    });
    
    // 按优先级排序
    this.taskQueue.sort((a, b) => (b.priority || 0) - (a.priority || 0));
    this.updateStats();
    this.processQueue();
  }

  /**
   * 执行所有任务
   */
  async executeAll<T>(tasks: Task<T>[]): Promise<TaskResult<T>[]> {
    return new Promise((resolve, reject) => {
      const results: TaskResult<T>[] = [];
      let completedCount = 0;
      
      if (tasks.length === 0) {
        resolve([]);
        return;
      }

      // 监听任务完成
      const onTaskComplete = (result: TaskResult<T>) => {
        results.push(result);
        completedCount++;
        
        if (completedCount === tasks.length) {
          resolve(results.sort((a, b) => a.index - b.index));
        }
      };

      // 添加任务到队列
      tasks.forEach((task, index) => {
        const enhancedTask: Task<T> = {
          ...task,
          execute: async () => {
            try {
              const result = await task.execute();
              const taskResult: TaskResult<T> = {
                id: task.id,
                index,
                result,
                duration: 0,
                retryCount: 0,
                priority: task.priority || TaskPriority.NORMAL,
                startTime: Date.now(),
                endTime: Date.now()
              };
              onTaskComplete(taskResult);
              return result;
            } catch (error) {
              const taskResult: TaskResult<T> = {
                id: task.id,
                index,
                error: error as Error,
                duration: 0,
                retryCount: 0,
                priority: task.priority || TaskPriority.NORMAL,
                startTime: Date.now(),
                endTime: Date.now()
              };
              onTaskComplete(taskResult);
              throw error;
            }
          }
        };
        
        this.addTask(enhancedTask);
      });
    });
  }

  /**
   * 处理任务队列
   */
  private async processQueue(): Promise<void> {
    while (this.taskQueue.length > 0 && 
           this.activeTasks.size < this.currentConcurrency &&
           !this.abortController.signal.aborted) {
      
      const task = this.taskQueue.shift();
      if (!task) break;

      // 等待速率限制
      if (this.rateLimiter) {
        await this.rateLimiter.waitForToken();
      }

      // 执行任务
      const promise = this.executeTaskWithRetry(task);
      this.activeTasks.set(task.id, promise);

      // 清理完成的任务
      promise.finally(() => {
        this.activeTasks.delete(task.id);
        this.updateStats();
        this.processQueue(); // 继续处理队列
      });
    }
  }

  /**
   * 执行单个任务（带重试和超时）
   */
  private async executeTaskWithRetry<T>(task: Task<T>): Promise<T> {
    const startTime = performance.now();
    let lastError: Error | undefined;
    let retryCount = 0;
    const maxRetries = task.retryCount ?? this.config.retryCount;
    const timeout = task.timeout ?? this.config.taskTimeout;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          const delay = this.config.retryDelay * Math.pow(2, attempt - 1); // 指数退避
          await this.delay(delay);
          retryCount++;
          this.log(`Retrying task ${task.id}, attempt ${attempt + 1}`);
        }

        // 添加超时控制
        const result = await Promise.race([
          task.execute(),
          this.createTimeoutPromise<T>(timeout)
        ]) as T;

        const duration = performance.now() - startTime;
        this.totalExecutionTime += duration;
        this.stats.successfulTasks++;
        this.stats.completedTasks++;

        this.log(`Task ${task.id} completed successfully`, { 
          duration: `${duration.toFixed(2)}ms`,
          retryCount 
        });

        return result;

      } catch (error) {
        lastError = error as Error;
        
        // 判断是否应该重试
        if (!this.shouldRetry(error as Error) || attempt === maxRetries) {
          break;
        }
      }
    }

    // 任务最终失败
    const duration = performance.now() - startTime;
    this.totalExecutionTime += duration;
    this.stats.failedTasks++;
    this.stats.completedTasks++;

    this.log(`Task ${task.id} failed after ${retryCount} retries`, { 
      error: lastError?.message,
      duration: `${duration.toFixed(2)}ms`
    });

    throw lastError || new Error('Task failed');
  }

  /**
   * 创建超时Promise
   */
  private createTimeoutPromise<T>(timeout: number): Promise<T> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Task timeout after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * 判断错误是否应该重试
   */
  private shouldRetry(error: Error): boolean {
    // 明确不应重试的错误
    const nonRetryablePatterns = [
      'abort',
      'unauthorized',
      'forbidden', 
      'not found',
      'bad request',
      'invalid',
      'malformed'
    ];

    // 可以重试的错误
    const retryablePatterns = [
      'timeout',
      'network',
      'connection',
      'rate limit',
      'too many requests',
      'service unavailable',
      'internal server error',
      'bad gateway',
      'gateway timeout'
    ];

    const errorMessage = error.message.toLowerCase();
    
    // 优先检查不可重试的错误
    if (nonRetryablePatterns.some(pattern => errorMessage.includes(pattern))) {
      return false;
    }

    // 检查可重试的错误
    return retryablePatterns.some(pattern => errorMessage.includes(pattern));
  }

  /**
   * 停止所有任务
   */
  abort(): void {
    this.abortController.abort();
    this.taskQueue = [];
    this.activeTasks.clear();
    this.log('All tasks aborted');
  }

  /**
   * 获取统计信息
   */
  getStats(): ConcurrencyStats {
    return {
      ...this.stats,
      activeTasks: this.activeTasks.size,
      queueLength: this.taskQueue.length,
      averageExecutionTime: this.stats.completedTasks > 0 
        ? this.totalExecutionTime / this.stats.completedTasks 
        : 0,
      successRate: this.stats.completedTasks > 0 
        ? (this.stats.successfulTasks / this.stats.completedTasks) * 100 
        : 0
    };
  }

  /**
   * 动态调整并发数
   */
  adjustConcurrency(newConcurrency: number): void {
    if (newConcurrency > 0 && newConcurrency !== this.currentConcurrency) {
      this.currentConcurrency = newConcurrency;
      this.log(`Concurrency adjusted to ${newConcurrency}`);
      this.processQueue(); // 立即处理队列
    }
  }

  /**
   * 清空队列
   */
  clearQueue(): void {
    this.taskQueue = [];
    this.updateStats();
    this.log('Task queue cleared');
  }

  /**
   * 暂停处理
   */
  pause(): void {
    this.abortController.abort();
    this.abortController = new AbortController();
    this.log('Processing paused');
  }

  /**
   * 恢复处理
   */
  resume(): void {
    this.processQueue();
    this.log('Processing resumed');
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): ConcurrencyStats {
    return {
      totalTasks: 0,
      completedTasks: 0,
      successfulTasks: 0,
      failedTasks: 0,
      activeTasks: 0,
      averageExecutionTime: 0,
      successRate: 0,
      queueLength: 0
    };
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats.totalTasks = this.stats.completedTasks + this.activeTasks.size + this.taskQueue.length;
    this.stats.activeTasks = this.activeTasks.size;
    this.stats.queueLength = this.taskQueue.length;
  }

  /**
   * 延迟工具函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 调试日志
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      console.log('🔧 [debug|DEBUG] ' + message, data || '');
    }
  }
}

/**
 * 简化的并发池函数
 */
export async function pool<T, R>(
  items: T[],
  worker: (item: T, index: number) => Promise<R>,
  options: {
    concurrency?: number;
    rateLimitPerSecond?: number;
    retryCount?: number;
    debug?: boolean;
  } = {}
): Promise<R[]> {
  const { concurrency = 5, rateLimitPerSecond = 10, retryCount = 2, debug = false } = options;
  
  const controller = new ConcurrencyController({ 
    maxConcurrent: concurrency,
    rateLimitPerSecond,
    retryCount,
    debug
  });
  
  // 转换为Task格式
  const tasks: Task<R>[] = items.map((item, index) => ({
    id: `task-${index}`,
    priority: TaskPriority.NORMAL,
    execute: () => worker(item, index)
  }));
  
  const results = await controller.executeAll(tasks);
  
  // 过滤出成功的结果
  return results
    .filter(result => result.result !== undefined)
    .map(result => result.result!);
}

/**
 * 批量处理工具函数
 */
export async function processBatches<T, R>(
  items: T[],
  batchProcessor: (batch: T[]) => Promise<R[]>,
  options: {
    batchSize: number;
    concurrency?: number;
    rateLimitPerSecond?: number;
    debug?: boolean;
  }
): Promise<R[]> {
  const { batchSize, concurrency = 3, rateLimitPerSecond = 5, debug = false } = options;
  
  // 将数组分批
  const batches: T[][] = [];
  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize));
  }

  // 并发处理批次
  const batchResults = await pool(batches, batchProcessor, {
    concurrency,
    rateLimitPerSecond,
    debug
  });
  
  // 扁平化结果
  return batchResults.flat();
}

/**
 * 高优先级任务执行器
 */
export async function executeHighPriorityTasks<T>(
  tasks: Array<{
    id: string;
    execute: () => Promise<T>;
    priority?: TaskPriority;
  }>,
  options: {
    concurrency?: number;
    timeout?: number;
    debug?: boolean;
  } = {}
): Promise<TaskResult<T>[]> {
  const { concurrency = 2, timeout = 30000, debug = false } = options;
  
  const controller = new ConcurrencyController({
    maxConcurrent: concurrency,
    rateLimitPerSecond: 20, // 高优先级任务更高的速率限制
    taskTimeout: timeout,
    debug
  });

  const priorityTasks: Task<T>[] = tasks.map(task => ({
    ...task,
    priority: task.priority || TaskPriority.HIGH
  }));

  return controller.executeAll(priorityTasks);
}

/**
 * 速率限制工具函数
 */
export class RateLimiter {
  private tokens: number;
  private lastRefill: number;
  private readonly fillRate: number; // tokens per second
  private readonly capacity: number;

  constructor(tokensPerSecond: number, capacity?: number) {
    this.fillRate = tokensPerSecond;
    this.capacity = capacity || tokensPerSecond * 2;
    this.tokens = this.capacity;
    this.lastRefill = Date.now();
  }

  /**
   * 等待令牌可用
   */
  async waitForToken(): Promise<void> {
    this.refillTokens();
    
    if (this.tokens >= 1) {
      this.tokens--;
      return;
    }

    // 计算需要等待的时间
    const waitTime = (1 - this.tokens) / this.fillRate * 1000;
    await new Promise(resolve => setTimeout(resolve, waitTime));
    
    this.refillTokens();
    this.tokens--;
  }

  /**
   * 补充令牌
   */
  private refillTokens(): void {
    const now = Date.now();
    const timePassed = (now - this.lastRefill) / 1000;
    const tokensToAdd = timePassed * this.fillRate;
    
    this.tokens = Math.min(this.capacity, this.tokens + tokensToAdd);
    this.lastRefill = now;
  }
}

/**
 * 带速率限制的并发处理
 */
export async function processWithRateLimit<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  options: {
    concurrency?: number;
    rateLimit?: number; // requests per second
    retryCount?: number;
    debug?: boolean;
  } = {}
): Promise<R[]> {
  const { concurrency = 3, rateLimit = 10, retryCount = 2, debug = false } = options;

  return pool(items, processor, {
    concurrency,
    rateLimitPerSecond: rateLimit,
    retryCount,
    debug
  });
}

/**
 * 翻译专用并发控制器
 * 针对翻译任务优化的并发控制
 */
export class TranslationConcurrencyController extends ConcurrencyController {
  constructor(options: Partial<ConcurrencyConfig> = {}) {
    super({
      maxConcurrent: 3,
      rateLimitPerSecond: 5, // 翻译API通常限制较严
      retryCount: 3,
      retryDelay: 2000,
      taskTimeout: 30000,
      enableAdaptiveConcurrency: true,
      debug: false,
      ...options
    });
  }

  /**
   * 添加翻译任务
   */
  addTranslationTask(
    id: string,
    text: string,
    targetLanguage: string,
    translateFunction: (text: string, lang: string) => Promise<string>,
    priority: TaskPriority = TaskPriority.NORMAL
  ): void {
    this.addTask({
      id,
      priority,
      execute: () => translateFunction(text, targetLanguage),
      metadata: {
        text: text.slice(0, 50),
        targetLanguage,
        textLength: text.length
      }
    });
  }

  /**
   * 批量添加翻译任务
   */
  addTranslationTasks(
    tasks: Array<{
      id: string;
      text: string;
      targetLanguage: string;
      priority?: TaskPriority;
    }>,
    translateFunction: (text: string, lang: string) => Promise<string>
  ): void {
    const translationTasks = tasks.map(task => ({
      id: task.id,
      priority: task.priority || TaskPriority.NORMAL,
      execute: () => translateFunction(task.text, task.targetLanguage),
      metadata: {
        text: task.text.slice(0, 50),
        targetLanguage: task.targetLanguage,
        textLength: task.text.length
      }
    }));

    this.addTasks(translationTasks);
  }
}