/**
 * 环境变量工具
 * 处理import.meta.env的兼容性问题
 */

// 类型声明
declare const __DEV__: boolean;

// 环境变量获取函数
export const getEnvVar = (key: string, defaultValue?: string): string => {
  // 在浏览器扩展环境中，我们可能需要不同的方式来获取环境变量
  try {
    // 尝试访问import.meta.env
    if (typeof globalThis !== 'undefined' && (globalThis as any).import?.meta?.env) {
      return (globalThis as any).import.meta.env[key] || defaultValue || '';
    }
  } catch (error) {
    // 忽略错误，使用默认值
  }

  // 回退到默认值
  return defaultValue || '';
};

// 调试模式检查
export const isDebugMode = (feature: string): boolean => {
  const debugVar = getEnvVar(`VITE_DEBUG_${feature.toUpperCase()}`);
  return debugVar === 'true' || debugVar === '1';
};

// 常用环境变量
export const ENV = {
  API_BASE_URL: getEnvVar('VITE_API_BASE_URL', 'http://localhost:4000/api'),
  DEBUG_AUTH: isDebugMode('auth'),
  DEBUG_API: isDebugMode('api'),
  NODE_ENV: getEnvVar('NODE_ENV', 'development')
};

// 开发模式检查
export const isDevelopment = (): boolean => {
  return ENV.NODE_ENV === 'development';
};

// 生产模式检查
export const isProduction = (): boolean => {
  return ENV.NODE_ENV === 'production';
};
