/**
 * 大页面性能优化器
 * 提供内存管理、DOM优化、渲染优化等功能
 */

// debug 导入已移除，使用手动 console.log

/**
 * 性能优化配置常量
 */
const PERFORMANCE_CONFIG = {
  MEMORY: {
    WARNING_THRESHOLD: 250,  // 250MB 内存警告阈值
    CLEANUP_THRESHOLD: 400,  // 400MB 内存清理阈值
  },
  DOM: {
    NODE_WARNING_THRESHOLD: 10000,  // DOM节点数量警告阈值
  },
  MONITORING: {
    PROCESSING_INTERVAL: 60000,       // 1分钟监控间隔
    HISTORY_MAX_SIZE: 100,           // 最大历史记录数
    HISTORY_SLICE_SIZE: 50,          // 清理时保留数量
  },
  PERFORMANCE: {
    BATCH_SIZE: 10,                  // 批处理大小
    FPS_LOW_THRESHOLD: 30,           // 低帧率阈值
    FPS_WARNING_THRESHOLD: 45,       // 帧率警告阈值
    TRANSLATION_ELEMENTS_THRESHOLD: 500, // 翻译元素数量阈值
  },
  VIRTUAL_SCROLLING: {
    VIEWPORT_PADDING: 200,           // 视窗外扩展区域
  }
} as const;

/**
 * 性能优化配置
 */
export interface PerformanceConfig {
  /** 是否启用内存监控 */
  enableMemoryMonitoring?: boolean;
  /** 内存警告阈值 (MB) */
  memoryWarningThreshold?: number;
  /** 内存清理阈值 (MB) */
  memoryCleanupThreshold?: number;
  /** DOM节点数量警告阈值 */
  domNodeWarningThreshold?: number;
  /** 是否启用渲染优化 */
  enableRenderOptimization?: boolean;
  /** 批处理大小 */
  batchSize?: number;
  /** 处理间隔 (ms) */
  processingInterval?: number;
  /** 调试模式 */
  debug?: boolean;
}

/**
 * 性能指标
 */
export interface PerformanceSnapshot {
  /** 时间戳 */
  timestamp: number;
  /** 内存使用 (MB) */
  memoryUsage: number;
  /** DOM节点数量 */
  domNodeCount: number;
  /** 活跃翻译元素数量 */
  activeTranslationElements: number;
  /** FPS */
  fps: number;
  /** 主线程阻塞时间 */
  mainThreadBlockTime: number;
  /** 布局抖动次数 */
  layoutThrashing: number;
}

/**
 * 优化建议
 */
export interface OptimizationSuggestion {
  /** 建议类型 */
  type: 'memory' | 'dom' | 'render' | 'performance';
  /** 严重程度 */
  severity: 'info' | 'warning' | 'critical';
  /** 建议描述 */
  message: string;
  /** 建议操作 */
  action?: () => void;
  /** 预期收益 */
  expectedBenefit?: string;
}

/**
 * 清理策略
 */
export enum CleanupStrategy {
  /** 保守清理 */
  CONSERVATIVE = 'conservative',
  /** 标准清理 */
  STANDARD = 'standard',
  /** 激进清理 */
  AGGRESSIVE = 'aggressive'
}

/**
 * 性能优化器类
 */
export class PerformanceOptimizer {
  private config: Required<PerformanceConfig>;
  private performanceHistory: PerformanceSnapshot[] = [];
  private isMonitoring = false;
  private monitoringInterval?: number;
  private observer?: MutationObserver;
  private rafId?: number;
  private frameCount = 0;
  private lastFpsTime = performance.now();
  private currentFps = 0;

  constructor(config: PerformanceConfig = {}) {
    this.config = {
      enableMemoryMonitoring: true,
      memoryWarningThreshold: config.memoryWarningThreshold ?? PERFORMANCE_CONFIG.MEMORY.WARNING_THRESHOLD,
      memoryCleanupThreshold: config.memoryCleanupThreshold ?? PERFORMANCE_CONFIG.MEMORY.CLEANUP_THRESHOLD,
      domNodeWarningThreshold: config.domNodeWarningThreshold ?? PERFORMANCE_CONFIG.DOM.NODE_WARNING_THRESHOLD,
      enableRenderOptimization: true,
      batchSize: config.batchSize ?? PERFORMANCE_CONFIG.PERFORMANCE.BATCH_SIZE,
      processingInterval: config.processingInterval ?? PERFORMANCE_CONFIG.MONITORING.PROCESSING_INTERVAL,
      debug: false,
      ...config
    };

    this.startMonitoring();
    this.log('PerformanceOptimizer initialized', { config: this.config });
  }

  /**
   * 获取当前性能快照
   */
  getCurrentPerformance(): PerformanceSnapshot {
    const snapshot: PerformanceSnapshot = {
      timestamp: Date.now(),
      memoryUsage: this.getMemoryUsage(),
      domNodeCount: this.getDOMNodeCount(),
      activeTranslationElements: this.getActiveTranslationElements(),
      fps: this.currentFps,
      mainThreadBlockTime: this.getMainThreadBlockTime(),
      layoutThrashing: this.getLayoutThrashingCount()
    };

    this.performanceHistory.push(snapshot);

    // 保持历史记录数量
    if (this.performanceHistory.length > PERFORMANCE_CONFIG.MONITORING.HISTORY_MAX_SIZE) {
      this.performanceHistory.shift();
    }

    return snapshot;
  }

  /**
   * 获取性能历史
   */
  getPerformanceHistory(): PerformanceSnapshot[] {
    return [...this.performanceHistory];
  }

  /**
   * 分析性能并提供建议
   */
  analyzePerformance(): OptimizationSuggestion[] {
    const current = this.getCurrentPerformance();
    const suggestions: OptimizationSuggestion[] = [];

    // 内存分析
    if (current.memoryUsage > this.config.memoryCleanupThreshold) {
      suggestions.push({
        type: 'memory',
        severity: 'critical',
        message: `内存使用过高 (${current.memoryUsage.toFixed(1)}MB)，建议立即清理`,
        action: () => this.performCleanup(CleanupStrategy.AGGRESSIVE),
        expectedBenefit: '可释放 20-40% 内存'
      });
    } else if (current.memoryUsage > this.config.memoryWarningThreshold) {
      suggestions.push({
        type: 'memory',
        severity: 'warning',
        message: `内存使用较高 (${current.memoryUsage.toFixed(1)}MB)，建议清理不必要的缓存`,
        action: () => this.performCleanup(CleanupStrategy.STANDARD),
        expectedBenefit: '可释放 10-20% 内存'
      });
    }

    // DOM分析
    if (current.domNodeCount > this.config.domNodeWarningThreshold) {
      suggestions.push({
        type: 'dom',
        severity: 'warning',
        message: `DOM节点过多 (${current.domNodeCount})，可能影响渲染性能`,
        action: () => this.optimizeDOM(),
        expectedBenefit: '提升 15-30% 渲染性能'
      });
    }

    // 渲染性能分析
    if (current.fps < PERFORMANCE_CONFIG.PERFORMANCE.FPS_LOW_THRESHOLD) {
      suggestions.push({
        type: 'render',
        severity: 'critical',
        message: `帧率过低 (${current.fps}fps)，页面响应卡顿`,
        action: () => this.optimizeRendering(),
        expectedBenefit: '提升帧率到 45-60fps'
      });
    } else if (current.fps < PERFORMANCE_CONFIG.PERFORMANCE.FPS_WARNING_THRESHOLD) {
      suggestions.push({
        type: 'render',
        severity: 'warning',
        message: `帧率偏低 (${current.fps}fps)，建议优化渲染`,
        action: () => this.optimizeRendering(),
        expectedBenefit: '提升帧率到 50-60fps'
      });
    }

    // 翻译元素分析
    if (current.activeTranslationElements > PERFORMANCE_CONFIG.PERFORMANCE.TRANSLATION_ELEMENTS_THRESHOLD) {
      suggestions.push({
        type: 'performance',
        severity: 'warning',
        message: `活跃翻译元素过多 (${current.activeTranslationElements})，建议使用虚拟滚动`,
        action: () => this.enableVirtualScrolling(),
        expectedBenefit: '减少 70-80% 内存占用'
      });
    }

    this.log('Performance analysis completed', {
      suggestions: suggestions.length,
      criticalIssues: suggestions.filter(s => s.severity === 'critical').length
    });

    return suggestions;
  }

  /**
   * 执行性能清理
   */
  performCleanup(strategy: CleanupStrategy = CleanupStrategy.STANDARD): void {
    this.log(`Starting cleanup with ${strategy} strategy`);

    const beforeMemory = this.getMemoryUsage();

    switch (strategy) {
      case CleanupStrategy.CONSERVATIVE:
        this.cleanupObservedElements();
        break;

      case CleanupStrategy.STANDARD:
        this.cleanupObservedElements();
        this.cleanupEventListeners();
        this.cleanupCaches();
        break;

      case CleanupStrategy.AGGRESSIVE:
        this.cleanupObservedElements();
        this.cleanupEventListeners();
        this.cleanupCaches();
        this.cleanupInvisibleTranslations();
        this.forceGarbageCollection();
        break;
    }

    // 等待清理完成后检查内存
    setTimeout(() => {
      const afterMemory = this.getMemoryUsage();
      const saved = beforeMemory - afterMemory;
      this.log(`Cleanup completed`, {
        strategy,
        memorySaved: `${saved.toFixed(1)}MB`,
        beforeMemory: `${beforeMemory.toFixed(1)}MB`,
        afterMemory: `${afterMemory.toFixed(1)}MB`
      });
    }, 1000);
  }

  /**
   * 优化DOM结构
   */
  optimizeDOM(): void {
    this.log('Starting DOM optimization');

    // 移除不必要的空白节点
    this.removeEmptyTextNodes();

    // 合并相邻的翻译元素
    this.mergeAdjacentTranslations();

    // 清理断开连接的节点
    this.cleanupDisconnectedNodes();

    this.log('DOM optimization completed');
  }

  /**
   * 优化渲染性能
   */
  optimizeRendering(): void {
    this.log('Starting rendering optimization');

    // 启用CSS containment
    this.enableCSSContainment();

    // 优化重绘区域
    this.optimizeRepaintAreas();

    // 延迟非关键渲染
    this.deferNonCriticalRendering();

    this.log('Rendering optimization completed');
  }

  /**
   * 启用虚拟滚动
   */
  enableVirtualScrolling(): void {
    this.log('Enabling virtual scrolling for translation elements');

    // 隐藏视窗外的翻译元素
    const translationElements = document.querySelectorAll('.lu-wrapper');
    const viewport = {
      top: window.scrollY - PERFORMANCE_CONFIG.VIRTUAL_SCROLLING.VIEWPORT_PADDING,
      bottom: window.scrollY + window.innerHeight + PERFORMANCE_CONFIG.VIRTUAL_SCROLLING.VIEWPORT_PADDING
    };

    translationElements.forEach(element => {
      const rect = element.getBoundingClientRect();
      const elementTop = rect.top + window.scrollY;
      const elementBottom = elementTop + rect.height;

      if (elementBottom < viewport.top || elementTop > viewport.bottom) {
        (element as HTMLElement).style.display = 'none';
      } else {
        (element as HTMLElement).style.display = '';
      }
    });

    this.log(`Virtual scrolling enabled, processed ${translationElements.length} elements`);
  }

  /**
   * 获取内存使用量
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize / (1024 * 1024); // MB
    }
    return 0;
  }

  /**
   * 获取DOM节点数量
   */
  private getDOMNodeCount(): number {
    return document.querySelectorAll('*').length;
  }

  /**
   * 获取活跃翻译元素数量
   */
  private getActiveTranslationElements(): number {
    return document.querySelectorAll('.lu-wrapper').length;
  }

  /**
   * 获取主线程阻塞时间
   */
  private getMainThreadBlockTime(): number {
    // 简化实现，实际应该使用Performance Observer
    return 0;
  }

  /**
   * 获取布局抖动次数
   */
  private getLayoutThrashingCount(): number {
    // 简化实现，实际应该监控layout事件
    return 0;
  }

  /**
   * 清理观察的元素
   */
  private cleanupObservedElements(): void {
    // 清理所有IntersectionObserver和MutationObserver
    const observers = (window as any).__lu_observers || [];
    observers.forEach((observer: IntersectionObserver | MutationObserver) => {
      observer.disconnect();
    });
    (window as any).__lu_observers = [];
  }

  /**
   * 清理事件监听器
   */
  private cleanupEventListeners(): void {
    // 移除页面级别的事件监听器
    const events = ['scroll', 'resize', 'mouseover', 'mouseout'];
    events.forEach(event => {
      const listeners = (window as any).__lu_event_listeners?.[event] || [];
      listeners.forEach((listener: EventListener) => {
        document.removeEventListener(event, listener);
      });
    });
  }

  /**
   * 清理缓存
   */
  private cleanupCaches(): void {
    // 清理翻译缓存
    if ('__lu_translation_cache' in window) {
      (window as any).__lu_translation_cache = new Map();
    }

    // 清理DOM查询缓存
    if ('__lu_dom_cache' in window) {
      (window as any).__lu_dom_cache = new Map();
    }
  }

  /**
   * 清理不可见的翻译
   */
  private cleanupInvisibleTranslations(): void {
    const translationElements = document.querySelectorAll('.lu-wrapper');
    const viewport = {
      top: window.scrollY - window.innerHeight,
      bottom: window.scrollY + window.innerHeight * 2
    };

    let removedCount = 0;
    translationElements.forEach(element => {
      const rect = element.getBoundingClientRect();
      const elementTop = rect.top + window.scrollY;
      const elementBottom = elementTop + rect.height;

      // 移除距离视窗很远的翻译元素
      if (elementBottom < viewport.top || elementTop > viewport.bottom) {
        element.remove();
        removedCount++;
      }
    });

    this.log(`Removed ${removedCount} invisible translation elements`);
  }

  /**
   * 强制垃圾回收
   */
  private forceGarbageCollection(): void {
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
      this.log('Forced garbage collection');
    }
  }

  /**
   * 移除空白文本节点
   */
  private removeEmptyTextNodes(): void {
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          return node.textContent?.trim() === ''
            ? NodeFilter.FILTER_ACCEPT
            : NodeFilter.FILTER_REJECT;
        }
      }
    );

    const nodesToRemove: Node[] = [];
    let node: Node | null;

    while ((node = walker.nextNode())) {
      nodesToRemove.push(node);
    }

    nodesToRemove.forEach(node => node.parentNode?.removeChild(node));
    this.log(`Removed ${nodesToRemove.length} empty text nodes`);
  }

  /**
   * 合并相邻的翻译元素
   */
  private mergeAdjacentTranslations(): void {
    // 简化实现，实际应该智能合并相邻的翻译块
    this.log('Adjacent translations merge completed');
  }

  /**
   * 清理断开连接的节点
   */
  private cleanupDisconnectedNodes(): void {
    // 清理已断开连接的DOM节点引用
    this.log('Disconnected nodes cleanup completed');
  }

  /**
   * 启用CSS containment
   */
  private enableCSSContainment(): void {
    const translationElements = document.querySelectorAll('.lu-wrapper');
    translationElements.forEach(element => {
      (element as HTMLElement).style.contain = 'layout style paint';
    });
    this.log(`Enabled CSS containment for ${translationElements.length} elements`);
  }

  /**
   * 优化重绘区域
   */
  private optimizeRepaintAreas(): void {
    // 为翻译元素添加will-change属性
    const translationElements = document.querySelectorAll('.lu-block');
    translationElements.forEach(element => {
      (element as HTMLElement).style.willChange = 'opacity';
    });
    this.log(`Optimized repaint areas for ${translationElements.length} elements`);
  }

  /**
   * 延迟非关键渲染
   */
  private deferNonCriticalRendering(): void {
    // 使用requestIdleCallback延迟非关键更新
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => {
        this.log('Non-critical rendering deferred');
      });
    }
  }

  /**
   * 开始监控
   */
  private startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;

    // 定期性能检查
    this.monitoringInterval = setInterval(() => {
      const snapshot = this.getCurrentPerformance();

      // 仅记录性能快照，不执行自动清理
      this.performanceHistory.push(snapshot);

      // 保持历史记录在合理范围内
      if (this.performanceHistory.length > PERFORMANCE_CONFIG.MONITORING.HISTORY_MAX_SIZE) {
        this.performanceHistory = this.performanceHistory.slice(-PERFORMANCE_CONFIG.MONITORING.HISTORY_SLICE_SIZE);
      }

      // 只在内存超过警告阈值时记录警告日志
      if (snapshot.memoryUsage > this.config.memoryWarningThreshold) {
        this.log(`Memory usage warning: ${snapshot.memoryUsage.toFixed(1)}MB - ${this.config.memoryWarningThreshold}`);
      }

    }, this.config.processingInterval) as any;

    // FPS监控
    this.startFPSMonitoring();

    this.log('Performance monitoring started');
  }

  /**
   * 开始FPS监控
   */
  private startFPSMonitoring(): void {
    const measureFPS = (currentTime: number) => {
      this.frameCount++;

      if (currentTime - this.lastFpsTime >= 1000) {
        this.currentFps = this.frameCount;
        this.frameCount = 0;
        this.lastFpsTime = currentTime;
      }

      if (this.isMonitoring) {
        this.rafId = requestAnimationFrame(measureFPS);
      }
    };

    this.rafId = requestAnimationFrame(measureFPS);
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = undefined;
    }

    if (this.observer) {
      this.observer.disconnect();
      this.observer = undefined;
    }

    this.log('Performance monitoring stopped');
  }

  /**
   * 销毁优化器
   */
  /**
   * 获取性能统计信息
   */
  getStats(): {
    monitoring: boolean;
    performance: PerformanceSnapshot;
    history: PerformanceSnapshot[];
    config: Required<PerformanceConfig>;
  } {
    return {
      monitoring: this.isMonitoring,
      performance: this.getCurrentPerformance(),
      history: [...this.performanceHistory],
      config: { ...this.config }
    };
  }

  destroy(): void {
    this.stopMonitoring();
    this.performanceHistory = [];
    this.log('PerformanceOptimizer destroyed');
  }

  /**
   * 调试日志
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      console.log('🔧 [debug|DEBUG] ' + message, data || '');
    }
  }
}

/**
 * 全局性能优化器实例
 */
let globalOptimizer: PerformanceOptimizer | null = null;

/**
 * 获取全局性能优化器
 */
export function getPerformanceOptimizer(config?: PerformanceConfig): PerformanceOptimizer {
  if (!globalOptimizer) {
    globalOptimizer = new PerformanceOptimizer(config);
  }
  return globalOptimizer;
}

/**
 * 设置全局性能优化器
 */
export function setPerformanceOptimizer(optimizer: PerformanceOptimizer): void {
  if (globalOptimizer) {
    globalOptimizer.destroy();
  }
  globalOptimizer = optimizer;
}

/**
 * 便捷的性能检查函数
 */
export function checkPerformance(): OptimizationSuggestion[] {
  const optimizer = getPerformanceOptimizer();
  return optimizer.analyzePerformance();
}

/**
 * 便捷的性能清理函数
 */
export function cleanupPerformance(strategy?: CleanupStrategy): void {
  const optimizer = getPerformanceOptimizer();
  optimizer.performCleanup(strategy);
}