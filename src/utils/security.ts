/**
 * 安全防护工具
 * 防止XSS攻击和恶意内容注入
 */

/**
 * 恶意内容检测规则（针对真正的安全威胁）
 */
const MALICIOUS_PATTERNS = [
  // JavaScript协议
  /javascript:/i,
  // 事件处理器（XSS攻击向量）- 要求前面有空格或在开头
  /(^|\s)on\w+\s*=/i,
  // 脚本标签（完全禁止）
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  // 样式注入（可能包含恶意代码）
  /<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi,
  // 危险数据URI
  /data:\s*text\/html/i,
  /data:\s*[^;]*;base64.*script/i,
  // 其他危险协议
  /vbscript:|livescript:|mocha:|tcl:/i,
  // CSS表达式注入
  /expression\s*\(/i,
  // CSS导入（可能导致CSRF）
  /@import/i,
  // 内联框架
  /<iframe\b[^>]*>/gi,
  // 对象和嵌入标签
  /<(object|embed)\b[^>]*>/gi,
  // 表单标签（在翻译内容中不应出现）
  /<form\b[^>]*>/gi,
  // Meta刷新重定向
  /<meta\b[^>]*http-equiv\s*=\s*["']?refresh["']?/gi
];

/**
 * 安全的HTML标签白名单（翻译内容中允许的标签）
 * 扩展后包含更多常见的安全HTML标签
 */
const SAFE_HTML_TAGS = [
  'a', 'strong', 'b', 'em', 'i', 'u', 'span', 'mark', 'small', 'sup', 'sub',
  'div', 'p', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 
  'ul', 'ol', 'li', 'blockquote', 'section', 'article',
  'button', 'input', 'label', 'textarea', // 常见交互元素
  'lu-trans' // 自定义翻译标签
];

/**
 * 检查HTML标签是否安全
 * 支持严格模式和宽松模式
 */
function isSafeHtmlTag(tagMatch: string, strictMode: boolean = false): boolean {
  // 提取标签名
  const tagName = tagMatch.match(/<\/?(\w+)/)?.[1]?.toLowerCase();
  if (!tagName) return false;
  
  // 在宽松模式下，允许更多常见的安全标签
  const isInWhitelist = SAFE_HTML_TAGS.includes(tagName);
  
  if (strictMode && !isInWhitelist) {
    return false;
  }
  
  // 即使在宽松模式下，也要检查明显危险的标签
  const dangerousTags = ['script', 'iframe', 'object', 'embed', 'form', 'meta'];
  if (dangerousTags.includes(tagName)) {
    return false;
  }
  
  // 检查标签是否包含危险属性 - 修复data属性误判
  const dangerousAttributes = /\s+(on\w+|javascript:|vbscript:)/i;
  if (dangerousAttributes.test(tagMatch)) {
    return false;
  }
  
  return true;
}

/**
 * 检测文本是否包含恶意内容（翻译内容专用版本）
 * 优化后的版本：区分安全级别，减少误报
 */
export function hasMaliciousContent(text: string, strictMode: boolean = false): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }

  // 检查基本长度限制 - 在严格模式下更严格
  const maxLength = strictMode ? 8000 : 15000;
  if (text.length > maxLength) {
    return true;
  }

  // 首先检查明确的恶意模式
  for (const pattern of MALICIOUS_PATTERNS) {
    if (pattern.test(text)) {
      return true;
    }
  }

  // 对于包含HTML标签的内容，进行更细致的检查
  const htmlTagMatches = text.match(/<[^>]*>/g);
  if (htmlTagMatches) {
    // 检查每个标签的安全性
    for (const tagMatch of htmlTagMatches) {
      if (!isSafeHtmlTag(tagMatch, strictMode)) {
        return true; // 发现不安全标签，返回true表示包含恶意内容
      }
    }
  }

  return false;
}

/**
 * 检测纯文本内容是否包含恶意内容（不含HTML）
 */
export function hasMaliciousTextContent(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }

  // 基本长度检查
  if (text.length > 15000) {
    return true;
  }

  // 纯文本不应包含任何HTML标签
  if (/<[^>]*>/.test(text)) {
    return true;
  }

  // 检查危险协议和脚本
  const textOnlyPatterns = [
    /javascript:/i,
    /vbscript:|livescript:|mocha:|tcl:/i,
    /data:\s*[^;]*;base64/i
  ];

  for (const pattern of textOnlyPatterns) {
    if (pattern.test(text)) {
      return true;
    }
  }

  return false;
}

/**
 * 根据内容格式选择合适的安全检查方法
 * 新增安全级别参数
 * 修复：当format为'text'但内容包含HTML时，自动使用HTML验证
 */
export function validateContent(
  content: string, 
  format: 'text' | 'html' = 'text',
  securityLevel: 'strict' | 'normal' | 'lenient' = 'normal'
): boolean {
  const strictMode = securityLevel === 'strict';
  
  // 检查内容是否包含HTML标签
  const containsHtml = /<[^>]*>/.test(content);
  
  if (format === 'html' || containsHtml) {
    // 如果指定为HTML格式，或者内容包含HTML标签，使用HTML验证
    return !hasMaliciousContent(content, strictMode);
  } else {
    // 纯文本验证
    return !hasMaliciousTextContent(content);
  }
}

/**
 * 清理和转义文本内容
 */
export function sanitizeText(text: string): string {
  if (!text || typeof text !== 'string') {
    return '';
  }

  return text
    // 移除HTML标签
    .replace(/<[^>]*>/g, '')
    // 转义特殊字符
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    // 移除控制字符
    .replace(/[\x00-\x1F\x7F]/g, '')
    // 规范化空白字符
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * 验证语言代码
 */
export function isValidLanguageCode(code: string): boolean {
  if (!code || typeof code !== 'string') {
    return false;
  }

  // ISO 639-1 (2字符) 或 BCP 47 (2-5字符含连字符)
  const langPattern = /^[a-z]{2}(-[A-Z]{2})?$/i;
  return langPattern.test(code) && code.length <= 5;
}

/**
 * 验证CSS类名
 */
export function isValidClassName(className: string): boolean {
  if (!className || typeof className !== 'string') {
    return false;
  }

  // CSS类名规则：字母开头，可包含字母、数字、连字符、下划线
  const classPattern = /^[a-zA-Z][a-zA-Z0-9_-]*$/;
  return classPattern.test(className) && className.length <= 50;
}

/**
 * 创建安全的DOM元素
 */
export function createSecureElement(
  tagName: string,
  attributes: Record<string, string> = {},
  textContent?: string
): HTMLElement {
  // 验证标签名 - 使用更宽松的白名单（包含自定义翻译标签）
  const allowedTags = ['font', 'span', 'div', 'br', 'p', 'strong', 'em', 'b', 'i', 'u', 'mark', 'a', 'lu-trans'];
  if (!allowedTags.includes(tagName.toLowerCase())) {
    throw new Error(`Tag "${tagName}" not allowed`);
  }

  const element = document.createElement(tagName);

  // 设置属性
  Object.entries(attributes).forEach(([key, value]) => {
    if (isValidAttribute(key, value)) {
      element.setAttribute(key, value);
    } else {
      console.warn(`Invalid attribute: ${key}="${value}"`);
    }
  });

  // 设置文本内容（安全方式）
  if (textContent) {
    element.textContent = sanitizeText(textContent);
  }

  return element;
}

/**
 * 验证HTML属性
 */
export function isValidAttribute(name: string, value: string): boolean {
  // 允许的属性白名单
  const allowedAttributes = [
    'class', 'id', 'lang', 'dir', 'title',
    'aria-label', 'aria-describedby', 'aria-hidden',
    'data-lu-translated', 'data-lu-id', 'data-lu-lang',
    'data-lu-injected',
    'tabindex', 'href'
  ];

  if (!allowedAttributes.includes(name)) {
    return false;
  }

  // 特殊验证规则
  switch (name) {
    case 'class':
      return value.split(' ').every(isValidClassName);
    case 'lang':
      return isValidLanguageCode(value);
    case 'tabindex':
      return /^-?[0-9]+$/.test(value);
    case 'aria-hidden':
      return ['true', 'false'].includes(value);
    case 'href':
      // 允许相对URL、锚点链接和安全的HTTP(S)协议
      return /^(#|\/|https?:\/\/|mailto:)/.test(value) && value.length <= 500;
    default:
      return value.length <= 100 && !hasMaliciousContent(value);
  }
}

/**
 * 安全地插入HTML内容
 */
export function secureInnerHTML(element: HTMLElement, content: string): void {
  // 清理内容
  const cleanContent = sanitizeText(content);
  
  // 使用textContent而不是innerHTML
  element.textContent = cleanContent;
}

/**
 * 验证DOM元素是否安全
 */
export function isSecureElement(element: HTMLElement): boolean {
  // 检查标签名 - 使用更宽松的白名单（包含自定义翻译标签）
  const allowedTags = ['font', 'span', 'div', 'br', 'p', 'strong', 'em', 'b', 'i', 'u', 'mark', 'a', 'lu-trans'];
  if (!allowedTags.includes(element.tagName.toLowerCase())) {
    return false;
  }

  // 检查属性
  for (const attr of element.attributes) {
    if (!isValidAttribute(attr.name, attr.value)) {
      return false;
    }
  }

  // 检查文本内容
  if (element.textContent && hasMaliciousContent(element.textContent)) {
    return false;
  }

  return true;
}

/**
 * 内容安全策略(CSP)检查器
 */
export class CSPValidator {
  private static allowedSources = new Set([
    "'self'",
    "'unsafe-inline'", // 仅用于样式
    "chrome-extension:",
    "moz-extension:"
  ]);

  /**
   * 检查是否符合CSP策略
   */
  static isCSPCompliant(content: string): boolean {
    // 检查是否包含内联脚本
    if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(content)) {
      return false;
    }

    // 检查是否包含事件处理器
    if (/on\w+\s*=/i.test(content)) {
      return false;
    }

    return true;
  }

  /**
   * 生成安全的CSP策略
   */
  static generateCSP(): string {
    return [
      "default-src 'self'",
      "script-src 'self'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data:",
      "font-src 'self'",
      "connect-src 'self'",
      "object-src 'none'",
      "frame-src 'none'"
    ].join('; ');
  }
}

/**
 * DOM注入安全检查器
 */
export class InjectionGuard {
  private static injectedElements = new WeakSet<HTMLElement>();

  /**
   * 标记元素为安全注入
   */
  static markAsInjected(element: HTMLElement): void {
    this.injectedElements.add(element);
    element.setAttribute('data-lu-injected', 'true');
  }

  /**
   * 检查元素是否为安全注入
   */
  static isInjected(element: HTMLElement): boolean {
    return this.injectedElements.has(element) || 
           element.hasAttribute('data-lu-injected');
  }

  /**
   * 清理所有注入的元素
   */
  static cleanupInjected(): void {
    const injectedElements = document.querySelectorAll('[data-lu-injected]');
    injectedElements.forEach(element => {
      element.remove();
    });
  }

  /**
   * 验证注入操作的安全性
   */
  static validateInjection(
    targetElement: HTMLElement, 
    contentElement: HTMLElement
  ): boolean {
    // 检查目标元素
    if (!targetElement || !targetElement.isConnected) {
      console.warn('Target element is not valid or connected');
      return false;
    }

    // 检查内容元素
    if (!isSecureElement(contentElement)) {
      console.warn('Content element failed security check');
      return false;
    }

    // 检查是否会造成循环引用
    if (contentElement.contains(targetElement)) {
      console.warn('Circular reference detected');
      return false;
    }

    return true;
  }
}