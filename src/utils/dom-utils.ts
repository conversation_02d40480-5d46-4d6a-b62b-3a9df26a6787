/**
 * DOM操作工具
 * 简化的DOM工具函数
 */

/**
 * DOM工具类
 */
export class DOMUtils {
  /**
   * 检查元素是否可见
   * @deprecated Use isElementTrulyHidden() instead for comprehensive visibility checking
   */
  static isElementVisible(element: HTMLElement): boolean {
    return !this.isElementTrulyHidden(element);
  }

  /**
   * 检查元素是否真正隐藏（综合版本）
   * 🔧 增强版：支持更多隐藏检测方式，区分真正隐藏和仅仅在屏幕外的元素
   */
  static isElementTrulyHidden(element: HTMLElement): boolean {
    if (!element.isConnected) {
      return true;
    }

    // 1. 检查基本CSS隐藏属性
    const style = getComputedStyle(element);
    if (style.display === 'none' || style.visibility === 'hidden') {
      return true;
    }

    // 2. 检查透明度
    if (parseFloat(style.opacity) < 0.1) {
      return true;
    }

    // 3. 检查 aria-hidden 属性
    if (element.getAttribute('aria-hidden') === 'true') {
      return true;
    }

    // 4. 检查常见的隐藏CSS类名
    const hiddenClasses = ['hidden', 'hide', 'invisible', 'collapsed', 'd-none', 'sr-only'];
    if (hiddenClasses.some(cls => element.classList.contains(cls))) {
      return true;
    }

    // 5. 检查故意隐藏技术
    if (this.isIntentionallyHidden(element, style)) {
      return true;
    }

    // 6. 检查剪切隐藏
    if (this.isClippedHidden(element, style)) {
      return true;
    }

    // 7. 检查变换隐藏
    if (this.isTransformHidden(element, style)) {
      return true;
    }

    // 8. 检查尺寸隐藏
    if (this.isSizeHidden(element, style)) {
      return true;
    }

    // 9. 检查是否在隐藏的对话框中
    if (this.isInHiddenDialog(element)) {
      return true;
    }

    // 10. 检查父元素的特殊隐藏情况
    if (this.isParentHiddenSpecialCases(element)) {
      return true;
    }

    return false;
  }

  /**
   * 检查元素是否被故意隐藏（区分故意隐藏和自然的屏幕外位置）
   * 🔧 专门检测通过CSS技术故意隐藏的元素，而不是自然滚动到屏幕外的元素
   */
  private static isIntentionallyHidden(element: HTMLElement, style: CSSStyleDeclaration): boolean {
    const position = style.position;

    // 1. 检查极端的负偏移（常见的无障碍跳转链接隐藏技术）
    if (position === 'absolute' || position === 'fixed') {
      const left = parseFloat(style.left) || 0;
      const top = parseFloat(style.top) || 0;
      const right = parseFloat(style.right) || 0;

      // 检查大幅负偏移（> 1000px，明显是故意隐藏）
      if (left < -1000 || top < -1000 || right > window.innerWidth + 1000) {
        return true;
      }
    }

    // 2. 检查text-indent负值隐藏技术
    const textIndent = parseFloat(style.textIndent) || 0;
    if (textIndent < -1000) {
      return true;
    }

    // 3. 检查overflow hidden + 极小尺寸的组合
    if (style.overflow === 'hidden') {
      const rect = element.getBoundingClientRect();
      if (rect.width < 1 && rect.height < 1) {
        return true;
      }
    }

    // 4. 检查z-index极小值隐藏
    const zIndex = parseInt(style.zIndex) || 0;
    if (zIndex < -1000) {
      return true;
    }

    return false;
  }

  /**
   * 检查元素是否被CSS剪切隐藏
   * 🔧 检测 clip, clip-path 等剪切隐藏技术
   */
  private static isClippedHidden(element: HTMLElement, style: CSSStyleDeclaration): boolean {
    // 检查旧式 clip 属性（常用于隐藏跳转链接）
    const clip = style.clip;
    if (clip && clip !== 'auto') {
      // 检查是否是极小的剪切区域 (如 rect(1px, 1px, 1px, 1px))
      const clipMatch = clip.match(/rect\((\d+)px,?\s*(\d+)px,?\s*(\d+)px,?\s*(\d+)px\)/);
      if (clipMatch) {
        const [, top, right, bottom, left] = clipMatch.map(Number);
        const width = right - left;
        const height = bottom - top;

        if (width <= 1 && height <= 1) {
          return true;
        }
      }
    }

    // 检查 clip-path 属性
    const clipPath = style.clipPath || (style as any).webkitClipPath;
    if (clipPath && clipPath !== 'none') {
      // 检查是否是隐藏用的 clip-path
      const hiddenClipPaths = [
        'inset(50%)',
        'circle(0%)',
        'polygon(0px 0px, 0px 0px, 0px 0px, 0px 0px)',
        'inset(100%)'
      ];

      if (hiddenClipPaths.some(hiddenPath => clipPath.includes(hiddenPath))) {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查元素是否被CSS变换隐藏
   * 🔧 检测 transform 变换隐藏技术
   */
  private static isTransformHidden(element: HTMLElement, style: CSSStyleDeclaration): boolean {
    const transform = style.transform;

    if (transform && transform !== 'none') {
      // 检查大幅平移隐藏
      const translateMatch = transform.match(/translateX?\((-?\d+(?:\.\d+)?)(%|px|em|rem)\)/);
      if (translateMatch) {
        const value = parseFloat(translateMatch[1]);
        const unit = translateMatch[2];

        // 检查是否是大幅负平移
        if (unit === '%' && value <= -100) {
          return true;
        }
        if (unit === 'px' && value <= -1000) {
          return true;
        }
      }

      // 检查缩放隐藏
      const scaleMatch = transform.match(/scale\((\d+(?:\.\d+)?)\)/);
      if (scaleMatch) {
        const scale = parseFloat(scaleMatch[1]);
        if (scale === 0) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 检查元素是否通过尺寸隐藏
   * 🔧 检测宽度/高度为0或极小的隐藏技术
   */
  private static isSizeHidden(element: HTMLElement, style: CSSStyleDeclaration): boolean {
    const rect = element.getBoundingClientRect();

    // 检查计算后的尺寸
    if (rect.width <= 1 && rect.height <= 1) {
      return true;
    }

    // 检查CSS设置的尺寸
    const width = parseFloat(style.width) || 0;
    const height = parseFloat(style.height) || 0;

    if ((width > 0 && width <= 1) || (height > 0 && height <= 1)) {
      return true;
    }

    // 检查 max-width/max-height 限制
    const maxWidth = parseFloat(style.maxWidth) || Infinity;
    const maxHeight = parseFloat(style.maxHeight) || Infinity;

    if (maxWidth <= 1 || maxHeight <= 1) {
      return true;
    }

    return false;
  }

  /**
   * 检查是否在隐藏的对话框中
   * 🔧 专门处理社交分享等对话框组件
   */
  private static isInHiddenDialog(element: HTMLElement): boolean {
    // 检查是否在社交分享对话框中
    const shareDialog = element.closest('.uni-social-share__dialog, .social-share-dialog, .share-popup');
    if (shareDialog) {
      const dialogStyle = getComputedStyle(shareDialog);
      if (dialogStyle.display === 'none' || dialogStyle.visibility === 'hidden') {
        return true;
      }

      // 检查 aria-expanded 属性
      const trigger = shareDialog.parentElement?.querySelector('[aria-expanded]');
      if (trigger && trigger.getAttribute('aria-expanded') === 'false') {
        return true;
      }
    }

    // 检查是否在其他类型的隐藏对话框中
    const dialog = element.closest('[role="dialog"], .dialog, .modal, .popup, .dropdown-menu');
    if (dialog) {
      const dialogStyle = getComputedStyle(dialog);
      if (dialogStyle.display === 'none' || dialogStyle.visibility === 'hidden') {
        return true;
      }

      // 检查 aria-hidden 属性
      if (dialog.getAttribute('aria-hidden') === 'true') {
        return true;
      }
    }

    return false;
  }

  /**
   * 检查父元素的特殊隐藏情况
   * 🔧 仅处理明确的隐藏组件，避免对正常元素误判
   */
  private static isParentHiddenSpecialCases(element: HTMLElement): boolean {
    let parent = element.parentElement;
    let levelsChecked = 0;
    const maxLevels = 3; // 减少检查层级，仅检查直接父级

    while (parent && levelsChecked < maxLevels) {
      // 跳过对链接和标题等正常容器的检查
      if (parent.tagName === 'A' || parent.tagName.match(/^H[1-6]$/)) {
        parent = parent.parentElement;
        levelsChecked++;
        continue;
      }

      const parentStyle = getComputedStyle(parent);

      // 仅检查明确的CSS隐藏属性
      if (parentStyle.display === 'none' || parentStyle.visibility === 'hidden') {
        return true;
      }

      // 仅检查社交分享等特殊组件的隐藏状态
      const specialHiddenClasses = [
        'uni-social-share__dialog',
        'uni-social-share__content',
        'social-share-dropdown',
        'share-dialog',
        'share-popup'
      ];

      if (specialHiddenClasses.some(cls => parent?.classList.contains(cls))) {
        // 检查这些特殊组件是否真的隐藏
        if (parent.getAttribute('aria-expanded') === 'false' ||
          parentStyle.display === 'none') {
          return true;
        }
      }

      parent = parent.parentElement;
      levelsChecked++;
    }

    return false;
  }

  /**
   * 检查元素是否在视口内
   */
  static isInViewport(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }

  /**
   * 检查元素是否在屏幕外（用于懒加载判断，不排除翻译）
   * 🔧 这个方法用于懒加载系统判断元素是否需要延迟处理
   */
  static isElementOffScreen(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect();
    const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

    // 元素完全在视口外（但不是极远距离，说明是正常的页面内容）
    const isOffScreen = (
      rect.right < 0 ||
      rect.left > viewportWidth ||
      rect.bottom < 0 ||
      rect.top > viewportHeight
    );

    return isOffScreen;
  }

  /**
   * 安全地创建元素
   */
  static createElement(
    tagName: string, 
    attributes: Record<string, string> = {},
    textContent?: string
  ): HTMLElement {
    // 验证标签名
    const allowedTags = ['font', 'span', 'div', 'br', 'p', 'strong', 'em', 'b', 'i', 'u', 'mark', 'a'];
    if (!allowedTags.includes(tagName.toLowerCase())) {
      throw new Error(`Tag "${tagName}" not allowed`);
    }

    const element = document.createElement(tagName);

    // 设置属性
    Object.entries(attributes).forEach(([key, value]) => {
      if (this.isValidAttribute(key, value)) {
        element.setAttribute(key, value);
      }
    });

    // 设置文本内容
    if (textContent) {
      element.textContent = textContent;
    }

    return element;
  }

  /**
   * 验证属性是否安全
   */
  static isValidAttribute(name: string, value: string): boolean {
    // 允许的属性白名单
    const allowedAttributes = [
      'class', 'id', 'lang', 'dir', 'title',
      'aria-label', 'aria-describedby', 'aria-hidden',
      'data-lu-translated', 'data-lu-id', 'data-lu-lang',
      'data-lu-injected', 'data-lu-timestamp',
      'tabindex', 'href', 'target', 'rel'
    ];

    if (!allowedAttributes.includes(name)) {
      return false;
    }

    // 基本长度检查
    if (value.length > 500) {
      return false;
    }

    // 特殊验证规则
    switch (name) {
      case 'href':
        return this.isValidUrl(value);
      case 'target':
        return ['_blank', '_self', '_parent', '_top'].includes(value);
      case 'rel':
        return /^[a-z\s]+$/.test(value);
      case 'tabindex':
        return /^-?[0-9]+$/.test(value);
      case 'aria-hidden':
        return ['true', 'false'].includes(value);
      default:
        return true;
    }
  }

  /**
   * 验证URL是否安全
   */
  static isValidUrl(url: string): boolean {
    try {
      const parsed = new URL(url);
      return ['http:', 'https:', 'mailto:'].includes(parsed.protocol);
    } catch {
      // 相对URL检查
      return /^[.\/][^<>]*$/.test(url);
    }
  }

  /**
   * 查找可翻译的元素
   */
  static findTranslatableElements(root: Element = document.body): HTMLElement[] {
    const translatableSelectors = 'p, h1, h2, h3, h4, h5, h6, li, td, th, div[title], span[title]';
    const excludeSelectors = '.lu-wrapper, .lu-block, .notranslate, [data-no-translate], [data-lu-translated]';
    
    const elements = Array.from(root.querySelectorAll(translatableSelectors)) as HTMLElement[];
    
    return elements.filter(element => {
      // 排除不需要翻译的元素
      if (element.matches(excludeSelectors)) {
        return false;
      }
      
      // 检查是否可见
      if (!this.isElementVisible(element)) {
        return false;
      }
      
      // 检查文本内容
      const text = element.textContent?.trim();
      if (!text || text.length < 2) {
        return false;
      }
      
      return true;
    });
  }

  /**
   * 获取元素的唯一标识符
   */
  static getElementId(element: HTMLElement): string {
    // 尝试使用现有的ID
    if (element.id) {
      return element.id;
    }
    
    // 生成基于位置和内容的唯一ID
    const tagName = element.tagName.toLowerCase();
    const textContent = (element.textContent || '').slice(0, 20);
    const hash = this.simpleHash(textContent + element.className);
    
    return `${tagName}-${hash}`;
  }

  /**
   * 简单哈希函数
   */
  private static simpleHash(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * 等待DOM就绪
   */
  static waitForDOMReady(): Promise<void> {
    if (document.readyState === 'loading') {
      return new Promise(resolve => {
        document.addEventListener('DOMContentLoaded', () => resolve(), { once: true });
      });
    }
    return Promise.resolve();
  }

  /**
   * 观察DOM变化
   */
  static observeChanges(
    callback: (mutations: MutationRecord[]) => void,
    options: MutationObserverInit = {}
  ): MutationObserver {
    const defaultOptions: MutationObserverInit = {
      childList: true,
      subtree: true,
      attributes: false,
      characterData: false
    };

    const observer = new MutationObserver(callback);
    observer.observe(document.body, { ...defaultOptions, ...options });
    
    return observer;
  }

  /**
   * 节流DOM操作
   */
  static throttledDOMOperation(
    operation: () => void,
    delay: number = 16 // ~60fps
  ): () => void {
    let timeoutId: number | null = null;
    let lastExecution = 0;

    return () => {
      const now = Date.now();
      
      if (now - lastExecution >= delay) {
        operation();
        lastExecution = now;
      } else {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        
        timeoutId = window.setTimeout(() => {
          operation();
          lastExecution = Date.now();
          timeoutId = null;
        }, delay - (now - lastExecution));
      }
    };
  }

  /**
   * 获取元素的文本内容长度（排除空白）
   */
  static getTextLength(element: HTMLElement): number {
    return (element.textContent || '').trim().length;
  }

  /**
   * 检查元素是否包含翻译标记
   */
  static isTranslated(element: HTMLElement): boolean {
    return element.hasAttribute('data-lu-translated') ||
           element.querySelector('.lu-wrapper') !== null;
  }

  /**
   * 标记元素为已翻译
   */
  static markAsTranslated(element: HTMLElement, targetLang: string = 'zh'): void {
    element.setAttribute('data-lu-translated', 'true');
    element.setAttribute('data-lu-timestamp', Date.now().toString());
    element.setAttribute('data-lu-target-lang', targetLang);
  }

  /**
   * 清除翻译标记
   */
  static clearTranslationMarks(element: HTMLElement): void {
    element.removeAttribute('data-lu-translated');
    element.removeAttribute('data-lu-timestamp');
    element.removeAttribute('data-lu-target-lang');
    
    // 移除翻译包装器
    const wrappers = element.querySelectorAll('.lu-wrapper');
    wrappers.forEach(wrapper => wrapper.remove());
  }
}