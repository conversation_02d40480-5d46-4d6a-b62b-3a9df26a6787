/**
 * 并行批处理器
 * 支持多个批处理器并行工作，提高整体性能
 */

import { DOMBatchProcessor, type DOMOperation, type BatchProcessorOptions } from './dom-batch-processor';

export interface ParallelProcessorOptions {
  /** 并行处理器数量 */
  workerCount?: number;
  /** 负载均衡策略 */
  loadBalanceStrategy?: 'round-robin' | 'least-load' | 'priority-based';
  /** 最大等待时间 (ms) */
  maxWaitTime?: number;
  /** 是否启用统计 */
  enableStats?: boolean;
  /** 单个处理器配置 */
  processorOptions?: BatchProcessorOptions;
}

export interface ParallelStats {
  /** 总处理器数量 */
  totalProcessors: number;
  /** 活跃处理器数量 */
  activeProcessors: number;
  /** 总待处理操作数 */
  totalPendingOperations: number;
  /** 平均负载 */
  averageLoad: number;
  /** 各处理器统计 */
  processors: Array<ReturnType<DOMBatchProcessor['getStats']>>;
}

export class ParallelBatchProcessor {
  private processors: DOMBatchProcessor[] = [];
  private currentWorkerIndex = 0;
  private options: Required<ParallelProcessorOptions>;
  private stats = {
    totalOperations: 0,
    distributedOperations: 0,
    processingTime: 0
  };

  constructor(options: ParallelProcessorOptions = {}) {
    this.options = {
      workerCount: 3,
      loadBalanceStrategy: 'round-robin',
      maxWaitTime: 200,
      enableStats: true,
      processorOptions: {
        batchSize: 25,
        debounceTime: 10,
        maxWaitTime: 60,
        enableProfiling: false
      },
      ...options
    };

    this.initializeProcessors();
  }

  /**
   * 初始化处理器池
   */
  private initializeProcessors(): void {
    for (let i = 0; i < this.options.workerCount; i++) {
      const processor = new DOMBatchProcessor({
        ...this.options.processorOptions,
        enableProfiling: this.options.enableStats
      });
      this.processors.push(processor);
    }
  }

  /**
   * 添加单个操作
   */
  addOperation(operation: DOMOperation): void {
    const processor = this.selectProcessor(operation);
    processor.addOperation(operation);
    
    this.stats.totalOperations++;
    this.stats.distributedOperations++;
  }

  /**
   * 批量添加操作
   */
  addOperations(operations: DOMOperation[]): void {
    const startTime = performance.now();
    
    // 按策略分发操作
    const distributedOps = this.distributeOperations(operations);
    
    distributedOps.forEach((ops, processorIndex) => {
      if (ops.length > 0) {
        this.processors[processorIndex].addOperations(ops);
      }
    });

    // 更新统计
    this.stats.totalOperations += operations.length;
    this.stats.distributedOperations += operations.length;
    this.stats.processingTime += performance.now() - startTime;
  }

  /**
   * 选择处理器
   */
  private selectProcessor(operation: DOMOperation): DOMBatchProcessor {
    switch (this.options.loadBalanceStrategy) {
      case 'least-load':
        return this.selectLeastLoadedProcessor();
      
      case 'priority-based':
        return this.selectByPriority(operation);
      
      case 'round-robin':
      default:
        return this.selectRoundRobin();
    }
  }

  /**
   * 轮询选择处理器
   */
  private selectRoundRobin(): DOMBatchProcessor {
    const processor = this.processors[this.currentWorkerIndex];
    this.currentWorkerIndex = (this.currentWorkerIndex + 1) % this.processors.length;
    return processor;
  }

  /**
   * 选择负载最低的处理器
   */
  private selectLeastLoadedProcessor(): DOMBatchProcessor {
    let leastLoadedIndex = 0;
    let minLoad = this.processors[0].getStats().pendingOperations;

    for (let i = 1; i < this.processors.length; i++) {
      const load = this.processors[i].getStats().pendingOperations;
      if (load < minLoad) {
        minLoad = load;
        leastLoadedIndex = i;
      }
    }

    return this.processors[leastLoadedIndex];
  }

  /**
   * 根据优先级选择处理器
   */
  private selectByPriority(operation: DOMOperation): DOMBatchProcessor {
    const priority = operation.priority || 0;
    
    // 高优先级操作分配给负载较低的处理器
    if (priority >= 2) {
      return this.selectLeastLoadedProcessor();
    }
    
    // 低优先级操作使用轮询
    return this.selectRoundRobin();
  }

  /**
   * 分发操作到多个处理器
   */
  private distributeOperations(operations: DOMOperation[]): DOMOperation[][] {
    const distributed: DOMOperation[][] = this.processors.map(() => []);

    switch (this.options.loadBalanceStrategy) {
      case 'priority-based':
        this.distributByPriority(operations, distributed);
        break;
        
      case 'least-load':
        this.distributByLoad(operations, distributed);
        break;
        
      case 'round-robin':
      default:
        this.distributRoundRobin(operations, distributed);
        break;
    }

    return distributed;
  }

  /**
   * 轮询分发
   */
  private distributRoundRobin(operations: DOMOperation[], distributed: DOMOperation[][]): void {
    operations.forEach((op, index) => {
      const processorIndex = index % this.processors.length;
      distributed[processorIndex].push(op);
    });
  }

  /**
   * 按优先级分发
   */
  private distributByPriority(operations: DOMOperation[], distributed: DOMOperation[][]): void {
    // 按优先级排序
    const sortedOps = [...operations].sort((a, b) => {
      const priorityA = a.priority || 0;
      const priorityB = b.priority || 0;
      return priorityB - priorityA; // 高优先级在前
    });

    // 高优先级操作分配给前面的处理器
    sortedOps.forEach((op, index) => {
      const priority = op.priority || 0;
      
      if (priority >= 3) {
        // 最高优先级 - 第一个处理器
        distributed[0].push(op);
      } else if (priority >= 2) {
        // 高优先级 - 前两个处理器
        distributed[index % Math.min(2, this.processors.length)].push(op);
      } else {
        // 普通优先级 - 所有处理器
        distributed[index % this.processors.length].push(op);
      }
    });
  }

  /**
   * 按负载分发
   */
  private distributByLoad(operations: DOMOperation[], distributed: DOMOperation[][]): void {
    operations.forEach(op => {
      // 找到当前负载最低的处理器
      let minLoadIndex = 0;
      let minLoad = distributed[0].length + this.processors[0].getStats().pendingOperations;

      for (let i = 1; i < this.processors.length; i++) {
        const currentLoad = distributed[i].length + this.processors[i].getStats().pendingOperations;
        if (currentLoad < minLoad) {
          minLoad = currentLoad;
          minLoadIndex = i;
        }
      }

      distributed[minLoadIndex].push(op);
    });
  }

  /**
   * 刷新所有处理器
   */
  async flush(): Promise<void> {
    const flushPromises = this.processors.map(processor => processor.flush());
    await Promise.all(flushPromises);
  }

  /**
   * 清空所有处理器
   */
  clear(): void {
    this.processors.forEach(processor => processor.clear());
    this.resetStats();
  }

  /**
   * 获取并行统计
   */
  getStats(): ParallelStats {
    const processorStats = this.processors.map(p => p.getStats());
    const totalPending = processorStats.reduce((sum, stat) => sum + stat.pendingOperations, 0);
    const activeProcessors = processorStats.filter(stat => stat.pendingOperations > 0).length;
    const averageLoad = this.processors.length > 0 ? totalPending / this.processors.length : 0;

    return {
      totalProcessors: this.processors.length,
      activeProcessors,
      totalPendingOperations: totalPending,
      averageLoad,
      processors: processorStats
    };
  }

  /**
   * 获取处理器实例 (用于调试)
   */
  getProcessor(index: number): DOMBatchProcessor | null {
    return this.processors[index] || null;
  }

  /**
   * 动态调整处理器数量
   */
  async resizeProcessorPool(newSize: number): Promise<void> {
    if (newSize === this.processors.length) {
      return;
    }

    if (newSize > this.processors.length) {
      // 增加处理器
      const additionalCount = newSize - this.processors.length;
      for (let i = 0; i < additionalCount; i++) {
        const processor = new DOMBatchProcessor({
          ...this.options.processorOptions,
          enableProfiling: this.options.enableStats
        });
        this.processors.push(processor);
      }
    } else {
      // 减少处理器 - 先刷新再移除
      const toRemove = this.processors.slice(newSize);
      await Promise.all(toRemove.map(p => p.flush()));
      
      toRemove.forEach(p => p.destroy());
      this.processors = this.processors.slice(0, newSize);
    }

    this.options.workerCount = newSize;
    this.currentWorkerIndex = 0; // 重置轮询索引
  }

  /**
   * 重置统计
   */
  resetStats(): void {
    this.stats = {
      totalOperations: 0,
      distributedOperations: 0,
      processingTime: 0
    };
    
    this.processors.forEach(processor => processor.resetStats());
  }

  /**
   * 销毁并行处理器
   */
  destroy(): void {
    this.processors.forEach(processor => processor.destroy());
    this.processors = [];
    this.resetStats();
  }

  /**
   * 获取处理策略说明
   */
  getStrategyInfo(): string {
    const strategy = this.options.loadBalanceStrategy;
    const descriptions = {
      'round-robin': '轮询分发 - 操作平均分配到各处理器',
      'least-load': '负载均衡 - 优先分配给负载最低的处理器',
      'priority-based': '优先级分发 - 高优先级操作分配给专用处理器'
    };
    
    return descriptions[strategy] || '未知策略';
  }
}