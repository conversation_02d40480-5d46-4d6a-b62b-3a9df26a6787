/**
 * 批处理工具函数
 * 简单实用的批处理实现
 */

/**
 * 批处理函数 - 通用版本
 */
export async function processBatch<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  options: {
    batchSize?: number;
    concurrency?: number;
    onProgress?: (completed: number, total: number) => void;
  } = {}
): Promise<R[]> {
  const { batchSize = 10, concurrency = 3, onProgress } = options;
  const results: R[] = [];
  
  // 分组处理
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    
    // 控制并发数
    const chunks = createConcurrentChunks(batch, concurrency);
    
    for (const chunk of chunks) {
      const chunkResults = await Promise.allSettled(
        chunk.map(item => processor(item))
      );
      
      // 提取成功的结果
      const successResults = chunkResults
        .filter((result): result is PromiseFulfilledResult<Awaited<R>> => 
          result.status === 'fulfilled')
        .map(result => result.value);
      
      results.push(...successResults);
      
      // 报告进度
      if (onProgress) {
        onProgress(results.length, items.length);
      }
    }
  }
  
  return results;
}

/**
 * DOM批处理 - 专门用于DOM操作
 */
export async function processDOMBatch(
  operations: Array<() => void | Promise<void>>,
  options: {
    useRequestAnimationFrame?: boolean;
    batchSize?: number;
  } = {}
): Promise<void> {
  const { useRequestAnimationFrame = true, batchSize = 20 } = options;
  
  return new Promise(resolve => {
    const executeOperations = async () => {
      for (let i = 0; i < operations.length; i += batchSize) {
        const batch = operations.slice(i, i + batchSize);
        
        // 执行当前批次
        for (const operation of batch) {
          try {
            await operation();
          } catch (error) {
            console.warn('DOM operation failed:', error);
          }
        }
        
        // 如果还有更多操作，让出控制权
        if (i + batchSize < operations.length) {
          await new Promise(resolve => setTimeout(resolve, 0));
        }
      }
      
      resolve();
    };
    
    if (useRequestAnimationFrame) {
      requestAnimationFrame(executeOperations);
    } else {
      executeOperations();
    }
  });
}

/**
 * 防抖批处理器
 */
export class DebouncedBatchProcessor<T> {
  private queue: T[] = [];
  private timeoutId: number | null = null;
  
  constructor(
    private processor: (items: T[]) => Promise<void>,
    private delay: number = 100,
    private maxBatchSize: number = 50
  ) {}

  /**
   * 添加项目到队列
   */
  add(item: T): void {
    this.queue.push(item);
    
    // 如果队列满了，立即处理
    if (this.queue.length >= this.maxBatchSize) {
      this.flush();
      return;
    }
    
    // 重置防抖定时器
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
    
    this.timeoutId = window.setTimeout(() => {
      this.flush();
    }, this.delay);
  }

  /**
   * 立即处理队列中的所有项目
   */
  async flush(): Promise<void> {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    
    if (this.queue.length === 0) {
      return;
    }
    
    const items = [...this.queue];
    this.queue = [];
    
    try {
      await this.processor(items);
    } catch (error) {
      console.error('Batch processing failed:', error);
      // 将失败的项目重新加入队列
      this.queue.unshift(...items);
    }
  }

  /**
   * 获取当前队列大小
   */
  getQueueSize(): number {
    return this.queue.length;
  }

  /**
   * 清空队列
   */
  clear(): void {
    this.queue = [];
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }
}

/**
 * 创建并发块
 */
function createConcurrentChunks<T>(items: T[], concurrency: number): T[][] {
  if (concurrency >= items.length) {
    return items.map(item => [item]);
  }
  
  const chunks: T[][] = [];
  const chunkSize = Math.ceil(items.length / concurrency);
  
  for (let i = 0; i < items.length; i += chunkSize) {
    chunks.push(items.slice(i, i + chunkSize));
  }
  
  return chunks;
}

/**
 * 错误重试包装器
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: {
    maxRetries?: number;
    baseDelay?: number;
    maxDelay?: number;
    shouldRetry?: (error: Error) => boolean;
  } = {}
): Promise<T> {
  const { 
    maxRetries = 3, 
    baseDelay = 1000, 
    maxDelay = 5000,
    shouldRetry = () => true 
  } = options;
  
  let lastError: Error = new Error('Operation failed without specific error');
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      // 最后一次尝试失败，直接抛出错误
      if (attempt === maxRetries) {
        break;
      }
      
      // 检查是否应该重试
      if (!shouldRetry(lastError)) {
        break;
      }
      
      // 计算延迟时间 (指数退避 + 抖动)
      const delay = Math.min(
        baseDelay * Math.pow(2, attempt),
        maxDelay
      );
      const jitter = Math.random() * 0.3 * delay; // 30% 抖动
      
      await new Promise(resolve => setTimeout(resolve, delay + jitter));
    }
  }
  
  throw lastError;
}