{"version": "1.0", "description": "智能注入规则配置文件", "updatedAt": "2025-07-23T00:00:00.000Z", "rules": [{"id": "inline-short-text", "name": "短文本行内注入", "description": "对于短文本（少于50字符）使用行内注入", "condition": {"textLength": {"max": 50}, "displayType": ["inline", "inline-block"]}, "strategy": "inline", "priority": 1, "enabled": true, "options": {"animation": "fade", "delay": 0}}, {"id": "inline-links", "name": "链接行内注入", "description": "链接元素使用行内注入，保持流式布局", "condition": {"tagName": "a", "textLength": {"max": 100}}, "strategy": "inline", "priority": 2, "enabled": true}, {"id": "inline-spans", "name": "Span元素行内注入", "description": "span元素优先使用行内注入", "condition": {"tagName": "span", "displayType": ["inline", "inline-block"], "textLength": {"max": 200}}, "strategy": "inline", "priority": 3, "enabled": true}, {"id": "inline-navigation", "name": "导航菜单行内注入", "description": "导航菜单项使用行内注入", "condition": {"parent": {"tagName": ["nav", "menu"], "className": ["nav", "menu", "navigation"]}, "textLength": {"max": 80}}, "strategy": "inline", "priority": 5, "enabled": true}, {"id": "block-headings", "name": "标题侧边注入", "description": "所有标题元素使用侧边注入（同行右边翻译）", "condition": {"tagName": ["h1", "h2", "h3", "h4", "h5", "h6"]}, "strategy": "beside", "priority": 10, "enabled": true, "options": {"animation": "slide", "customClasses": ["lu-heading-translation"]}}, {"id": "block-paragraphs", "name": "段落块级注入", "description": "段落元素使用块级注入", "condition": {"tagName": "p", "textLength": {"min": 10}}, "strategy": "block", "priority": 11, "enabled": true}, {"id": "block-list-items", "name": "列表项块级注入", "description": "列表项使用块级注入", "condition": {"tagName": "li"}, "strategy": "block", "priority": 12, "enabled": true}, {"id": "block-long-divs", "name": "长内容Div块级注入", "description": "包含较长文本的div使用块级注入", "condition": {"tagName": "div", "textLength": {"min": 100}, "displayType": ["block", "flex"]}, "strategy": "block", "priority": 13, "enabled": true}, {"id": "block-table-cells", "name": "表格单元格块级注入", "description": "表格单元格使用块级注入", "condition": {"tagName": ["td", "th"], "textLength": {"min": 20}}, "strategy": "block", "priority": 14, "enabled": true}, {"id": "skip-code-elements", "name": "跳过代码元素", "description": "跳过代码相关元素的翻译", "condition": {"tagName": ["code", "pre", "kbd", "samp", "var"]}, "strategy": "skip", "priority": 0, "enabled": true}, {"id": "hidden-content-preserve", "name": "隐藏内容状态保持", "description": "对于隐藏的内容，翻译后保持相同的隐藏状态", "condition": {"and": [{"not": {"tagName": ["h1", "h2", "h3", "h4", "h5", "h6"]}}, {"or": [{"attributes": {"data-exclusion-reason": "hidden"}}, {"style": {"display": "none"}}, {"style": {"visibility": "hidden"}}, {"attributes": {"aria-hidden": "true"}}, {"className": ["hidden", "hide", "invisible", "collapsed"]}]}]}, "strategy": "hidden-preserve", "priority": 15, "enabled": true, "options": {"preserveVisibility": true, "inheritParentVisibility": true, "customClasses": ["lu-hidden-translation"]}}, {"id": "social-share-components", "name": "社交分享组件特殊处理", "description": "社交分享组件的隐藏对话框内容保持隐藏状态", "condition": {"and": [{"or": [{"className": ["uni-social-share__dialog", "uni-social-share__content", "social-share-dropdown", "share-dialog", "share-popup"]}, {"parent": {"className": ["uni-social-share", "social-share", "share-component"]}}]}, {"or": [{"attributes": {"data-exclusion-reason": "hidden"}}, {"style": {"display": "none"}}, {"attributes": {"aria-expanded": "false"}}]}]}, "strategy": "hidden-preserve", "priority": 16, "enabled": true, "options": {"preserveVisibility": true, "inheritParentVisibility": true, "customClasses": ["lu-social-share-hidden"]}}, {"id": "custom-brand-inline", "name": "品牌标识行内注入", "description": "品牌相关元素使用行内注入", "condition": {"customFunction": "brandDetection"}, "strategy": "inline", "priority": 4, "enabled": true}, {"id": "beside-tooltips", "name": "提示框侧边注入", "description": "提示框元素使用侧边注入", "condition": {"className": ["tooltip", "popup", "dropdown"], "textLength": {"min": 5, "max": 200}}, "strategy": "beside", "priority": 6, "enabled": false}], "customFunctions": {"brandDetection": "检测品牌相关的类名或属性，如 brand、logo、site-name、site-title 等关键词"}, "settings": {"defaultStrategy": "block", "enableAutoFallback": true, "performanceMode": "balanced", "debugMode": true}}