{"version": "2.0", "description": "高级注入规则配置文件 - 支持用户定义的复杂语法", "updatedAt": "2025-07-25T00:00:00.000Z", "rules": [{"id": "exclude-high-priority", "name": "高优先级排除", "description": "排除不应翻译的元素，优先级最高", "conditions": [{"tagName": ["header", "footer", "uni-feedback-survey-popup", "code", "script", "pre", "style"], "comment": "排除这些标签下面的所有元素"}, {"className": ["uni-feedback-survey-popup", "notranslate", "lu-skip"], "comment": "排除这些className的元素"}, {"nestedSelector": ["article div div span", "article div time"], "url": ["https://news.google.com/*", "*://news.google.com/*", "news.google.com"], "comment": "仅在 Google News 页面排除 article/div/div/span 路径的元素"}], "strategy": "skip", "priority": 100, "enabled": true}, {"id": "google-news-intelligent", "name": "Google News 智能新闻卡片", "description": "专门针对 Google News 新闻卡片的智能翻译，使用两层过滤和克隆替换策略", "conditions": [{"nestedSelector": ["article a"], "textLength": {"min": 20, "max": 200}, "customFunction": "isNewsCardHeadline", "comment": "先用 article a 快速过滤，再用自定义函数详细判断新闻标题"}], "strategy": "clone_replace", "priority": 98, "enabled": true, "options": {"animation": "fade", "delay": 100}}, {"id": "inline-short-text", "name": "短文本内联策略", "description": "短文本元素使用内联翻译", "conditions": [{"tagName": ["a", "div", "span"], "textLength": {"max": 20}, "comment": "a, div, span 标签的文本长度小于20"}, {"tagName": ["span"], "textLength": {"max": 500}, "comment": "span 标签的文本长度小于500"}], "strategy": "inline", "priority": 12, "enabled": true}, {"id": "nested-inline-elements", "name": "嵌套内联元素", "description": "特定嵌套结构使用内联策略", "conditions": [{"nestedSelector": "a div", "comment": "a 下的 div 需要用inline的策略"}, {"nestedSelector": ["button span", "button div"], "comment": "button 下面的span/div需要用inline的策略"}, {"tagName": ["button"], "comment": "button 内部文本用inline策略"}], "strategy": "inline", "priority": 14, "enabled": true}, {"id": "navigation-inline", "name": "导航菜单内联", "description": "导航相关元素使用内联翻译", "conditions": [{"nestedSelector": ["nav li", "nav a", "menu li", "menu a"], "textLength": {"max": 80}, "comment": "导航菜单项"}], "strategy": "inline", "priority": 13, "enabled": true}, {"id": "block-paragraphs", "name": "段落块级策略", "description": "段落和块级元素使用块级翻译", "conditions": [{"tagName": ["p"], "textLength": {"min": 10}, "comment": "段落元素，最小长度10字符"}], "strategy": "block", "priority": 10, "enabled": true}, {"id": "block-list-items", "name": "列表项块级", "description": "列表项使用块级翻译", "conditions": [{"tagName": ["li"], "textLength": {"min": 5}, "comment": "列表项元素"}], "strategy": "block", "priority": 11, "enabled": true}, {"id": "inline-headings", "name": "标题内联", "description": "标题元素使用内联翻译", "conditions": [{"tagName": ["h1", "h2", "h3", "h4", "h5", "h6"], "comment": "所有标题元素"}], "strategy": "inline", "priority": 15, "enabled": true}, {"id": "block-divs", "name": "Div块级策略", "description": "Div元素使用块级翻译", "conditions": [{"tagName": ["div"], "textLength": {"min": 10}, "comment": "Div元素，最小长度10字符"}], "strategy": "block", "priority": 8, "enabled": true}, {"id": "block-table-cells", "name": "表格单元格块级", "description": "表格单元格使用块级翻译", "conditions": [{"tagName": ["td", "th"], "textLength": {"min": 3}, "comment": "表格单元格"}], "strategy": "block", "priority": 9, "enabled": true}, {"id": "default-fallback", "name": "默认回退策略", "description": "默认策略，用于所有未匹配的元素", "conditions": [{"comment": "默认条件，匹配所有未处理的元素"}], "strategy": "block", "priority": 1, "enabled": true}], "customFunctions": {}, "settings": {"defaultStrategy": "block", "enableAutoFallback": true, "performanceMode": "balanced", "debugMode": true}, "metadata": {"author": "Advanced Translation System", "description": "支持复杂嵌套选择器和条件组合的翻译注入配置", "examples": {"nestedSelector": ["a div - a标签下的div元素", "button span - button标签下的span元素", "nav li a - 导航菜单中的链接"], "textLength": {"min": "最小文本长度", "max": "最大文本长度"}, "url": ["https://example.com - 完整URL匹配", "https://*.google.com - 通配符域名匹配", "https://news.google.com/* - 路径通配符匹配", "/news/ - 相对路径匹配", "条件说明: url数组中的任一URL匹配当前页面URL时，该条件才会被应用"], "priority": "数值越高优先级越高，建议范围0-100"}}}