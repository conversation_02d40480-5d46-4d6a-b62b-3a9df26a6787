/**
 * 默认配置 - 轻量化版本
 * 专注实用性，避免过度配置
 */

import { TranslationConfig } from '../types';

export const DEFAULT_CONFIG: TranslationConfig = {
  security: {
    // 调整后的长度限制 - 更宽松但安全
    maxLength: 15000,
    // 允许的HTML标签白名单
    allowedTags: [
      'a', 'strong', 'b', 'em', 'i', 'u', 'span', 'mark', 'small', 'sup', 'sub',
      'div', 'p', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 
      'ul', 'ol', 'li', 'blockquote'
    ],
    strictMode: false
  },
  
  cache: {
    // 简单但有效的缓存设置
    maxSize: 1000,
    ttl: 24 * 60 * 60 * 1000, // 24小时
    enabled: true
  },
  
  performance: {
    // 性能优化参数
    batchSize: 10,
    concurrency: 3,
    debounceMs: 100
  },
  
  rendering: {
    defaultMode: 'append',
    enableHtmlStructure: true,
    preserveLinks: true
  },
  
  debug: false
};

/**
 * 创建配置的工厂函数
 * 支持部分覆盖
 */
export function createConfig(overrides: Partial<TranslationConfig> = {}): TranslationConfig {
  return {
    security: {
      ...DEFAULT_CONFIG.security,
      ...overrides.security
    },
    cache: {
      ...DEFAULT_CONFIG.cache,
      ...overrides.cache
    },
    performance: {
      ...DEFAULT_CONFIG.performance,
      ...overrides.performance
    },
    rendering: {
      ...DEFAULT_CONFIG.rendering,
      ...overrides.rendering
    },
    debug: overrides.debug ?? DEFAULT_CONFIG.debug
  };
}

/**
 * 环境相关配置
 */
export function getEnvironmentConfig(): Partial<TranslationConfig> {
  const isDevelopment = typeof window !== 'undefined' && 
    (window.location.hostname === 'localhost' || 
     window.location.hostname.startsWith('127.0.0.1'));

  if (isDevelopment) {
    return {
      debug: true,
      cache: {
        ttl: 5 * 60 * 1000, // 开发环境5分钟缓存
        maxSize: 100,
        enabled: true
      }
    };
  }

  return {};
}