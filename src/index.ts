/**
 * 智能注入翻译系统主入口
 * 提供简单易用的API，集成智能注入规则引擎
 */

import { TranslateManagerAdapter, createTranslationManager } from './features/translation_pipeline';
import { 
  toManagerOptions, 
  updateTranslationConfig, 
  configDebugTools,
  getTranslationConfig,
  enableDebugMode,
  disableDebugMode,
  isDebugEnabled
} from './features/translation_pipeline/config';
import { resetInjectionRuleEngine } from './core/injection-rules';
import { createConfig, getEnvironmentConfig, DEFAULT_CONFIG } from './config/default-config';
import { 
  TranslationOptions, 
  TranslationResult, 
  BatchTranslationResult,
  TranslationConfig
} from './types';
// debug 导入已移除，使用手动 console.log

// 全局实例
let globalTranslationManager: TranslateManagerAdapter | null = null;

/**
 * 初始化智能翻译系统
 */
export function initializeTranslationSystem(config?: Partial<TranslationConfig>): TranslateManagerAdapter {
  // 如果传入了配置，更新全局配置
  if (config) {
    const configUpdates: any = {};
    if (config.debug !== undefined) configUpdates.debug = config.debug;
    if (config.targetLanguage !== undefined) configUpdates.targetLanguage = config.targetLanguage;
    if (config.performance?.concurrency !== undefined) configUpdates.concurrency = config.performance.concurrency;
    
    updateTranslationConfig(configUpdates);
  }

  // 使用统一配置创建管理器选项
  const managerOptions = toManagerOptions({
    // 允许传入的config覆盖特定选项 - 这些字段可能不在TranslationConfig中
    ...(config && 'translateFunction' in config && { translateFunction: (config as any).translateFunction }),
    ...(config && 'onError' in config && { onError: (config as any).onError }),
    ...(config && 'onProgress' in config && { onProgress: (config as any).onProgress }),
    ...(config && 'forceStrategy' in config && { forceStrategy: (config as any).forceStrategy }),
  });

  globalTranslationManager = createTranslationManager(managerOptions);
  
  if (managerOptions.debug) {
    console.log('🔧 [translation|DEBUG] 🚀 Smart injection translation system initialized with debug mode enabled', {
      config: managerOptions,
      smartInjectionEnabled: managerOptions.enableSmartInjection
    });
  }

  return globalTranslationManager;
}

/**
 * 获取翻译管理器实例
 */
export function getTranslationManager(): TranslateManagerAdapter {
  if (!globalTranslationManager) {
    globalTranslationManager = initializeTranslationSystem();
  }
  return globalTranslationManager;
}

/**
 * 便捷API：翻译单个元素
 */
export async function translateElement(
  element: HTMLElement, 
  options?: TranslationOptions
): Promise<TranslationResult> {
  const manager = getTranslationManager();
  return manager.translateElement(element, options);
}

/**
 * 便捷API：批量翻译元素
 */
export async function translateElements(
  elements: HTMLElement[], 
  options?: TranslationOptions
): Promise<BatchTranslationResult> {
  const manager = getTranslationManager();
  return manager.translateElements(elements, options);
}

/**
 * 便捷API：翻译整个页面
 */
export async function translatePage(options?: TranslationOptions): Promise<BatchTranslationResult> {
  const manager = getTranslationManager();
  return manager.translatePage(options);
}

/**
 * 便捷API：清除页面翻译
 */
export function clearPageTranslations(): number {
  const manager = getTranslationManager();
  return manager.clearPageTranslations();
}

/**
 * 便捷API：获取统计信息
 */
export function getTranslationStats() {
  const manager = getTranslationManager();
  return manager.getStats();
}

/**
 * 便捷API：重置统计信息
 */
export function resetTranslationStats(): void {
  const manager = getTranslationManager();
  manager.resetStats();
}

/**
 * 便捷API：清理缓存
 */
export function clearTranslationCache(): void {
  const manager = getTranslationManager();
  manager.clearCache();
}

// 导出类型和配置
export type {
  TranslationOptions,
  TranslationResult,
  BatchTranslationResult,
  TranslationConfig
} from './types';

export { DEFAULT_CONFIG, createConfig } from './config/default-config';
export { TranslateManagerAdapter as TranslationManager } from './features/translation_pipeline';
export { 
  getTranslationConfig, 
  updateTranslationConfig, 
  enableDebugMode, 
  disableDebugMode, 
  isDebugEnabled, 
  configDebugTools 
} from './features/translation_pipeline/config';
export { resetInjectionRuleEngine } from './core/injection-rules';

// 浏览器环境自动初始化
if (typeof window !== 'undefined') {
  // 在浏览器环境中暴露到全局对象
  (window as any).LucidTranslation = {
    initialize: initializeTranslationSystem,
    translateElement,
    translateElements,
    translatePage,
    clearPageTranslations,
    getStats: getTranslationStats,
    resetStats: resetTranslationStats,
    clearCache: clearTranslationCache,
    // 配置管理
    config: {
      get: getTranslationConfig,
      update: updateTranslationConfig,
      enableDebug: enableDebugMode,
      disableDebug: disableDebugMode,
      isDebugEnabled: isDebugEnabled,
      debug: configDebugTools
    },
    // 高级功能
    resetRuleEngine: resetInjectionRuleEngine
  };

  // DOM就绪后自动初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      initializeTranslationSystem();
    });
  } else {
    initializeTranslationSystem();
  }
}