import { TooltipProps } from '@tooltip/Tooltip';
import { errorLogger, StorageError, ErrorContext } from '../utils/error-logger';

// 存储配置接口
interface StorageConfig {
  maxMemorySize?: number;        // 最大内存缓存数量
  expireHours?: number;          // 数据过期时间（小时）
  keyPrefix?: string;            // 存储键前缀
}

// 存储项接口
interface StorageItem {
  data: TooltipProps;
  timestamp: number;
}

// 缓存统计信息
interface CacheStats {
  memoryCount: number;
  storageEstimate: number;
}

/**
 * 词典数据存储服务
 * 实现双层缓存：内存 Map + browser.storage.local
 */
export class DictionaryStorage {
  private memoryCache = new Map<string, StorageItem>();
  private readonly config: Required<StorageConfig>;
  private readonly storageKey: string;

  constructor(config: StorageConfig = {}) {
    this.config = {
      maxMemorySize: config.maxMemorySize ?? 100,
      expireHours: config.expireHours ?? 24,
      keyPrefix: config.keyPrefix ?? 'dict_'
    };
    this.storageKey = 'dictionary_cache';
  }

  /**
   * 获取数据
   * @param word 单词
   * @returns 词典数据或null
   */
  async get(word: string): Promise<TooltipProps | null> {
    const key = this.getKey(word);
    
    // 1. 检查内存缓存
    const memoryItem = this.memoryCache.get(key);
    if (memoryItem) {
      if (this.isExpired(memoryItem.timestamp)) {
        this.memoryCache.delete(key);
      } else {
        return memoryItem.data;
      }
    }

    // 2. 检查本地存储
    try {
      const storageData = await this.getFromStorage(key);
      if (storageData) {
        if (this.isExpired(storageData.timestamp)) {
          await this.removeFromStorage(key);
          return null;
        }
        
        // 将数据重新加载到内存缓存
        this.memoryCache.set(key, storageData);
        return storageData.data;
      }
    } catch (error) {
      const context: ErrorContext = {
        method: 'DictionaryStorage.get',
        component: 'DictionaryStorage',
        data: { word, key }
      };
      errorLogger.error(`Failed to get ${word} from storage`, error as Error, context);
    }

    return null;
  }

  /**
   * 存储数据
   * @param word 单词
   * @param data 词典数据
   */
  async set(word: string, data: TooltipProps): Promise<void> {
    const key = this.getKey(word);
    const item: StorageItem = {
      data,
      timestamp: Date.now()
    };

    // 1. 存储到内存缓存
    this.memoryCache.set(key, item);

    // 2. 管理内存缓存大小
    this.enforceMemoryLimit();

    // 3. 存储到本地存储
    try {
      await this.setToStorage(key, item);
    } catch (error) {
      const context: ErrorContext = {
        method: 'DictionaryStorage.set',
        component: 'DictionaryStorage',
        data: { word, key }
      };
      errorLogger.warn(`Failed to save ${word} to storage`, error as Error, context);
      // 即使本地存储失败，内存缓存仍然有效
    }
  }

  /**
   * 删除数据
   * @param word 单词
   */
  async remove(word: string): Promise<void> {
    const key = this.getKey(word);
    
    // 从内存缓存删除
    this.memoryCache.delete(key);
    
    // 从本地存储删除
    try {
      await this.removeFromStorage(key);
    } catch (error) {
      const context: ErrorContext = {
        method: 'DictionaryStorage.remove',
        component: 'DictionaryStorage',
        data: { word, key }
      };
      errorLogger.warn(`Failed to remove ${word} from storage`, error as Error, context);
    }
  }

  /**
   * 清空所有数据
   */
  async clear(): Promise<void> {
    // 清空内存缓存
    this.memoryCache.clear();
    
    // 清空本地存储
    try {
      if (typeof browser !== 'undefined' && browser.storage) {
        await browser.storage.local.clear();
      }
    } catch (error) {
      const context: ErrorContext = {
        method: 'DictionaryStorage.clear',
        component: 'DictionaryStorage'
      };
      errorLogger.warn('Failed to clear storage', error as Error, context);
    }
  }

  /**
   * 清理过期数据
   */
  async cleanExpiredData(): Promise<void> {
    try {
      if (typeof browser === 'undefined' || !browser.storage) return;

      const allData = await browser.storage.local.get(null);
      const expiredKeys: string[] = [];

      for (const [key, value] of Object.entries(allData)) {
        if (key.startsWith(this.config.keyPrefix) && 
            typeof value === 'object' && 
            value !== null &&
            'timestamp' in value) {
          const item = value as StorageItem;
          if (this.isExpired(item.timestamp)) {
            expiredKeys.push(key);
          }
        }
      }

      if (expiredKeys.length > 0) {
        for (const key of expiredKeys) {
          await browser.storage.local.remove(key);
        }
      }
    } catch (error) {
      const context: ErrorContext = {
        method: 'DictionaryStorage.cleanExpiredData',
        component: 'DictionaryStorage'
      };
      errorLogger.warn('Failed to clean expired data', error as Error, context);
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(): Promise<CacheStats> {
    const memoryCount = this.memoryCache.size;
    let storageEstimate = 0;

    try {
      if (typeof browser !== 'undefined' && browser.storage) {
        const allData = await browser.storage.local.get(null);
        storageEstimate = JSON.stringify(allData).length;
      }
    } catch (error) {
      const context: ErrorContext = {
        method: 'DictionaryStorage.getStats',
        component: 'DictionaryStorage'
      };
      errorLogger.warn('Failed to get storage stats', error as Error, context);
    }

    return {
      memoryCount,
      storageEstimate
    };
  }

  /**
   * 清理资源，防止内存泄漏
   */
  destroy(): void {
    try {
      // 清空内存缓存
      this.memoryCache.clear();
      
      errorLogger.info('DictionaryStorage destroyed successfully', {
        method: 'DictionaryStorage.destroy',
        component: 'DictionaryStorage'
      });
    } catch (error) {
      const context: ErrorContext = {
        method: 'DictionaryStorage.destroy',
        component: 'DictionaryStorage'
      };
      errorLogger.error('Failed to destroy DictionaryStorage', error as Error, context);
    }
  }

  // === 私有方法 ===

  private getKey(word: string): string {
    return `${this.config.keyPrefix}${word.toLowerCase()}`;
  }

  private isExpired(timestamp: number): boolean {
    const expireTime = this.config.expireHours * 60 * 60 * 1000;
    return Date.now() - timestamp > expireTime;
  }

  private async getFromStorage(key: string): Promise<StorageItem | null> {
    if (typeof browser === 'undefined' || !browser.storage) {
      return null;
    }

    try {
      const result = await browser.storage.local.get(key);
      return result && result[key] ? result[key] : null;
    } catch (error) {
      if (error instanceof Error && error.message.includes('Extension context invalidated')) {
        console.log('⚠️ [storage-service|WARN] Extension context invalidated, skipping storage read');
        return null;
      }
      throw error;
    }
  }

  private async setToStorage(key: string, item: StorageItem): Promise<void> {
    if (typeof browser === 'undefined' || !browser.storage) {
      return;
    }

    try {
      await browser.storage.local.set({ [key]: item });
    } catch (error) {
      if (error instanceof Error && error.message.includes('Extension context invalidated')) {
        console.log('⚠️ [storage-service|WARN] Extension context invalidated, skipping storage write');
        return;
      }
      throw error;
    }
  }

  private async removeFromStorage(key: string): Promise<void> {
    if (typeof browser === 'undefined' || !browser.storage) {
      return;
    }

    await browser.storage.local.remove(key);
  }

  private enforceMemoryLimit(): void {
    while (this.memoryCache.size > this.config.maxMemorySize) {
      this.evictOldestMemoryItem();
    }
  }

  private evictOldestMemoryItem(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, item] of this.memoryCache.entries()) {
      if (item.timestamp < oldestTime) {
        oldestTime = item.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.memoryCache.delete(oldestKey);
    }
  }
}

// 导出单例实例
export const dictionaryStorage = new DictionaryStorage();