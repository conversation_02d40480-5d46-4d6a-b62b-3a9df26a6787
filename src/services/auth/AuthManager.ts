import { storage } from 'wxt/utils/storage';
import { apiClient } from '../api';
import { ENV } from '../../utils/env';
import type {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  User,
  AuthState,
  AuthEvent,
  AuthEventType,
  TokenPair
} from '../../types/auth';

/**
 * 认证管理器 - 核心认证逻辑
 */
export class AuthManager {
  private currentState: AuthState = {
    isAuthenticated: false,
    user: null,
    tokens: null,
    loading: false,
    error: null
  };

  private listeners: ((state: AuthState) => void)[] = [];
  private eventListeners: ((event: AuthEvent) => void)[] = [];
  private initializePromise: Promise<void> | null = null;

  // 存储键配置
  private readonly storageKeys = {
    accessToken: 'local:auth.accessToken',
    refreshToken: 'local:auth.refreshToken',
    expiresAt: 'local:auth.expiresAt',
    user: 'local:auth.user'
  } as const;

  constructor() {
    this.initializeState();
  }

  /**
   * 公共初始化方法
   */
  async initialize(): Promise<void> {
    return this.initializeState();
  }

  /**
   * 初始化认证状态
   */
  private async initializeState(): Promise<void> {
    // 防止重复初始化
    if (this.initializePromise) {
      return this.initializePromise;
    }

    this.initializePromise = this.performInitialization();
    return this.initializePromise;
  }

  /**
   * 执行初始化逻辑
   */  
  private async performInitialization(): Promise<void> {
    try {
      const [hasTokens, user] = await Promise.all([
        this.hasTokens(),
        this.getStoredUser()
      ]);

      if (hasTokens && user) {
        // 验证令牌有效性
        const validToken = await this.getValidToken();
        if (validToken) {
          this.updateState({
            isAuthenticated: true,
            user,
            tokens: null, // 不在内存中保存令牌
            loading: false,
            error: null
          });

          this.emitEvent('login', { user });
          
          if (ENV.DEBUG_AUTH) {
            console.log('[AuthManager] Initialized with valid session');
          }
          return;
        }
      }

      // 清理无效状态
      await this.clearTokens();
      this.updateState({
        isAuthenticated: false,
        user: null,
        tokens: null,
        loading: false,
        error: null
      });

      if (ENV.DEBUG_AUTH) {
        console.log('[AuthManager] Initialized without session');
      }
    } catch (error) {
      console.error('[AuthManager] Failed to initialize state:', error);
      this.updateState({
        isAuthenticated: false,
        user: null,
        tokens: null,
        loading: false,
        error: '初始化失败'
      });
    }
  }

  /**
   * 存储令牌对
   */
  private async setTokens(tokens: TokenPair): Promise<void> {
    try {
      const expiresAt = Date.now() + (tokens.expiresIn * 1000);
      
      await Promise.all([
        storage.setItem(this.storageKeys.accessToken, tokens.accessToken),
        storage.setItem(this.storageKeys.refreshToken, tokens.refreshToken),
        storage.setItem(this.storageKeys.expiresAt, expiresAt)
      ]);

      if (ENV.DEBUG_AUTH) {
        console.log('[AuthManager] Tokens stored successfully', {
          expiresIn: tokens.expiresIn,
          expiresAt: new Date(expiresAt).toISOString()
        });
      }
    } catch (error) {
      console.error('[AuthManager] Failed to store tokens:', error);
      throw new Error('令牌存储失败');
    }
  }

  /**
   * 获取有效的访问令牌
   */
  private async getValidToken(): Promise<string | null> {
    try {
      const [token, expiresAt] = await Promise.all([
        storage.getItem<string>(this.storageKeys.accessToken),
        storage.getItem<number>(this.storageKeys.expiresAt)
      ]);

      if (!token || !expiresAt) {
        return null;
      }

      // 检查令牌是否过期（提前5分钟刷新）
      const bufferTime = 5 * 60 * 1000; // 5分钟缓冲时间
      if (expiresAt <= Date.now() + bufferTime) {
        if (ENV.DEBUG_AUTH) {
          console.log('[AuthManager] Token expired or expiring soon');
        }
        return null;
      }

      return token;
    } catch (error) {
      console.error('[AuthManager] Failed to get valid token:', error);
      return null;
    }
  }

  /**
   * 获取刷新令牌
   */
  private async getRefreshToken(): Promise<string | null> {
    try {
      return await storage.getItem<string>(this.storageKeys.refreshToken);
    } catch (error) {
      console.error('[AuthManager] Failed to get refresh token:', error);
      return null;
    }
  }

  /**
   * 存储用户信息
   */
  private async setUser(user: User): Promise<void> {
    try {
      await storage.setItem(this.storageKeys.user, user);
      if (ENV.DEBUG_AUTH) {
        console.log('[AuthManager] User info stored successfully');
      }
    } catch (error) {
      console.error('[AuthManager] Failed to store user info:', error);
      throw new Error('用户信息存储失败');
    }
  }

  /**
   * 获取存储的用户信息
   */
  private async getStoredUser(): Promise<User | null> {
    try {
      return await storage.getItem<User>(this.storageKeys.user);
    } catch (error) {
      console.error('[AuthManager] Failed to get user info:', error);
      return null;
    }
  }

  /**
   * 清空所有认证相关数据
   */
  private async clearTokens(): Promise<void> {
    try {
      await Promise.all([
        storage.removeItem(this.storageKeys.accessToken),
        storage.removeItem(this.storageKeys.refreshToken),
        storage.removeItem(this.storageKeys.expiresAt),
        storage.removeItem(this.storageKeys.user)
      ]);

      if (ENV.DEBUG_AUTH) {
        console.log('[AuthManager] All auth data cleared');
      }
    } catch (error) {
      console.error('[AuthManager] Failed to clear tokens:', error);
      throw new Error('认证数据清理失败');
    }
  }

  /**
   * 检查令牌是否存在（不验证有效性）
   */
  private async hasTokens(): Promise<boolean> {
    try {
      const [accessToken, refreshToken] = await Promise.all([
        storage.getItem<string>(this.storageKeys.accessToken),
        storage.getItem<string>(this.storageKeys.refreshToken)
      ]);
      return !!(accessToken && refreshToken);
    } catch (error) {
      console.error('[AuthManager] Failed to check tokens existence:', error);
      return false;
    }
  }

  /**
   * 用户登录
   */
  async login(email: string, password: string): Promise<LoginResponse> {
    this.updateState({ ...this.currentState, loading: true, error: null });

    try {
      const loginData: LoginRequest = {
        email,
        password,
        clientType: 'extension',
        redirect: false
      };

      const response = await apiClient.post('/auth/signin', loginData);
      
      if (response.ok) {
        const data = await response.json();
        
        if (data.success && data.user && data.accessToken && data.refreshToken) {
          // 存储令牌和用户信息
          await Promise.all([
            this.setTokens({
              accessToken: data.accessToken,
              refreshToken: data.refreshToken,
              expiresIn: data.expiresIn || 3600
            }),
            this.setUser(data.user)
          ]);

          // 更新状态
          this.updateState({
            isAuthenticated: true,
            user: data.user,
            tokens: null,
            loading: false,
            error: null
          });

          this.emitEvent('login', { user: data.user });

          if (ENV.DEBUG_AUTH) {
            console.log('[AuthManager] Login successful');
          }

          return {
            success: true,
            user: data.user
          };
        }
      }

      // 登录失败
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = errorData.message || '登录失败';

      this.updateState({
        ...this.currentState,
        loading: false,
        error: errorMessage
      });

      this.emitEvent('auth_error', { error: errorMessage });

      return {
        success: false,
        error: errorMessage
      };

    } catch (error) {
      const errorMessage = '网络错误，请检查连接';
      console.error('[AuthManager] Login error:', error);

      this.updateState({
        ...this.currentState,
        loading: false,
        error: errorMessage
      });

      this.emitEvent('auth_error', { error: errorMessage });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * 用户注册
   */
  async register(email: string, password: string, name?: string): Promise<LoginResponse> {
    this.updateState({ ...this.currentState, loading: true, error: null });

    try {
      const registerData: RegisterRequest = {
        email,
        password,
        name,
        clientType: 'extension'
      };

      const response = await apiClient.post('/auth/signup', registerData);
      
      if (response.ok) {
        const data = await response.json();
        
        if (data.success) {
          if (ENV.DEBUG_AUTH) {
            console.log('[AuthManager] Registration successful');
          }

          // 注册成功后自动登录
          return await this.login(email, password);
        }
      }

      // 注册失败
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = errorData.message || '注册失败';

      this.updateState({
        ...this.currentState,
        loading: false,
        error: errorMessage
      });

      this.emitEvent('auth_error', { error: errorMessage });

      return {
        success: false,
        error: errorMessage
      };

    } catch (error) {
      const errorMessage = '网络错误，请检查连接';
      console.error('[AuthManager] Registration error:', error);

      this.updateState({
        ...this.currentState,
        loading: false,
        error: errorMessage
      });

      this.emitEvent('auth_error', { error: errorMessage });

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      this.updateState({ ...this.currentState, loading: true });

      // 清理本地存储
      await this.clearTokens();

      // 更新状态
      this.updateState({
        isAuthenticated: false,
        user: null,
        tokens: null,
        loading: false,
        error: null
      });

      this.emitEvent('logout', {});

      if (ENV.DEBUG_AUTH) {
        console.log('[AuthManager] Logout successful');
      }
    } catch (error) {
      console.error('[AuthManager] Logout error:', error);
      this.updateState({ ...this.currentState, loading: false });
    }
  }

  /**
   * 检查认证状态
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      const validToken = await this.getValidToken();
      return !!validToken;
    } catch (error) {
      console.error('[AuthManager] Auth check error:', error);
      return false;
    }
  }

  /**
   * 获取当前用户
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const isAuth = await this.isAuthenticated();
      if (!isAuth) return null;

      return await this.getStoredUser();
    } catch (error) {
      console.error('[AuthManager] Get user error:', error);
      return null;
    }
  }

  /**
   * 获取当前状态
   */
  getState(): AuthState {
    return { ...this.currentState };
  }

  /**
   * 订阅状态变化
   */
  subscribe(listener: (state: AuthState) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * 订阅认证事件
   */
  subscribeToEvents(listener: (event: AuthEvent) => void): () => void {
    this.eventListeners.push(listener);
    return () => {
      const index = this.eventListeners.indexOf(listener);
      if (index > -1) {
        this.eventListeners.splice(index, 1);
      }
    };
  }

  /**
   * 更新状态并通知监听器
   */
  private updateState(newState: AuthState): void {
    this.currentState = newState;
    this.listeners.forEach(listener => {
      try {
        listener(newState);
      } catch (error) {
        console.error('[AuthManager] Listener error:', error);
      }
    });
  }

  /**
   * 发送认证事件
   */
  private emitEvent(type: AuthEventType, data?: any): void {
    const event: AuthEvent = {
      type,
      data,
      timestamp: Date.now()
    };

    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('[AuthManager] Event listener error:', error);
      }
    });
  }

  /**
   * 刷新用户信息
   */
  async refreshUserInfo(): Promise<User | null> {
    try {
      const response = await apiClient.get('/auth/profile');
      
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.user) {
          await this.setUser(data.user);
          
          this.updateState({
            ...this.currentState,
            user: data.user
          });

          return data.user;
        }
      }
    } catch (error) {
      console.error('[AuthManager] Refresh user info error:', error);
    }
    
    return null;
  }
}

// 导出单例实例
export const authManager = new AuthManager();