/**
 * AuthManager Tests - 错误消息翻译测试
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AuthManager } from './AuthManager';

// Mock APIClient
vi.mock('../api/APIClient', () => ({
  apiClient: {
    post: vi.fn(),
    get: vi.fn(),
    clearTokens: vi.fn(),
    setTokens: vi.fn(),
  }
}));

// Mock storage
vi.mock('wxt/utils/storage', () => ({
  storage: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
  }
}));

describe('AuthManager Error Message Translation', () => {
  let authManager: AuthManager;

  beforeEach(() => {
    authManager = new AuthManager();
    vi.clearAllMocks();
  });

  it('should translate common English error messages to Chinese', () => {
    // 使用反射访问私有方法进行测试
    const translateMethod = (authManager as any).translateErrorMessage.bind(authManager);

    // 测试常见错误消息翻译
    expect(translateMethod('Invalid email or password')).toBe('邮箱或密码错误');
    expect(translateMethod('Invalid credentials')).toBe('邮箱或密码错误');
    expect(translateMethod('User not found')).toBe('用户不存在');
    expect(translateMethod('Incorrect password')).toBe('密码错误');
    expect(translateMethod('Email not verified')).toBe('邮箱未验证');
    expect(translateMethod('Account locked')).toBe('账户已锁定');
    expect(translateMethod('Too many attempts')).toBe('尝试次数过多，请稍后再试');
    expect(translateMethod('Email already exists')).toBe('邮箱已存在');
  });

  it('should handle partial matches', () => {
    const translateMethod = (authManager as any).translateErrorMessage.bind(authManager);

    // 测试部分匹配
    expect(translateMethod('invalid email or password format')).toBe('邮箱或密码错误');
    expect(translateMethod('USER NOT FOUND')).toBe('用户不存在');
    expect(translateMethod('The password is incorrect')).toBe('密码错误');
  });

  it('should return original message if no translation found', () => {
    const translateMethod = (authManager as any).translateErrorMessage.bind(authManager);

    // 测试未知错误消息
    expect(translateMethod('Some unknown error')).toBe('Some unknown error');
    expect(translateMethod('Custom server error message')).toBe('Custom server error message');
  });

  it('should handle empty or null messages', () => {
    const translateMethod = (authManager as any).translateErrorMessage.bind(authManager);

    // 测试空消息
    expect(translateMethod('')).toBe('');
    expect(translateMethod(null as any)).toBe('');
    expect(translateMethod(undefined as any)).toBe('');
  });

  it('should be case insensitive', () => {
    const translateMethod = (authManager as any).translateErrorMessage.bind(authManager);

    // 测试大小写不敏感
    expect(translateMethod('INVALID EMAIL OR PASSWORD')).toBe('邮箱或密码错误');
    expect(translateMethod('invalid email or password')).toBe('邮箱或密码错误');
    expect(translateMethod('Invalid Email Or Password')).toBe('邮箱或密码错误');
  });
});
