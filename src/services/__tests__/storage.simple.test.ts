import { describe, it, expect, beforeEach, beforeAll, vi } from 'vitest';
import { DictionaryStorage } from '../storage';
import { TooltipProps } from '@tooltip/Tooltip';
import { errorLogger } from '../../utils/error-logger';

// Mock browser storage to track calls
const mockBrowserStorage = {
  get: vi.fn(),
  set: vi.fn(),
  remove: vi.fn(),
  clear: vi.fn()
};

// Override browser storage in tests
beforeAll(() => {
  (globalThis as any).browser = {
    storage: {
      local: mockBrowserStorage
    }
  };
  
  // Mock error logger to prevent console output during tests
  vi.spyOn(errorLogger, 'error').mockImplementation(() => {});
  vi.spyOn(errorLogger, 'warn').mockImplementation(() => {});
  vi.spyOn(errorLogger, 'info').mockImplementation(() => {});
});

describe('DictionaryStorage - 基础功能', () => {
  let storage: DictionaryStorage;
  
  const mockWordData: TooltipProps = {
    word: 'hello',
    phonetic: { us: '/həˈləʊ/', uk: '/həˈləʊ/' },
    explain: [{
      pos: 'noun',
      definitions: [{
        definition: 'A greeting or expression of goodwill.',
        chinese: '问候或善意的表达',
        chinese_short: '问候'
      }]
    }],
    theme: 'dark'
  };

  beforeEach(() => {
    storage = new DictionaryStorage();
    vi.clearAllMocks();
    // Default mock behavior
    mockBrowserStorage.get.mockResolvedValue({});
    mockBrowserStorage.set.mockResolvedValue(undefined);
    mockBrowserStorage.remove.mockResolvedValue(undefined);
    mockBrowserStorage.clear.mockResolvedValue(undefined);
  });

  describe('内存缓存基础操作', () => {
    it('应该能够存储和获取数据', async () => {
      await storage.set('hello', mockWordData);
      const result = await storage.get('hello');
      expect(result).toEqual(mockWordData);
    });

    it('应该在数据不存在时返回null', async () => {
      const result = await storage.get('nonexistent');
      expect(result).toBeNull();
    });

    it('应该能够删除数据', async () => {
      await storage.set('hello', mockWordData);
      await storage.remove('hello');
      const result = await storage.get('hello');
      expect(result).toBeNull();
    });

    it('应该能够清空所有数据', async () => {
      await storage.set('hello', mockWordData);
      await storage.set('test', mockWordData);
      await storage.clear();
      
      const result1 = await storage.get('hello');
      const result2 = await storage.get('test');
      
      expect(result1).toBeNull();
      expect(result2).toBeNull();
    });
  });

  describe('本地存储集成', () => {
    it('应该调用存储 API 保存数据', async () => {
      await storage.set('hello', mockWordData);
      
      expect(mockBrowserStorage.set).toHaveBeenCalledWith(
        expect.objectContaining({
          'dict_hello': expect.objectContaining({
            data: mockWordData,
            timestamp: expect.any(Number)
          })
        })
      );
    });

    it('应该从本地存储加载数据', async () => {
      const storedData = {
        'dict_hello': {
          data: mockWordData,
          timestamp: Date.now()
        }
      };
      
      mockBrowserStorage.get.mockResolvedValue(storedData);
      
      const result = await storage.get('hello');
      
      expect(mockBrowserStorage.get).toHaveBeenCalledWith('dict_hello');
      expect(result).toEqual(mockWordData);
    });

    it('应该处理存储错误', async () => {
      mockBrowserStorage.get.mockRejectedValue(new Error('Storage error'));
      
      // 应该返回 null 而不是抛出错误
      const result = await storage.get('hello');
      expect(result).toBeNull();
    });
  });

  describe('缓存过期机制', () => {
    it('应该识别过期数据', async () => {
      const expiredData = {
        'dict_hello': {
          data: mockWordData,
          timestamp: Date.now() - 25 * 60 * 60 * 1000 // 25小时前
        }
      };
      
      mockBrowserStorage.get.mockResolvedValue(expiredData);
      
      const result = await storage.get('hello');
      expect(result).toBeNull();
    });

    it('应该返回未过期数据', async () => {
      const freshData = {
        'dict_hello': {
          data: mockWordData,
          timestamp: Date.now() - 1000 // 1秒前
        }
      };
      
      mockBrowserStorage.get.mockResolvedValue(freshData);
      
      const result = await storage.get('hello');
      expect(result).toEqual(mockWordData);
    });
  });

  describe('容量管理', () => {
    it('应该获取统计信息', async () => {
      await storage.set('hello', mockWordData);
      await storage.set('test', mockWordData);
      
      const stats = await storage.getStats();
      
      expect(stats.memoryCount).toBe(2);
      expect(stats.storageEstimate).toBeGreaterThanOrEqual(0);
    });

    it('应该清理超过限制的内存缓存', async () => {
      const storageWithLimit = new DictionaryStorage({ maxMemorySize: 2 });
      
      await storageWithLimit.set('word1', mockWordData);
      await storageWithLimit.set('word2', mockWordData);
      await storageWithLimit.set('word3', mockWordData); // 这应该清理 word1
      
      const result1 = await storageWithLimit.get('word1');
      const result2 = await storageWithLimit.get('word2');
      const result3 = await storageWithLimit.get('word3');
      
      expect(result1).toBeNull(); // 最旧的被清理
      expect(result2).toEqual(mockWordData);
      expect(result3).toEqual(mockWordData);
    });
  });
});