/**
 * 翻译系统统一配置管理
 * 
 * 提供统一的配置接口，便于开发和调试
 */

import { TranslateManagerOptions } from './adapter';
import { OrchestratorConfig } from './orchestrator';

/**
 * 翻译系统全局配置
 */
/**
 * 缓存配置
 */
export interface CacheConfig {
  /** 是否启用缓存 */
  enabled: boolean;
  /** 内存缓存最大条目数 */
  maxMemorySize: number;
  /** 缓存过期时间（毫秒） */
  ttl: number;
  /** 是否启用持久化存储 */
  enablePersistence: boolean;
  /** 存储键名前缀 */
  storageKeyPrefix: string;
}

/**
 * 翻译系统全局配置
 */
export interface GlobalTranslationConfig {
  /** 调试模式 - 控制所有组件的调试输出 */
  debug: boolean;
  /** 目标语言 */
  targetLanguage: string;
  /** 并发翻译数量 */
  concurrency: number;
  /** 无障碍支持 */
  enableAccessibility: boolean;
  /** 懒加载 */
  enableLazyLoading: boolean;
  /** 性能优化 */
  enablePerformanceOptimization: boolean;
  /** 智能注入 */
  enableSmartInjection: boolean;
  /** 高级注入系统 */
  enableAdvancedInjection: boolean;
  /** 自动翻译新增节点 */
  enableAutoTranslation: boolean;
  /** 缓存配置 */
  cache: CacheConfig;
}

/**
 * 默认缓存配置
 */
const DEFAULT_CACHE_CONFIG: CacheConfig = {
  enabled: false, // 默认启用缓存
  maxMemorySize: 1000, // 内存缓存最大1000条
  ttl: 24 * 60 * 60 * 1000, // 24小时过期
  enablePersistence: true, // 启用持久化存储
  storageKeyPrefix: 'lucid_translation_cache',
};

/**
 * 默认配置
 */
const DEFAULT_CONFIG: GlobalTranslationConfig = {
  debug: true, // 👈 启用debug模式查看翻译日志
  targetLanguage: 'zh',
  concurrency: 3,
  enableAccessibility: true,
  enableLazyLoading: true,
  enablePerformanceOptimization: true,
  enableSmartInjection: true,
  enableAdvancedInjection: true, // 👈 默认启用高级注入系统
  enableAutoTranslation: false, // 👈 默认关闭自动翻译，避免性能问题
  cache: DEFAULT_CACHE_CONFIG,
};

/**
 * 开发环境配置 - 启用调试模式
 */
const DEVELOPMENT_CONFIG: Partial<GlobalTranslationConfig> = {
  debug: true, // 开发环境默认启用调试
  concurrency: 2, // 开发环境降低并发数便于调试
  cache: {
    ...DEFAULT_CACHE_CONFIG,
    enabled: true, // 开发环境可以禁用缓存便于测试
    maxMemorySize: 500, // 开发环境减少内存使用
  },
};

/**
 * 生产环境配置 - 优化性能
 */
const PRODUCTION_CONFIG: Partial<GlobalTranslationConfig> = {
  debug: false, // 生产环境关闭调试
  concurrency: 5, // 生产环境提高并发数
  enablePerformanceOptimization: true,
  cache: {
    ...DEFAULT_CACHE_CONFIG,
    enabled: true, // 生产环境启用缓存优化性能
    maxMemorySize: 2000, // 生产环境增加缓存容量
    ttl: 7 * 24 * 60 * 60 * 1000, // 生产环境延长缓存时间到7天
  },
};

/**
 * 当前激活的配置
 */
let currentConfig: GlobalTranslationConfig = { ...DEFAULT_CONFIG };

/**
 * 环境检测
 */
function getEnvironment(): 'development' | 'production' | 'test' {
  // 检查是否在开发环境
  if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'development') {
    return 'development';
  }

  // 检查是否在测试环境
  if (typeof process !== 'undefined' && process.env?.NODE_ENV === 'test') {
    return 'test';
  }

  // 检查扩展开发模式
  if (typeof chrome !== 'undefined' && chrome.runtime?.getManifest) {
    const manifest = chrome.runtime.getManifest();
    // 检查是否在开发模式（通常开发版本会有特殊标识）
    if (manifest.version_name?.includes('dev') || manifest.name.includes('Dev')) {
      return 'development';
    }
  }

  // 默认为生产环境
  return 'production';
}

/**
 * 初始化配置
 */
function initializeConfig(): void {
  const env = getEnvironment();

  switch (env) {
    case 'development':
      currentConfig = { ...DEFAULT_CONFIG, ...DEVELOPMENT_CONFIG };
      break;
    case 'production':
      currentConfig = { ...DEFAULT_CONFIG, ...PRODUCTION_CONFIG };
      break;
    case 'test':
      currentConfig = { ...DEFAULT_CONFIG, debug: false }; // 测试环境通常不需要调试输出
      break;
  }
}

/**
 * 获取当前配置
 */
export function getTranslationConfig(): GlobalTranslationConfig {
  if (!currentConfig) {
    initializeConfig();
  }
  return { ...currentConfig };
}

/**
 * 更新配置
 */
export function updateTranslationConfig(updates: Partial<GlobalTranslationConfig>): void {
  currentConfig = { ...currentConfig, ...updates };

  if (updates.debug !== undefined) {
    console.log(`🔧 翻译系统调试模式已${updates.debug ? '启用' : '关闭'}`);
  }
}

/**
 * 启用调试模式
 */
export function enableDebugMode(): void {
  updateTranslationConfig({ debug: true });
}

/**
 * 关闭调试模式
 */
export function disableDebugMode(): void {
  updateTranslationConfig({ debug: false });
}

/**
 * 获取调试状态
 */
export function isDebugEnabled(): boolean {
  return getTranslationConfig().debug;
}

/**
 * 启用缓存
 */
export function enableCache(): void {
  const config = getTranslationConfig();
  updateTranslationConfig({
    cache: { ...config.cache, enabled: true }
  });
  console.log('🔧 翻译缓存已启用');
}

/**
 * 禁用缓存
 */
export function disableCache(): void {
  const config = getTranslationConfig();
  updateTranslationConfig({
    cache: { ...config.cache, enabled: false }
  });
  console.log('🔧 翻译缓存已禁用');
}

/**
 * 获取缓存启用状态
 */
export function isCacheEnabled(): boolean {
  return getTranslationConfig().cache.enabled;
}

/**
 * 更新缓存配置
 */
export function updateCacheConfig(cacheUpdates: Partial<CacheConfig>): void {
  const config = getTranslationConfig();
  updateTranslationConfig({
    cache: { ...config.cache, ...cacheUpdates }
  });
  console.log('🔧 缓存配置已更新:', cacheUpdates);
}

/**
 * 获取缓存配置
 */
export function getCacheConfig(): CacheConfig {
  return getTranslationConfig().cache;
}

/**
 * 重置为默认配置
 */
export function resetTranslationConfig(): void {
  initializeConfig();
  console.log('🔧 翻译系统配置已重置为默认值');
}

/**
 * 将全局配置转换为 TranslateManagerOptions
 */
/**
 * 将全局配置转换为 TranslateManagerOptions
 */
export function toManagerOptions(
  customOptions: Partial<TranslateManagerOptions> = {}
): TranslateManagerOptions {
  const config = getTranslationConfig();

  return {
    targetLanguage: config.targetLanguage,
    concurrency: config.concurrency,
    debug: config.debug,
    enableAccessibility: config.enableAccessibility,
    enableLazyLoading: config.enableLazyLoading,
    enablePerformanceOptimization: config.enablePerformanceOptimization,
    enableSmartInjection: config.enableSmartInjection,
    enableAdvancedInjection: config.enableAdvancedInjection, // 👈 新增高级注入配置
    enableAutoTranslation: config.enableAutoTranslation, // 👈 新增自动翻译配置
    ...customOptions, // 允许覆盖特定选项
  };
}

/**
 * 将全局配置转换为 OrchestratorConfig
 */
/**
 * 将全局配置转换为 OrchestratorConfig
 */
export function toOrchestratorConfig(
  customOptions: Partial<OrchestratorConfig> = {}
): OrchestratorConfig {
  const config = getTranslationConfig();

  return {
    targetLanguage: config.targetLanguage,
    concurrency: config.concurrency,
    debug: config.debug,
    enableAccessibility: config.enableAccessibility,
    enableLazyLoading: config.enableLazyLoading,
    enablePerformanceOptimization: config.enablePerformanceOptimization,
    enableSmartInjection: config.enableSmartInjection,
    enableAdvancedInjection: config.enableAdvancedInjection, // 👈 新增高级注入配置
    ...customOptions, // 允许覆盖特定选项
  };
}

/**
 * 配置调试工具
 */
export const configDebugTools = {
  /**
   * 打印当前配置
   */
  printConfig(): void {
    console.table(getTranslationConfig());
  },

  /**
   * 打印环境信息
   */
  printEnvironment(): void {
    console.log('🌍 环境信息:', {
      detected: getEnvironment(),
      isDebugEnabled: isDebugEnabled(),
      userAgent: navigator.userAgent.slice(0, 100),
      isExtension: typeof chrome !== 'undefined' && !!chrome.runtime
    });
  },

  /**
   * 临时启用调试模式（会话级别）
   */
  enableDebugForSession(): void {
    enableDebugMode();
    console.log('🔧 调试模式已临时启用（仅本次会话有效）');
  },

  /**
   * 配置预设
   */
  presets: {
    development: () => updateTranslationConfig(DEVELOPMENT_CONFIG),
    production: () => updateTranslationConfig(PRODUCTION_CONFIG),
    debug: () => updateTranslationConfig({
      debug: true,
      concurrency: 1,
      cache: { ...DEFAULT_CACHE_CONFIG, enabled: false } // 调试时禁用缓存
    }),
    performance: () => updateTranslationConfig({
      debug: false,
      concurrency: 8,
      enablePerformanceOptimization: true,
      cache: { ...DEFAULT_CACHE_CONFIG, enabled: true, maxMemorySize: 3000 } // 性能模式强化缓存
    }),
    noCache: () => updateTranslationConfig({
      cache: { ...DEFAULT_CACHE_CONFIG, enabled: false }
    }),
    maxCache: () => updateTranslationConfig({
      cache: {
        ...DEFAULT_CACHE_CONFIG,
        enabled: true,
        maxMemorySize: 5000,
        ttl: 30 * 24 * 60 * 60 * 1000 // 30天
      }
    }),
  },

  /**
   * 缓存管理工具
   */
  cache: {
    enable: enableCache,
    disable: disableCache,
    isEnabled: isCacheEnabled,
    getConfig: getCacheConfig,
    updateConfig: updateCacheConfig,

    /**
     * 临时禁用缓存（调试用）
     */
    disableForSession(): void {
      disableCache();
      console.log('🔧 缓存已临时禁用（仅本次会话有效）');
    },

    /**
     * 打印缓存配置
     */
    printConfig(): void {
      console.table(getCacheConfig());
    },

    /**
     * 设置快速配置
     */
    setQuickConfig(size: 'small' | 'medium' | 'large'): void {
      const configs = {
        small: { maxMemorySize: 200, ttl: 6 * 60 * 60 * 1000 }, // 6小时
        medium: { maxMemorySize: 1000, ttl: 24 * 60 * 60 * 1000 }, // 24小时
        large: { maxMemorySize: 3000, ttl: 7 * 24 * 60 * 60 * 1000 }, // 7天
      };
      updateCacheConfig(configs[size]);
      console.log(`🔧 缓存配置已设置为 ${size} 模式`);
    }
  }
};

// 初始化配置
initializeConfig();

// 开发环境下暴露调试工具到全局作用域
if (getEnvironment() === 'development') {
  (globalThis as any).translationConfigDebug = configDebugTools;
  console.log('🔧 调试工具已暴露到 globalThis.translationConfigDebug');
}