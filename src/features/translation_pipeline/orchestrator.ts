/**
 * Translation Orchestrator
 * 翻译编排器 - 协调所有模块按正确顺序工作的核心流程编排器
 */

import {
  TranslationState,
  TranslationConfig,
  TranslationStats,
  ScannedNode,
  TranslationResult,
  RenderResult,
  ScanConfig,
  RenderConfig,
  TranslationEventType,
  ProgressEventData,
  ErrorEventData,
  PageComplexity,
  ViewportScanResult
} from './types';
import { DomScanner } from './scanner';
import { DomRenderer } from './renderer';
import { StateManager } from './state-manager';
import { LifecycleManager } from './lifecycle-manager';
import { TranslationEventBus } from './event-bus';
import { ViewModeController, ViewMode } from '../../content/view-controller';
import { TranslateFormat } from '../translate/types';
import { TaskPriority } from '../../utils/promise-pool';
import { BatchLazyLoadItem } from '../../utils/lazy-loader';
// debug 导入已移除，使用手动 console.log

/**
 * 编排器配置
 */
export interface OrchestratorConfig extends TranslationConfig {
  /** 视图控制器 */
  viewController?: ViewModeController;
}

/**
 * 批量翻译结果
 */
export interface BatchTranslationResult {
  /** 总数量 */
  totalCount: number;
  /** 成功数量 */
  successCount: number;
  /** 失败数量 */
  failureCount: number;
  /** 详细结果 */
  results: TranslationResult[];
  /** 总时长 */
  totalDuration: number;
}

/**
 * 翻译编排器
 * 
 * 职责：
 * 1. 协调扫描、翻译、渲染的完整流程
 * 2. 管理翻译状态和进度
 * 3. 处理错误和异常情况
 * 4. 发布事件和统计信息
 */
export class TranslationOrchestrator {
  private scanner: DomScanner;
  private renderer: DomRenderer;
  private stateManager: StateManager;
  private lifecycleManager: LifecycleManager;
  private eventBus: TranslationEventBus;
  private viewController?: ViewModeController;
  private logger = console;

  private config: Required<TranslationConfig>;
  private abortController?: AbortController;

  constructor(
    scanner: DomScanner,
    renderer: DomRenderer,
    stateManager: StateManager,
    lifecycleManager: LifecycleManager,
    eventBus: TranslationEventBus,
    config: OrchestratorConfig
  ) {
    this.scanner = scanner;
    this.renderer = renderer;
    this.stateManager = stateManager;
    this.lifecycleManager = lifecycleManager;
    this.eventBus = eventBus;
    this.viewController = config.viewController;

    // 合并默认配置
    this.config = this.mergeDefaultConfig(config);

    if (this.config.debug) {
      this.logger.info('TranslationOrchestrator initialized', {
        hasViewController: !!this.viewController,
        config: this.config
      });
    }
  }

  /**
   * 翻译整个页面
   * @param options 翻译选项
   * @returns 翻译结果
   */
  async translatePage(options: {
    rootNode?: HTMLElement;
    excludeSelectors?: string[];
    minTextLength?: number;
    maxDepth?: number;
  } = {}): Promise<BatchTranslationResult> {
    const startTime = performance.now();

    // 检查是否可以开始翻译
    if (!this.stateManager.canStart()) {
      throw new Error(`Cannot start translation in state: ${this.stateManager.getState()}`);
    }

    this.abortController = new AbortController();

    try {
      if (this.config.debug) {
        this.logger.info('Starting page translation');
      }

      // 1. 状态管理：开始扫描
      this.stateManager.setState(TranslationState.SCANNING, 'translatePage');

      // 2. 性能预检查
      await this.performPreflightCheck();

      // 3. 扫描页面
      const scanResult = await this.scanPage(options);

      if (scanResult.nodes.length === 0) {
        if (this.config.debug) {
          this.logger.info('No translatable content found');
        }
        this.stateManager.setState(TranslationState.COMPLETED, 'no-content');
        return this.createEmptyResult(startTime);
      }

      // 🔍 调试日志：打印扫描结果中所有需要翻译的节点
      if (this.config.debug) {
        this.logger.info('🔍 扫描结果 - 需要翻译的节点详情:', {
          totalNodes: scanResult.nodes.length,
          scanStats: scanResult.stats,
          nodes: scanResult.nodes.map((node, index) => ({
            index,
            element: `${node.element.tagName}${node.element.className ? '.' + node.element.className : ''}${node.element.id ? '#' + node.element.id : ''}`,
            text: node.text.slice(0, 100) + (node.text.length > 100 ? '...' : ''),
            textLength: node.text.length,
            hasHtmlStructure: node.hasHtmlStructure,
            htmlContent: node.htmlContent?.slice(0, 100) + (node.htmlContent && node.htmlContent.length > 100 ? '...' : ''),
            linksCount: node.links?.length || 0,
            links: node.links?.map(link => ({ href: link.href, text: link.text })) || []
          }))
        });
      }

      // 4. 执行翻译流程
      const translationResult = await this.executeTranslationFlow(scanResult.nodes);

      // 🔍 调试日志：打印翻译结果详情
      if (this.config.debug) {
        this.logger.info('🔍 翻译结果详情:', {
          totalNodes: scanResult.nodes.length,
          successful: translationResult.successful,
          failed: translationResult.failed,
          results: translationResult.results.map((result, index) => ({
            index,
            success: result.success,
            originalText: result.originalText?.slice(0, 100) + (result.originalText && result.originalText.length > 100 ? '...' : ''),
            translatedText: result.translatedText?.slice(0, 100) + (result.translatedText && result.translatedText.length > 100 ? '...' : ''),
            format: result.format,
            error: result.error,
            duration: result.duration
          })),
          // 额外统计信息
          successfulTexts: translationResult.results
            .filter(r => r.success)
            .map(r => ({
              original: r.originalText?.slice(0, 50),
              translated: r.translatedText?.slice(0, 50)
            })),
          failedTexts: translationResult.results
            .filter(r => !r.success)
            .map(r => ({
              original: r.originalText?.slice(0, 50),
              error: r.error
            }))
        });
      }

      // 5. 切换到双语模式显示结果
      if (this.viewController) {
        this.viewController.setViewMode(ViewMode.DUAL);
      }

      // 6. 完成状态
      this.stateManager.setState(TranslationState.COMPLETED, 'success');

      const totalDuration = performance.now() - startTime;
      const result: BatchTranslationResult = {
        totalCount: scanResult.nodes.length,
        successCount: translationResult.successful,
        failureCount: translationResult.failed,
        results: translationResult.results,
        totalDuration
      };

      if (this.config.debug) {
        this.logger.info('Page translation completed', {
          ...result,
          duration: `${totalDuration.toFixed(2)}ms`
        });
      }

      return result;

    } catch (error) {
      // 错误处理
      this.stateManager.setState(TranslationState.ERROR, 'exception');

      const errorData: ErrorEventData = {
        error: error as Error,
        context: 'TranslationOrchestrator.translatePage',
        fatal: true
      };

      this.eventBus.publish(TranslationEventType.ERROR_OCCURRED, errorData, 'Orchestrator');

      if (this.config.onError) {
        this.config.onError(error as Error, 'translatePage');
      }

      if (this.config.debug) {
        this.logger.error('Page translation failed:', error);
      }

      throw error;
    }
  }

  /**
   * 翻译单个元素
   * @param element HTML元素
   * @param options 翻译选项
   * @returns 翻译结果
   */
  async translateElement(
    element: HTMLElement,
    options: { force?: boolean } = {}
  ): Promise<TranslationResult> {
    const startTime = performance.now();

    try {
      if (this.config.debug) {
        this.logger.info('Starting element translation', {
          element: element.tagName,
          force: options.force
        });
      }

      // 检查元素是否可用
      if (!options.force && !this.stateManager.isElementAvailable(element)) {
        throw new Error('Element is not available for translation');
      }

      // 分析节点
      const scannedNode = this.scanner.analyzeNode(element, 0);
      if (!scannedNode) {
        throw new Error('Element cannot be analyzed for translation');
      }

      // 标记为处理中
      if (!this.stateManager.markAsProcessing(element)) {
        throw new Error('Failed to mark element as processing');
      }

      try {
        // 🔧 优化方案：始终使用纯文本进行翻译，避免HTML标签干扰翻译质量
        const format = scannedNode.hasHtmlStructure ? 'html' : 'text';
        // 🔧 关键修改：无论节点是否有HTML结构，都只传递纯文本给翻译服务
        const contentToTranslate = scannedNode.text; // 始终使用纯文本进行翻译
        
        const translatedText = await this.translateText(
          contentToTranslate,  // 发送纯文本内容
          format              // 告知翻译服务内容格式
        );

        if (this.config.debug) {
          this.logger.info('🔧 Element translation completed', {
            originalText: scannedNode.text.slice(0, 50),
            translatedText: translatedText.slice(0, 50),
            hasHtmlStructure: scannedNode.hasHtmlStructure,
            originalLinksCount: scannedNode.links?.length || 0,
            contentToTranslate: contentToTranslate.slice(0, 50), // 显示实际传递给翻译引擎的内容
            actualFormat: format
          });
        }

        // 渲染配置：包含HTML格式信息和链接数据
        const renderConfig: RenderConfig & {
          format?: 'text' | 'html';
          originalLinks?: Array<{ href: string; text: string; attributes: Record<string, string> }>;
        } = {
          language: this.config.targetLanguage,
          enableAccessibility: this.config.enableAccessibility,
          debug: this.config.debug,
          format: format,
          originalLinks: scannedNode.links?.map(link => ({
            href: link.href,
            text: link.text,
            attributes: {}
          })) || []
        };

        const renderResult = await this.renderer.render(
          element,
          translatedText, // 传入纯文本翻译结果
          renderConfig
        );

        const duration = performance.now() - startTime;
        const result: TranslationResult = {
          success: renderResult.success,
          originalText: scannedNode.text,
          translatedText: translatedText,
          error: renderResult.error,
          format: format as TranslateFormat, // 使用实际的翻译格式
          duration
        };

        if (this.config.debug) {
          const status = result.success ? 'succeeded' : 'failed';
          this.logger.info(`Element translation ${status}`, {
            element: element.tagName,
            duration: `${duration.toFixed(2)}ms`,
            error: result.error
          });
        }

        return result;

      } catch (error) {
        // 翻译或渲染失败，清理处理状态
        this.stateManager.markAsFailed(element, (error as Error).message);
        throw error;
      }

    } catch (error) {
      const duration = performance.now() - startTime;
      const result: TranslationResult = {
        success: false,
        originalText: element.textContent || '',
        error: (error as Error).message,
        format: 'text',
        duration
      };

      if (this.config.debug) {
        this.logger.error('Element translation failed', {
          element: element.tagName,
          error: result.error,
          duration: `${duration.toFixed(2)}ms`
        });
      }

      return result;
    }
  }

  /**
   * 停止当前翻译
   */
  stopTranslation(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.stateManager.setState(TranslationState.IDLE, 'user-stop');

      if (this.config.debug) {
        this.logger.info('Translation stopped by user');
      }
    }
  }

  /**
   * 清除所有翻译
   */
  clearTranslations(): void {
    const clearedCount = this.renderer.clearAll();

    if (this.viewController) {
      this.viewController.setViewMode(ViewMode.ORIGIN);
    }

    this.stateManager.reset();

    if (this.config.debug) {
      this.logger.info('All translations cleared', { count: clearedCount });
    }
  }

  /**
   * 获取当前状态
   */
  getCurrentState(): TranslationState {
    return this.stateManager.getState();
  }

  /**
   * 获取统计信息
   */
  getStats(): TranslationStats {
    const baseStats = this.stateManager.getStats();
    const rendererStats = this.renderer.getStats();

    // 合并渲染统计
    baseStats.render = {
      totalRenders: rendererStats.totalRenders,
      successfulRenders: rendererStats.successfulRenders,
      failedRenders: rendererStats.failedRenders
    };

    return baseStats;
  }

  /**
   * 获取详细统计信息
   */
  getDetailedStats(): {
    translation: TranslationStats;
    scanner: any;
    renderer: any;
    lifecycle: any;
    eventBus: any;
  } {
    return {
      translation: this.getStats(),
      scanner: this.scanner.getStats(),
      renderer: this.renderer.getStats(),
      lifecycle: this.lifecycleManager.getStats(),
      eventBus: this.eventBus.getStats()
    };
  }

  /**
   * 销毁编排器
   */
  destroy(): void {
    this.stopTranslation();
    this.clearTranslations();

    // 销毁子组件
    this.scanner.destroy();
    this.renderer.destroy();
    this.stateManager.destroy();
    this.lifecycleManager.destroy();
    this.eventBus.destroy();

    if (this.config.debug) {
      this.logger.info('TranslationOrchestrator destroyed');
    }
  }

  /**
   * 执行预检查
   * @private
   */
  private async performPreflightCheck(): Promise<void> {
    if (!this.config.enablePerformanceOptimization) {
      return;
    }

    const suggestions = this.lifecycleManager.analyzePerformance();
    const criticalIssues = suggestions.filter(s => s.severity === 'critical');

    if (criticalIssues.length > 0) {
      if (this.config.debug) {
        this.logger.warn('Critical performance issues detected, performing cleanup');
      }

      this.lifecycleManager.performCleanup();
      await this.delay(1000); // 等待清理完成
    }
  }

  /**
   * 智能扫描页面 - 根据页面复杂度自动选择最优策略
   * 🚀 性能优化：集成异步分块、视口优先等优化策略
   * @private
   */
  private async scanPage(options: {
    rootNode?: HTMLElement;
    excludeSelectors?: string[];
    minTextLength?: number;
    maxDepth?: number;
  }): Promise<{ nodes: ScannedNode[]; stats: any }> {
    const scanConfig: ScanConfig = {
      rootNode: options.rootNode || document.body,
      excludeSelectors: options.excludeSelectors,
      minTextLength: options.minTextLength || 5,
      maxDepth: options.maxDepth,
      enablePerformanceMonitoring: this.config.enablePerformanceOptimization
    };

    // 使用智能扫描决策
    const scanResult = await this.performIntelligentScan(scanConfig);
    
    // 更新状态管理器的扫描统计
    this.stateManager.updateScanStats(
      scanResult.stats?.totalScanned || scanResult.nodes.length,
      scanResult.nodes.length
    );

    return scanResult;
  }

  /**
   * 执行智能扫描决策
   * 🚀 性能核心：根据页面复杂度选择最优扫描策略
   * @private
   */
  private async performIntelligentScan(config: ScanConfig): Promise<{
    nodes: ScannedNode[];
    stats: any;
  }> {
    const startTime = performance.now();
    
    try {
      // 分析页面复杂度
      const pageMetrics = this.analyzePageComplexity(config.rootNode || document.body);
      
      if (this.config.debug) {
        this.logger.info('🧠 智能扫描决策', {
          complexity: pageMetrics.estimatedComplexity,
          domNodeCount: pageMetrics.domNodeCount,
          hasInfiniteScroll: pageMetrics.hasInfiniteScroll,
          isDynamic: pageMetrics.isDynamic
        });
      }

      let scanResult: ScannedNode[];
      let scanStats: any = {};

      // 策略1: 大型页面使用视口优先扫描
      if (pageMetrics.domNodeCount > 5000 || pageMetrics.estimatedComplexity === 'extreme') {
        if (this.config.debug) {
          this.logger.info('📱 使用视口优先扫描策略');
        }
        
        const viewportResult = await this.scanner.scanViewportFirst(config);
        scanResult = viewportResult.viewportNodes;
        
        // 立即处理视口内节点，异步处理背景节点
        this.processBackgroundNodesAsync(viewportResult.backgroundScanPromise);
        
        scanStats = {
          strategy: 'viewport-first',
          viewportNodes: viewportResult.viewportNodes.length,
          hasBackgroundScan: true
        };
      }
      // 策略2: 中型页面使用异步分块扫描
      else if (pageMetrics.domNodeCount > 1000 || pageMetrics.estimatedComplexity === 'high') {
        if (this.config.debug) {
          this.logger.info('⚡ 使用异步分块扫描策略');
        }
        
        scanResult = await this.scanner.scanAsync(config, config.chunkSize || 200);
        scanStats = {
          strategy: 'async-chunked',
          chunkSize: config.chunkSize || 200
        };
      }
      // 策略3: 小型页面使用同步扫描
      else {
        if (this.config.debug) {
          this.logger.info('🔄 使用同步扫描策略');
        }
        
        const syncResult = this.scanner.scan(config);
        scanResult = syncResult.nodes;
        scanStats = {
          strategy: 'synchronous',
          ...syncResult.stats
        };
      }

      const duration = performance.now() - startTime;
      
      if (this.config.debug) {
        this.logger.info('🎯 智能扫描完成', {
          strategy: scanStats.strategy,
          nodesFound: scanResult.length,
          duration: `${duration.toFixed(2)}ms`,
          performance: `${(scanResult.length / duration * 1000).toFixed(0)} nodes/sec`
        });
      }

      return {
        nodes: scanResult,
        stats: {
          ...scanStats,
          totalScanned: pageMetrics.domNodeCount,
          translatableFound: scanResult.length,
          duration,
          complexity: pageMetrics.estimatedComplexity
        }
      };

    } catch (error) {
      // 降级到同步扫描
      if (this.config.debug) {
        this.logger.warn('⚠️ 智能扫描失败，降级到同步扫描', { error });
      }
      
      const fallbackResult = this.scanner.scan(config);
      return {
        nodes: fallbackResult.nodes,
        stats: {
          ...fallbackResult.stats,
          strategy: 'fallback-sync',
          fallbackReason: (error as Error).message
        }
      };
    }
  }

  /**
   * 分析页面复杂度
   * @private
   */
  private analyzePageComplexity(rootNode: HTMLElement): PageComplexity {
    const startTime = performance.now();
    
    // 计算DOM节点总数
    const domNodeCount = rootNode.querySelectorAll('*').length;
    
    // 检测无限滚动
    const hasInfiniteScroll = this.detectInfiniteScroll(rootNode);
    
    // 检测动态内容
    const isDynamic = this.detectDynamicContent(rootNode);
    
    // 估算复杂度等级
    let estimatedComplexity: PageComplexity['estimatedComplexity'];
    if (domNodeCount > 15000) {
      estimatedComplexity = 'extreme';
    } else if (domNodeCount > 5000 || hasInfiniteScroll) {
      estimatedComplexity = 'high';
    } else if (domNodeCount > 1000 || isDynamic) {
      estimatedComplexity = 'medium';
    } else {
      estimatedComplexity = 'low';
    }
    
    const analysisTime = performance.now() - startTime;
    
    if (this.config.debug) {
      this.logger.info('📊 页面复杂度分析', {
        domNodeCount,
        estimatedComplexity,
        hasInfiniteScroll,
        isDynamic,
        analysisTime: `${analysisTime.toFixed(2)}ms`
      });
    }

    return {
      domNodeCount,
      hasInfiniteScroll,
      isDynamic,
      estimatedComplexity
    };
  }

  /**
   * 检测无限滚动特征
   * @private
   */
  private detectInfiniteScroll(rootNode: HTMLElement): boolean {
    // 检查常见的无限滚动指示器
    const infiniteScrollSelectors = [
      '[data-infinite-scroll]',
      '[data-lazy-load]',
      '.infinite-scroll',
      '.lazy-load',
      '[data-scroll-loading]',
      '.scroll-loading'
    ];
    
    return infiniteScrollSelectors.some(selector => 
      rootNode.querySelector(selector) !== null
    );
  }

  /**
   * 检测动态内容特征
   * @private
   */
  private detectDynamicContent(rootNode: HTMLElement): boolean {
    // 检查React、Vue等SPA框架的特征
    const spaIndicators = [
      '[data-reactroot]',
      '[data-react-root]', 
      '[data-vue]',
      '#app',
      '.app',
      '[ng-app]',
      '[data-ng-app]'
    ];
    
    // 检查AJAX容器
    const ajaxContainers = [
      '[data-ajax]',
      '.ajax-content',
      '[data-dynamic]',
      '.dynamic-content'
    ];
    
    const indicators = [...spaIndicators, ...ajaxContainers];
    
    return indicators.some(selector => 
      rootNode.querySelector(selector) !== null
    );
  }

  /**
   * 异步处理背景节点
   * @private
   */
  private async processBackgroundNodesAsync(backgroundScanPromise: Promise<ScannedNode[]>): Promise<void> {
    try {
      const backgroundNodes = await backgroundScanPromise;
      
      if (this.config.debug) {
        this.logger.info('🔄 开始处理背景节点', {
          backgroundNodeCount: backgroundNodes.length
        });
      }
      
      // TODO: 这里可以添加后台翻译处理逻辑
      // 目前先简单记录日志
      if (backgroundNodes.length > 0) {
        if (this.config.debug) {
          this.logger.info('📝 背景节点处理完成', {
            processedCount: backgroundNodes.length
          });
        }
      }
      
    } catch (error) {
      if (this.config.debug) {
        this.logger.warn('⚠️ 背景节点处理失败', { error });
      }
    }
  }

  /**
   * 执行翻译流程
   * @private
   */
  private async executeTranslationFlow(nodes: ScannedNode[]): Promise<{
    successful: number;
    failed: number;
    results: TranslationResult[];
  }> {
    this.stateManager.setState(TranslationState.TRANSLATING, 'start-translation');

    // 根据是否启用懒加载选择策略
    if (this.config.enableLazyLoading && nodes.length > 50) {
      return this.executeLazyTranslation(nodes);
    } else {
      return this.executeBatchTranslation(nodes);
    }
  }

  /**
   * 执行批量翻译 - 🚀 性能优化版本：真正的批量并行翻译
   * @private
   */
  private async executeBatchTranslation(nodes: ScannedNode[]): Promise<{
    successful: number;
    failed: number;
    results: TranslationResult[];
  }> {
    const results: TranslationResult[] = [];
    let successful = 0;
    let failed = 0;

    this.stateManager.setState(TranslationState.TRANSLATING, 'start-batch-translation');

    // 更新翻译统计
    this.stateManager.updateTranslationStats(nodes.length);

    if (this.config.debug) {
      console.log(`🚀 [orchestrator|BATCH] 开始批量翻译 ${nodes.length} 个节点`);
    }

    // 🚀 性能优化：预加载配置缓存，避免在每个节点渲染时重复解析
    try {
      const preloadStartTime = performance.now();
      
      // 通过渲染器预加载配置
      await this.renderer.preloadConfig();
      
      const preloadDuration = performance.now() - preloadStartTime;
      if (this.config.debug) {
        console.log(`✅ [orchestrator|CONFIG] 配置预加载完成`, {
          duration: `${preloadDuration.toFixed(2)}ms`,
          willBenefitNodes: nodes.length,
          estimatedBenefit: preloadDuration > 0 ? 
            `避免每节点 ${(preloadDuration / Math.max(nodes.length, 1)).toFixed(2)}ms 重复解析` : 
            '配置已缓存'
        });
      }
    } catch (configError) {
      if (this.config.debug) {
        console.warn(`⚠️ [orchestrator|CONFIG] 配置预加载失败，将回退到按需加载`, configError);
      }
      // 配置预加载失败不影响翻译流程，继续执行
    }

    try {
      // 🚀 性能优化：一次性提取所有文本内容
      const textsToTranslate = nodes.map(node => node.text);
      
      if (this.config.debug) {
        console.log(`📦 [orchestrator|BATCH] 准备批量翻译`, {
          totalTexts: textsToTranslate.length,
          textPreviews: textsToTranslate.slice(0, 3).map((text, i) => ({
            index: i,
            preview: text.slice(0, 50) + (text.length > 50 ? '...' : ''),
            length: text.length
          })),
          averageLength: Math.round(textsToTranslate.reduce((sum, t) => sum + t.length, 0) / textsToTranslate.length)
        });
      }

      // 🚀 关键优化：使用批量翻译代替循环串行翻译，添加超时保护
      const batchStartTime = performance.now();
      const BATCH_TIMEOUT_MS = 30000; // 30秒超时
      
      let translatedTexts: string[];
      try {
        // 使用Promise.race实现超时控制
        const translationPromise = this.translateTextBatchInternal(
          textsToTranslate,
          'text' // 始终使用纯文本格式确保最佳性能
        );
        
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`批量翻译超时：${BATCH_TIMEOUT_MS}ms`));
          }, BATCH_TIMEOUT_MS);
        });

        translatedTexts = await Promise.race([translationPromise, timeoutPromise]);
      } catch (error) {
        if ((error as Error).message.includes('超时')) {
          if (this.config.debug) {
            console.warn(`⏰ [orchestrator|TIMEOUT] 批量翻译超时，回退到单个处理`, {
              timeout: BATCH_TIMEOUT_MS,
              nodesCount: nodes.length
            });
          }
          // 超时时回退到单个处理模式
          throw error;
        }
        // 其他错误也抛出，会触发回退逻辑
        throw error;
      }
      
      const batchDuration = performance.now() - batchStartTime;

      if (this.config.debug) {
        console.log(`✅ [orchestrator|BATCH] 批量翻译完成`, {
          duration: `${batchDuration.toFixed(2)}ms`,
          totalTexts: translatedTexts.length,
          averagePerText: `${(batchDuration / translatedTexts.length).toFixed(2)}ms`,
          successfulTranslations: translatedTexts.filter(t => t && t.length > 0).length,
          actualThroughput: `${(nodes.length * 1000 / batchDuration).toFixed(1)} 文本/秒`,
          batchEfficiency: batchDuration > 0 ? 
            `平均 ${(batchDuration / nodes.length).toFixed(2)}ms 每文本` : 
            '批量处理完成'
        });
      }

      // 🎯 批量渲染阶段：将翻译结果渲染到DOM
      this.stateManager.setState(TranslationState.RENDERING, 'start-batch-rendering');
      
      // 并行处理渲染（渲染不涉及网络请求，可以安全并行）
      const renderPromises = nodes.map(async (node, index) => {
        const translatedText = translatedTexts[index] || node.text;
        
        try {
          // 检查是否被中止
          if (this.abortController?.signal.aborted) {
            throw new Error('Translation aborted');
          }

          const format = node.hasHtmlStructure ? 'html' : 'text';
          
          // 构建渲染配置
          const renderConfig: RenderConfig & {
            format?: 'text' | 'html';
            originalLinks?: Array<{ href: string; text: string; attributes: Record<string, string> }>;
          } = {
            language: this.config.targetLanguage,
            enableAccessibility: this.config.enableAccessibility,
            debug: this.config.debug,
            format: format,
            originalLinks: node.links?.map(link => ({
              href: link.href,
              text: link.text,
              attributes: {}
            })) || []
          };

          const renderResult = await this.renderer.render(
            node.element,
            translatedText,
            renderConfig
          );

          const translationResult: TranslationResult = {
            success: renderResult.success,
            originalText: node.text,
            translatedText,
            error: renderResult.error,
            format: format as TranslateFormat,
            duration: renderResult.duration
          };

          return translationResult;

        } catch (error) {
          const errorResult: TranslationResult = {
            success: false,
            originalText: node.text,
            error: (error as Error).message,
            format: 'text',
            duration: 0
          };

          if (this.config.onError) {
            this.config.onError(error as Error, 'batch-rendering');
          }

          return errorResult;
        }
      });

      // 等待所有渲染完成
      const renderResults = await Promise.all(renderPromises);
      results.push(...renderResults);

      // 统计成功和失败数量
      successful = renderResults.filter(r => r.success).length;
      failed = renderResults.filter(r => !r.success).length;

      // 报告最终进度
      this.reportProgress(nodes.length, nodes.length, successful, failed, '批量翻译完成');

      if (this.config.debug) {
        console.log(`🎉 [orchestrator|BATCH] 批量翻译和渲染全部完成`, {
          totalDuration: `${(performance.now() - batchStartTime).toFixed(2)}ms`,
          translationDuration: `${batchDuration.toFixed(2)}ms`,
          renderingDuration: `${(performance.now() - batchStartTime - batchDuration).toFixed(2)}ms`,
          successful,
          failed,
          successRate: `${((successful / nodes.length) * 100).toFixed(1)}%`
        });
      }

    } catch (error) {
      if (this.config.debug) {
        console.error(`❌ [orchestrator|BATCH] 批量翻译失败，回退到单个处理`, error);
      }

      // 🔄 回退策略：如果批量翻译失败，回退到原来的单个处理方式
      return this.executeFallbackTranslation(nodes);
    }

    return { successful, failed, results };
  }

  /**
   * 回退翻译策略 - 当批量翻译失败时使用
   * @private
   */
  private async executeFallbackTranslation(nodes: ScannedNode[]): Promise<{
    successful: number;
    failed: number;
    results: TranslationResult[];
  }> {
    const results: TranslationResult[] = [];
    let successful = 0;
    let failed = 0;

    if (this.config.debug) {
      console.log(`🔄 [orchestrator|FALLBACK] 使用单个翻译模式处理 ${nodes.length} 个节点`);
    }

    // 🚀 在回退翻译中也预加载配置，减少重复解析
    try {
      await this.renderer.preloadConfig();
      if (this.config.debug) {
        console.log(`✅ [orchestrator|FALLBACK] 配置预加载完成，将应用于 ${nodes.length} 个节点`);
      }
    } catch (configError) {
      if (this.config.debug) {
        console.warn(`⚠️ [orchestrator|FALLBACK] 配置预加载失败，使用按需加载`, configError);
      }
    }

    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];

      // 检查是否被中止
      if (this.abortController?.signal.aborted) {
        break;
      }

      try {
        const format = node.hasHtmlStructure ? 'html' : 'text';
        
        // 🚀 为单个翻译添加超时保护
        const SINGLE_TIMEOUT_MS = 10000; // 10秒超时
        const translationPromise = this.translateText(node.text, format);
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`单个翻译超时：${SINGLE_TIMEOUT_MS}ms`));
          }, SINGLE_TIMEOUT_MS);
        });

        const translatedText = await Promise.race([translationPromise, timeoutPromise]);

        const renderConfig: RenderConfig & {
          format?: 'text' | 'html';
          originalLinks?: Array<{ href: string; text: string; attributes: Record<string, string> }>;
        } = {
          language: this.config.targetLanguage,
          enableAccessibility: this.config.enableAccessibility,
          debug: this.config.debug,
          format: format,
          originalLinks: node.links?.map(link => ({
            href: link.href,
            text: link.text,
            attributes: {}
          })) || []
        };

        const renderResult = await this.renderer.render(
          node.element,
          translatedText,
          renderConfig
        );

        const translationResult: TranslationResult = {
          success: renderResult.success,
          originalText: node.text,
          translatedText,
          error: renderResult.error,
          format: format as TranslateFormat,
          duration: renderResult.duration
        };

        results.push(translationResult);

        if (renderResult.success) {
          successful++;
        } else {
          failed++;
        }

        this.reportProgress(i + 1, nodes.length, successful, failed, node.text);

      } catch (error) {
        failed++;
        const errorResult: TranslationResult = {
          success: false,
          originalText: node.text,
          error: (error as Error).message,
          format: 'text',
          duration: 0
        };
        results.push(errorResult);

        this.reportProgress(i + 1, nodes.length, successful, failed, node.text);

        if (this.config.onError) {
          this.config.onError(error as Error, 'fallback-translation');
        }

        if (this.config.debug) {
          this.logger.warn('Fallback translation failed for node', {
            element: node.element.tagName,
            error: (error as Error).message
          });
        }
      }
    }

    return { successful, failed, results };
  }

  /**
   * 执行懒加载翻译 - 🚀 优化版本：使用批量处理大幅提升性能
   * @private
   */
  private async executeLazyTranslation(nodes: ScannedNode[]): Promise<{
    successful: number;
    failed: number;
    results: TranslationResult[];
  }> {
    const elements = nodes.map(n => n.element);
    const { visibleElements, invisibleElements } =
      this.lifecycleManager.categorizeElementsByVisibility(elements);

    // 获取可见节点
    const visibleNodes = nodes.filter(node =>
      visibleElements.includes(node.element)
    );

    // 立即翻译可见节点
    const visibleResult = await this.executeBatchTranslation(visibleNodes);

    // 🚀 核心优化：为不可见节点设置批量懒加载
    const invisibleNodes = nodes.filter(node =>
      invisibleElements.includes(node.element)
    );

    if (invisibleNodes.length > 0) {
      // 创建批量懒加载项目（不包含单独的handler）
      const batchLazyItems = invisibleNodes.map((node, index) => ({
        id: `lazy-translate-${Date.now()}-${index}`,
        element: node.element,
        priority: this.lifecycleManager.calculateLazyPriority(node.element),
        once: true,
        metadata: {
          text: node.text.slice(0, 100),
          tagName: node.element.tagName,
          hasHtmlStructure: node.hasHtmlStructure,
          nodeIndex: index
        }
      }));

      // 🎯 关键优化：设置批量处理回调，一次性处理多个可见元素
      const batchTranslationHandler = async (batchItems: BatchLazyLoadItem[]) => {
        const startTime = performance.now();
        
        if (this.config.debug) {
          console.log(`🚀 [orchestrator|LAZY-BATCH] 开始批量懒加载翻译`, {
            itemCount: batchItems.length,
            items: batchItems.map(item => ({
              id: item.id,
              element: item.element.tagName,
              textPreview: item.metadata?.text || 'N/A'
            }))
          });
        }

        // 🚀 性能优化：将批量懒加载项目转换为扫描节点，复用批量翻译流程
        const batchNodes: ScannedNode[] = batchItems.map(item => {
          // 根据元素ID找到对应的原始节点
          const originalNode = invisibleNodes.find(node => 
            node.element === item.element
          );
          
          if (!originalNode) {
            // 如果找不到原始节点，创建基本节点
            return {
              element: item.element,
              text: item.element.textContent || '',
              hasHtmlStructure: false,
              priority: TaskPriority.NORMAL,
              position: 0
            };
          }
          
          return originalNode;
        });

        try {
          // 🎯 复用现有的批量翻译逻辑，获得最佳性能
          const batchResult = await this.executeBatchTranslation(batchNodes);
          
          const duration = performance.now() - startTime;
          
          if (this.config.debug) {
            console.log(`✅ [orchestrator|LAZY-BATCH] 批量懒加载翻译完成`, {
              duration: `${duration.toFixed(2)}ms`,
              processedNodes: batchNodes.length,
              successfulTranslations: batchResult.successful,
              failedTranslations: batchResult.failed,
              throughput: `${(batchNodes.length * 1000 / duration).toFixed(1)} 节点/秒`,
              averagePerNode: `${(duration / batchNodes.length).toFixed(2)}ms`
            });
          }

          // 这里不需要返回结果，因为批量翻译已经完成了DOM渲染
          
        } catch (error) {
          if (this.config.debug) {
            console.error(`❌ [orchestrator|LAZY-BATCH] 批量懒加载翻译失败，尝试回退处理`, {
              error: (error as Error).message,
              nodeCount: batchNodes.length
            });
          }
          
          // 🔧 修复：实现渐进式回退机制
          await this.executeFallbackLazyTranslation(batchNodes);
        }
      };

      // 🚀 设置批量处理回调并观察元素
      this.lifecycleManager.setBatchLazyHandler(batchTranslationHandler);
      this.lifecycleManager.observeLazyBatch(batchLazyItems);

      if (this.config.debug) {
        this.logger.info('🚀 Enhanced lazy translation setup completed', {
          visible: visibleNodes.length,
          invisibleBatch: invisibleNodes.length,
          optimizationEnabled: true,
          batchAccumulationDelay: '150ms' // 从 LazyLoadConfig 获取
        });
      }
    } else {
      if (this.config.debug) {
        this.logger.info('Lazy translation setup completed', {
          visible: visibleNodes.length,
          invisible: 0,
          note: 'All nodes were visible, no lazy loading needed'
        });
      }
    }

    return visibleResult;
  }

  /**
   * 🔧 回退懒加载翻译 - 当批量处理失败时的渐进式降级
   * @private
   */
  private async executeFallbackLazyTranslation(failedNodes: ScannedNode[]): Promise<void> {
    if (this.config.debug) {
      console.log(`🔄 [orchestrator|FALLBACK] 开始回退懒加载翻译`, {
        nodeCount: failedNodes.length
      });
    }

    // 尝试分组处理
    const chunkSize = Math.max(1, Math.floor(failedNodes.length / 2));
    const chunks: ScannedNode[][] = [];
    
    for (let i = 0; i < failedNodes.length; i += chunkSize) {
      chunks.push(failedNodes.slice(i, i + chunkSize));
    }

    for (const chunk of chunks) {
      try {
        // 尝试批量处理较小的组
        await this.executeBatchTranslation(chunk);
        
        if (this.config.debug) {
          console.log(`✅ [orchestrator|FALLBACK] 回退批次处理成功`, { 
            chunkSize: chunk.length 
          });
        }
      } catch (chunkError) {
        if (this.config.debug) {
          console.warn(`⚠️ [orchestrator|FALLBACK] 回退批次失败，使用单个处理`, { 
            chunkSize: chunk.length,
            error: (chunkError as Error).message 
          });
        }

        // 最后的回退：单个处理
        for (const node of chunk) {
          try {
            await this.translateElement(node.element);
          } catch (individualError) {
            if (this.config.debug) {
              console.error(`❌ [orchestrator|FALLBACK] 单个元素翻译失败`, {
                element: node.element.tagName,
                error: (individualError as Error).message
              });
            }
            // 记录个别失败，但不阻止其他节点处理
          }
        }
      }
    }

    if (this.config.debug) {
      console.log(`🎯 [orchestrator|FALLBACK] 回退懒加载翻译完成`);
    }
  }

  /**
   * 翻译文本 (现在只期望纯文本作为 `text` 参数)
   * `format` 参数仅作为 `translateService` 内部的上下文提示，不影响实际发送给外部API的文本格式。
   * @private
   */
  private async translateText(text: string, format: TranslateFormat): Promise<string> {
    if (this.config.translateFunction) {
      // 调用 `translateFunction`（它现在是一个适配器，会调用 `translateService`）
      // `translateService` 内部将确保发送纯文本给真正的翻译引擎。
      return this.config.translateFunction(text, this.config.targetLanguage, format);
    } else {
      // 默认的透传函数，返回原文（此时 `text` 已经是纯文本）
      return text;
    }
  }

  /**
   * 内部批量翻译函数，用于协调器调用 (现在只期望纯文本输入)
   * @private
   */
  private async translateTextBatchInternal(contents: (string | null | undefined)[], format: TranslateFormat): Promise<string[]> {
    const validContents: string[] = contents.filter((c): c is string => typeof c === 'string' && c.length > 0);
    if (validContents.length === 0) {
      return contents.map(() => '');
    }

    if (this.config.translateFunction && typeof this.config.translateFunction === 'function') {
      const { translateService } = await import('../translate');
      // 🔧 关键修改：始终以纯文本格式处理，确保翻译引擎返回纯文本结果
      const result = await translateService.translateTexts(validContents, {
        to: this.config.targetLanguage,
        from: 'auto',
        format: 'text' // 强制使用纯文本格式，忽略原始format参数
      });
      return result;
    } else {
      return contents.map(c => c || ''); // 如果没有翻译函数，原样返回（此时应为纯文本）
    }
  }

  /**
   * 报告进度
   * @private
   */
  private reportProgress(
    processed: number,
    total: number,
    successful: number,
    failed: number,
    currentText?: string
  ): void {
    const progressData: ProgressEventData = {
      processed,
      total,
      successful,
      failed,
      currentText: currentText?.slice(0, 50),
      percentage: (processed / total) * 100
    };

    this.stateManager.reportProgress(processed, total, successful, failed, currentText);

    if (this.config.onProgress) {
      this.config.onProgress(progressData);
    }
  }

  /**
   * 创建空结果
   * @private
   */
  private createEmptyResult(startTime: number): BatchTranslationResult {
    return {
      totalCount: 0,
      successCount: 0,
      failureCount: 0,
      results: [],
      totalDuration: performance.now() - startTime
    };
  }

  /**
   * 延迟工具函数
   * @private
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 合并默认配置
   * @private
   */
  private mergeDefaultConfig(config: OrchestratorConfig): Required<TranslationConfig> {
    return {
      targetLanguage: config.targetLanguage ?? 'zh',
      concurrency: config.concurrency ?? 3,
      debug: config.debug ?? true,
      enableAccessibility: config.enableAccessibility ?? true,
      enableLazyLoading: config.enableLazyLoading ?? true,
      enablePerformanceOptimization: config.enablePerformanceOptimization ?? true,
      enableSmartInjection: config.enableSmartInjection ?? true,
      enableAdvancedInjection: config.enableAdvancedInjection ?? true,
      translateFunction: config.translateFunction || (async (text: string) => text),
      onError: config.onError ?? (() => { }),
      onProgress: config.onProgress ?? (() => { })
    };
  }
}

// ====================
// 📦 新增: ScanStrategyManager - 扫描策略管理器
// ====================

export interface ScanStrategyResult {
  nodes: ScannedNode[];
  stats: {
    strategy: 'viewport-first' | 'async-chunked' | 'synchronous' | 'fallback-sync';
    totalScanned?: number;
    translatableFound: number;
    duration: number;
    complexity: PageComplexity['estimatedComplexity'];
    [key: string]: any;
  };
}

export class ScanStrategyManager {
  private scanner: DomScanner;
  private debug: boolean;
  private logger = console;

  constructor(scanner: DomScanner, debug = false) {
    this.scanner = scanner;
    this.debug = debug;
  }

  /**
   * 🧠 智能扫描决策 - 根据页面复杂度自动选择最优策略
   */
  async performIntelligentScan(config: ScanConfig): Promise<ScanStrategyResult> {
    const startTime = performance.now();
    
    try {
      // 分析页面复杂度
      const pageMetrics = this.analyzePageComplexity(config.rootNode || document.body);
      
      if (this.debug) {
        this.logger.info('🧠 智能扫描决策', {
          complexity: pageMetrics.estimatedComplexity,
          domNodeCount: pageMetrics.domNodeCount,
          hasInfiniteScroll: pageMetrics.hasInfiniteScroll,
          isDynamic: pageMetrics.isDynamic
        });
      }

      let scanResult: ScannedNode[];
      let scanStats: any = {};

      // 策略1: 大型页面使用视口优先扫描
      if (pageMetrics.domNodeCount > 5000 || pageMetrics.estimatedComplexity === 'extreme') {
        scanResult = await this.executeViewportFirstStrategy(config);
        scanStats = { strategy: 'viewport-first', viewportNodes: scanResult.length, hasBackgroundScan: true };
      }
      // 策略2: 中型页面使用异步分块扫描
      else if (pageMetrics.domNodeCount > 1000 || pageMetrics.estimatedComplexity === 'high') {
        scanResult = await this.executeAsyncChunkedStrategy(config);
        scanStats = { strategy: 'async-chunked', chunkSize: config.chunkSize || 200 };
      }
      // 策略3: 小型页面使用同步扫描
      else {
        scanResult = await this.executeSynchronousStrategy(config);
        scanStats = { strategy: 'synchronous' };
      }

      const duration = performance.now() - startTime;

      return {
        nodes: scanResult,
        stats: {
          ...scanStats,
          totalScanned: pageMetrics.domNodeCount,
          translatableFound: scanResult.length,
          duration,
          complexity: pageMetrics.estimatedComplexity
        }
      };

    } catch (error) {
      // 降级到同步扫描
      const fallbackResult = this.scanner.scan(config);
      return {
        nodes: fallbackResult.nodes,
        stats: {
          ...fallbackResult.stats,
          strategy: 'fallback-sync',
          translatableFound: fallbackResult.nodes.length,
          duration: performance.now() - startTime,
          complexity: 'unknown' as const,
          fallbackReason: (error as Error).message
        }
      };
    }
  }

  // ... 其他方法省略保持响应简洁
}
