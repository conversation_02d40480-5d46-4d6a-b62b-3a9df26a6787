import type { ScannedNode, TranslationResult, TranslateFormat, RenderConfig } from './types';
import type { DomRenderer } from './renderer';

export interface BatchTranslationConfig {
  targetLanguage: string;
  enableAccessibility: boolean;
  debug: boolean;
  translateFunction?: (text: string, language: string, format: TranslateFormat) => Promise<string>;
  onError?: (error: Error, context: string) => void;
}

export interface BatchTranslationResult {
  successful: number;
  failed: number;
  results: TranslationResult[];
}

/**
 * 🎯 批量翻译处理器 - 专门处理批量翻译和渲染逻辑
 * 
 * 职责边界：
 * - 批量翻译文本处理
 * - 回退翻译策略
 * - 超时控制和错误处理
 * - 翻译结果渲染协调
 */
export class BatchTranslationProcessor {
  private renderer: DomRenderer;
  private config: BatchTranslationConfig;
  private logger = console;
  private abortController?: AbortController;

  constructor(renderer: <PERSON><PERSON><PERSON><PERSON>, config: BatchTranslationConfig) {
    this.renderer = renderer;
    this.config = config;
  }

  /**
   * 🚀 执行批量翻译 - 真正的批量并行翻译
   */
  async processBatch(nodes: ScannedNode[]): Promise<BatchTranslationResult> {
    const results: TranslationResult[] = [];
    let successful = 0;
    let failed = 0;

    if (this.config.debug) {
      console.log(`🚀 [batch-processor] 开始批量翻译 ${nodes.length} 个节点`);
    }

    try {
      // 🚀 性能优化：预加载配置缓存，避免在每个节点渲染时重复解析
      await this.renderer.preloadConfig();
      
      if (this.config.debug) {
        console.log(`✅ [batch-processor] 配置预加载完成，将应用于 ${nodes.length} 个节点`);
      }
    } catch (configError) {
      if (this.config.debug) {
        console.warn(`⚠️ [batch-processor] 配置预加载失败，使用按需加载`, configError);
      }
    }

    try {
      // 🚀 关键优化：使用批量翻译代替循环串行翻译，添加超时保护
      const textsToTranslate = nodes.map(node => node.text);
      const BATCH_TIMEOUT_MS = 30000; // 30秒超时
      
      let translatedTexts: string[];
      try {
        const translationPromise = this.translateTextBatch(textsToTranslate, 'text');
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`批量翻译超时：${BATCH_TIMEOUT_MS}ms`));
          }, BATCH_TIMEOUT_MS);
        });

        translatedTexts = await Promise.race([translationPromise, timeoutPromise]);
      } catch (error) {
        if ((error as Error).message.includes('超时')) {
          if (this.config.debug) {
            console.warn(`⏰ [batch-processor] 批量翻译超时，回退到单个处理`);
          }
          throw error;
        }
        throw error;
      }

      // 🎯 批量渲染阶段：将翻译结果渲染到DOM
      const renderPromises = nodes.map(async (node, index) => {
        const translatedText = translatedTexts[index] || node.text;
        
        try {
          if (this.abortController?.signal.aborted) {
            throw new Error('Translation aborted');
          }

          const format = node.hasHtmlStructure ? 'html' : 'text';
          const renderConfig: RenderConfig & {
            format?: 'text' | 'html';
            originalLinks?: Array<{ href: string; text: string; attributes: Record<string, string> }>;
          } = {
            language: this.config.targetLanguage,
            enableAccessibility: this.config.enableAccessibility,
            debug: this.config.debug,
            format: format,
            originalLinks: node.links?.map(link => ({
              href: link.href,
              text: link.text,
              attributes: {}
            })) || []
          };

          const renderResult = await this.renderer.render(node.element, translatedText, renderConfig);

          return {
            success: renderResult.success,
            originalText: node.text,
            translatedText,
            error: renderResult.error,
            format: format as TranslateFormat,
            duration: renderResult.duration
          } as TranslationResult;

        } catch (error) {
          return {
            success: false,
            originalText: node.text,
            error: (error as Error).message,
            format: 'text',
            duration: 0
          } as TranslationResult;
        }
      });

      const renderResults = await Promise.all(renderPromises);
      results.push(...renderResults);
      successful = renderResults.filter(r => r.success).length;
      failed = renderResults.filter(r => !r.success).length;

      if (this.config.debug) {
        console.log(`🎉 [batch-processor] 批量翻译和渲染全部完成`, {
          successful, failed, successRate: `${((successful / nodes.length) * 100).toFixed(1)}%`
        });
      }

    } catch (error) {
      if (this.config.debug) {
        console.error(`❌ [batch-processor] 批量翻译失败，回退到单个处理`, error);
      }
      return this.processFallback(nodes);
    }

    return { successful, failed, results };
  }

  /**
   * 🔄 回退翻译策略 - 当批量翻译失败时使用
   */
  private async processFallback(nodes: ScannedNode[]): Promise<BatchTranslationResult> {
    const results: TranslationResult[] = [];
    let successful = 0;
    let failed = 0;

    if (this.config.debug) {
      console.log(`🔄 [batch-processor] 使用单个翻译模式处理 ${nodes.length} 个节点`);
    }

    try {
      await this.renderer.preloadConfig();
    } catch (configError) {
      if (this.config.debug) {
        console.warn(`⚠️ [batch-processor] 回退配置预加载失败，使用按需加载`, configError);
      }
    }

    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];

      if (this.abortController?.signal.aborted) {
        break;
      }

      try {
        const format = node.hasHtmlStructure ? 'html' : 'text';
        const SINGLE_TIMEOUT_MS = 10000; // 10秒超时
        
        const translationPromise = this.translateSingleText(node.text, format);
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`单个翻译超时：${SINGLE_TIMEOUT_MS}ms`));
          }, SINGLE_TIMEOUT_MS);
        });

        const translatedText = await Promise.race([translationPromise, timeoutPromise]);

        const renderConfig: RenderConfig & {
          format?: 'text' | 'html';
          originalLinks?: Array<{ href: string; text: string; attributes: Record<string, string> }>;
        } = {
          language: this.config.targetLanguage,
          enableAccessibility: this.config.enableAccessibility,
          debug: this.config.debug,
          format: format,
          originalLinks: node.links?.map(link => ({
            href: link.href,
            text: link.text,
            attributes: {}
          })) || []
        };

        const renderResult = await this.renderer.render(node.element, translatedText, renderConfig);

        const translationResult: TranslationResult = {
          success: renderResult.success,
          originalText: node.text,
          translatedText,
          error: renderResult.error,
          format: format as TranslateFormat,
          duration: renderResult.duration
        };

        results.push(translationResult);

        if (renderResult.success) {
          successful++;
        } else {
          failed++;
        }

      } catch (error) {
        failed++;
        const errorResult: TranslationResult = {
          success: false,
          originalText: node.text,
          error: (error as Error).message,
          format: 'text',
          duration: 0
        };
        results.push(errorResult);

        if (this.config.onError) {
          this.config.onError(error as Error, 'fallback-translation');
        }
      }
    }

    return { successful, failed, results };
  }

  /**
   * 批量翻译文本
   */
  private async translateTextBatch(contents: string[], format: TranslateFormat): Promise<string[]> {
    const validContents = contents.filter(c => typeof c === 'string' && c.length > 0);
    if (validContents.length === 0) {
      return contents.map(() => '');
    }

    if (this.config.translateFunction) {
      const { translateService } = await import('../translate');
      const result = await translateService.translateTexts(validContents, {
        to: this.config.targetLanguage,
        from: 'auto',
        format: 'text' // 强制使用纯文本格式
      });
      return result;
    } else {
      return contents.map(c => c || '');
    }
  }

  /**
   * 单个文本翻译
   */
  private async translateSingleText(text: string, format: TranslateFormat): Promise<string> {
    if (this.config.translateFunction) {
      return this.config.translateFunction(text, this.config.targetLanguage, format);
    } else {
      return text;
    }
  }

  /**
   * 设置中止控制器
   */
  setAbortController(controller: AbortController): void {
    this.abortController = controller;
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      rendererStats: this.renderer.getStats()
    };
  }

  /**
   * 清理资源
   */
  destroy(): void {
    if (this.config.debug) {
      this.logger.info('BatchTranslationProcessor destroyed');
    }
  }
}