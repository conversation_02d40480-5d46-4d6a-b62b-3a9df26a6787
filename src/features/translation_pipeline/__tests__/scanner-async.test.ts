/**
 * DomScanner 异步扫描功能测试
 * 测试新增的异步分块扫描、视口优先扫描和增量扫描功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DomScanner } from '../scanner';
import { ScanConfig } from '../types';

describe('DomScanner 异步扫描功能', () => {
  let scanner: DomScanner;
  let container: HTMLElement;

  beforeEach(() => {
    // 初始化扫描器
    scanner = new DomScanner({ debug: false });
    
    // 创建测试容器
    container = document.createElement('div');
    document.body.appendChild(container);
  });

  describe('API 接口测试', () => {
    it('scanAsync 方法应该存在并返回Promise', () => {
      expect(typeof scanner.scanAsync).toBe('function');
      
      const promise = scanner.scanAsync({ rootNode: container });
      expect(promise).toBeInstanceOf(Promise);
    });

    it('scanViewportFirst 方法应该存在并返回Promise', () => {
      expect(typeof scanner.scanViewportFirst).toBe('function');
      
      const promise = scanner.scanViewportFirst({ rootNode: container });
      expect(promise).toBeInstanceOf(Promise);
    });

    it('scanIncremental 方法应该存在并返回Promise', () => {
      expect(typeof scanner.scanIncremental).toBe('function');
      
      const promise = scanner.scanIncremental([]);
      expect(promise).toBeInstanceOf(Promise);
    });
  });

  describe('基础功能测试', () => {
    it('scanAsync 应该能处理空容器', async () => {
      container.innerHTML = '';
      
      const result = await scanner.scanAsync({ rootNode: container });
      
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });

    it('scanIncremental 应该能处理空节点数组', async () => {
      const result = await scanner.scanIncremental([]);
      
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
    });

    it('scanViewportFirst 应该返回正确的结构', async () => {
      container.innerHTML = '<p>测试内容</p>';
      
      const result = await scanner.scanViewportFirst({ rootNode: container });
      
      expect(result).toBeDefined();
      expect(result.viewportNodes).toBeDefined();
      expect(result.backgroundScanPromise).toBeDefined();
      expect(Array.isArray(result.viewportNodes)).toBe(true);
      expect(result.backgroundScanPromise).toBeInstanceOf(Promise);
      
      // 确保背景扫描能完成
      const backgroundNodes = await result.backgroundScanPromise;
      expect(Array.isArray(backgroundNodes)).toBe(true);
    });
  });

  describe('类型定义测试', () => {
    it('ScanConfig 应该支持新的配置项', () => {
      const config: ScanConfig = {
        rootNode: container,
        chunkSize: 100,
        viewportMargin: 300,
        enablePerformanceMonitoring: true,
      };
      
      expect(config.chunkSize).toBe(100);
      expect(config.viewportMargin).toBe(300);
      expect(config.enablePerformanceMonitoring).toBe(true);
    });
  });

  describe('私有方法访问测试', () => {
    it('calculateViewportBounds 应该能被调用', () => {
      // Mock window properties
      Object.defineProperty(window, 'scrollY', { value: 100, writable: true });
      Object.defineProperty(window, 'scrollX', { value: 50, writable: true });
      Object.defineProperty(window, 'innerHeight', { value: 800, writable: true });
      Object.defineProperty(window, 'innerWidth', { value: 1200, writable: true });

      const margin = 200;
      const bounds = (scanner as any).calculateViewportBounds(margin);

      expect(bounds).toEqual({
        top: 100 - margin,
        bottom: 100 + 800 + margin,
        left: 50 - margin,
        right: 50 + 1200 + margin
      });
    });

    it('yieldToMain 应该返回Promise', async () => {
      const yieldPromise = (scanner as any).yieldToMain();
      
      expect(yieldPromise).toBeInstanceOf(Promise);
      await expect(yieldPromise).resolves.toBeUndefined();
    });
  });

  describe('错误处理测试', () => {
    it('isElementInViewport 应该能处理 getBoundingClientRect 错误', () => {
      const element = document.createElement('div');
      element.getBoundingClientRect = vi.fn().mockImplementation(() => {
        throw new Error('Mock getBoundingClientRect error');
      });

      const viewport = {
        top: 0,
        bottom: 800,
        left: 0,
        right: 1200
      };

      const isInViewport = (scanner as any).isElementInViewport(element, viewport);
      
      // 错误时应该返回false
      expect(isInViewport).toBe(false);
    });
  });
});