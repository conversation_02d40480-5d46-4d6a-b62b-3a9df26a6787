# 翻译管道缓存配置使用指南

## 概览

翻译管道现在包含完整的缓存配置管理功能，支持开发和生产环境的不同缓存策略。

## 基本用法

### 导入配置函数

```typescript
import {
  enableCache,
  disableCache,
  isCacheEnabled,
  getCacheConfig,
  updateCacheConfig,
  configDebugTools
} from './config';
```

### 缓存控制

```typescript
// 启用缓存
enableCache();

// 禁用缓存
disableCache();

// 检查缓存状态
const isEnabled = isCacheEnabled();

// 获取缓存配置
const cacheConfig = getCacheConfig();
```

### 更新缓存配置

```typescript
// 更新特定配置项
updateCacheConfig({
  maxMemorySize: 2000,
  ttl: 48 * 60 * 60 * 1000, // 48小时
  enablePersistence: true
});

// 使用快速配置预设
configDebugTools.cache.setQuickConfig('large'); // small, medium, large
```

## 调试工具

### 浏览器控制台中的调试工具

在开发环境下，可以通过以下方式访问调试工具：

```javascript
// 全局调试工具
translationConfigDebug.cache.disable();
translationConfigDebug.cache.enable();
translationConfigDebug.cache.printConfig();

// 配置预设
translationConfigDebug.presets.noCache();
translationConfigDebug.presets.maxCache();
translationConfigDebug.presets.debug(); // 禁用缓存便于调试
```

## 环境配置

### 开发环境
- 缓存：启用，但容量较小（500条）
- 适合开发调试

### 生产环境
- 缓存：完全启用，大容量（2000条）
- 延长缓存时间（7天）
- 优化性能

### 调试模式
- 缓存：禁用
- 并发数：1
- 便于问题排查

## 配置选项详解

```typescript
interface CacheConfig {
  enabled: boolean;              // 是否启用缓存
  maxMemorySize: number;         // 内存缓存最大条目数
  ttl: number;                   // 缓存过期时间（毫秒）
  enablePersistence: boolean;    // 是否启用持久化存储
  storageKeyPrefix: string;      // 存储键名前缀
}
```

## 使用场景

### 1. 开发调试
```typescript
// 临时禁用缓存查看翻译效果
configDebugTools.cache.disableForSession();
```

### 2. 性能优化
```typescript
// 最大化缓存配置
configDebugTools.presets.maxCache();
```

### 3. 内存优化
```typescript
// 最小化缓存配置
configDebugTools.cache.setQuickConfig('small');
```

## 注意事项

1. **环境自动检测**: 系统会自动根据环境应用不同的缓存策略
2. **会话级别**: 通过调试工具的临时设置仅在当前会话有效
3. **持久化**: 生产环境会启用持久化存储，提高缓存命中率
4. **内存管理**: 缓存会自动清理过期条目，避免内存泄漏

## 迁移指南

如果你之前直接使用缓存管理器，现在建议通过配置系统统一管理：

```typescript
// 旧方式
const cacheManager = new TranslationCacheManager();

// 新方式
import { getCacheConfig, updateCacheConfig } from './config';
const config = getCacheConfig();
```