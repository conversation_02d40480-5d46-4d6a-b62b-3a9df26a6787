/**
 * Translation Event Bus
 * 翻译事件总线 - 提供模块间解耦通信机制
 */

import { TranslationEvent, TranslationEventType } from './types';
// debug 导入已移除，使用手动 console.log

/**
 * 内存管理配置常量
 */
const MEMORY_CONFIG = {
  DEFAULT_MAX_HISTORY: 100,
  DEFAULT_MEMORY_THRESHOLD: 500, // 降低默认阈值，更主动管理内存
  EMERGENCY_CLEANUP_RATIO: 0.3,  // 紧急清理时保留30%的事件
  MEMORY_CHECK_INTERVAL: 50,     // 每50个事件检查一次内存
  WARNING_MEMORY_RATIO: 0.8      // 80%时发出警告
} as const;

/**
 * 事件监听器函数类型
 */
export type EventListener<T = any> = (event: TranslationEvent<T>) => void;

/**
 * 事件订阅者信息
 */
interface EventSubscription {
  /** 监听器函数 */
  listener: EventListener;
  /** 是否只执行一次 */
  once: boolean;
  /** 订阅时间 */
  subscribedAt: number;
  /** 订阅者ID */
  subscriberId?: string;
}

/**
 * 翻译事件总线
 * 
 * 提供发布/订阅机制，实现模块间解耦通信
 * 支持类型安全的事件处理和调试功能
 */
export class TranslationEventBus {
  private listeners = new Map<TranslationEventType, EventSubscription[]>();
  private eventHistory: TranslationEvent[] = [];
  private maxHistorySize: number;
  private memoryThreshold: number;
  private eventCounter = 0; // 用于跟踪事件计数，优化内存检查频率
  private logger = console;
  private debugMode: boolean;

  constructor(options: { debug?: boolean; maxHistorySize?: number; memoryThreshold?: number } = {}) {
    this.debugMode = options.debug ?? false;
    this.maxHistorySize = options.maxHistorySize ?? MEMORY_CONFIG.DEFAULT_MAX_HISTORY;
    this.memoryThreshold = options.memoryThreshold ?? MEMORY_CONFIG.DEFAULT_MEMORY_THRESHOLD;
    
    if (this.debugMode) {
      console.log('✅ [event-bus|STARTUP] 翻译事件总线初始化完成', {
        maxHistorySize: this.maxHistorySize,
        memoryThreshold: this.memoryThreshold,
        memoryCheckInterval: MEMORY_CONFIG.MEMORY_CHECK_INTERVAL
      });
    }
  }

  /**
   * 发布事件
   * @param type 事件类型
   * @param data 事件数据
   * @param source 事件来源
   */
  publish<T = any>(
    type: TranslationEventType, 
    data: T, 
    source: string = 'unknown'
  ): void {
    const event: TranslationEvent<T> = {
      type,
      data,
      timestamp: Date.now(),
      source
    };

    // 记录事件历史
    this.addToHistory(event);

    // 获取监听器
    const subscriptions = this.listeners.get(type) || [];
    
    if (this.debugMode) {
      console.log(`📢 [event-bus|PUBLISH] 发布事件: ${type}`, {
        source,
        listenersCount: subscriptions.length,
        data: this.debugMode ? data : '[hidden]'
      });
    }

    // 执行监听器（使用副本避免在执行过程中修改）
    const subscriptionsCopy = [...subscriptions];
    subscriptionsCopy.forEach((subscription, index) => {
      try {
        subscription.listener(event);
        
        // 如果是一次性监听器，执行后移除
        if (subscription.once) {
          this.removeSubscription(type, index);
        }
      } catch (error) {
        console.error(`❌ [event-bus|ERROR] 事件监听器执行错误 ${type}:`, {
          error,
          source,
          subscriberId: subscription.subscriberId
        });
      }
    });
  }

  /**
   * 订阅事件
   * @param type 事件类型
   * @param listener 监听器函数
   * @param options 订阅选项
   * @returns 取消订阅函数
   */
  subscribe<T = any>(
    type: TranslationEventType,
    listener: EventListener<T>,
    options: {
      once?: boolean;
      subscriberId?: string;
    } = {}
  ): () => void {
    const subscription: EventSubscription = {
      listener: listener as EventListener,
      once: options.once ?? false,
      subscribedAt: Date.now(),
      subscriberId: options.subscriberId
    };

    // 添加到监听器列表
    if (!this.listeners.has(type)) {
      this.listeners.set(type, []);
    }
    this.listeners.get(type)!.push(subscription);

    if (this.debugMode) {
      console.log(`➕ [event-bus|SUBSCRIBE] 事件订阅已添加: ${type}`, {
        subscriberId: options.subscriberId,
        once: options.once,
        totalListeners: this.listeners.get(type)!.length
      });
    }

    // 返回取消订阅函数
    return () => {
      this.unsubscribe(type, listener);
    };
  }

  /**
   * 一次性订阅事件
   * @param type 事件类型
   * @param listener 监听器函数
   * @param subscriberId 订阅者ID
   * @returns 取消订阅函数
   */
  once<T = any>(
    type: TranslationEventType,
    listener: EventListener<T>,
    subscriberId?: string
  ): () => void {
    return this.subscribe(type, listener, { once: true, subscriberId });
  }

  /**
   * 取消订阅
   * @param type 事件类型
   * @param listener 监听器函数
   */
  unsubscribe(type: TranslationEventType, listener: EventListener): void {
    const subscriptions = this.listeners.get(type);
    if (!subscriptions) {
      return;
    }

    const index = subscriptions.findIndex(sub => sub.listener === listener);
    if (index !== -1) {
      this.removeSubscription(type, index);
      
      if (this.debugMode) {
        console.log(`➖ [event-bus|UNSUBSCRIBE] 事件订阅已移除: ${type}`, {
          remainingListeners: subscriptions.length
        });
      }
    }
  }

  /**
   * 取消所有订阅
   * @param type 事件类型（可选，不指定则清除所有）
   */
  unsubscribeAll(type?: TranslationEventType): void {
    if (type) {
      this.listeners.delete(type);
      if (this.debugMode) {
        console.log(`🧹 [event-bus|CLEAR] 所有订阅已移除: ${type}`);
      }
    } else {
      const totalListeners = Array.from(this.listeners.values())
        .reduce((sum, subs) => sum + subs.length, 0);
      
      this.listeners.clear();
      
      if (this.debugMode) {
        console.log(`🧹 [event-bus|CLEAR] 全部事件订阅已清理`, {
          totalRemoved: totalListeners
        });
      }
    }
  }

  /**
   * 获取事件历史
   * @param type 事件类型（可选）
   * @param limit 限制数量
   * @returns 事件历史数组
   */
  getEventHistory(type?: TranslationEventType, limit?: number): TranslationEvent[] {
    let history = type 
      ? this.eventHistory.filter(event => event.type === type)
      : this.eventHistory;
    
    if (limit && limit > 0) {
      history = history.slice(-limit);
    }
    
    return [...history]; // 返回副本
  }


  /**
   * 获取监听器统计
   */
  getStats(): {
    totalTypes: number;
    totalListeners: number;
    listenersByType: Record<string, number>;
    historySize: number;
  } {
    const listenersByType: Record<string, number> = {};
    let totalListeners = 0;

    this.listeners.forEach((subscriptions, type) => {
      listenersByType[type] = subscriptions.length;
      totalListeners += subscriptions.length;
    });

    return {
      totalTypes: this.listeners.size,
      totalListeners,
      listenersByType,
      historySize: this.eventHistory.length
    };
  }

  /**
   * 等待特定事件
   * @param type 事件类型
   * @param timeout 超时时间（毫秒）
   * @param filter 事件过滤器
   * @returns Promise，解析为事件数据
   */
  waitForEvent<T = any>(
    type: TranslationEventType,
    timeout: number = 5000,
    filter?: (data: T) => boolean
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        unsubscribe();
        reject(new Error(`Timeout waiting for event: ${type}`));
      }, timeout);

      const unsubscribe = this.once<T>(type, (event) => {
        if (!filter || filter(event.data)) {
          clearTimeout(timeoutId);
          resolve(event.data);
        }
      }, `waitForEvent-${Date.now()}`);
    });
  }

  /**
   * 销毁事件总线
   */
  destroy(): void {
    this.unsubscribeAll();
    this.clearHistory();
    
    if (this.debugMode) {
      console.log('✅ [event-bus|DESTROY] 翻译事件总线已销毁');
    }
  }

  /**
   * 添加事件到历史记录
   * 改进的内存管理策略，更主动和智能
   * @private
   */
  private addToHistory(event: TranslationEvent): void {
    this.eventHistory.push(event);
    this.eventCounter++;
    
    // 只在特定间隔检查内存，避免频繁检查影响性能
    if (this.eventCounter % MEMORY_CONFIG.MEMORY_CHECK_INTERVAL === 0) {
      this.performMemoryCheck();
    }
    
    // 标准清理：超过最大历史大小时进行清理
    if (this.eventHistory.length > this.maxHistorySize) {
      this.performStandardCleanup();
    }
  }

  /**
   * 执行内存检查和管理
   */
  private performMemoryCheck(): void {
    const currentSize = this.eventHistory.length;
    const warningThreshold = this.memoryThreshold * MEMORY_CONFIG.WARNING_MEMORY_RATIO;
    
    // 发出内存警告
    if (currentSize > warningThreshold && currentSize <= this.memoryThreshold) {
      if (this.debugMode) {
        console.warn(`⚠️ [event-bus|MEMORY] 内存使用接近阈值`, {
          currentSize,
          threshold: this.memoryThreshold,
          warningAt: Math.round(warningThreshold),
          suggestion: '即将进行主动清理'
        });
      }
    }
    
    // 超过阈值时执行紧急清理
    if (currentSize > this.memoryThreshold) {
      this.performEmergencyCleanup();
    }
  }

  /**
   * 执行标准清理
   */
  private performStandardCleanup(): void {
    const excessCount = this.eventHistory.length - this.maxHistorySize;
    this.eventHistory.splice(0, excessCount);

    if (this.debugMode) {
      console.log(`🧹 [event-bus|MEMORY] 标准清理完成`, {
        removedEvents: excessCount,
        currentSize: this.eventHistory.length,
        maxSize: this.maxHistorySize
      });
    }
  }

  /**
   * 执行紧急清理
   */
  private performEmergencyCleanup(): void {
    const targetSize = Math.floor(this.memoryThreshold * MEMORY_CONFIG.EMERGENCY_CLEANUP_RATIO);
    const removedCount = this.eventHistory.length - targetSize;
    
    console.warn(`🚨 [event-bus|MEMORY] 执行紧急清理`, {
      currentSize: this.eventHistory.length,
      threshold: this.memoryThreshold,
      targetSize,
      willRemove: removedCount
    });
    
    // 保留最近的事件
    this.eventHistory = this.eventHistory.slice(-targetSize);
    
    console.log(`✅ [event-bus|MEMORY] 紧急清理完成，移除${removedCount}个事件`);
  }

  /**
   * 移除订阅
   * @private
   */
  private removeSubscription(type: TranslationEventType, index: number): void {
    const subscriptions = this.listeners.get(type);
    if (subscriptions) {
      subscriptions.splice(index, 1);
      
      // 如果没有监听器了，删除这个类型
      if (subscriptions.length === 0) {
        this.listeners.delete(type);
      }
    }
  }

  /**
   * 获取内存使用情况
   */
  getMemoryStats(): {
    eventHistorySize: number;
    maxHistorySize: number;
    memoryThreshold: number;
    listenersCount: number;
    eventTypesCount: number;
    memoryUsagePercentage: number;
  } {
    const eventHistorySize = this.eventHistory.length;
    const listenersCount = Array.from(this.listeners.values()).reduce((sum, subs) => sum + subs.length, 0);
    const eventTypesCount = this.listeners.size;
    const memoryUsagePercentage = Math.round((eventHistorySize / this.maxHistorySize) * 100);

    return {
      eventHistorySize,
      maxHistorySize: this.maxHistorySize,
      memoryThreshold: this.memoryThreshold,
      listenersCount,
      eventTypesCount,
      memoryUsagePercentage
    };
  }

  /**
   * 手动清理历史事件
   * @param keepCount 保留的事件数量，默认为maxHistorySize的一半
   */
  clearHistory(keepCount?: number): void {
    const targetSize = keepCount ?? Math.floor(this.maxHistorySize / 2);
    const removedCount = Math.max(0, this.eventHistory.length - targetSize);
    
    if (removedCount > 0) {
      this.eventHistory = this.eventHistory.slice(-targetSize);
      
      if (this.debugMode) {
        console.log(`🧹 [event-bus|CLEANUP] 手动清理完成`, {
          removedCount,
          remainingCount: this.eventHistory.length,
          targetSize
        });
      }
    }
  }
}

/**
 * 创建新的事件总线实例
 */
export function createEventBus(options?: { debug?: boolean; maxHistorySize?: number }): TranslationEventBus {
  return new TranslationEventBus(options);
}

/**
 * 默认事件总线实例（单例）
 */
let defaultEventBus: TranslationEventBus | null = null;

/**
 * 获取默认事件总线实例
 */
export function getDefaultEventBus(): TranslationEventBus {
  if (!defaultEventBus) {
    defaultEventBus = new TranslationEventBus({ debug: true });
  }
  return defaultEventBus;
}

/**
 * 设置默认事件总线实例
 */
export function setDefaultEventBus(eventBus: TranslationEventBus): void {
  if (defaultEventBus) {
    defaultEventBus.destroy();
  }
  defaultEventBus = eventBus;
}