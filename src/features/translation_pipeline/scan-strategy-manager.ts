import type { ScanConfig, ScannedNode, PageComplexity } from './types';
import type { DomScanner } from './scanner';

export interface ScanStrategyResult {
  nodes: ScannedNode[];
  stats: {
    strategy: 'viewport-first' | 'async-chunked' | 'synchronous' | 'fallback-sync';
    totalScanned?: number;
    translatableFound: number;
    duration: number;
    complexity: PageComplexity['estimatedComplexity'];
    [key: string]: any;
  };
}

/**
 * 🎯 扫描策略管理器 - 根据页面复杂度智能选择最优扫描策略
 * 
 * 职责边界：
 * - 页面复杂度分析和评估
 * - 扫描策略自动选择和执行
 * - 无限滚动和动态内容检测
 * - 视口优先、异步分块、同步扫描策略
 */
export class ScanStrategyManager {
  private scanner: DomScanner;
  private debug: boolean;
  private logger = console;

  constructor(scanner: DomScanner, debug = false) {
    this.scanner = scanner;
    this.debug = debug;
  }

  /**
   * 🧠 智能扫描决策 - 根据页面复杂度自动选择最优策略
   */
  async performIntelligentScan(config: ScanConfig): Promise<ScanStrategyResult> {
    const startTime = performance.now();
    
    try {
      const pageMetrics = this.analyzePageComplexity(config.rootNode || document.body);
      
      if (this.debug) {
        this.logger.info('🧠 智能扫描决策', pageMetrics);
      }

      let scanResult: ScannedNode[];
      let scanStats: Record<string, any> = {};

      if (pageMetrics.domNodeCount > 5000 || pageMetrics.estimatedComplexity === 'extreme') {
        scanResult = await this.executeViewportFirstStrategy(config);
        scanStats = { strategy: 'viewport-first' };
      } else if (pageMetrics.domNodeCount > 1000 || pageMetrics.estimatedComplexity === 'high') {
        scanResult = await this.executeAsyncChunkedStrategy(config);
        scanStats = { strategy: 'async-chunked' };
      } else {
        scanResult = await this.executeSynchronousStrategy(config);
        scanStats = { strategy: 'synchronous' };
      }

      const duration = performance.now() - startTime;

      return {
        nodes: scanResult,
        stats: {
          ...scanStats,
          totalScanned: pageMetrics.domNodeCount,
          translatableFound: scanResult.length,
          duration,
          complexity: pageMetrics.estimatedComplexity
        }
      };

    } catch (error) {
      const fallbackResult = this.scanner.scan(config);
      return {
        nodes: fallbackResult.nodes,
        stats: {
          strategy: 'fallback-sync',
          translatableFound: fallbackResult.nodes.length,
          duration: performance.now() - startTime,
          complexity: 'unknown' as const,
          fallbackReason: (error as Error).message
        }
      };
    }
  }

  /**
   * 📊 分析页面复杂度
   */
  private analyzePageComplexity(rootNode: HTMLElement): PageComplexity {
    const domNodeCount = rootNode.querySelectorAll('*').length;
    const hasInfiniteScroll = this.detectInfiniteScroll(rootNode);
    const isDynamic = this.detectDynamicContent(rootNode);
    
    let estimatedComplexity: PageComplexity['estimatedComplexity'];
    if (domNodeCount > 15000) {
      estimatedComplexity = 'extreme';
    } else if (domNodeCount > 5000 || hasInfiniteScroll) {
      estimatedComplexity = 'high';
    } else if (domNodeCount > 1000 || isDynamic) {
      estimatedComplexity = 'medium';
    } else {
      estimatedComplexity = 'low';
    }

    return { domNodeCount, hasInfiniteScroll, isDynamic, estimatedComplexity };
  }

  private detectInfiniteScroll(rootNode: HTMLElement): boolean {
    const selectors = [
      '[data-infinite-scroll]', '[data-lazy-load]', '.infinite-scroll', 
      '.lazy-load', '[data-scroll-loading]', '.scroll-loading'
    ];
    return selectors.some(selector => rootNode.querySelector(selector) !== null);
  }

  private detectDynamicContent(rootNode: HTMLElement): boolean {
    const indicators = [
      '[data-reactroot]', '[data-react-root]', '[data-vue]', '#app', '.app',
      '[ng-app]', '[data-ng-app]', '[data-ajax]', '.ajax-content', 
      '[data-dynamic]', '.dynamic-content'
    ];
    return indicators.some(selector => rootNode.querySelector(selector) !== null);
  }

  private async executeViewportFirstStrategy(config: ScanConfig): Promise<ScannedNode[]> {
    const viewportResult = await this.scanner.scanViewportFirst(config);
    return viewportResult.viewportNodes;
  }

  private async executeAsyncChunkedStrategy(config: ScanConfig): Promise<ScannedNode[]> {
    return await this.scanner.scanAsync(config, config.chunkSize || 200);
  }

  private async executeSynchronousStrategy(config: ScanConfig): Promise<ScannedNode[]> {
    const syncResult = this.scanner.scan(config);
    return syncResult.nodes;
  }

  getStats() {
    return { scannerStats: this.scanner.getStats() };
  }

  destroy(): void {
    if (this.debug) {
      this.logger.info('ScanStrategyManager destroyed');
    }
  }
}