/**
 * 🚨 翻译系统统一错误处理器
 * 
 * 解决问题：
 * - 替换空的catch块
 * - 统一错误记录和报告
 * - 类型化错误对象
 * - 规范化错误处理流程
 */

export enum TranslationErrorType {
  SCAN_ERROR = 'SCAN_ERROR',
  TRANSLATION_ERROR = 'TRANSLATION_ERROR',
  RENDER_ERROR = 'RENDER_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR'
}

export interface TranslationErrorContext {
  component: string;
  method: string;
  element?: HTMLElement;
  text?: string;
  duration?: number;
  additionalData?: Record<string, any>;
}

export class TranslationError extends Error {
  public readonly type: TranslationErrorType;
  public readonly context: TranslationErrorContext;
  public readonly timestamp: number;
  public readonly originalError?: Error;

  constructor(
    type: TranslationErrorType,
    message: string,
    context: TranslationErrorContext,
    originalError?: Error
  ) {
    super(message);
    this.name = 'TranslationError';
    this.type = type;
    this.context = context;
    this.timestamp = Date.now();
    this.originalError = originalError;

    // 确保在错误堆栈中显示正确的错误位置
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, TranslationError);
    }
  }

  /**
   * 获取格式化的错误信息
   */
  getFormattedMessage(): string {
    const contextStr = `${this.context.component}.${this.context.method}`;
    const elementStr = this.context.element 
      ? ` [${this.context.element.tagName}]` 
      : '';
    
    return `[${this.type}] ${contextStr}${elementStr}: ${this.message}`;
  }

  /**
   * 获取错误的详细信息对象
   */
  getDetails() {
    return {
      type: this.type,
      message: this.message,
      context: this.context,
      timestamp: this.timestamp,
      originalError: this.originalError?.message,
      stack: this.stack
    };
  }
}

export interface ErrorLogger {
  error(message: string, data?: any): void;
  warn(message: string, data?: any): void;
  info(message: string, data?: any): void;
}

export class TranslationErrorHandler {
  private logger: ErrorLogger;
  private debug: boolean;
  private onError?: (error: TranslationError) => void;

  constructor(logger: ErrorLogger = console, debug = false, onError?: (error: TranslationError) => void) {
    this.logger = logger;
    this.debug = debug;
    this.onError = onError;
  }

  /**
   * 🛡️ 安全执行异步操作 - 替换空catch块的标准模式
   */
  async safeExecute<T>(
    operation: () => Promise<T>,
    errorType: TranslationErrorType,
    context: TranslationErrorContext,
    fallbackValue?: T
  ): Promise<T | undefined> {
    try {
      return await operation();
    } catch (error) {
      const translationError = this.createError(
        errorType,
        `Operation failed in ${context.component}.${context.method}`,
        context,
        error as Error
      );
      
      this.handleError(translationError);
      
      return fallbackValue;
    }
  }

  /**
   * 🛡️ 安全执行同步操作
   */
  safeExecuteSync<T>(
    operation: () => T,
    errorType: TranslationErrorType,
    context: TranslationErrorContext,
    fallbackValue?: T
  ): T | undefined {
    try {
      return operation();
    } catch (error) {
      const translationError = this.createError(
        errorType,
        `Sync operation failed in ${context.component}.${context.method}`,
        context,
        error as Error
      );
      
      this.handleError(translationError);
      
      return fallbackValue;
    }
  }

  /**
   * 📝 创建标准化的翻译错误
   */
  createError(
    type: TranslationErrorType,
    message: string,
    context: TranslationErrorContext,
    originalError?: Error
  ): TranslationError {
    return new TranslationError(type, message, context, originalError);
  }

  /**
   * 🚨 处理翻译错误 - 记录、报告和通知
   */
  handleError(error: TranslationError): void {
    // 1. 记录到日志
    this.logger.error(error.getFormattedMessage(), error.getDetails());

    // 2. 开发模式下的详细输出
    if (this.debug) {
      this.logger.error('Translation Error Details:', {
        type: error.type,
        context: error.context,
        originalError: error.originalError,
        stack: error.stack
      });
    }

    // 3. 调用自定义错误处理器
    if (this.onError) {
      try {
        this.onError(error);
      } catch (handlerError) {
        this.logger.error('Error handler itself failed:', handlerError);
      }
    }
  }

  /**
   * 📊 网络错误专用处理
   */
  handleNetworkError(
    originalError: Error,
    context: TranslationErrorContext,
    retryable = true
  ): TranslationError {
    const message = retryable 
      ? `Network operation failed, retry recommended: ${originalError.message}`
      : `Network operation failed permanently: ${originalError.message}`;
    
    const error = this.createError(
      TranslationErrorType.NETWORK_ERROR,
      message,
      { ...context, additionalData: { retryable } },
      originalError
    );

    this.handleError(error);
    return error;
  }

  /**
   * ⏰ 超时错误专用处理
   */
  handleTimeoutError(
    timeoutMs: number,
    context: TranslationErrorContext
  ): TranslationError {
    const error = this.createError(
      TranslationErrorType.TIMEOUT_ERROR,
      `Operation timed out after ${timeoutMs}ms`,
      { ...context, additionalData: { timeoutMs } }
    );

    this.handleError(error);
    return error;
  }

  /**
   * ✅ 验证错误专用处理
   */
  handleValidationError(
    field: string,
    value: any,
    expectedType: string,
    context: TranslationErrorContext
  ): TranslationError {
    const error = this.createError(
      TranslationErrorType.VALIDATION_ERROR,
      `Validation failed for field '${field}': expected ${expectedType}, got ${typeof value}`,
      { ...context, additionalData: { field, value, expectedType } }
    );

    this.handleError(error);
    return error;
  }

  /**
   * 📊 获取错误处理器统计信息
   */
  getStats() {
    return {
      handled: 'Error stats tracking not implemented yet',
      // TODO: 可以在未来添加错误统计功能
    };
  }
}

/**
 * 🎯 工厂函数：创建带有错误处理的翻译操作包装器
 */
export function createSafeTranslationWrapper(
  errorHandler: TranslationErrorHandler,
  component: string
) {
  return {
    async safeTranslate<T>(
      method: string,
      operation: () => Promise<T>,
      fallbackValue?: T,
      element?: HTMLElement,
      text?: string
    ): Promise<T | undefined> {
      return errorHandler.safeExecute(
        operation,
        TranslationErrorType.TRANSLATION_ERROR,
        { component, method, element, text },
        fallbackValue
      );
    },

    async safeScan<T>(
      method: string,
      operation: () => Promise<T>,
      fallbackValue?: T,
      element?: HTMLElement
    ): Promise<T | undefined> {
      return errorHandler.safeExecute(
        operation,
        TranslationErrorType.SCAN_ERROR,
        { component, method, element },
        fallbackValue
      );
    },

    async safeRender<T>(
      method: string,
      operation: () => Promise<T>,
      fallbackValue?: T,
      element?: HTMLElement,
      text?: string
    ): Promise<T | undefined> {
      return errorHandler.safeExecute(
        operation,
        TranslationErrorType.RENDER_ERROR,
        { component, method, element, text },
        fallbackValue
      );
    }
  };
}

// 🏭 默认错误处理器实例
export const defaultErrorHandler = new TranslationErrorHandler();