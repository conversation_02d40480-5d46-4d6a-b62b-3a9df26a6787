import { DomRenderer } from './renderer';
import { ConfigLoader } from '../../core/config-loader';
import { TranslationEventBus } from './event-bus';
import { StateManager } from './state-manager';

/**
 * 简化的翻译系统工厂
 * 
 * 用于创建基于 advanced-injection-rules.json 的简化翻译系统
 */

export interface SimplifiedTranslationSystemOptions {
  debug?: boolean;
  enableEventBus?: boolean;
  enableStateManager?: boolean;
}

export interface SimplifiedTranslationSystem {
  renderer: DomRenderer;
  configLoader: ConfigLoader;
  eventBus?: TranslationEventBus;
  stateManager?: StateManager;
}

/**
 * 创建简化的翻译系统
 */
export function createSimplifiedTranslationSystem(
  options: SimplifiedTranslationSystemOptions = {}
): SimplifiedTranslationSystem {
  const {
    debug = false,
    enableEventBus = true,
    enableStateManager = true
  } = options;

  // 1. 创建配置加载器
  const configLoader = new ConfigLoader();

  // 2. 可选：创建事件总线
  let eventBus: TranslationEventBus | undefined;
  if (enableEventBus) {
    eventBus = new TranslationEventBus({ debug });
  }

  // 3. 可选：创建状态管理器
  let stateManager: StateManager | undefined;
  if (enableStateManager) {
    stateManager = new StateManager({
      debug,
      eventBus
    });
  }

  // 4. 创建简化的渲染器
  const renderer = new DomRenderer({
    configLoader,
    eventBus,
    stateManager,
    debug
  });

  return {
    renderer,
    configLoader,
    eventBus,
    stateManager
  };
}

/**
 * 创建最小化的翻译系统（仅核心功能）
 */
export function createMinimalTranslationSystem(debug: boolean = false): {
  renderer: DomRenderer;
  configLoader: ConfigLoader;
} {
  const configLoader = new ConfigLoader();
  const renderer = new DomRenderer({
    configLoader,
    debug
  });

  return {
    renderer,
    configLoader
  };
}

/**
 * 快速翻译单个元素（工具函数）
 */
export async function quickTranslateElement(
  element: HTMLElement,
  translation: string,
  options: {
    language?: string;
    format?: 'text' | 'html';
    enableAccessibility?: boolean;
    debug?: boolean;
  } = {}
): Promise<boolean> {
  try {
    const { renderer } = createMinimalTranslationSystem(options.debug);
    
    const result = await renderer.render(element, translation, {
      language: options.language || 'zh-CN',
      format: options.format || 'text',
      enableAccessibility: options.enableAccessibility || false,
      debug: options.debug || false
    });

    return result.success;
  } catch (error) {
    console.error('Quick translate failed:', error);
    return false;
  }
}