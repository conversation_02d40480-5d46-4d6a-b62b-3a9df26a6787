/**
 * TranslateManager Adapter
 * 适配器 - 提供与旧TranslateManager完全相同的API，内部委托给新的编排器
 */

import { TranslationOrchestrator, OrchestratorConfig, BatchTranslationResult } from './orchestrator';
import { DomScanner } from './scanner';
import { DomRenderer } from './renderer';
import { StateManager } from './state-manager';
import { LifecycleManager } from './lifecycle-manager';
import { TranslationEventBus } from './event-bus';
import {
  TranslationConfig,
  TranslationState,
  TranslationStats
} from './types';
import { ViewModeController, ViewMode, ViewModeChangeEvent } from '../../content/view-controller';
import { TranslateFormat } from '../translate/types';
import { InjectionStrategy } from '../../core/injection-rules';
import { ConfigLoader, getConfigLoader } from '../../core/config-loader';
import { 
  translationErrorHandler, 
  TranslationError, 
  NetworkError, 
  RateLimitError, 
  ErrorSeverity 
} from '../../utils/error-handler';

/**
 * 日志接口定义
 */
interface LoggerAdapter {
  log: (...args: any[]) => void;
  error: (...args: any[]) => void;
  warn: (...args: any[]) => void;
  info: (...args: any[]) => void;
}
// debug 导入已移除，使用手动 console.log

/**
 * 适配器选项（兼容原TranslateManagerOptions）
 */
export interface TranslateManagerOptions {
  /** 目标语言 */
  targetLanguage?: string;
  /** 并发翻译数量 */
  concurrency?: number;
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 是否启用无障碍支持 */
  enableAccessibility?: boolean;
  /** 是否启用懒加载 */
  enableLazyLoading?: boolean;
  /** 是否启用性能优化 */
  enablePerformanceOptimization?: boolean;
  /** 翻译API函数 */
  translateFunction?: (text: string, targetLang: string, format?: TranslateFormat) => Promise<string>;
  /** 错误处理回调 */
  onError?: (error: Error, context: string) => void;
  /** 翻译进度回调 */
  onProgress?: (progress: any) => void;
  /** 是否启用智能注入规则引擎 */
  enableSmartInjection?: boolean;
  /** 是否启用高级注入系统 */
  enableAdvancedInjection?: boolean;
  /** 自定义配置加载器 */
  configLoader?: ConfigLoader;
  /** 强制使用特定注入策略 */
  forceStrategy?: InjectionStrategy;
  /** 是否启用自动翻译新增节点 */
  enableAutoTranslation?: boolean;
}

/**
 * TranslateManager适配器
 * 
 * 职责：
 * 1. 提供与原TranslateManager完全相同的公共API
 * 2. 内部委托所有调用给新的TranslationOrchestrator
 * 3. 处理API兼容性和数据格式转换
 * 4. 维护向后兼容性
 */
export class TranslateManagerAdapter {
  private orchestrator: TranslationOrchestrator;
  private viewController: ViewModeController;
  private logger: LoggerAdapter = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.log, // console 没有 info 方法，映射到 log
  };
  private options: Required<TranslateManagerOptions>;

  // 内部模块引用（用于兼容性API）
  private scanner: DomScanner;
  private renderer: DomRenderer;
  private stateManager: StateManager;
  private lifecycleManager: LifecycleManager;
  private eventBus: TranslationEventBus;

  constructor(options: TranslateManagerOptions = {}) {
    // 合并默认选项
    this.options = this.mergeDefaultOptions(options);

    if (this.options.debug) {
      console.log('✅ [translation-pipeline|STARTUP] 翻译管理器适配器初始化中', {
        options: this.options
      });
    }

    // 创建事件总线
    this.eventBus = new TranslationEventBus({
      debug: this.options.debug
    });

    // 创建状态管理器
    this.stateManager = new StateManager({
      debug: this.options.debug,
      eventBus: this.eventBus
    });

    // 创建扫描器
    this.scanner = new DomScanner({
      debug: this.options.debug
    });

    // 创建渲染器
    this.renderer = new DomRenderer({
      debug: this.options.debug,
      enableSmartInjection: this.options.enableSmartInjection,
      enableAdvancedInjection: this.options.enableAdvancedInjection,
      configLoader: this.options.configLoader,
      stateManager: this.stateManager,
      eventBus: this.eventBus
    });

    // 创建生命周期管理器
    this.lifecycleManager = new LifecycleManager({
      debug: this.options.debug,
      concurrency: {
        maxConcurrent: this.options.concurrency,
        rateLimitPerSecond: 8
      },
      lazyLoading: {
        enabled: this.options.enableLazyLoading,
        rootMargin: '100px',
        threshold: [0, 0.1, 0.5],
        batchSize: 3
      },
      performance: {
        enabled: this.options.enablePerformanceOptimization,
        enableMemoryMonitoring: true,
        memoryWarningThreshold: 80,
        memoryCleanupThreshold: 120,
        enableRenderOptimization: true
      }
    });

    // 创建视图控制器
    this.viewController = new ViewModeController({
      defaultMode: ViewMode.ORIGIN,
      enableDOMObserver: true,
      onModeChange: this.handleViewModeChange.bind(this),
      debug: this.options.debug
    });

    // 创建编排器配置
    const orchestratorConfig: OrchestratorConfig = {
      targetLanguage: this.options.targetLanguage,
      concurrency: this.options.concurrency,
      debug: this.options.debug,
      enableAccessibility: this.options.enableAccessibility,
      enableLazyLoading: this.options.enableLazyLoading,
      enablePerformanceOptimization: this.options.enablePerformanceOptimization,
      enableSmartInjection: this.options.enableSmartInjection,
      enableAdvancedInjection: this.options.enableAdvancedInjection,
      translateFunction: this.options.translateFunction,
      onError: this.options.onError,
      onProgress: this.options.onProgress,
      viewController: this.viewController
    };

    // 创建编排器
    this.orchestrator = new TranslationOrchestrator(
      this.scanner,
      this.renderer,
      this.stateManager,
      this.lifecycleManager,
      this.eventBus,
      orchestratorConfig
    );

    // 监听新节点
    this.setupNewNodesListener();

    if (this.options.debug) {
      console.log('✅ [translation-pipeline|STARTUP] 翻译管理器适配器初始化完成');
    }
  }

  /**
   * 开始翻译页面（兼容原API）
   */
  async translatePage(options?: any): Promise<any> {
    try {
      const result = await this.orchestrator.translatePage(options || {});

      // 转换为原API格式
      return {
        totalCount: result.totalCount,
        successCount: result.successCount,
        failureCount: result.failureCount,
        results: result.results,
        totalDuration: result.totalDuration
      };
    } catch (error) {
      if (this.options.debug) {
        console.error('❌ [translation-pipeline|ERROR] 页面翻译失败:', error);
      }
      throw error;
    }
  }

  /**
   * 翻译单个元素（兼容原API）
   */
  async translateElement(element: HTMLElement, options?: any): Promise<any> {
    try {
      const result = await this.orchestrator.translateElement(element, options || {});

      // 转换为原API格式
      return {
        success: result.success,
        element,
        originalText: result.originalText,
        translatedText: result.translatedText,
        error: result.error,
        duration: result.duration
      };
    } catch (error) {
      return {
        success: false,
        element,
        originalText: element.textContent || '',
        error: (error as Error).message,
        duration: 0
      };
    }
  }

  /**
   * 翻译多个元素（兼容原API）
   */
  async translateElements(elements: HTMLElement[], options?: any): Promise<any> {
    const results = await Promise.allSettled(
      elements.map(element => this.translateElement(element, options))
    );

    const successfulResults = results
      .filter(r => r.status === 'fulfilled')
      .map(r => (r as any).value);

    const failedCount = results.filter(r => r.status === 'rejected').length;

    return {
      totalCount: elements.length,
      successCount: successfulResults.filter(r => r.success).length,
      failureCount: failedCount,
      results: successfulResults,
      totalDuration: 0
    };
  }

  /**
   * 停止当前翻译（兼容原API）
   */
  stopTranslation(): void {
    this.orchestrator.stopTranslation();
  }

  /**
   * 清除所有翻译（兼容原API）
   */
  clearTranslations(): void {
    this.orchestrator.clearTranslations();
  }

  /**
   * 清除页面翻译（兼容原API）
   */
  clearPageTranslations(): number {
    const stats = this.renderer.getStats();
    this.clearTranslations();
    return stats.totalRenders;
  }

  /**
   * 设置视图模式（兼容原API）
   */
  setViewMode(mode: ViewMode): void {
    this.viewController.setViewMode(mode);
  }

  /**
   * 获取当前视图模式（兼容原API）
   */
  getCurrentViewMode(): ViewMode {
    return this.viewController.getCurrentMode();
  }

  /**
   * 切换视图模式（兼容原API）
   */
  toggleViewMode(): ViewMode {
    return this.viewController.toggleMode();
  }

  /**
   * 获取当前状态（兼容原API）
   */
  getCurrentState(): TranslationState {
    return this.orchestrator.getCurrentState();
  }

  /**
   * 获取统计信息（兼容原API）
   */
  getStats(): TranslationStats {
    return this.orchestrator.getStats();
  }

  /**
   * 重置统计信息（兼容原API）
   */
  resetStats(): void {
    this.stateManager.reset();
    this.renderer.resetStats();
  }

  /**
   * 清理缓存（兼容原API）
   */
  clearCache(): void {
    if (this.options.debug) {
      console.log('🧹 [translation-pipeline|CACHE] 缓存已清理');
    }
  }

  // === 智能注入相关方法（兼容原API） ===

  /**
   * 获取智能注入状态（兼容原API）
   */
  isSmartInjectionActive(): boolean {
    return this.options.enableSmartInjection;
  }

  /**
   * 获取注入规则引擎（兼容原API）
   */
  getRuleEngine(): any {
    const report = this.renderer.getSmartInjectionReport();
    return report?.ruleEngineStats || null;
  }

  /**
   * 获取配置加载器（兼容原API）
   */
  getConfigLoader(): ConfigLoader {
    return this.options.configLoader || getConfigLoader();
  }

  /**
   * 重新加载智能注入配置（兼容原API）
   */
  async reloadSmartInjectionConfig(): Promise<boolean> {
    await this.renderer.reloadSmartInjectionConfig();
    return true;
  }

  /**
   * 切换智能注入开关（兼容原API）
   */
  toggleSmartInjection(enabled?: boolean): boolean {
    this.renderer.toggleSmartInjection(enabled || false);
    return true;
  }

  /**
   * 获取智能注入统计（兼容原API）
   */
  getSmartInjectionStats(): any {
    return this.renderer.getSmartInjectionReport();
  }

  /**
   * 预览元素的注入策略（兼容原API）
   */
  previewElementStrategy(element: HTMLElement): any {
    // 占位符实现
    return null;
  }

  // === 状态管理相关方法（兼容原API） ===

  /**
   * 标记元素为正在处理状态（兼容原API）
   */
  markElementAsProcessing(element: HTMLElement): boolean {
    return this.stateManager.markAsProcessing(element);
  }

  /**
   * 标记元素为已翻译状态（兼容原API）
   */
  markElementAsTranslated(element: HTMLElement): void {
    this.stateManager.markAsTranslated(element);
  }

  /**
   * 检查元素是否可用于处理（兼容原API）
   */
  isElementAvailableForProcessing(element: HTMLElement): boolean {
    return this.stateManager.isElementAvailable(element);
  }

  /**
   * 检查元素是否已存在翻译（兼容原API）
   */
  hasExistingTranslation(element: HTMLElement): boolean {
    return this.stateManager.hasExistingTranslation(element);
  }

  /**
   * 销毁管理器（兼容原API）
   */
  destroy(): void {
    this.orchestrator.destroy();
    this.viewController.destroy();

    if (this.options.debug) {
      console.log('✅ [translation-pipeline|DESTROY] 翻译管理器适配器已销毁');
    }
  }

  /**
   * 处理视图模式变化
   * @private
   */
  private handleViewModeChange(event: ViewModeChangeEvent): void {
    if (this.options.debug) {
      console.log('🔄 [view-controller|MODE_CHANGE] 视图模式变化', event);
    }

    // 如果切换到需要翻译的模式但没有翻译内容，可以考虑自动翻译
    if (this.viewController.isTranslationVisible() &&
      this.getStats().render.successfulRenders === 0 &&
      this.getCurrentState() === TranslationState.IDLE) {

      if (this.options.debug) {
        console.log('⚡️ [view-controller|AUTO_TRANSLATE] 因视图模式变化触发自动翻译');
      }
      // 可以选择自动开始翻译
      // this.translatePage().catch(error => this.options.onError(error, 'auto-translate'));
    }
  }

  /**
   * 监听新增节点
   * @private
   */
  private setupNewNodesListener(): void {
    this.viewController.addNewNodesListener(() => {
      if (this.viewController.isTranslationVisible() &&
        this.getCurrentState() === TranslationState.IDLE &&
        this.options.enableAutoTranslation) { // 👈 检查自动翻译配置

        if (this.options.debug) {
          console.log('🤖 [auto-translate|ENABLED] 自动翻译已启用，开始翻译新增节点');
        }
        
        // 🤖 自动翻译新节点
        this.translateNewNodes().catch(error => {
          if (this.options.debug) {
            console.error('❌ [auto-translate|ERROR] 自动翻译新节点失败:', error);
          }
          if (this.options.onError) {
            this.options.onError(error, 'auto-translate-new-nodes');
          }
        });
      } else if (this.options.debug) {
        console.log('⚙️ [auto-translate|DISABLED] 新节点检测到但未启用自动翻译', {
          isTranslationVisible: this.viewController.isTranslationVisible(),
          currentState: this.getCurrentState(),
          enableAutoTranslation: this.options.enableAutoTranslation
        });
      }
    });
  }

  /**
   * 翻译新增的节点
   * @private
   */
  private async translateNewNodes(): Promise<void> {
    try {
      if (this.options.debug) {
        console.log('🤖 [auto-translate|START] 开始翻译页面新增节点');
      }

      // 使用现有的 translatePage 方法翻译整页
      // 编排器会自动过滤已翻译的节点，只翻译新增的未翻译节点
      await this.orchestrator.translatePage({
        // force: false // 不强制重新翻译已翻译的内容
      });

      if (this.options.debug) {
        console.log('✅ [auto-translate|SUCCESS] 新增节点自动翻译完成');
      }
    } catch (error) {
      if (this.options.debug) {
        console.error('❌ [auto-translate|ERROR] 新增节点自动翻译失败:', error);
      }
      throw error;
    }
  }


  /**
   * 合并默认选项
   * @private
   */
  private mergeDefaultOptions(options: TranslateManagerOptions): Required<TranslateManagerOptions> {
    return {
      targetLanguage: options.targetLanguage ?? 'zh',
      concurrency: options.concurrency ?? 3,
      debug: options.debug ?? true,
      enableAccessibility: options.enableAccessibility ?? true,
      enableLazyLoading: options.enableLazyLoading ?? true,
      enablePerformanceOptimization: options.enablePerformanceOptimization ?? true,
      translateFunction: options.translateFunction ?? this.defaultTranslateFunction,
      onError: options.onError ?? (() => { }),
      onProgress: options.onProgress ?? (() => { }),
      enableSmartInjection: options.enableSmartInjection ?? true,
      enableAdvancedInjection: options.enableAdvancedInjection ?? true, // 👈 默认启用高级注入
      configLoader: options.configLoader ?? getConfigLoader(),
      forceStrategy: options.forceStrategy ?? undefined,
      enableAutoTranslation: options.enableAutoTranslation ?? false, // 👈 默认关闭自动翻译
    } as Required<TranslateManagerOptions>;
  }

  /**
   * 默认翻译函数（使用真实的翻译服务）
   * @private
   */
  private defaultTranslateFunction = async (
    text: string,
    targetLang: string,
    format: TranslateFormat = 'text'
  ): Promise<string> => {
    try {
      // 🔧 修复：使用真实的翻译服务而不是透传
      const { translateService } = await import('../translate');

      if (this.options.debug) {
        console.log('⚙️ [translate-service|REQUEST] 调用翻译服务', {
          textPreview: text.slice(0, 100) + '...',
          textLength: text.length,
          originalFormat: format, // 原始格式
          actualFormat: 'text', // 实际使用的格式
          targetLang,
          hasHtmlTags: text.includes('<'),
          hasLinks: text.includes('<a ')
        });
      }

      // 🔧 关键修改：始终以纯文本格式处理，忽略原始format参数
      const result = await translateService.translateText(text, {
        to: targetLang,
        from: 'auto',
        format: 'text' // 强制使用纯文本格式，确保翻译引擎返回纯文本结果
      });

      if (this.options.debug) {
        console.log('✅ [translate-service|RESPONSE] 翻译服务返回结果', {
          originalPreview: text.slice(0, 50) + '...',
          translatedPreview: result.slice(0, 50) + '...',
          originalLength: text.length,
          translatedLength: result.length,
          hasHtmlInResult: result.includes('<a '),
          isTranslationSuccess: result !== text,
          usedFormat: 'text' // 显示实际使用的格式
        });
      }

      return result;

    } catch (error: unknown) {
      return this.handleTranslationError(error, text, targetLang, format);
    }
  }

  /**
   * 处理翻译错误的专用方法
   * 将复杂的错误处理逻辑提取到单独方法中以提高性能和可维护性
   */
  private handleTranslationError(
    error: unknown, 
    text: string, 
    targetLang: string, 
    format: TranslateFormat
  ): never {
    // 创建错误上下文信息
    const context = {
      originalText: text.slice(0, 100) + (text.length > 100 ? '...' : ''),
      targetLang,
      format,
      textLength: text.length
    };

    // 根据错误类型进行分类处理
    if (error instanceof NetworkError) {
      translationErrorHandler.network(error, context, ErrorSeverity.MEDIUM);
      throw new TranslationError(`网络错误导致翻译失败: ${error.message}`, {
        originalText: text,
        targetLang,
        cause: error
      });
    }
    
    if (error instanceof RateLimitError) {
      translationErrorHandler.translation(
        error, 
        { ...context, retryAfter: error.retryAfter }, 
        ErrorSeverity.MEDIUM
      );
      throw new TranslationError(`翻译频率限制: ${error.message}`, {
        originalText: text,
        targetLang,
        cause: error
      });
    }
    
    // 检查是否为配额或限制错误
    const errorMessage = this.extractErrorMessage(error);
    if (this.isQuotaLimitError(errorMessage)) {
      const errorObj = error instanceof Error ? error : new Error(errorMessage);
      translationErrorHandler.translation(errorObj, context, ErrorSeverity.HIGH);
      throw new TranslationError(`翻译服务配额不足: ${errorMessage}`, {
        originalText: text,
        targetLang,
        cause: errorObj
      });
    }
    
    // 处理其他未知错误
    const errorObj = error instanceof Error ? error : new Error(errorMessage);
    translationErrorHandler.translation(errorObj, context, ErrorSeverity.HIGH);
    throw new TranslationError(`翻译失败: ${errorMessage}`, {
      originalText: text,
      targetLang,
      cause: errorObj
    });
  }

  /**
   * 提取错误消息的辅助方法
   */
  private extractErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    if (error && typeof error === 'object' && 'message' in error) {
      return String((error as any).message);
    }
    return '未知错误';
  }

  /**
   * 判断是否为配额或限制错误的辅助方法
   */
  private isQuotaLimitError(message: string): boolean {
    const quotaKeywords = ['quota', 'limit', '配额', '限制', 'exceeded', 'rate'];
    return quotaKeywords.some(keyword => 
      message.toLowerCase().includes(keyword.toLowerCase())
    );
  }
}

// === 全局管理器实例（兼容原API） ===

/**
 * 全局翻译管理器实例
 */
let globalManager: TranslateManagerAdapter | null = null;

/**
 * 获取全局翻译管理器
 */
export function getTranslateManager(options?: TranslateManagerOptions): TranslateManagerAdapter {
  if (!globalManager) {
    globalManager = new TranslateManagerAdapter(options);
  }
  return globalManager;
}

/**
 * 设置全局翻译管理器
 */
export function setTranslateManager(manager: TranslateManagerAdapter): void {
  if (globalManager) {
    globalManager.destroy();
  }
  globalManager = manager;
}

/**
 * 便捷的页面翻译函数
 */
export async function translateCurrentPage(options?: TranslateManagerOptions): Promise<void> {
  const manager = getTranslateManager(options);
  return manager.translatePage();
}

/**
 * 便捷的视图切换函数
 */
export function toggleTranslationView(): ViewMode {
  const manager = getTranslateManager();
  return manager.toggleViewMode();
}