/**
 * Translation Pipeline Module
 * 翻译流水线模块 - 统一导出入口
 */

// === 核心类型 ===
export * from './types';

// === 核心模块 ===
export { TranslationEventBus, createEventBus, getDefaultEventBus, setDefaultEventBus } from './event-bus';
export { StateManager } from './state-manager';
export { DomScanner } from './scanner';
export { DomRenderer } from './renderer';
export { 
  createSimplifiedTranslationSystem,
  createMinimalTranslationSystem,
  quickTranslateElement
} from './simplified-factory';
export { LifecycleManager } from './lifecycle-manager';
export { TranslationOrchestrator } from './orchestrator';

// === 适配器（主要API） ===
export { 
  TranslateManagerAdapter,
  getTranslateManager,
  setTranslateManager,
  translateCurrentPage,
  toggleTranslationView,
  type TranslateManagerOptions
} from './adapter';

// === 便捷工厂函数 ===

import { TranslateManagerAdapter, TranslateManagerOptions } from './adapter';
import { TranslationEventBus } from './event-bus';
import { StateManager } from './state-manager';
import { DomScanner } from './scanner';
import { DomRenderer } from './renderer';
import { LifecycleManager } from './lifecycle-manager';
import { TranslationOrchestrator, OrchestratorConfig } from './orchestrator';
import { ViewModeController, ViewMode } from '../../content/view-controller';

/**
 * 创建完整的翻译管理器实例
 * @param options 配置选项
 * @returns TranslateManagerAdapter实例
 */
export function createTranslationManager(options: TranslateManagerOptions = {}): TranslateManagerAdapter {
  return new TranslateManagerAdapter(options);
}

/**
 * 创建自定义翻译编排器
 * @param config 编排器配置
 * @returns 自定义编排器实例
 */
export function createCustomOrchestrator(config: OrchestratorConfig): {
  orchestrator: TranslationOrchestrator;
  components: {
    scanner: DomScanner;
    renderer: DomRenderer;
    stateManager: StateManager;
    lifecycleManager: LifecycleManager;
    eventBus: TranslationEventBus;
    viewController?: ViewModeController;
  };
} {
  const eventBus = new TranslationEventBus({ debug: config.debug });
  
  const stateManager = new StateManager({
    debug: config.debug,
    eventBus
  });

  const scanner = new DomScanner({
    debug: config.debug
  });

  const renderer = new DomRenderer({
    debug: config.debug,
    enableSmartInjection: config.enableSmartInjection,
    stateManager,
    eventBus
  });

  const lifecycleManager = new LifecycleManager({
    debug: config.debug,
    concurrency: {
      maxConcurrent: config.concurrency,
      rateLimitPerSecond: 8
    },
    lazyLoading: {
      enabled: config.enableLazyLoading
    },
    performance: {
      enabled: config.enablePerformanceOptimization
    }
  });

  const viewController = config.viewController || new ViewModeController({
    defaultMode: ViewMode.ORIGIN,
    enableDOMObserver: true,
    debug: config.debug
  });

  const orchestrator = new TranslationOrchestrator(
    scanner,
    renderer,
    stateManager,
    lifecycleManager,
    eventBus,
    { ...config, viewController }
  );

  return {
    orchestrator,
    components: {
      scanner,
      renderer,
      stateManager,
      lifecycleManager,
      eventBus,
      viewController
    }
  };
}

/**
 * 模块版本信息
 */
export const VERSION = '2.0.0';

/**
 * 模块信息
 */
export const MODULE_INFO = {
  name: 'Translation Pipeline',
  version: VERSION,
  description: 'Modular translation system with event-driven architecture',
  author: 'Lucid Extension Team',
  architecture: 'Event-driven, dependency injection, modular design'
};

// === 重新导出兼容性类型 ===

// 为了完全兼容原TranslateManager，重新导出一些关键类型
export { ViewMode, ViewModeController } from '../../content/view-controller';
export { TranslateFormat } from '../translate/types';
export { TaskPriority } from '../../utils/promise-pool';
export { InjectionStrategy } from '../../core/injection-rules';

// === 配置管理系统 ===

// 导出统一配置管理功能
export { 
  getTranslationConfig, 
  updateTranslationConfig, 
  enableDebugMode, 
  disableDebugMode, 
  isDebugEnabled, 
  resetTranslationConfig,
  configDebugTools,
  toManagerOptions,
  toOrchestratorConfig,
  type GlobalTranslationConfig
} from './config';

// === 调试和诊断工具 ===

/**
 * 获取模块健康状态
 */
export function getModuleHealth(): {
  status: 'healthy' | 'warning' | 'error';
  checks: Array<{
    name: string;
    status: 'pass' | 'fail' | 'warning';
    message: string;
  }>;
} {
  const checks = [
    {
      name: 'EventBus',
      status: 'pass' as const,
      message: 'Event system is operational'
    },
    {
      name: 'StateManager',
      status: 'pass' as const,
      message: 'State management is operational'
    },
    {
      name: 'DomScanner',
      status: 'pass' as const,
      message: 'DOM scanning is operational'
    },
    {
      name: 'DomRenderer',
      status: 'pass' as const,
      message: 'DOM rendering is operational'
    },
    {
      name: 'LifecycleManager',
      status: 'pass' as const,
      message: 'Lifecycle management is operational'
    }
  ];

  const hasFailures = checks.some(check => (check.status as string) === 'fail');
  const hasWarnings = checks.some(check => (check.status as string) === 'warning');

  return {
    status: hasFailures ? 'error' : hasWarnings ? 'warning' : 'healthy',
    checks
  };
}

/**
 * 模块诊断信息
 */
export function getModuleDiagnostics(): {
  version: string;
  components: string[];
  features: string[];
  compatibility: {
    originalAPI: boolean;
    eventDriven: boolean;
    dependencyInjection: boolean;
    smartInjection: boolean;
    lazyLoading: boolean;
    performanceOptimization: boolean;
  };
} {
  return {
    version: VERSION,
    components: [
      'TranslationEventBus',
      'StateManager', 
      'DomScanner',
      'DomRenderer',
      'LifecycleManager',
      'TranslationOrchestrator',
      'TranslateManagerAdapter'
    ],
    features: [
      'Event-driven architecture',
      'Dependency injection',
      'Modular design',
      'Smart injection rules',
      'Lazy loading',
      'Performance optimization',
      'Full backward compatibility'
    ],
    compatibility: {
      originalAPI: true,
      eventDriven: true,
      dependencyInjection: true,
      smartInjection: true,
      lazyLoading: true,
      performanceOptimization: true
    }
  };
}