/**
 * Lifecycle Manager
 * 生命周期管理器 - 封装并发、懒加载、性能优化等非核心流程
 */

import { LifecycleTask } from './types';
import { TaskPriority, TranslationConcurrencyController } from '../../utils/promise-pool';
import { LazyLoadManager, LazyLoadItem, BatchLazyLoadItem, BatchProcessHandler } from '../../utils/lazy-loader';
import { PerformanceOptimizer, CleanupStrategy } from '../../utils/performance-optimizer';
// debug 导入已移除，使用手动 console.log
import { DomElementAnalyzer } from '../../core/dom-element-analyzer';

/**
 * 生命周期管理器配置
 */
export interface LifecycleManagerConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 并发控制配置 */
  concurrency?: {
    maxConcurrent?: number;
    rateLimitPerSecond?: number;
  };
  /** 懒加载配置 */
  lazyLoading?: {
    enabled?: boolean;
    rootMargin?: string;
    threshold?: number[];
    batchSize?: number;
  };
  /** 性能优化配置 */
  performance?: {
    enabled?: boolean;
    enableMemoryMonitoring?: boolean;
    memoryWarningThreshold?: number;
    memoryCleanupThreshold?: number;
    enableRenderOptimization?: boolean;
  };
}

/**
 * 生命周期管理器
 * 
 * 职责：
 * 1. 管理任务的并发执行
 * 2. 处理懒加载逻辑
 * 3. 执行性能优化
 * 4. 协调各种生命周期任务
 */
export class LifecycleManager {
  private concurrencyController: TranslationConcurrencyController;
  private lazyLoadManager?: LazyLoadManager;
  private performanceOptimizer?: PerformanceOptimizer;
  private logger = console;
  private debugMode: boolean;
  private config: Required<LifecycleManagerConfig>;

  // 任务队列
  private taskQueue: LifecycleTask[] = [];
  private activeTasks = new Map<string, Promise<any>>();

  constructor(config: LifecycleManagerConfig = {}) {
    this.debugMode = config.debug ?? false;
    this.config = this.mergeDefaultConfig(config);

    // 初始化并发控制器
    this.concurrencyController = new TranslationConcurrencyController({
      maxConcurrent: this.config.concurrency.maxConcurrent!,
      rateLimitPerSecond: this.config.concurrency.rateLimitPerSecond!,
      debug: this.debugMode
    });

    // 初始化懒加载管理器
    if (this.config.lazyLoading.enabled) {
      this.lazyLoadManager = new LazyLoadManager({
        rootMargin: this.config.lazyLoading.rootMargin!,
        threshold: this.config.lazyLoading.threshold!,
        batchSize: this.config.lazyLoading.batchSize!,
        debug: this.debugMode
      });
    }

    // 初始化性能优化器
    if (this.config.performance.enabled) {
      this.performanceOptimizer = new PerformanceOptimizer({
        enableMemoryMonitoring: this.config.performance.enableMemoryMonitoring!,
        memoryWarningThreshold: this.config.performance.memoryWarningThreshold!,
        memoryCleanupThreshold: this.config.performance.memoryCleanupThreshold!,
        enableRenderOptimization: this.config.performance.enableRenderOptimization!,
        debug: this.debugMode
      });
    }

    if (this.debugMode) {
      console.log('✅ [lifecycle-manager|STARTUP] 生命周期管理器初始化完成', {
        concurrency: this.config.concurrency,
        lazyLoadingEnabled: this.config.lazyLoading.enabled,
        performanceEnabled: this.config.performance.enabled
      });
    }
  }

  /**
   * 添加任务到队列
   * @param task 生命周期任务
   * @returns Promise，解析为任务结果
   */
  async addTask<T = any>(task: LifecycleTask): Promise<T> {
    if (this.debugMode) {
      this.logger.info('Adding task to lifecycle manager', {
        id: task.id,
        type: task.type,
        priority: task.priority
      });
    }

    // 将任务添加到队列
    this.taskQueue.push(task);

    // 立即尝试执行任务
    return this.executeTask<T>(task);
  }

  /**
   * 观察元素进行懒加载
   * @param item 懒加载项
   */
  observeLazy(item: LazyLoadItem): void {
    if (!this.lazyLoadManager) {
      if (this.debugMode) {
        this.logger.warn('Lazy loading is not enabled');
      }
      return;
    }

    if (this.debugMode) {
      this.logger.info('Adding element to lazy loading observer', {
        id: item.id,
        priority: item.priority,
        tagName: item.element.tagName
      });
    }

    this.lazyLoadManager.observe(item);
  }

  /**
   * 🚀 设置批量懒加载处理回调
   */
  setBatchLazyHandler(handler: (items: any[]) => Promise<void>): void {
    if (!this.lazyLoadManager) {
      if (this.debugMode) {
        this.logger.warn('Lazy loading is not enabled, cannot set batch handler');
      }
      return;
    }

    this.lazyLoadManager.setBatchProcessHandler(handler);

    if (this.debugMode) {
      this.logger.info('🚀 Batch lazy loading handler set', {
        handlerType: typeof handler
      });
    }
  }

  /**
   * 🚀 批量观察元素进行懒加载 - 性能优化版本
   */
  observeLazyBatch(items: Array<{
    id: string;
    element: HTMLElement;
    priority?: number;
    once?: boolean;
    metadata?: Record<string, any>;
  }>): void {
    if (!this.lazyLoadManager) {
      if (this.debugMode) {
        this.logger.warn('Lazy loading is not enabled');
      }
      return;
    }

    this.lazyLoadManager.observeBatch(items);

    if (this.debugMode) {
      this.logger.info('🚀 Adding batch elements to lazy loading observer', {
        itemCount: items.length,
        items: items.map(item => ({
          id: item.id,
          tagName: item.element.tagName,
          priority: item.priority
        }))
      });
    }
  }

  /**
   * 停止观察懒加载元素
   * @param element HTML元素
   */
  unobserveLazy(element: HTMLElement): void {
    if (!this.lazyLoadManager) {
      return;
    }

    this.lazyLoadManager.unobserve(element.id || element.tagName);

    if (this.debugMode) {
      this.logger.info('Element removed from lazy loading observer', {
        tagName: element.tagName
      });
    }
  }

  /**
   * 执行性能检查
   * @returns 性能建议列表
   */
  analyzePerformance(): Array<{
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    suggestion: string;
  }> {
    if (!this.performanceOptimizer) {
      if (this.debugMode) {
        this.logger.warn('Performance optimization is not enabled');
      }
      return [];
    }

    const suggestions = this.performanceOptimizer.analyzePerformance();

    if (this.debugMode && suggestions.length > 0) {
      this.logger.info('Performance analysis completed', {
        suggestionsCount: suggestions.length,
        critical: suggestions.filter(s => s.severity === 'critical').length,
        high: suggestions.filter(s => (s.severity as string) === 'high').length
      });
    }

    return suggestions.map(s => ({
      ...s,
      suggestion: s.message || 'No specific suggestion',
      severity: s.severity === 'info' ? 'low' as const : 
               s.severity === 'warning' ? 'medium' as const : 
               s.severity === 'critical' ? 'critical' as const : 'low' as const
    }));
  }

  /**
   * 执行性能清理
   * @param strategy 清理策略
   */
  performCleanup(strategy: CleanupStrategy = CleanupStrategy.STANDARD): void {
    if (!this.performanceOptimizer) {
      if (this.debugMode) {
        this.logger.warn('Performance optimization is not enabled');
      }
      return;
    }

    if (this.debugMode) {
      this.logger.info('Performing cleanup', { strategy });
    }

    this.performanceOptimizer.performCleanup(strategy);
  }

  /**
   * 分离可见和不可见节点
   * @param elements HTML元素数组
   * @returns 分类后的节点
   */
  categorizeElementsByVisibility(elements: HTMLElement[]): {
    visibleElements: HTMLElement[];
    invisibleElements: HTMLElement[];
  } {
    // 🔧 修复：重新启用懒加载，使用正确的屏幕外检测逻辑
    const visibleElements: HTMLElement[] = [];
    const invisibleElements: HTMLElement[] = [];

    // 使用新的DOM元素分析器检查元素可见性
    const elementAnalyzer = new DomElementAnalyzer();

    const viewport = {
      top: window.scrollY - 200,    // 扩展200px预加载区域
      bottom: window.scrollY + window.innerHeight + 200
    };

    elements.forEach(element => {
      // 使用元素位置判断是否在视窗内
      const rect = element.getBoundingClientRect();
      const isOffScreen = rect.bottom < viewport.top || rect.top > viewport.bottom;

      if (isOffScreen) {
        invisibleElements.push(element);
      } else {
        visibleElements.push(element);
      }
    });

    if (this.debugMode) {
      this.logger.info('Elements categorized by visibility', {
        total: elements.length,
        visible: visibleElements.length,
        invisible: invisibleElements.length
      });
    }

    return {
      visibleElements,
      invisibleElements
    };
  }

  /**
   * 计算懒加载优先级
   * @param element HTML元素
   * @returns 优先级数值
   */
  calculateLazyPriority(element: HTMLElement): number {
    let priority = 1;

    // 标题元素优先级更高
    const tagName = element.tagName.toLowerCase();
    if (['h1', 'h2', 'h3'].includes(tagName)) {
      priority += 3;
    } else if (['h4', 'h5', 'h6'].includes(tagName)) {
      priority += 2;
    }

    // 短文本优先级更高
    const textLength = element.textContent?.length || 0;
    if (textLength < 100) {
      priority += 2;
    } else if (textLength > 500) {
      priority -= 1;
    }

    // 距离视窗距离影响优先级
    const rect = element.getBoundingClientRect();
    const distanceFromViewport = Math.abs(
      rect.top + window.scrollY - window.scrollY - window.innerHeight / 2
    );

    if (distanceFromViewport < 500) {
      priority += 1;
    }

    return Math.max(1, priority);
  }

  /**
   * 获取并发控制器统计信息
   */
  getConcurrencyStats(): any {
    return this.concurrencyController.getStats();
  }

  /**
   * 获取懒加载统计信息
   */
  getLazyLoadStats(): any {
    return this.lazyLoadManager?.getStats() || null;
  }

  /**
   * 获取性能统计信息
   */
  getPerformanceStats(): any {
    return (this.performanceOptimizer as any)?.getStats?.() || null;
  }

  /**
   * 获取整体统计信息
   */
  getStats(): {
    taskQueue: number;
    activeTasks: number;
    concurrency: any;
    lazyLoad?: any;
    performance?: any;
  } {
    return {
      taskQueue: this.taskQueue.length,
      activeTasks: this.activeTasks.size,
      concurrency: this.getConcurrencyStats(),
      lazyLoad: this.getLazyLoadStats(),
      performance: this.getPerformanceStats()
    };
  }

  /**
   * 等待所有任务完成
   */
  async waitForAllTasks(): Promise<void> {
    if (this.activeTasks.size === 0) {
      return;
    }

    if (this.debugMode) {
      this.logger.info('Waiting for all tasks to complete', {
        activeTasks: this.activeTasks.size
      });
    }

    await Promise.allSettled(Array.from(this.activeTasks.values()));
  }

  /**
   * 取消所有任务
   */
  cancelAllTasks(): void {
    this.taskQueue = [];
    this.activeTasks.clear();

    if (this.debugMode) {
      this.logger.info('All tasks cancelled');
    }
  }

  /**
   * 销毁生命周期管理器
   */
  destroy(): void {
    // 取消所有任务
    this.cancelAllTasks();

    // 销毁子组件
    if (this.lazyLoadManager) {
      this.lazyLoadManager.destroy();
    }

    if (this.performanceOptimizer) {
      this.performanceOptimizer.destroy();
    }

    if (this.debugMode) {
      this.logger.info('LifecycleManager destroyed');
    }
  }

  /**
   * 执行任务
   * @private
   */
  private async executeTask<T = any>(task: LifecycleTask): Promise<T> {
    try {
      // 检查任务是否已经在执行
      if (this.activeTasks.has(task.id)) {
        return await this.activeTasks.get(task.id);
      }

      // 创建任务Promise
      const taskPromise = this.runTask<T>(task);
      this.activeTasks.set(task.id, taskPromise);

      const result = await taskPromise;

      // 清理已完成的任务
      this.activeTasks.delete(task.id);
      this.taskQueue = this.taskQueue.filter(t => t.id !== task.id);

      return result;

    } catch (error) {
      // 清理失败的任务
      this.activeTasks.delete(task.id);
      this.taskQueue = this.taskQueue.filter(t => t.id !== task.id);

      if (this.debugMode) {
        this.logger.error('Task execution failed', {
          id: task.id,
          type: task.type,
          error: (error as Error).message
        });
      }

      throw error;
    }
  }

  /**
   * 运行任务
   * @private
   */
  private async runTask<T = any>(task: LifecycleTask): Promise<T> {
    if (this.debugMode) {
      this.logger.info('Executing task', {
        id: task.id,
        type: task.type,
        priority: task.priority
      });
    }

    const startTime = performance.now();

    try {
      const result = await task.execute();
      const duration = performance.now() - startTime;

      if (this.debugMode) {
        this.logger.info('Task completed successfully', {
          id: task.id,
          type: task.type,
          duration: `${duration.toFixed(2)}ms`
        });
      }

      return result;

    } catch (error) {
      const duration = performance.now() - startTime;

      if (this.debugMode) {
        this.logger.error('Task failed', {
          id: task.id,
          type: task.type,
          duration: `${duration.toFixed(2)}ms`,
          error: (error as Error).message
        });
      }

      throw error;
    }
  }

  /**
   * 合并默认配置
   * @private
   */
  private mergeDefaultConfig(config: LifecycleManagerConfig): Required<LifecycleManagerConfig> {
    return {
      debug: config.debug ?? false,
      concurrency: {
        maxConcurrent: config.concurrency?.maxConcurrent ?? 3,
        rateLimitPerSecond: config.concurrency?.rateLimitPerSecond ?? 8
      },
      lazyLoading: {
        enabled: config.lazyLoading?.enabled ?? true,
        rootMargin: config.lazyLoading?.rootMargin ?? '100px',
        threshold: config.lazyLoading?.threshold ?? [0, 0.1, 0.5],
        batchSize: config.lazyLoading?.batchSize ?? 3
      },
      performance: {
        enabled: config.performance?.enabled ?? true,
        enableMemoryMonitoring: config.performance?.enableMemoryMonitoring ?? true,
        memoryWarningThreshold: config.performance?.memoryWarningThreshold ?? 250,
        memoryCleanupThreshold: config.performance?.memoryCleanupThreshold ?? 400,
        enableRenderOptimization: config.performance?.enableRenderOptimization ?? true
      }
    };
  }
}