/**
 * DOM Renderer
 * DOM渲染器 - 负责将译文注入到DOM中，管理注入策略
 */

import { RenderResult, RenderConfig, TranslationEventType } from './types';
import { StateManager } from './state-manager';
import { TranslationEventBus } from './event-bus';
import {
  DOMInjector,
  InjectionOptions,
  InjectionResult
} from '../../content/injector';
import {
  EnhancedDOMInjector,
  EnhancedInjectionOptions,
  EnhancedInjectionResult
} from '../../core/enhanced-injector';
import {
  AdvancedDOMInjector,
  AdvancedInjectionOptions,
  AdvancedInjectionResult
} from '../../core/advanced-injector';
import {
  InjectionRuleEngine,
  InjectionStrategy
} from '../../core/injection-rules';
import { ConfigLoader, getConfigLoader } from '../../core/config-loader';
import { debug } from '../../utils/debug';

/**
 * 渲染器配置
 */
export interface RendererConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否启用智能注入 */
  enableSmartInjection?: boolean;
  /** 是否启用高级注入 */
  enableAdvancedInjection?: boolean;
  /** 配置加载器 */
  configLoader?: ConfigLoader;
  /** 强制使用特定策略 */
  forceStrategy?: InjectionStrategy;
  /** 状态管理器 */
  stateManager?: StateManager;
  /** 事件总线 */
  eventBus?: TranslationEventBus;
}

/**
 * 初始化状态枚举
 */
enum InitializationState {
  PENDING = 'pending',
  LOADING = 'loading',
  READY = 'ready',
  FAILED = 'failed'
}

/**
 * DOM渲染器
 * 
 * 职责：
 * 1. 将翻译结果注入到DOM中
 * 2. 管理注入策略和规则
 * 3. 处理注入结果和错误
 * 4. 发布渲染事件
 */
export class DomRenderer {
  private injector!: DOMInjector | EnhancedDOMInjector | AdvancedDOMInjector;
  private ruleEngine?: InjectionRuleEngine;
  private configLoader: ConfigLoader;
  private stateManager?: StateManager;
  private eventBus?: TranslationEventBus;
  private logger = debug;
  private debugMode: boolean;
  private isSmartInjectionEnabled: boolean;
  private useAdvancedInjection: boolean;
  private initializationState: InitializationState = InitializationState.PENDING;
  private initializationError?: string;

  // 渲染统计
  private stats = {
    totalRenders: 0,
    successfulRenders: 0,
    failedRenders: 0,
    totalDuration: 0
  };

  constructor(config: RendererConfig = {}) {
    this.debugMode = config.debug ?? false;
    this.isSmartInjectionEnabled = config.enableSmartInjection ?? true;
    this.useAdvancedInjection = config.enableAdvancedInjection ?? true; // 从配置读取
    this.configLoader = config.configLoader || getConfigLoader();
    this.stateManager = config.stateManager;
    this.eventBus = config.eventBus;

    // 初始化注入器 - 修复异步初始化问题
    if (this.useAdvancedInjection) {
      // 先创建基础注入器作为临时方案，异步初始化高级注入器
      this.injector = new DOMInjector();
      this.initializationState = InitializationState.LOADING;

      // 立即开始异步初始化，但不阻塞构造函数
      if (this.debugMode) {
        this.logger.info('🚀 [DEBUG] Constructor: Starting async advanced injection initialization');
      }

      this.initializeAdvancedInjection().then((advancedInjector) => {
        if (this.debugMode) {
          this.logger.info('🔄 [DEBUG] Constructor: initializeAdvancedInjection resolved', {
            advancedInjector: !!advancedInjector,
            injectorType: advancedInjector?.constructor?.name || 'null'
          });
        }

        if (advancedInjector) {
          this.injector = advancedInjector;
          this.initializationState = InitializationState.READY;
          if (this.debugMode) {
            this.logger.info('🎉 [DEBUG] Constructor: Advanced injection system ready for use', {
              injectorType: this.injector.constructor.name,
              initializationState: this.initializationState
            });
          }
        } else {
          // 高级注入器初始化失败，回退到增强注入器
          if (this.debugMode) {
            this.logger.warn('🔄 [DEBUG] Constructor: Advanced injector returned null, falling back to enhanced injection');
          }
          this.initializeSmartInjection();
          this.initializationState = InitializationState.READY;
          if (this.debugMode) {
            this.logger.info('🔄 [DEBUG] Constructor: Fallback to enhanced injection system', {
              injectorType: this.injector?.constructor?.name || 'null',
              initializationState: this.initializationState,
              isSmartInjectionEnabled: this.isSmartInjectionEnabled
            });
          }
        }
      }).catch(error => {
        if (this.debugMode) {
          this.logger.error('❌ [DEBUG] Constructor: initializeAdvancedInjection promise rejected', {
            error: (error as Error).message,
            stack: (error as Error).stack
          });
        }
        this.initializationState = InitializationState.FAILED;
        this.initializationError = (error as Error).message;
        if (this.debugMode) {
          this.logger.error('❌ [DEBUG] Constructor: Advanced injection initialization failed, using fallback:', error);
        }
        // 回退到增强注入器
        this.initializeSmartInjection();
        if (this.debugMode) {
          this.logger.info('🔄 [DEBUG] Constructor: Fallback injection initialized after error', {
            injectorType: this.injector?.constructor?.name || 'null',
            initializationState: this.initializationState
          });
        }
      });
    } else if (this.isSmartInjectionEnabled) {
      this.initializeSmartInjection(config.forceStrategy);
      this.initializationState = InitializationState.READY;
    } else {
      this.injector = new DOMInjector();
      this.initializationState = InitializationState.READY;
    }

    // 🔍 DEBUG: Log constructor completion
    if (this.debugMode) {
      this.logger.info('🏗️ [DEBUG] DomRenderer constructor completed', {
        smartInjectionEnabled: this.isSmartInjectionEnabled,
        advancedInjectionEnabled: this.useAdvancedInjection,
        currentInjectorType: this.injector?.constructor?.name || 'null',
        initializationState: this.initializationState,
        hasStateManager: !!this.stateManager,
        hasEventBus: !!this.eventBus
      });
    }

    if (this.debugMode) {
      this.logger.info('DomRenderer initialized', {
        smartInjectionEnabled: this.isSmartInjectionEnabled,
        advancedInjectionEnabled: this.useAdvancedInjection,
        hasStateManager: !!this.stateManager,
        hasEventBus: !!this.eventBus
      });
    }
  }

  /**
   * 渲染翻译到指定元素
   * @param element 目标元素
   * @param translation 翻译文本
   * @param config 渲染配置
   * @returns 渲染结果
   */
  async render(
    element: HTMLElement,
    translation: string,
    config: RenderConfig & {
      format?: 'text' | 'html';
      originalLinks?: Array<{ href: string; text: string; attributes: Record<string, string> }>;
    }
  ): Promise<RenderResult> {
    const startTime = performance.now();

    // 🔧 异常监控：声明在函数顶层，以便在catch块中访问
    let originalOnError: any = null;
    let originalOnUnhandledRejection: any = null;

    try {
      if (this.debugMode) {
        this.logger.info('Starting render', {
          element: element.tagName,
          textPreview: translation.slice(0, 50),
          smartInjection: this.isSmartInjectionEnabled
        });
      }

      // 🔧 异常监控：添加全局错误处理器来捕获可能的未处理异常
      originalOnError = window.onerror;
      originalOnUnhandledRejection = window.onunhandledrejection;

      window.onerror = (message, source, lineno, colno, error) => {
        console.error(`[DomRenderer] 🚨 全局错误捕获在渲染期间`, {
          element: `${element.tagName}.${element.className || 'no-class'}`,
          message, source, lineno, colno, error
        });
        if (originalOnError) originalOnError(message, source, lineno, colno, error);
        return true;
      };

      window.onunhandledrejection = (event) => {
        console.error(`[DomRenderer] 🚨 未处理的Promise拒绝在渲染期间`, {
          element: `${element.tagName}.${element.className || 'no-class'}`,
          reason: event.reason
        });
        if (originalOnUnhandledRejection) originalOnUnhandledRejection(event);
      };

      // 恢复原始处理器的函数
      const restoreErrorHandlers = () => {
        window.onerror = originalOnError;
        window.onunhandledrejection = originalOnUnhandledRejection;
      };

      // 检查元素状态
      if (this.stateManager && !this.stateManager.isElementAvailable(element)) {
        restoreErrorHandlers();
        return this.createErrorResult(
          element,
          'Element is not available for rendering',
          startTime
        );
      }

      // 🚀 FIX: Smart advanced injection wait - only wait if there's a chance of success
      if (this.useAdvancedInjection && !this.isAdvancedInjectionReady()) {
        // Don't wait if we're already in READY state with Enhanced injector (fallback already happened)
        if (this.initializationState === InitializationState.READY && 
            !(this.injector instanceof AdvancedDOMInjector)) {
          if (this.debugMode) {
            this.logger.info('🔄 [RENDER] Skipping advanced injection wait - already fell back to enhanced injection', {
              injectorType: this.injector?.constructor?.name || 'null',
              initializationState: this.initializationState
            });
          }
        } else {
          if (this.debugMode) {
            this.logger.info('Waiting for advanced injection system to initialize...');
          }
          const isReady = await this.waitForAdvancedInjection();
          if (!isReady) {
            if (this.debugMode) {
              this.logger.warn('Advanced injection system not ready, using fallback');
            }
          }
        }
      }

      // 构建注入选项
      if (this.debugMode) {
        this.logger.info('Building injection options', {
          element: `${element.tagName}.${element.className || 'no-class'}`,
          config: {
            language: config.language,
            enableAccessibility: config.enableAccessibility,
            debug: config.debug,
            forceStrategy: config.forceStrategy
          },
          smartInjectionEnabled: this.isSmartInjectionEnabled
        });
      }

      const injectionOptions = this.buildInjectionOptions(config, {
        format: config.format || 'text',
        originalLinks: config.originalLinks || []
      });

      if (this.debugMode) {
        this.logger.info('Injection options built successfully', {
          element: `${element.tagName}.${element.className || 'no-class'}`,
          injectionOptions: {
            language: injectionOptions.language,
            format: injectionOptions.format,
            enableAccessibility: injectionOptions.enableAccessibility,
            useRuleEngine: ('useRuleEngine' in injectionOptions) ? injectionOptions.useRuleEngine : 'N/A'
          }
        });
      }

      // 执行注入
      if (this.debugMode) {
        this.logger.info('Executing injection', {
          element: `${element.tagName}.${element.className || 'no-class'}`,
          translation: translation.slice(0, 30) + '...',
          injectionOptions: {
            language: injectionOptions.language,
            format: injectionOptions.format,
            enableAccessibility: injectionOptions.enableAccessibility
          }
        });
      }

      const injectionResult = await this.injector.injectTranslation(
        element,
        translation,
        injectionOptions
      );

      if (this.debugMode) {
        this.logger.info('Injection result', {
          element: `${element.tagName}.${element.className || 'no-class'}`,
          success: injectionResult.success,
          error: injectionResult.error,
          hasInjectedElement: !!injectionResult.injectedElement
        });
      }

      const duration = performance.now() - startTime;

      // 更新统计
      this.updateStats(injectionResult.success, duration);

      // 创建渲染结果
      const renderResult: RenderResult = {
        success: injectionResult.success,
        element,
        strategy: this.getUsedStrategy(injectionResult),
        error: injectionResult.error,
        duration
      };

      // 更新状态管理器
      if (this.stateManager) {
        if (injectionResult.success) {
          this.stateManager.markAsTranslated(element);
        } else {
          this.stateManager.markAsFailed(element, injectionResult.error);
        }
      }

      // 发布渲染完成事件
      if (this.eventBus) {
        this.eventBus.publish(
          TranslationEventType.RENDER_COMPLETED,
          renderResult,
          'DomRenderer'
        );
      }

      if (this.debugMode) {
        const status = injectionResult.success ? 'succeeded' : 'failed';
        this.logger.info(`Render ${status}`, {
          element: element.tagName,
          duration: `${duration.toFixed(2)}ms`,
          strategy: renderResult.strategy,
          error: renderResult.error
        });
      }

      // 恢复错误处理器
      restoreErrorHandlers();

      return renderResult;

    } catch (error) {
      const duration = performance.now() - startTime;
      const errorMessage = (error as Error).message;

      // 恢复错误处理器（即使在异常情况下）
      try {
        window.onerror = originalOnError;
        window.onunhandledrejection = originalOnUnhandledRejection;
      } catch (handlerError) {
        console.error('[DomRenderer] 无法恢复错误处理器:', handlerError);
      }

      // 🔧 增强错误调试：详细记录异常信息
      console.error(`[DomRenderer] 🚨 渲染异常捕获`, {
        element: `${element.tagName}.${element.className || 'no-class'}`,
        elementText: element.textContent?.slice(0, 50) + '...',
        translation: translation.slice(0, 50) + '...',
        error: errorMessage,
        stack: (error as Error).stack,
        duration: `${duration.toFixed(2)}ms`,
        timestamp: new Date().toISOString()
      });

      // 更新统计
      this.updateStats(false, duration);

      // 更新状态管理器
      if (this.stateManager) {
        this.stateManager.markAsFailed(element, errorMessage);
      }

      const renderResult = this.createErrorResult(element, errorMessage, startTime);

      // 发布错误事件
      if (this.eventBus) {
        this.eventBus.publish(
          TranslationEventType.ERROR_OCCURRED,
          {
            error: error as Error,
            context: 'DomRenderer.render',
            element,
            fatal: false
          },
          'DomRenderer'
        );
      }

      if (this.debugMode) {
        this.logger.error('Render failed with exception', {
          element: element.tagName,
          error: errorMessage,
          duration: `${duration.toFixed(2)}ms`
        });
      }

      return renderResult;
    }
  }

  /**
   * 清理指定元素的翻译
   * @param element 目标元素
   * @returns 是否成功清理
   */
  clear(element: HTMLElement): boolean {
    try {
      // 使用注入器清理翻译
      let success = true;
      if (this.injector instanceof AdvancedDOMInjector) {
        success = this.injector.removeTranslation(element);
      } else if ('clearTranslation' in this.injector) {
        success = (this.injector as any).clearTranslation?.(element) ?? true;
      }

      // 清理状态管理器中的状态
      if (this.stateManager) {
        // 从WeakSet中移除（如果有的话）
        element.removeAttribute('data-lu-translated');
        element.removeAttribute('data-lu-processing');
        element.removeAttribute('data-lu-failed');
        element.removeAttribute('data-lu-error');
      }

      if (this.debugMode) {
        this.logger.info('Translation cleared', {
          element: element.tagName,
          success
        });
      }

      return success;
    } catch (error) {
      if (this.debugMode) {
        this.logger.error('Error clearing translation', {
          element: element.tagName,
          error: (error as Error).message
        });
      }
      return false;
    }
  }

  /**
   * 清理所有翻译
   * @returns 清理的数量
   */
  clearAll(): number {
    try {
      let clearedCount = 0;
      if (this.injector instanceof AdvancedDOMInjector) {
        // Count before clearing
        clearedCount = document.querySelectorAll('[data-lu-translated]').length;
        this.injector.clearAllTranslations();
      } else {
        clearedCount = this.injector.clearAllTranslations();
      }

      // 清理状态管理器
      if (this.stateManager) {
        this.stateManager.cleanupElementStates();
      }

      // 重置统计
      this.resetStats();

      if (this.debugMode) {
        this.logger.info(`All translations cleared`, { count: clearedCount });
      }

      return clearedCount;
    } catch (error) {
      if (this.debugMode) {
        this.logger.error('Error clearing all translations', {
          error: (error as Error).message
        });
      }
      return 0;
    }
  }

  /**
   * 检查高级注入系统是否已初始化
   */
  isAdvancedInjectionReady(): boolean {
    const useAdvanced = this.useAdvancedInjection;
    const stateReady = this.initializationState === InitializationState.READY;
    const isAdvancedInstance = this.injector instanceof AdvancedDOMInjector;
    const result = useAdvanced && stateReady && isAdvancedInstance;

    if (this.debugMode) {
      this.logger.info('🔍 [DEBUG] Advanced injection readiness check:', {
        useAdvancedInjection: useAdvanced,
        initializationState: this.initializationState,
        stateIsReady: stateReady,
        injectorType: this.injector?.constructor?.name || 'null',
        isAdvancedDOMInjector: isAdvancedInstance,
        finalResult: result,
        initializationError: this.initializationError || 'none'
      });
    }

    return result;
  }

  /**
   * 等待高级注入系统初始化完成
   */
  async waitForAdvancedInjection(timeoutMs: number = 5000): Promise<boolean> {
    if (!this.useAdvancedInjection) {
      if (this.debugMode) {
        this.logger.info('🔍 [DEBUG] waitForAdvancedInjection: useAdvancedInjection is false, returning false');
      }
      return false;
    }

    if (this.debugMode) {
      this.logger.info('🔍 [DEBUG] waitForAdvancedInjection: Starting wait loop', {
        timeoutMs,
        currentState: this.initializationState,
        injectorType: this.injector?.constructor?.name || 'null',
        useAdvancedInjection: this.useAdvancedInjection
      });
    }

    // 🚀 FIX: If we're in READY state but using EnhancedDOMInjector, 
    // it means advanced injection failed and we fell back
    if (this.initializationState === InitializationState.READY &&
      !(this.injector instanceof AdvancedDOMInjector)) {
      if (this.debugMode) {
        this.logger.warn('🔄 [DEBUG] waitForAdvancedInjection: Already fell back to enhanced injection, skipping wait', {
          injectorType: this.injector?.constructor?.name || 'null',
          initializationState: this.initializationState
        });
      }
      return false; // Don't wait for something that has already failed
    }

    const startTime = Date.now();
    let checkCount = 0;

    while (Date.now() - startTime < timeoutMs) {
      checkCount++;

      // Check if already successfully initialized
      if (this.isAdvancedInjectionReady()) {
        if (this.debugMode) {
          this.logger.info('🎉 [DEBUG] waitForAdvancedInjection: Advanced injection is ready!', {
            checkCount,
            elapsedMs: Date.now() - startTime
          });
        }
        return true;
      }

      // Check if initialization failed
      if (this.initializationState === InitializationState.FAILED) {
        if (this.debugMode) {
          this.logger.warn('❌ [DEBUG] waitForAdvancedInjection: Initialization failed', {
            checkCount,
            elapsedMs: Date.now() - startTime,
            error: this.initializationError
          });
        }
        return false;
      }

      // 🚀 ADDITIONAL FIX: Check for fallback condition during wait
      if (this.initializationState === InitializationState.READY &&
        !(this.injector instanceof AdvancedDOMInjector)) {
        if (this.debugMode) {
          this.logger.warn('🔄 [DEBUG] waitForAdvancedInjection: Detected fallback during wait', {
            checkCount,
            elapsedMs: Date.now() - startTime,
            injectorType: this.injector?.constructor?.name || 'null'
          });
        }
        return false;
      }

      // Log every 20th check or first 5 checks to avoid spam
      if (checkCount % 20 === 0 || (checkCount <= 5)) {
        if (this.debugMode) {
          this.logger.info('⏳ [DEBUG] waitForAdvancedInjection: Still waiting...', {
            checkCount,
            elapsedMs: Date.now() - startTime,
            currentState: this.initializationState,
            injectorType: this.injector?.constructor?.name || 'null',
            remainingMs: timeoutMs - (Date.now() - startTime)
          });
        }
      }

      await new Promise(resolve => setTimeout(resolve, 50));
    }

    if (this.debugMode) {
      this.logger.warn('⏰ [DEBUG] waitForAdvancedInjection: Timeout reached', {
        checkCount,
        elapsedMs: Date.now() - startTime,
        currentState: this.initializationState,
        injectorType: this.injector?.constructor?.name || 'null',
        error: this.initializationError,
        timeoutMs,
        finalReadinessCheck: {
          useAdvancedInjection: this.useAdvancedInjection,
          stateReady: this.initializationState === InitializationState.READY,
          isAdvancedInstance: this.injector instanceof AdvancedDOMInjector
        }
      });
    }
    return false;
  }

  /**
   * 获取初始化状态信息
   */
  getInitializationStatus(): {
    state: InitializationState;
    error?: string;
    isAdvancedInjectionEnabled: boolean;
    isReady: boolean;
  } {
    return {
      state: this.initializationState,
      error: this.initializationError,
      isAdvancedInjectionEnabled: this.useAdvancedInjection,
      isReady: this.isAdvancedInjectionReady()
    };
  }

  /**
   * 获取渲染统计信息
   */
  getStats(): {
    totalRenders: number;
    successfulRenders: number;
    failedRenders: number;
    averageDuration: number;
    injectorStats?: any;
  } {
    let injectorStats = null;

    // Check if injector has getStats method
    if (this.injector && typeof this.injector.getStats === 'function') {
      try {
        injectorStats = this.injector.getStats();
      } catch (error) {
        if (this.debugMode) {
          this.logger.warn('Failed to get injector stats:', error);
        }
      }
    }

    return {
      ...this.stats,
      averageDuration: this.stats.totalRenders > 0
        ? this.stats.totalDuration / this.stats.totalRenders
        : 0,
      injectorStats
    };
  }

  /**
   * 获取智能注入报告（如果启用）
   */
  getSmartInjectionReport(): any {
    if (this.useAdvancedInjection && this.injector instanceof AdvancedDOMInjector) {
      return {
        injectionStats: this.injector.getStats(),
        configInfo: this.configLoader.getVersionInfo()
      };
    }

    if (!this.isSmartInjectionEnabled || !(this.injector instanceof EnhancedDOMInjector)) {
      return null;
    }

    return {
      ruleEngineStats: this.ruleEngine?.getStats(),
      strategyReport: this.injector.getStrategyReport(),
      configInfo: this.configLoader.getVersionInfo()
    };
  }

  /**
   * 重新加载智能注入配置
   */
  async reloadSmartInjectionConfig(): Promise<boolean> {
    if (this.useAdvancedInjection) {
      try {
        const advancedInjector = await this.initializeAdvancedInjection();
        if (advancedInjector) {
          this.injector = advancedInjector;
        }
        if (this.debugMode) {
          this.logger.info('Advanced injection configuration reloaded');
        }
        return true;
      } catch (error) {
        if (this.debugMode) {
          this.logger.error('Error reloading advanced injection configuration:', error);
        }
        return false;
      }
    }

    if (!this.isSmartInjectionEnabled) {
      if (this.debugMode) {
        this.logger.warn('Smart injection is not enabled');
      }
      return false;
    }

    try {
      const reloadSuccess = await this.configLoader.reloadConfig();
      if (reloadSuccess) {
        // 重新初始化智能注入系统
        this.initializeSmartInjection();
        if (this.debugMode) {
          this.logger.info('Smart injection configuration reloaded');
        }
        return true;
      } else {
        if (this.debugMode) {
          this.logger.error('Failed to reload smart injection configuration');
        }
        return false;
      }
    } catch (error) {
      if (this.debugMode) {
        this.logger.error('Error reloading smart injection configuration:', error);
      }
      return false;
    }
  }

  /**
   * 切换智能注入开关
   */
  toggleSmartInjection(enabled?: boolean): boolean {
    const newState = enabled !== undefined ? enabled : !this.isSmartInjectionEnabled;

    if (newState === this.isSmartInjectionEnabled) {
      return this.isSmartInjectionEnabled;
    }

    this.isSmartInjectionEnabled = newState;

    if (newState) {
      // 启用智能注入 - 优先使用高级注入
      if (this.useAdvancedInjection) {
        this.initializeAdvancedInjection().then((advancedInjector) => {
          if (advancedInjector) {
            this.injector = advancedInjector;
          }
        });
      } else {
        this.initializeSmartInjection();
      }
    } else {
      // 禁用智能注入，回退到基础注入器
      this.injector = new DOMInjector();
      this.ruleEngine = undefined;
    }

    if (this.debugMode) {
      this.logger.info(`Smart injection ${newState ? 'enabled' : 'disabled'}`);
    }

    return this.isSmartInjectionEnabled;
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalRenders: 0,
      successfulRenders: 0,
      failedRenders: 0,
      totalDuration: 0
    };

    // 重置注入器统计
    if (this.injector && typeof this.injector.resetStats === 'function') {
      try {
        this.injector.resetStats();
      } catch (error) {
        if (this.debugMode) {
          this.logger.warn('Failed to reset injector stats:', error);
        }
      }
    }

    if (this.debugMode) {
      this.logger.info('DomRenderer stats reset');
    }
  }

  /**
   * 销毁渲染器
   */
  destroy(): void {
    this.clearAll();
    this.resetStats();

    if (this.debugMode) {
      this.logger.info('DomRenderer destroyed');
    }
  }

  /**
   * 初始化高级注入系统
   * @private
   */
  private async initializeAdvancedInjection(): Promise<AdvancedDOMInjector | null> {
    const initStartTime = performance.now();

    if (this.debugMode) {
      this.logger.info('🔧 Starting advanced injection system initialization...');
    }

    try {
      // 检查浏览器支持
      const stepStartTime = performance.now();
      if (this.debugMode) {
        this.logger.info('🔧 Step 1: Checking custom elements support...');
      }

      if (this.debugMode) {
        this.logger.info('🔍 [DEBUG] Step 1a: Importing custom-elements module...');
      }

      const { checkCustomElementsSupport } = await import('../../core/custom-elements');

      if (this.debugMode) {
        this.logger.info('✅ [DEBUG] Step 1b: custom-elements module imported successfully');
      }
      if (!checkCustomElementsSupport()) {
        if (this.debugMode) {
          this.logger.warn('Custom elements not supported, falling back to enhanced injector');
        }
        return null;
      }

      if (this.debugMode) {
        this.logger.info('✅ Custom elements supported', {
          duration: `${(performance.now() - stepStartTime).toFixed(2)}ms`
        });
      }

      // 动态加载高级注入配置 - 添加超时保护
      const configStepStartTime = performance.now();
      if (this.debugMode) {
        this.logger.info('🔧 Step 2: Loading advanced injection configuration...');
      }

      const configLoadPromise = import('../../config/advanced-injection-rules.json');
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Configuration import timeout after 3 seconds')), 3000);
      });

      const configModule = await Promise.race([configLoadPromise, timeoutPromise]) as any;
      const config = configModule.default;

      if (!config || !config.strategies || !Array.isArray(config.strategies)) {
        throw new Error('Invalid advanced injection configuration');
      }

      if (this.debugMode) {
        this.logger.info('✅ Configuration loaded successfully', {
          strategiesCount: config.strategies.length,
          hasSettings: !!config.settings,
          duration: `${(performance.now() - configStepStartTime).toFixed(2)}ms`
        });
      }

      // 创建高级注入器
      const injectorStepStartTime = performance.now();
      if (this.debugMode) {
        this.logger.info('🔧 Step 3: Creating AdvancedDOMInjector...');
      }

      if (this.debugMode) {
        this.logger.info('🔍 [DEBUG] Step 3a: Importing AdvancedDOMInjector...');
      }

      // Import AdvancedDOMInjector dynamically to catch import errors
      const { AdvancedDOMInjector } = await import('../../core/advanced-injector');

      if (this.debugMode) {
        this.logger.info('✅ [DEBUG] Step 3b: AdvancedDOMInjector imported successfully');
      }

      const advancedInjector = new AdvancedDOMInjector();

      if (this.debugMode) {
        this.logger.info('✅ AdvancedDOMInjector created', {
          duration: `${(performance.now() - injectorStepStartTime).toFixed(2)}ms`
        });
      }

      // 加载配置
      const configLoadStepStartTime = performance.now();
      if (this.debugMode) {
        this.logger.info('🔧 Step 4: Loading config into injector...');
      }

      advancedInjector.loadConfig(config);

      if (this.debugMode) {
        this.logger.info('✅ Config loaded into injector', {
          duration: `${(performance.now() - configLoadStepStartTime).toFixed(2)}ms`
        });
      }

      // 返回成功初始化的注入器
      if (this.debugMode) {
        const totalDuration = performance.now() - initStartTime;
        this.logger.info('🚀 Advanced injection system initialized successfully', {
          configLoaded: true,
          strategiesCount: config.strategies?.length || 0,
          customElementsSupported: true,
          injectorType: advancedInjector.constructor.name,
          totalInitDuration: `${totalDuration.toFixed(2)}ms`
        });
      }

      return advancedInjector;
    } catch (error) {
      if (this.debugMode) {
        this.logger.error('❌ Failed to initialize advanced injection system:', error);
        this.logger.warn('Falling back to enhanced injector');
      }
      // 返回 null 表示初始化失败
      return null;
    }
  }

  /**
   * 初始化智能注入系统
   * @private
   */
  private initializeSmartInjection(forceStrategy?: InjectionStrategy): void {
    try {
      // 验证配置文件
      const validation = this.configLoader.validateConfig();
      if (!validation.isValid) {
        if (this.debugMode) {
          this.logger.error('Invalid injection rules configuration:', validation.errors);
          this.logger.warn('Falling back to basic injector');
        }
        this.injector = new DOMInjector();
        this.isSmartInjectionEnabled = false;
        return;
      }

      if (validation.warnings.length > 0 && this.debugMode) {
        this.logger.warn('Configuration warnings:', validation.warnings);
      }

      // 加载规则并创建规则引擎
      const rules = this.configLoader.loadRules();
      this.ruleEngine = new InjectionRuleEngine(rules);

      // 创建增强注入器
      this.injector = new EnhancedDOMInjector(this.ruleEngine);

      if (this.debugMode) {
        const configInfo = this.configLoader.getVersionInfo();
        this.logger.info('Smart injection system initialized', {
          rulesCount: rules.length,
          enabledRules: rules.filter(r => r.enabled).length,
          configVersion: configInfo.version,
          configUpdated: configInfo.updatedAt,
          forceStrategy
        });
      }

    } catch (error) {
      if (this.debugMode) {
        this.logger.error('Failed to initialize smart injection system:', error);
        this.logger.warn('Falling back to basic injector');
      }
      this.injector = new DOMInjector();
      this.isSmartInjectionEnabled = false;
    }
  }

  /**
   * 构建注入选项
   * @private
   */
  private buildInjectionOptions(
    config: RenderConfig,
    additionalOptions: {
      format?: 'text' | 'html';
      originalLinks?: Array<{ href: string; text: string; attributes: Record<string, string> }>;
    } = {}
  ): InjectionOptions | EnhancedInjectionOptions | AdvancedInjectionOptions {
    try {
      if (this.debugMode) {
        this.logger.info('buildInjectionOptions: Starting', {
          configKeys: Object.keys(config),
          smartInjectionEnabled: this.isSmartInjectionEnabled
        });
      }

      const baseOptions = {
        language: config.language,
        enableAccessibility: config.enableAccessibility,
        debug: config.debug,
        allowProcessing: true,
        format: additionalOptions.format || 'text',
        originalLinks: additionalOptions.originalLinks || []
      };

      if (this.debugMode) {
        this.logger.info('buildInjectionOptions: Base options created', {
          baseOptions,
          willUseSmartInjection: this.isSmartInjectionEnabled
        });
      }

      // 如果启用高级注入，返回高级选项
      if (this.useAdvancedInjection) {
        const advancedOptions: AdvancedInjectionOptions = {
          language: baseOptions.language,
          debug: baseOptions.debug,
          allowProcessing: baseOptions.allowProcessing,
          customId: `lu-${Date.now().toString(36)}-${Math.random().toString(36).substr(2, 5)}`,
          format: baseOptions.format,
          originalLinks: baseOptions.originalLinks,
          enableAccessibility: baseOptions.enableAccessibility
        };

        if (this.debugMode) {
          this.logger.info('buildInjectionOptions: Advanced options created', {
            advancedOptions
          });
        }

        return advancedOptions;
      }

      // 如果启用智能注入，返回增强选项
      if (this.isSmartInjectionEnabled) {
        const enhancedOptions: EnhancedInjectionOptions = {
          ...baseOptions,
          useRuleEngine: true,
          forceStrategy: config.forceStrategy
        };

        if (this.debugMode) {
          this.logger.info('buildInjectionOptions: Enhanced options created', {
            enhancedOptions
          });
        }

        return enhancedOptions;
      }

      if (this.debugMode) {
        this.logger.info('buildInjectionOptions: Returning base options', {
          baseOptions
        });
      }

      return baseOptions;

    } catch (error) {
      if (this.debugMode) {
        this.logger.error('buildInjectionOptions: Exception caught', {
          error: (error as Error).message,
          stack: (error as Error).stack,
          config
        });
      }
      throw error;
    }
  }

  /**
   * 获取使用的注入策略
   * @private
   */
  private getUsedStrategy(result: InjectionResult | EnhancedInjectionResult | AdvancedInjectionResult): InjectionStrategy | undefined {
    if ('strategy' in result) {
      return (result as EnhancedInjectionResult | AdvancedInjectionResult).strategy;
    }
    return undefined;
  }

  /**
   * 创建错误结果
   * @private
   */
  private createErrorResult(element: HTMLElement, error: string, startTime: number): RenderResult {
    const duration = performance.now() - startTime;

    return {
      success: false,
      element,
      error,
      duration
    };
  }

  /**
   * 更新统计信息
   * @private
   */
  private updateStats(success: boolean, duration: number): void {
    this.stats.totalRenders++;
    this.stats.totalDuration += duration;

    if (success) {
      this.stats.successfulRenders++;
    } else {
      this.stats.failedRenders++;
    }
  }
}