import {
  RenderConfig,
  RenderResult,
  TranslationEventType
} from './types';
import { SimplifiedAdvancedDOMInjector } from '../../core/simplified-advanced-injector';
import { ConfigLoader } from '../../core/config-loader';
import { TranslationEventBus } from './event-bus';
import { StateManager } from './state-manager';
// debug 导入已移除，使用手动 console.log

/**
 * 🚀 可预加载配置的注入器接口
 */
interface PreloadableInjector {
  preloadConfig(): Promise<void>;
}

/**
 * 🚀 类型安全检查：检查对象是否实现了PreloadableInjector接口
 */
function isPreloadableInjector(obj: any): obj is PreloadableInjector {
  return obj && typeof obj.preloadConfig === 'function';
}

/**
 * DOM渲染器 - 简化版本
 * 
 * 核心职责：
 * - 协调翻译注入流程
 * - 管理配置和状态
 * - 提供统一的渲染接口
 * 
 * 设计原则：
 * - 单一职责：专注于渲染协调
 * - 依赖注入：可测试和可扩展
 * - 配置驱动：基于 advanced-injection-rules.json
 * 
 * 支持的策略：
 * - Block: 块级翻译（段落、标题等）
 * - Inline: 内联翻译（短文本、按钮等）
 * - Skip: 跳过翻译（代码、脚本等）
 */
export class DomRenderer {
  private injector: SimplifiedAdvancedDOMInjector;
  private configLoader: ConfigLoader;
  private eventBus?: TranslationEventBus;
  private stateManager?: StateManager;
  private debug: boolean;
  private stats = {
    totalRenders: 0,
    successfulRenders: 0,
    failedRenders: 0,
    totalDuration: 0
  };

  constructor(config: {
    debug?: boolean;
    enableSmartInjection?: boolean;
    enableAdvancedInjection?: boolean;
    configLoader?: ConfigLoader;
    stateManager?: StateManager;
    eventBus?: TranslationEventBus;
  } = {}) {
    const configLoader = config.configLoader || new ConfigLoader();
    const debug = config.debug || false;

    this.configLoader = configLoader;
    this.eventBus = config.eventBus;
    this.stateManager = config.stateManager;
    this.debug = debug;

    this.injector = new SimplifiedAdvancedDOMInjector(configLoader, debug);
    this.logDebug('DomRenderer initialized (simplified version)');
  }

  /**
   * 渲染翻译到指定元素
   */
  async render(
    element: HTMLElement,
    translation: string,
    config: RenderConfig
  ): Promise<RenderResult> {
    const startTime = performance.now();
    this.stats.totalRenders++;

    try {
      // 1. 验证输入
      this.validateInputs(element, translation);
      this.logDebug('Inputs validated', { element: element.tagName, translationLength: translation.length });

      // 2. 检查元素状态
      if (this.stateManager && !this.stateManager.isElementAvailable(element)) {
        // 🔧 增强调试：详细记录元素不可用的原因
        const elementInfo = {
          tagName: element.tagName,
          className: element.className,
          isConnected: element.isConnected,
          hasParent: !!element.parentElement,
          hasTranslatedAttr: element.getAttribute('data-lu-translated') === 'true',
          hasFailedAttr: element.getAttribute('data-lu-failed') === 'true',
          hasProcessingAttr: element.getAttribute('data-lu-processing') === 'true',
          hasWrapper: !!element.querySelector('.lu-wrapper, .lu-block, .lu-inline'),
          textPreview: element.textContent?.slice(0, 50)
        };

        this.logDebug('Element availability check failed', elementInfo);
        throw new Error(`Element is not available for rendering: ${JSON.stringify(elementInfo)}`);
      }

      // 3. 加载配置并执行注入
      const injectionResult = await this.injector.injectTranslation(
        element,
        translation,
        {
          language: config.language || 'zh-CN',
          format: config.format || 'text',
          enableAccessibility: config.enableAccessibility || false,
          useRuleEngine: true,
          debug: this.debug
        }
      );

      // 4. 创建渲染结果
      const duration = performance.now() - startTime;
      const renderResult = this.createRenderResult(injectionResult, element, duration);

      // 5. 更新状态和统计
      this.updateStats(renderResult.success, duration);
      this.updateStateManager(element, renderResult);
      this.publishEvent(renderResult);

      this.logDebug('Render completed', {
        success: renderResult.success,
        strategy: renderResult.strategy,
        duration: `${duration.toFixed(2)}ms`
      });

      return renderResult;

    } catch (error) {
      const duration = performance.now() - startTime;
      const renderResult = this.handleError(error as Error, element, duration);

      this.updateStats(false, duration);
      this.updateStateManager(element, renderResult);
      this.publishErrorEvent(error as Error, element);

      return renderResult;
    }
  }

  /**
   * 清除所有翻译
   */
  async clearAll(): Promise<void> {
    await this.injector.clearAllTranslations();
    this.logDebug('All translations cleared');
  }

  /**
   * 清除单个元素的翻译
   */
  async clear(element: HTMLElement): Promise<void> {
    this.injector.removeTranslation(element);
    this.logDebug('Translation cleared', { element: element.tagName });
  }

  /**
   * 获取渲染统计信息
   */
  getStats() {
    return {
      ...this.stats,
      averageDuration: this.stats.totalRenders > 0
        ? this.stats.totalDuration / this.stats.totalRenders
        : 0,
      successRate: this.stats.totalRenders > 0
        ? (this.stats.successfulRenders / this.stats.totalRenders) * 100
        : 0,
      injectorStats: this.injector.getStats()
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalRenders: 0,
      successfulRenders: 0,
      failedRenders: 0,
      totalDuration: 0
    };
    this.injector.resetStats();
    this.logDebug('Stats reset');
  }

  /**
   * 销毁渲染器
   */
  destroy(): void {
    this.resetStats();
    this.logDebug('DomRenderer destroyed');
  }

  /**
   * 检查高级注入是否准备就绪
   * (兼容性方法，简化版本中总是返回true)
   */
  isAdvancedInjectionReady(): boolean {
    return true;
  }

  /**
   * 获取初始化状态
   * (兼容性方法)
   */
  getInitializationStatus(): { state: string; ready: boolean } {
    return {
      state: 'ready',
      ready: true
    };
  }

  /**
   * 兼容性方法：获取智能注入报告
   */
  getSmartInjectionReport(): any {
    return {
      enabled: true,
      totalRules: 3,
      activeRules: 3,
      stats: this.getStats()
    };
  }


  /**
   * 兼容性方法：重新加载智能注入配置
   */
  async reloadSmartInjectionConfig(): Promise<void> {
    // 在简化版本中，配置是动态加载的，无需重新加载
    this.logDebug('Smart injection config reload requested (no-op in simplified version)');
  }

  /**
   * 兼容性方法：切换智能注入
   */
  toggleSmartInjection(enabled: boolean): void {
    this.logDebug('Smart injection toggle requested', { enabled });
    // 在简化版本中总是启用
  }

  /**
   * 🚀 预加载配置缓存 - 在批量渲染前调用以优化性能
   */
  async preloadConfig(): Promise<void> {
    try {
      if (isPreloadableInjector(this.injector)) {
        await this.injector.preloadConfig();
        this.logDebug('配置预加载完成');
      } else {
        this.logDebug('注入器不支持配置预加载，跳过');
      }
    } catch (error) {
      this.logDebug('配置预加载失败', { error: (error as Error).message });
      throw error;
    }
  }

  // === 私有方法 ===

  private validateInputs(element: HTMLElement, translation: string): void {
    if (!element || !(element instanceof HTMLElement)) {
      throw new Error('Invalid element provided');
    }
    if (!translation || typeof translation !== 'string') {
      throw new Error('Invalid translation provided');
    }
  }

  private createRenderResult(injectionResult: any, element: HTMLElement, duration: number): RenderResult {
    return {
      success: injectionResult.success,
      element,
      strategy: this.mapStrategy(injectionResult.strategy),
      error: injectionResult.error,
      duration
    };
  }

  private mapStrategy(strategy: string): any {
    // 映射策略名称以保持兼容性
    switch (strategy?.toLowerCase()) {
      case 'block':
        return 'BLOCK' as any;
      case 'inline':
        return 'INLINE' as any;
      case 'skip':
        return 'SKIP' as any;
      default:
        return 'BLOCK' as any;
    }
  }

  private updateStats(success: boolean, duration: number): void {
    if (success) {
      this.stats.successfulRenders++;
    } else {
      this.stats.failedRenders++;
    }
    this.stats.totalDuration += duration;
  }

  private updateStateManager(element: HTMLElement, result: RenderResult): void {
    if (!this.stateManager) return;

    if (result.success) {
      this.stateManager.markAsTranslated(element);
    } else {
      this.stateManager.markAsFailed(element, result.error);
    }
  }

  private publishEvent(result: RenderResult): void {
    if (!this.eventBus) return;

    this.eventBus.publish(
      TranslationEventType.RENDER_COMPLETED,
      result,
      'DomRenderer'
    );
  }

  private publishErrorEvent(error: Error, element: HTMLElement): void {
    if (!this.eventBus) return;

    this.eventBus.publish(
      TranslationEventType.ERROR_OCCURRED,
      {
        error,
        context: 'DomRenderer.render',
        element,
        fatal: false
      },
      'DomRenderer'
    );
  }

  private handleError(error: Error, element: HTMLElement, duration: number): RenderResult {
    this.logDebug('Render failed', {
      error: error.message,
      element: element.tagName,
      duration: `${duration.toFixed(2)}ms`
    });

    return {
      success: false,
      element,
      strategy: 'UNKNOWN' as any,
      error: error.message,
      duration
    };
  }

  private logDebug(message: string, data?: any): void {
    if (!this.debug) return;

    if (data) {
      console.log('🔧 [debug|DEBUG] ' + message, data);
    } else {
      console.log('🔧 [debug|DEBUG] ' + message);
    }
  }
}