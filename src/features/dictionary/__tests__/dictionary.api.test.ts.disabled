import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { DictionaryApi } from '../dictionary.api';
import { server } from '../../../test/setup';
import { http, HttpResponse } from 'msw';

describe('DictionaryApi', () => {
  let api: DictionaryApi;

  beforeEach(() => {
    api = new DictionaryApi();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  describe('基础API调用', () => {
    it('应该成功获取单词数据', async () => {
      const result = await api.getWord('hello');
      
      expect(result).toEqual({
        word: 'hello',
        phonetic: {
          us: '/həˈləʊ/',
          uk: '/həˈləʊ/'
        },
        explain: [
          {
            pos: 'noun',
            definitions: [
              {
                definition: 'A greeting or expression of goodwill.',
                chinese: '问候或善意的表达',
                chinese_short: '问候'
              }
            ]
          },
          {
            pos: 'verb',
            definitions: [
              {
                definition: 'To greet someone by saying hello.',
                chinese: '通过说你好来问候某人',
                chinese_short: '问候'
              }
            ]
          }
        ],
        theme: 'dark'
      });
    });

    it('应该正确处理fresh参数', async () => {
      const result = await api.getWord('hello', { fresh: true });
      
      expect(result).toBeDefined();
      expect(result.word).toBe('hello');
    });

    it('应该处理单词不存在的情况', async () => {
      const result = await api.getWord('nonexistent');
      
      expect(result).toBeNull();
    });
  });

  describe('错误处理', () => {
    it('应该处理服务器错误', async () => {
      server.use(
        http.get('http://localhost:3003/api/dictionary/en/servererror', () => {
          return HttpResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
          );
        })
      );

      const result = await api.getWord('servererror');
      expect(result).toBeNull();
    });

    it('应该处理网络超时', async () => {
      server.use(
        http.get('http://localhost:3003/api/dictionary/en/timeout', () => {
          return new Promise(() => {
            // 永远不resolve，模拟超时
          });
        })
      );

      const result = await api.getWord('timeout', { timeout: 1000 });
      expect(result).toBeNull();
    }, 10000); // 延长测试超时时间

    it('应该处理网络错误', async () => {
      server.use(
        http.get('http://localhost:3003/api/dictionary/en/networkerror', () => {
          return HttpResponse.error();
        })
      );

      const result = await api.getWord('networkerror');
      expect(result).toBeNull();
    });
  });

  describe('数据转换', () => {
    it('应该正确转换API响应为TooltipProps格式', async () => {
      const result = await api.getWord('test');
      
      expect(result).toEqual({
        word: 'test',
        phonetic: {
          us: '/tɛst/',
          uk: '/tɛst/'
        },
        explain: [
          {
            pos: 'noun',
            definitions: [
              {
                definition: 'A procedure intended to establish the quality, performance, or reliability of something.',
                chinese: '用于确定某事物质量、性能或可靠性的程序',
                chinese_short: '测试'
              }
            ]
          },
          {
            pos: 'verb',
            definitions: [
              {
                definition: 'To take measures to check the quality, performance, or reliability of something.',
                chinese: '采取措施检查某事物的质量、性能或可靠性',
                chinese_short: '测试'
              }
            ]
          }
        ],
        theme: 'dark'
      });
    });

    it('应该处理缺少音标的情况', async () => {
      server.use(
        http.get('http://localhost:3003/api/dictionary/en/nophonetic', () => {
          return HttpResponse.json({
            word: 'nophonetic',
            meanings: [
              {
                partOfSpeech: 'noun',
                definitions: [
                  {
                    definition: 'A word without phonetic information.',
                    example: 'This is an example.'
                  }
                ]
              }
            ]
          });
        })
      );

      const result = await api.getWord('nophonetic');
      
      expect(result).toEqual({
        word: 'nophonetic',
        phonetic: {
          us: '',
          uk: ''
        },
        explain: [
          {
            pos: 'noun',
            definitions: [
              {
                definition: 'A word without phonetic information.',
                chinese: '没有音标信息的单词',
                chinese_short: '单词'
              }
            ]
          }
        ],
        theme: 'dark'
      });
    });

    it('应该处理复杂的词性映射', async () => {
      server.use(
        http.get('http://localhost:3003/api/dictionary/en/complex', () => {
          return HttpResponse.json({
            word: 'complex',
            phonetic: '/kəmˈpleks/',
            meanings: [
              {
                partOfSpeech: 'adjective',
                definitions: [
                  {
                    definition: 'Consisting of many interconnected parts.',
                    example: 'A complex system.'
                  }
                ]
              },
              {
                partOfSpeech: 'noun',
                definitions: [
                  {
                    definition: 'A group of similar buildings.',
                    example: 'An apartment complex.'
                  }
                ]
              }
            ]
          });
        })
      );

      const result = await api.getWord('complex');
      
      expect(result.explain).toHaveLength(2);
      expect(result.explain[0].pos).toBe('adjective');
      expect(result.explain[1].pos).toBe('noun');
    });
  });

  describe('性能和缓存', () => {
    it('应该在合理时间内返回结果', async () => {
      const startTime = Date.now();
      const result = await api.getWord('hello');
      const endTime = Date.now();
      
      expect(result).toBeDefined();
      expect(endTime - startTime).toBeLessThan(2000); // 2秒内完成
    });

    it('应该支持并发请求', async () => {
      const promises = [
        api.getWord('hello'),
        api.getWord('test'),
        api.getWord('hello'), // 重复请求
      ];

      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(3);
      expect(results[0]).toBeDefined();
      expect(results[1]).toBeDefined();
      expect(results[2]).toBeDefined();
      // 第一个和第三个结果应该相同
      expect(results[0]).toEqual(results[2]);
    });
  });

  describe('API配置', () => {
    it('应该支持自定义API端点', () => {
      const customApi = new DictionaryApi({
        baseUrl: 'https://api.custom.com',
        timeout: 5000
      });

      expect(customApi).toBeDefined();
      // 由于是自定义端点，这里不实际测试网络请求
    });

    it('应该支持自定义超时设置', () => {
      const customApi = new DictionaryApi({
        timeout: 1000 // 1秒超时
      });

      expect(customApi).toBeDefined();
    });
  });
});