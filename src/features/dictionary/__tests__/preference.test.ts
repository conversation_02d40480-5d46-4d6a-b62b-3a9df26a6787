/**
 * 简化偏好系统测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { recordPreference, sortByPreference } from '../preference';

describe('Preference System', () => {
  let mockLocalStorage: { [key: string]: string } = {};

  beforeEach(() => {
    mockLocalStorage = {};
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn((key: string) => mockLocalStorage[key] || null),
        setItem: vi.fn((key: string, value: string) => {
          mockLocalStorage[key] = value;
        }),
      },
      writable: true,
    });
  });

  it('should record preferences correctly', () => {
    recordPreference('hello', 'noun', '问候');
    recordPreference('hello', 'noun', '问候');
    recordPreference('hello', 'verb', '招呼');

    const savedData = JSON.parse(mockLocalStorage['lucid-word-preferences']);
    expect(savedData).toEqual({
      hello: {
        'noun.问候': 2,
        'verb.招呼': 1
      }
    });
  });

  it('should sort definitions by preference', () => {
    // 设置偏好数据
    mockLocalStorage['lucid-word-preferences'] = JSON.stringify({
      hello: {
        'noun.欢迎': 3,
        'noun.问候': 1,
        'verb.招呼': 5
      }
    });

    const explain = [
      {
        pos: 'noun',
        definitions: [
          { definition: 'greeting', chinese: '问候', chinese_short: '问候' },
          { definition: 'welcome', chinese: '欢迎', chinese_short: '欢迎' }
        ]
      },
      {
        pos: 'verb',
        definitions: [
          { definition: 'to greet', chinese: '招呼', chinese_short: '招呼' }
        ]
      }
    ];

    const sorted = sortByPreference('hello', explain);

    // verb组应该排在前面（最高分5）
    expect(sorted[0].pos).toBe('verb');
    expect(sorted[1].pos).toBe('noun');

    // noun组内应该按偏好排序：欢迎(3) > 问候(1)
    expect(sorted[1].definitions[0].chinese_short).toBe('欢迎');
    expect(sorted[1].definitions[1].chinese_short).toBe('问候');
  });

  it('should handle empty or invalid data', () => {
    expect(sortByPreference('nonexistent', [])).toEqual([]);
    expect(sortByPreference('nonexistent', undefined as any)).toBeUndefined();
  });
});