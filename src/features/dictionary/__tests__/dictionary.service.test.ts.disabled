import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DictionaryService } from '../dictionary.service';
import { DictionaryStorage } from '../../../services/storage';
import { DictionaryApi } from '../dictionary.api';
import { TooltipProps } from '../../../components/Tooltip/Tooltip';

// Mock dependencies
vi.mock('../../../services/storage');
vi.mock('../dictionary.api');

describe('DictionaryService', () => {
  let service: DictionaryService;
  let mockStorage: any;
  let mockApi: any;

  const mockWordData: TooltipProps = {
    word: 'hello',
    phonetic: { us: '/həˈləʊ/', uk: '/həˈləʊ/' },
    explain: [{
      pos: 'noun',
      definitions: [{
        definition: 'A greeting or expression of goodwill.',
        chinese: '问候或善意的表达',
        chinese_short: '问候'
      }]
    }],
    theme: 'dark'
  };

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Mock storage
    mockStorage = {
      get: vi.fn(),
      set: vi.fn(),
      remove: vi.fn(),
      clear: vi.fn(),
      getStats: vi.fn().mockResolvedValue({ memoryCount: 0, storageEstimate: 0 }),
      cleanExpiredData: vi.fn()
    };
    
    // Mock API
    mockApi = {
      getWord: vi.fn(),
      getConfig: vi.fn().mockReturnValue({ baseUrl: 'http://localhost:3003' }),
      testConnection: vi.fn()
    };

    // Mock constructors
    (DictionaryStorage as any).mockImplementation(() => mockStorage);
    (DictionaryApi as any).mockImplementation(() => mockApi);

    service = new DictionaryService();
  });

  describe('基础功能', () => {
    it('应该能够获取单词数据', async () => {
      mockStorage.get.mockResolvedValue(null);
      mockApi.getWord.mockResolvedValue(mockWordData);
      mockStorage.set.mockResolvedValue();

      const result = await service.getWord('hello');

      expect(result).toEqual(mockWordData);
      expect(mockStorage.get).toHaveBeenCalledWith('hello');
      expect(mockApi.getWord).toHaveBeenCalledWith('hello', { fresh: false });
      expect(mockStorage.set).toHaveBeenCalledWith('hello', mockWordData);
    });

    it('应该从缓存返回数据', async () => {
      mockStorage.get.mockResolvedValue(mockWordData);

      const result = await service.getWord('hello');

      expect(result).toEqual(mockWordData);
      expect(mockStorage.get).toHaveBeenCalledWith('hello');
      expect(mockApi.getWord).not.toHaveBeenCalled();
      expect(mockStorage.set).not.toHaveBeenCalled();
    });

    it('应该处理单词不存在的情况', async () => {
      mockStorage.get.mockResolvedValue(null);
      mockApi.getWord.mockResolvedValue(null);

      const result = await service.getWord('nonexistent');

      expect(result).toBeNull();
      expect(mockStorage.get).toHaveBeenCalledWith('nonexistent');
      expect(mockApi.getWord).toHaveBeenCalledWith('nonexistent', { fresh: false });
      expect(mockStorage.set).not.toHaveBeenCalled();
    });
  });

  describe('缓存控制', () => {
    it('应该支持强制刷新', async () => {
      mockStorage.get.mockResolvedValue(mockWordData);
      mockApi.getWord.mockResolvedValue(mockWordData);
      mockStorage.set.mockResolvedValue();

      const result = await service.getWord('hello', { fresh: true });

      expect(result).toEqual(mockWordData);
      expect(mockStorage.get).not.toHaveBeenCalled();
      expect(mockApi.getWord).toHaveBeenCalledWith('hello', { fresh: true });
      expect(mockStorage.set).toHaveBeenCalledWith('hello', mockWordData);
    });

    it('应该支持跳过缓存', async () => {
      mockStorage.get.mockResolvedValue(mockWordData);
      mockApi.getWord.mockResolvedValue(mockWordData);

      const result = await service.getWord('hello', { skipCache: true });

      expect(result).toEqual(mockWordData);
      expect(mockStorage.get).not.toHaveBeenCalled();
      expect(mockApi.getWord).toHaveBeenCalledWith('hello', { fresh: false });
      expect(mockStorage.set).not.toHaveBeenCalled();
    });
  });

  describe('请求去重', () => {
    it('应该去重并发请求', async () => {
      mockStorage.get.mockResolvedValue(null);
      mockApi.getWord.mockResolvedValue(mockWordData);
      mockStorage.set.mockResolvedValue();

      const promises = [
        service.getWord('hello'),
        service.getWord('hello'),
        service.getWord('hello')
      ];

      const results = await Promise.all(promises);

      expect(results).toHaveLength(3);
      expect(results[0]).toEqual(mockWordData);
      expect(results[1]).toEqual(mockWordData);
      expect(results[2]).toEqual(mockWordData);
      
      // API应该只被调用一次
      expect(mockApi.getWord).toHaveBeenCalledTimes(1);
      expect(mockStorage.set).toHaveBeenCalledTimes(1);
    });

    it('应该处理不同单词的并发请求', async () => {
      mockStorage.get.mockResolvedValue(null);
      mockApi.getWord.mockImplementation((word) => 
        Promise.resolve({ ...mockWordData, word })
      );
      mockStorage.set.mockResolvedValue();

      const promises = [
        service.getWord('hello'),
        service.getWord('world'),
        service.getWord('test')
      ];

      const results = await Promise.all(promises);

      expect(results).toHaveLength(3);
      expect(results[0].word).toBe('hello');
      expect(results[1].word).toBe('world');
      expect(results[2].word).toBe('test');
      
      // 每个单词应该被调用一次
      expect(mockApi.getWord).toHaveBeenCalledTimes(3);
      expect(mockStorage.set).toHaveBeenCalledTimes(3);
    });
  });

  describe('错误处理', () => {
    it('应该处理存储错误', async () => {
      mockStorage.get.mockRejectedValue(new Error('Storage error'));
      mockApi.getWord.mockResolvedValue(mockWordData);
      mockStorage.set.mockResolvedValue();

      const result = await service.getWord('hello');

      expect(result).toEqual(mockWordData);
      expect(mockApi.getWord).toHaveBeenCalledWith('hello', { fresh: false });
      expect(mockStorage.set).toHaveBeenCalledWith('hello', mockWordData);
    });

    it('应该处理API错误', async () => {
      mockStorage.get.mockResolvedValue(null);
      mockApi.getWord.mockRejectedValue(new Error('API error'));

      const result = await service.getWord('hello');

      expect(result).toBeNull();
      expect(mockStorage.get).toHaveBeenCalledWith('hello');
      expect(mockApi.getWord).toHaveBeenCalledWith('hello', { fresh: false });
      expect(mockStorage.set).not.toHaveBeenCalled();
    });

    it('应该处理保存缓存错误', async () => {
      mockStorage.get.mockResolvedValue(null);
      mockApi.getWord.mockResolvedValue(mockWordData);
      mockStorage.set.mockRejectedValue(new Error('Save error'));

      const result = await service.getWord('hello');

      expect(result).toEqual(mockWordData);
      expect(mockApi.getWord).toHaveBeenCalledWith('hello', { fresh: false });
      expect(mockStorage.set).toHaveBeenCalledWith('hello', mockWordData);
    });
  });

  describe('批量操作', () => {
    it('应该支持批量获取单词', async () => {
      mockStorage.get.mockResolvedValue(null);
      mockApi.getWord.mockImplementation((word) => 
        Promise.resolve({ ...mockWordData, word })
      );
      mockStorage.set.mockResolvedValue();

      const words = ['hello', 'world', 'test'];
      const results = await service.getWords(words);

      expect(results).toHaveLength(3);
      expect(results[0].word).toBe('hello');
      expect(results[1].word).toBe('world');
      expect(results[2].word).toBe('test');
      
      expect(mockApi.getWord).toHaveBeenCalledTimes(3);
      expect(mockStorage.set).toHaveBeenCalledTimes(3);
    });

    it('应该处理批量操作中的错误', async () => {
      mockStorage.get.mockResolvedValue(null);
      mockApi.getWord.mockImplementation((word) => {
        if (word === 'error') {
          return Promise.reject(new Error('API error'));
        }
        return Promise.resolve({ ...mockWordData, word });
      });
      mockStorage.set.mockResolvedValue();

      const words = ['hello', 'error', 'world'];
      const results = await service.getWords(words);

      expect(results).toHaveLength(3);
      expect(results[0].word).toBe('hello');
      expect(results[1]).toBeNull();
      expect(results[2].word).toBe('world');
    });
  });

  describe('缓存管理', () => {
    it('应该能够清理缓存', async () => {
      mockStorage.clear.mockResolvedValue();

      await service.clearCache();

      expect(mockStorage.clear).toHaveBeenCalled();
    });

    it('应该能够清理过期数据', async () => {
      mockStorage.cleanExpiredData.mockResolvedValue();

      await service.cleanExpiredData();

      expect(mockStorage.cleanExpiredData).toHaveBeenCalled();
    });

    it('应该能够获取缓存统计', async () => {
      const stats = { memoryCount: 5, storageEstimate: 1024 };
      mockStorage.getStats.mockResolvedValue(stats);

      const result = await service.getCacheStats();

      expect(result).toEqual(stats);
      expect(mockStorage.getStats).toHaveBeenCalled();
    });
  });

  describe('配置管理', () => {
    it('应该能够获取配置信息', () => {
      const config = service.getConfig();

      expect(config).toBeDefined();
      expect(config.apiConfig).toBeDefined();
      expect(config.storageConfig).toBeDefined();
    });

    it('应该能够测试服务连接', async () => {
      mockApi.testConnection.mockResolvedValue(true);

      const result = await service.testConnection();

      expect(result).toBe(true);
      expect(mockApi.testConnection).toHaveBeenCalled();
    });
  });

  describe('性能监控', () => {
    it('应该记录性能指标', async () => {
      mockStorage.get.mockResolvedValue(null);
      mockApi.getWord.mockResolvedValue(mockWordData);
      mockStorage.set.mockResolvedValue();

      const result = await service.getWord('hello');

      expect(result).toEqual(mockWordData);
      
      const metrics = service.getMetrics();
      expect(metrics.totalRequests).toBe(1);
      expect(metrics.cacheHits).toBe(0);
      expect(metrics.cacheMisses).toBe(1);
    });

    it('应该记录缓存命中', async () => {
      mockStorage.get.mockResolvedValue(mockWordData);

      const result = await service.getWord('hello');

      expect(result).toEqual(mockWordData);
      
      const metrics = service.getMetrics();
      expect(metrics.totalRequests).toBe(1);
      expect(metrics.cacheHits).toBe(1);
      expect(metrics.cacheMisses).toBe(0);
    });
  });
});