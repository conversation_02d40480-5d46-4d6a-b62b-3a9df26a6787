import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useDictionary } from '../useDictionary';
import { dictionaryService } from '../dictionary.service';
import { TooltipProps } from '@tooltip/Tooltip';

// Mock dictionary service
vi.mock('../dictionary.service', () => ({
  dictionaryService: {
    getWord: vi.fn(),
  },
}));

const mockDictionaryService = dictionaryService as any;

describe('useDictionary Hook - Core Tests', () => {
  const mockTooltipData: TooltipProps = {
    word: 'test',
    explain: [
      {
        pos: 'noun',
        definitions: [
          {
            definition: 'A test definition',
            chinese: '测试定义',
            chinese_short: '测试',
          },
        ],
      },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockDictionaryService.getWord.mockResolvedValue(mockTooltipData);
  });

  it('should return correct initial state', () => {
    const { result } = renderHook(() => useDictionary(''));

    expect(result.current.data).toBe(null);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.isValidating).toBe(false);
  });

  it('should fetch data for valid word', async () => {
    const { result } = renderHook(() => useDictionary('test'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockTooltipData);
    expect(result.current.error).toBe(null);
    expect(mockDictionaryService.getWord).toHaveBeenCalledWith('test', {
      fresh: false,
      skipCache: false,
      timeout: 3000, // 更新为实际的默认值
    });
  });

  it('should not fetch data for empty word', () => {
    const { result } = renderHook(() => useDictionary(''));

    expect(result.current.loading).toBe(false);
    expect(result.current.data).toBe(null);
    expect(mockDictionaryService.getWord).not.toHaveBeenCalled();
  });

  it('should handle service errors', async () => {
    const error = new Error('Service error');
    mockDictionaryService.getWord.mockRejectedValue(error);

    const { result } = renderHook(() => useDictionary('test'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toEqual(error);
    expect(result.current.data).toBe(null);
  });

  it('should support enabled option', () => {
    const { result } = renderHook(() => 
      useDictionary('test', { enabled: false })
    );

    expect(result.current.loading).toBe(false);
    expect(result.current.data).toBe(null);
    expect(mockDictionaryService.getWord).not.toHaveBeenCalled();
  });

  it('should support fresh option', async () => {
    const { result } = renderHook(() => 
      useDictionary('test', { fresh: true })
    );

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(mockDictionaryService.getWord).toHaveBeenCalledWith('test', {
      fresh: true,
      skipCache: false,
      timeout: 3000, // 更新为实际的默认值
    });
  });
});