import { TooltipProps } from '@tooltip/Tooltip';

// Mock API response data matching the expected format
export const mockApiResponse = {
  word: 'hello',
  phonetic: '/həˈləʊ/',
  phonetics: [
    {
      text: '/həˈləʊ/',
      audio: 'https://api.dictionaryapi.dev/media/pronunciations/en/hello-us.mp3'
    }
  ],
  meanings: [
    {
      partOfSpeech: 'noun',
      definitions: [
        {
          definition: 'A greeting or expression of goodwill.',
          example: 'She gave me a warm hello.',
          synonyms: ['greeting', 'salutation']
        }
      ]
    },
    {
      partOfSpeech: 'verb',
      definitions: [
        {
          definition: 'To greet someone by saying hello.',
          example: 'I helloed to my neighbor.',
          synonyms: ['greet', 'salute']
        }
      ]
    }
  ]
};

// Expected transformed data for TooltipProps
export const mockTransformedData: TooltipProps = {
  word: 'hello',
  phonetic: {
    us: '/həˈləʊ/',
    uk: '/həˈləʊ/'
  },
  explain: [
    {
      pos: 'noun',
      definitions: [
        {
          definition: 'A greeting or expression of goodwill.',
          chinese: '问候或善意的表达',
          chinese_short: '问候'
        }
      ]
    },
    {
      pos: 'verb',
      definitions: [
        {
          definition: 'To greet someone by saying hello.',
          chinese: '通过说你好来问候某人',
          chinese_short: '问候'
        }
      ]
    }
  ],
  theme: 'dark'
};

// Error response mock
export const mockErrorResponse = {
  error: 'Word not found',
  status: 404
};

// Multiple test cases
export const testCases = {
  'hello': mockApiResponse,
  'test': {
    word: 'test',
    phonetic: '/tɛst/',
    phonetics: [
      {
        text: '/tɛst/',
        audio: 'https://api.dictionaryapi.dev/media/pronunciations/en/test-us.mp3'
      }
    ],
    meanings: [
      {
        partOfSpeech: 'noun',
        definitions: [
          {
            definition: 'A procedure intended to establish the quality, performance, or reliability of something.',
            example: 'The test results were positive.',
            synonyms: ['examination', 'trial']
          }
        ]
      },
      {
        partOfSpeech: 'verb',
        definitions: [
          {
            definition: 'To take measures to check the quality, performance, or reliability of something.',
            example: 'We need to test the new software.',
            synonyms: ['examine', 'try']
          }
        ]
      }
    ]
  }
};