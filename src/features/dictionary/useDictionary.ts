import { useState, useEffect, useRef, useCallback } from 'react';
import { TooltipProps } from '@tooltip/Tooltip';
import { dictionaryService } from './dictionary.service';
import { GetWordOptions } from './dictionary.service';

// Hook 选项接口
export interface UseDictionaryOptions {
  enabled?: boolean;           // 是否启用自动获取
  fresh?: boolean;            // 强制刷新缓存
  skipCache?: boolean;        // 跳过缓存
  timeout?: number;           // 请求超时
  retryCount?: number;        // 重试次数
  onSuccess?: (data: TooltipProps) => void;
  onError?: (error: Error) => void;
}

// Hook 返回值接口
export interface UseDictionaryReturn {
  data: TooltipProps | null;  // 完整的词典数据，包含 explain
  loading: boolean;
  error: Error | null;
  isValidating: boolean;
  refetch: () => Promise<void>;
  mutate: (data: TooltipProps) => void;
}

// 内部状态接口
interface DictionaryState {
  data: TooltipProps | null;
  loading: boolean;
  error: Error | null;
  isValidating: boolean;
}

// 默认选项
const defaultOptions: UseDictionaryOptions = {
  enabled: true,
  fresh: false,
  skipCache: false,
  timeout: 3000, // 缩短超时时间从10秒到3秒
  retryCount: 0  // 禁用重试，快速回退到mock数据
};

/**
 * 词典数据获取 Hook
 * 
 * @param word 要查询的单词
 * @param options 配置选项
 * @returns 词典数据和状态
 */
export const useDictionary = (
  word: string,
  options: UseDictionaryOptions = {}
): UseDictionaryReturn => {
  const opts = { ...defaultOptions, ...options };
  
  // Simple logger with prefix
  const log = (level: 'info' | 'warn' | 'error', message: string, ...args: any[]) => {
    const emoji = { info: '📖', warn: '⚠️', error: '❌' };
    console[level](`${emoji[level]} [useDictionary] ${message}`, ...args);
  };
  const [state, setState] = useState<DictionaryState>({
    data: null,
    loading: false,
    error: null,
    isValidating: false
  });

  // 用于取消请求的 AbortController
  const abortControllerRef = useRef<AbortController | null>(null);
  
  // 当前请求的单词，用于防止过时的响应
  const currentWordRef = useRef<string>('');
  
  // 重试计数
  const retryCountRef = useRef<number>(0);
  
  // 正在进行的请求，用于去重
  const pendingRequestRef = useRef<Promise<void> | null>(null);

  // 清理函数
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  // 获取数据的核心函数
  const fetchData = useCallback(async (
    targetWord: string,
    isRefetch: boolean = false
  ): Promise<void> => {
    if (!targetWord || !targetWord.trim()) {
      setState(prev => ({
        ...prev,
        data: null,
        loading: false,
        error: null,
        isValidating: false
      }));
      return;
    }

    // 如果有相同单词的请求正在进行，等待它完成
    if (!isRefetch && pendingRequestRef.current && currentWordRef.current === targetWord) {
      log('info', `Request for "${targetWord}" already in progress, waiting...`);
      return pendingRequestRef.current;
    }

    // 取消之前的请求
    cleanup();

    // 创建新的 AbortController
    abortControllerRef.current = new AbortController();
    currentWordRef.current = targetWord;

    // 设置加载状态
    setState(prev => ({
      ...prev,
      loading: !isRefetch,
      isValidating: isRefetch,
      error: null
    }));

    // 创建并记录当前请求
    const currentRequest = (async () => {
      try {
      // 构建请求选项
      const serviceOptions: GetWordOptions = {
        fresh: opts.fresh,
        skipCache: opts.skipCache,
        timeout: opts.timeout
      };

      // 调用词典服务获取数据
      const result = await dictionaryService.getWord(targetWord, serviceOptions);

      // 检查请求是否被取消或单词已变更
      if (abortControllerRef.current?.signal.aborted || 
          currentWordRef.current !== targetWord) {
        return;
      }

      // 更新状态
      setState(prev => ({
        ...prev,
        data: result,
        loading: false,
        isValidating: false,
        error: null
      }));

      // 重置重试计数
      retryCountRef.current = 0;

      // 调用成功回调
      if (result && opts.onSuccess) {
        opts.onSuccess(result);
      }

    } catch (error) {
      // 检查请求是否被取消
      if (abortControllerRef.current?.signal.aborted || 
          currentWordRef.current !== targetWord) {
        return;
      }

      const errorInstance = error instanceof Error ? error : new Error(String(error));

      // 处理重试逻辑
      if (retryCountRef.current < (opts.retryCount || 0)) {
        retryCountRef.current++;
        
        // 延迟后重试
        setTimeout(() => {
          if (currentWordRef.current === targetWord) {
            fetchData(targetWord, isRefetch);
          }
        }, Math.min(1000 * retryCountRef.current, 5000));
        
        return;
      }

      // 更新错误状态
      setState(prev => ({
        ...prev,
        data: null,
        loading: false,
        isValidating: false,
        error: errorInstance
      }));

      // 调用错误回调
      if (opts.onError) {
        opts.onError(errorInstance);
      }
    } finally {
      // 清理 pending request
      pendingRequestRef.current = null;
    }
    })();

    // 记录当前请求
    pendingRequestRef.current = currentRequest;
    
    // 等待请求完成
    await currentRequest;
  }, [opts, cleanup]);

  // 重新获取数据
  const refetch = useCallback(async (): Promise<void> => {
    if (word && word.trim()) {
      await fetchData(word, true);
    }
  }, [word, fetchData]);

  // 手动设置数据
  const mutate = useCallback((data: TooltipProps): void => {
    setState(prev => ({
      ...prev,
      data,
      loading: false,
      isValidating: false,
      error: null
    }));
  }, []);

  // 主要的数据获取效果
  useEffect(() => {
    if (opts.enabled && word && word.trim()) {
      fetchData(word);
    } else if (!opts.enabled) {
      // 如果禁用，清理状态
      setState({
        data: null,
        loading: false,
        error: null,
        isValidating: false
      });
    }
  }, [word, opts.enabled]);

  // 清理效果
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, []);

  // 返回 Hook 结果
  return {
    data: state.data,
    loading: state.loading,
    error: state.error,
    isValidating: state.isValidating,
    refetch,
    mutate
  };
};

// 导出默认 Hook 工厂函数
export const createUseDictionary = (
  defaultOptions: Partial<UseDictionaryOptions> = {}
) => {
  return (word: string, options: UseDictionaryOptions = {}) => {
    return useDictionary(word, { ...defaultOptions, ...options });
  };
};

// 预配置的 Hook 变体
export const useDictionaryFresh = (word: string, options: UseDictionaryOptions = {}) => {
  return useDictionary(word, { ...options, fresh: true });
};

export const useDictionaryNoCache = (word: string, options: UseDictionaryOptions = {}) => {
  return useDictionary(word, { ...options, skipCache: true });
};