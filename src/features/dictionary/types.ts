// TooltipProps import removed - not used in this types-only file

// 音标类型
export interface Phonetic {
  us?: string;
  uk?: string;
}

// API响应类型
export interface DictionaryApiResponse {
  word: string;
  phonetic?: string;
  phonetics?: Array<{
    text: string;
    audio?: string;
  }>;
  meanings: Array<{
    partOfSpeech: string;
    definitions: Array<{
      definition: string;
      chinese: string;
      chinese_short: string;
      example?: string;
      synonyms?: string[];
      antonyms?: string[];
    }>;
  }>;
}

// API配置
export interface DictionaryApiConfig {
  baseUrl?: string;
  timeout?: number;
  retryCount?: number;
  retryDelay?: number;
}

// API选项
export interface GetWordOptions {
  fresh?: boolean;
  timeout?: number;
}

// 错误类型
export class DictionaryApiError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'DictionaryApiError';
  }
}

// 词性映射表
export const PART_OF_SPEECH_MAP: Record<string, string> = {
  'noun': 'noun',
  'verb': 'verb',
  'adjective': 'adjective',
  'adverb': 'adverb',
  'pronoun': 'pronoun',
  'preposition': 'preposition',
  'conjunction': 'conjunction',
  'interjection': 'interjection',
  'article': 'article',
  'determiner': 'determiner',
  'numeral': 'numeral',
  'particle': 'particle'
};

// 词性简写映射
export const PART_OF_SPEECH_SHORT: Record<string, string> = {
  'noun': 'n.',
  'verb': 'v.',
  'adjective': 'adj.',
  'adverb': 'adv.',
  'pronoun': 'pron.',
  'preposition': 'prep.',
  'conjunction': 'conj.',
  'interjection': 'interj.',
  'article': 'art.',
  'determiner': 'det.',
  'numeral': 'num.',
  'particle': 'part.'
};

// 中文翻译函数接口
export interface ChineseTranslator {
  translate(definition: string, pos: string): Promise<{
    chinese: string;
    chinese_short: string;
  }>;
}

// 简单的中文翻译实现（用于测试）
export class SimpleChinese implements ChineseTranslator {
  private translations: Record<string, { chinese: string; chinese_short: string }> = {
    'A greeting or expression of goodwill.': {
      chinese: '问候或善意的表达',
      chinese_short: '问候'
    },
    'To greet someone by saying hello.': {
      chinese: '通过说你好来问候某人',
      chinese_short: '问候'
    },
    'A procedure intended to establish the quality, performance, or reliability of something.': {
      chinese: '用于确定某事物质量、性能或可靠性的程序',
      chinese_short: '测试'
    },
    'To take measures to check the quality, performance, or reliability of something.': {
      chinese: '采取措施检查某事物的质量、性能或可靠性',
      chinese_short: '测试'
    },
    'A word without phonetic information.': {
      chinese: '没有音标信息的单词',
      chinese_short: '单词'
    },
    'Consisting of many interconnected parts.': {
      chinese: '由许多相互关联的部分组成',
      chinese_short: '复杂的'
    },
    'A group of similar buildings.': {
      chinese: '一组相似的建筑物',
      chinese_short: '建筑群'
    }
  };

  async translate(definition: string, pos: string): Promise<{
    chinese: string;
    chinese_short: string;
  }> {
    const translation = this.translations[definition];
    if (translation) {
      return translation;
    }

    // 简单的fallback翻译
    const posTranslation = this.getPositionTranslation(pos);
    return {
      chinese: `${definition}（${posTranslation}）`,
      chinese_short: posTranslation
    };
  }

  private getPositionTranslation(pos: string): string {
    const posMap: Record<string, string> = {
      'noun': '名词',
      'verb': '动词',
      'adjective': '形容词',
      'adverb': '副词',
      'pronoun': '代词',
      'preposition': '介词',
      'conjunction': '连词',
      'interjection': '感叹词',
      'article': '冠词',
      'determiner': '限定词',
      'numeral': '数词',
      'particle': '小词'
    };
    return posMap[pos] || pos;
  }
}