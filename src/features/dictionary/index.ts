/**
 * 词典数据层统一导出
 * 
 * 这个模块提供了完整的词典数据管理功能，包括：
 * - API 数据获取
 * - 本地缓存管理
 * - 统一的服务接口
 * - 类型定义
 */

// 主要服务类
export { DictionaryService, dictionaryService } from './dictionary.service';
export { DictionaryApi, dictionaryApi } from './dictionary.api';

// 类型定义
export type {
  DictionaryApiResponse,
  DictionaryApiConfig,
  DictionaryApiError,
  ChineseTranslator
} from './types';

export type {
  DictionaryServiceConfig,
  GetWordOptions,
  ServiceMetrics,
  ServiceConfig
} from './dictionary.service';

// 导入已经导出的实例
import { dictionaryService, type GetWordOptions } from './dictionary.service';

// 存储相关（从 services 重新导出）
export { DictionaryStorage, dictionaryStorage } from '../../services/storage';

// 工具函数
export { 
  PART_OF_SPEECH_MAP, 
  PART_OF_SPEECH_SHORT,
  SimpleChinese 
} from './types';

// 使用示例和默认实例
export const dictionary = {
  // 默认服务实例 (lazy getter)
  get service() {
    return dictionaryService;
  },
  
  // 便捷方法
  async getWord(word: string, options?: GetWordOptions) {
    return await dictionaryService.getWord(word, options);
  },
  
  async getWords(words: string[], options?: GetWordOptions) {
    return await dictionaryService.getWords(words, options);
  },
  
  async clearCache() {
    return await dictionaryService.clearCache();
  },
  
  async getStatus() {
    return await dictionaryService.getServiceStatus();
  },
  
  getMetrics() {
    return dictionaryService.getMetrics();
  }
};

// Hook 相关导出
export { useDictionary, createUseDictionary, useDictionaryFresh, useDictionaryNoCache } from './useDictionary';
export type { UseDictionaryOptions, UseDictionaryReturn } from './useDictionary';

// 导出类型，供外部使用
export type { TooltipProps } from '../../components/Tooltip/Tooltip';