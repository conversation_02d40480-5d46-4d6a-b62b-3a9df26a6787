/**
 * 简化的用户偏好系统
 * 单文件实现，无复杂架构
 */

// 存储键
const STORAGE_KEY = 'lucid-word-preferences';

// 偏好数据结构
interface Preferences {
  [word: string]: {
    [key: string]: number; // "pos.chinese_short": count
  };
}

// 获取偏好数据
function getPreferences(): Preferences {
  try {
    const data = localStorage.getItem(STORAGE_KEY);
    return data ? JSON.parse(data) : {};
  } catch {
    return {};
  }
}

// 保存偏好数据
function savePreferences(prefs: Preferences): void {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(prefs));
  } catch {
    // 静默失败
  }
}

// 记录偏好
export function recordPreference(word: string, pos: string, chineseShort: string): void {
  const prefs = getPreferences();
  const key = `${pos}.${chineseShort}`;
  
  if (!prefs[word]) prefs[word] = {};
  prefs[word][key] = (prefs[word][key] || 0) + 1;
  
  console.log(`📊 Recording preference: ${word} -> ${key} = ${prefs[word][key]}`);
  console.log('📊 All preferences:', prefs);
  
  savePreferences(prefs);
}

// 根据偏好排序explain数据
export function sortByPreference(word: string, explain: any[]): any[] {
  if (!explain?.length) return explain;
  
  const prefs = getPreferences()[word] || {};
  console.log(`🔄 Sorting preferences for word: ${word}`, prefs);
  
  // 为每个组计算最高分数
  const groupScores = explain.map(group => {
    const maxScore = Math.max(
      ...group.definitions.map((def: any) => 
        prefs[`${group.pos}.${def.chinese_short}`] || 0
      )
    );
    return { ...group, maxScore };
  });
  
  // 组间排序：按最高分数排序
  groupScores.sort((a, b) => b.maxScore - a.maxScore);
  
  // 组内排序：按偏好分数排序
  const sorted = groupScores.map(group => ({
    ...group,
    definitions: group.definitions.slice().sort((a: any, b: any) => {
      const scoreA = prefs[`${group.pos}.${a.chinese_short}`] || 0;
      const scoreB = prefs[`${group.pos}.${b.chinese_short}`] || 0;
      return scoreB - scoreA;
    })
  }));
  
  console.log(`🔄 Sorted result:`, sorted);
  return sorted;
}