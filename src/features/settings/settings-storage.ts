/**
 * Settings Storage
 * 基于 browser.storage.sync 的设置存储，支持跨设备同步
 */

import { ExtensionSettings, SettingsSyncStatus } from './types';
import { DEFAULT_SETTINGS } from './default-settings';

const SETTINGS_KEY = 'lucid-extension-settings';
const SYNC_STATUS_KEY = 'lucid-sync-status';

export class SettingsStorage {
  private static instance: SettingsStorage | null = null;

  private constructor() {}

  static getInstance(): SettingsStorage {
    if (!SettingsStorage.instance) {
      SettingsStorage.instance = new SettingsStorage();
    }
    return SettingsStorage.instance;
  }

  /**
   * 获取设置数据
   */
  async getSettings(): Promise<ExtensionSettings> {
    try {
      // 优先使用 browser.storage.sync
      if (typeof browser !== 'undefined' && browser.storage && browser.storage.sync) {
        const result = await browser.storage.sync.get(SETTINGS_KEY);
        if (result[SETTINGS_KEY]) {
          return this.mergeWithDefaults(result[SETTINGS_KEY]);
        }
      }

      // 降级到 browser.storage.local
      if (typeof browser !== 'undefined' && browser.storage && browser.storage.local) {
        const result = await browser.storage.local.get(SETTINGS_KEY);
        if (result[SETTINGS_KEY]) {
          return this.mergeWithDefaults(result[SETTINGS_KEY]);
        }
      }

      // 最后降级到 localStorage
      const localData = localStorage.getItem(SETTINGS_KEY);
      if (localData) {
        const parsed = JSON.parse(localData);
        return this.mergeWithDefaults(parsed);
      }
    } catch (error) {
      console.warn('Failed to load settings:', error);
    }

    // 返回默认设置
    return { ...DEFAULT_SETTINGS };
  }

  /**
   * 保存设置数据
   */
  async saveSettings(settings: ExtensionSettings): Promise<void> {
    const settingsToSave = {
      ...settings,
      lastUpdated: Date.now(),
    };

    const errors: string[] = [];

    try {
      // 优先保存到 browser.storage.sync
      if (typeof browser !== 'undefined' && browser.storage && browser.storage.sync) {
        await browser.storage.sync.set({ [SETTINGS_KEY]: settingsToSave });
        await this.updateSyncStatus();
        return;
      }
    } catch (error) {
      errors.push(`Sync storage failed: ${error}`);
    }

    try {
      // 降级到 browser.storage.local
      if (typeof browser !== 'undefined' && browser.storage && browser.storage.local) {
        await browser.storage.local.set({ [SETTINGS_KEY]: settingsToSave });
        return;
      }
    } catch (error) {
      errors.push(`Local storage failed: ${error}`);
    }

    try {
      // 最后降级到 localStorage
      localStorage.setItem(SETTINGS_KEY, JSON.stringify(settingsToSave));
    } catch (error) {
      errors.push(`LocalStorage failed: ${error}`);
      throw new Error(`All storage methods failed: ${errors.join(', ')}`);
    }
  }

  /**
   * 获取特定设置值
   */
  async getSetting<K extends keyof ExtensionSettings>(
    key: K
  ): Promise<ExtensionSettings[K]> {
    const settings = await this.getSettings();
    return settings[key];
  }

  /**
   * 设置特定设置值
   */
  async setSetting<K extends keyof ExtensionSettings>(
    key: K,
    value: ExtensionSettings[K]
  ): Promise<void> {
    const currentSettings = await this.getSettings();
    const updatedSettings = {
      ...currentSettings,
      [key]: value,
    };
    await this.saveSettings(updatedSettings);
  }

  /**
   * 获取嵌套设置值
   */
  async getNestedSetting(path: string): Promise<any> {
    const settings = await this.getSettings();
    return this.getValueByPath(settings, path);
  }

  /**
   * 设置嵌套设置值
   */
  async setNestedSetting(path: string, value: any): Promise<void> {
    const currentSettings = await this.getSettings();
    const updatedSettings = this.setValueByPath(currentSettings, path, value);
    await this.saveSettings(updatedSettings);
  }

  /**
   * 重置为默认设置
   */
  async resetToDefaults(): Promise<void> {
    await this.saveSettings({ ...DEFAULT_SETTINGS });
  }

  /**
   * 导出设置
   */
  async exportSettings(): Promise<string> {
    const settings = await this.getSettings();
    return JSON.stringify(settings, null, 2);
  }

  /**
   * 导入设置
   */
  async importSettings(settingsJson: string): Promise<void> {
    try {
      const parsedSettings = JSON.parse(settingsJson);
      const validatedSettings = this.validateAndCleanSettings(parsedSettings);
      await this.saveSettings(validatedSettings);
    } catch (error) {
      throw new Error(`Failed to import settings: ${error}`);
    }
  }

  /**
   * 获取同步状态
   */
  async getSyncStatus(): Promise<SettingsSyncStatus> {
    try {
      if (typeof browser !== 'undefined' && browser.storage && browser.storage.local) {
        const result = await browser.storage.local.get(SYNC_STATUS_KEY);
        if (result[SYNC_STATUS_KEY]) {
          return result[SYNC_STATUS_KEY];
        }
      }
    } catch (error) {
      console.warn('Failed to get sync status:', error);
    }

    return {
      lastSyncTime: 0,
      syncInProgress: false,
      deviceId: this.generateDeviceId(),
    };
  }

  /**
   * 监听设置变化
   */
  onSettingsChanged(callback: (settings: ExtensionSettings) => void): () => void {
    const handleStorageChange = async (changes: any, areaName: string) => {
      if (areaName === 'sync' || areaName === 'local') {
        if (changes[SETTINGS_KEY]) {
          const newSettings = changes[SETTINGS_KEY].newValue;
          if (newSettings) {
            callback(this.mergeWithDefaults(newSettings));
          }
        }
      }
    };

    if (typeof browser !== 'undefined' && browser.storage && browser.storage.onChanged) {
      browser.storage.onChanged.addListener(handleStorageChange);
      return () => {
        browser.storage.onChanged.removeListener(handleStorageChange);
      };
    }

    // localStorage 降级处理
    const handleStorageEvent = async (event: StorageEvent) => {
      if (event.key === SETTINGS_KEY && event.newValue) {
        try {
          const newSettings = JSON.parse(event.newValue);
          callback(this.mergeWithDefaults(newSettings));
        } catch (error) {
          console.warn('Failed to parse localStorage settings:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageEvent);
    return () => {
      window.removeEventListener('storage', handleStorageEvent);
    };
  }

  // === 私有方法 ===

  private mergeWithDefaults(settings: Partial<ExtensionSettings>): ExtensionSettings {
    return {
      translation: { ...DEFAULT_SETTINGS.translation, ...settings.translation },
      appearance: { ...DEFAULT_SETTINGS.appearance, ...settings.appearance },
      behavior: { ...DEFAULT_SETTINGS.behavior, ...settings.behavior },
      account: { ...DEFAULT_SETTINGS.account, ...settings.account },
      version: settings.version || DEFAULT_SETTINGS.version,
      lastUpdated: settings.lastUpdated || Date.now(),
    };
  }

  private validateAndCleanSettings(settings: any): ExtensionSettings {
    // 基础验证和清理
    const cleaned = this.mergeWithDefaults(settings);
    
    // 验证关键字段
    if (typeof cleaned.behavior.minWordLength !== 'number' || cleaned.behavior.minWordLength < 1) {
      cleaned.behavior.minWordLength = DEFAULT_SETTINGS.behavior.minWordLength;
    }
    
    if (typeof cleaned.behavior.maxWordLength !== 'number' || cleaned.behavior.maxWordLength < 5) {
      cleaned.behavior.maxWordLength = DEFAULT_SETTINGS.behavior.maxWordLength;
    }

    return cleaned;
  }

  private getValueByPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private setValueByPath(obj: any, path: string, value: any): any {
    const keys = path.split('.');
    const lastKey = keys.pop();
    if (!lastKey) return obj;

    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key];
    }, obj);

    target[lastKey] = value;
    return obj;
  }

  private async updateSyncStatus(): Promise<void> {
    const status: SettingsSyncStatus = {
      lastSyncTime: Date.now(),
      syncInProgress: false,
      deviceId: this.generateDeviceId(),
    };

    try {
      if (typeof browser !== 'undefined' && browser.storage && browser.storage.local) {
        await browser.storage.local.set({ [SYNC_STATUS_KEY]: status });
      }
    } catch (error) {
      console.warn('Failed to update sync status:', error);
    }
  }

  private generateDeviceId(): string {
    // 简单的设备ID生成
    return `device-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 导出单例实例
export const settingsStorage = SettingsStorage.getInstance();