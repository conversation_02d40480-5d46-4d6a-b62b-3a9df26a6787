/**
 * Settings Manager
 * 设置管理核心逻辑，处理设置的读取、保存、验证和同步
 */

import { ExtensionSettings, SettingsUpdateEvent, SettingsValidationResult } from './types';
import { settingsStorage } from './settings-storage';
import { DEFAULT_SETTINGS, SETTINGS_VALIDATION_RULES } from './default-settings';

type SettingsChangeListener = (event: SettingsUpdateEvent) => void;

export class SettingsManager {
  private static instance: SettingsManager | null = null;
  private listeners: SettingsChangeListener[] = [];
  private currentSettings: ExtensionSettings = { ...DEFAULT_SETTINGS };
  private isInitialized = false;

  private constructor() {}

  static getInstance(): SettingsManager {
    if (!SettingsManager.instance) {
      SettingsManager.instance = new SettingsManager();
    }
    return SettingsManager.instance;
  }

  /**
   * 初始化设置管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 加载设置
      this.currentSettings = await settingsStorage.getSettings();
      
      // 监听存储变化
      settingsStorage.onSettingsChanged((settings) => {
        const oldSettings = { ...this.currentSettings };
        this.currentSettings = settings;
        
        // 发送变化事件
        this.notifyListeners({
          key: 'all',
          value: settings,
          oldValue: oldSettings,
        });
      });

      this.isInitialized = true;
      console.log('Settings Manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Settings Manager:', error);
      throw error;
    }
  }

  /**
   * 获取所有设置
   */
  getSettings(): ExtensionSettings {
    return { ...this.currentSettings };
  }

  /**
   * 获取特定设置
   */
  getSetting<K extends keyof ExtensionSettings>(key: K): ExtensionSettings[K] {
    return this.currentSettings[key];
  }

  /**
   * 获取嵌套设置值
   */
  getNestedSetting(path: string): any {
    return this.getValueByPath(this.currentSettings, path);
  }

  /**
   * 设置特定设置值
   */
  async setSetting<K extends keyof ExtensionSettings>(
    key: K,
    value: ExtensionSettings[K]
  ): Promise<void> {
    const oldValue = this.currentSettings[key];
    
    // 验证设置值
    const validationResult = this.validateSetting(key, value);
    if (!validationResult.isValid) {
      throw new Error(`Invalid setting value: ${validationResult.errors.join(', ')}`);
    }

    // 更新本地缓存
    this.currentSettings = {
      ...this.currentSettings,
      [key]: value,
    };

    try {
      // 保存到存储
      await settingsStorage.setSetting(key, value);
      
      // 通知监听器
      this.notifyListeners({
        key,
        value,
        oldValue,
      });
    } catch (error) {
      // 回滚本地更改
      this.currentSettings = {
        ...this.currentSettings,
        [key]: oldValue,
      };
      throw error;
    }
  }

  /**
   * 设置嵌套设置值
   */
  async setNestedSetting(path: string, value: any): Promise<void> {
    const oldValue = this.getValueByPath(this.currentSettings, path);
    
    // 验证设置值
    const validationResult = this.validateNestedSetting(path, value);
    if (!validationResult.isValid) {
      throw new Error(`Invalid setting value: ${validationResult.errors.join(', ')}`);
    }

    // 更新本地缓存
    const updatedSettings = { ...this.currentSettings };
    this.setValueByPath(updatedSettings, path, value);
    this.currentSettings = updatedSettings;

    try {
      // 保存到存储
      await settingsStorage.setNestedSetting(path, value);
      
      // 通知监听器
      this.notifyListeners({
        key: path,
        value,
        oldValue,
      });
    } catch (error) {
      // 回滚本地更改
      this.currentSettings = { ...this.currentSettings };
      this.setValueByPath(this.currentSettings, path, oldValue);
      throw error;
    }
  }

  /**
   * 批量更新设置
   */
  async updateSettings(updates: Partial<ExtensionSettings>): Promise<void> {
    const oldSettings = { ...this.currentSettings };
    const newSettings = { ...this.currentSettings, ...updates };

    // 验证所有更新
    for (const [key, value] of Object.entries(updates)) {
      const validationResult = this.validateSetting(key as keyof ExtensionSettings, value);
      if (!validationResult.isValid) {
        throw new Error(`Invalid setting ${key}: ${validationResult.errors.join(', ')}`);
      }
    }

    // 更新本地缓存
    this.currentSettings = newSettings;

    try {
      // 保存到存储
      await settingsStorage.saveSettings(newSettings);
      
      // 通知监听器
      for (const [key, value] of Object.entries(updates)) {
        this.notifyListeners({
          key: key as keyof ExtensionSettings,
          value,
          oldValue: oldSettings[key as keyof ExtensionSettings],
        });
      }
    } catch (error) {
      // 回滚本地更改
      this.currentSettings = oldSettings;
      throw error;
    }
  }

  /**
   * 重置为默认设置
   */
  async resetToDefaults(): Promise<void> {
    const oldSettings = { ...this.currentSettings };
    this.currentSettings = { ...DEFAULT_SETTINGS };

    try {
      await settingsStorage.resetToDefaults();
      
      this.notifyListeners({
        key: 'all',
        value: this.currentSettings,
        oldValue: oldSettings,
      });
    } catch (error) {
      this.currentSettings = oldSettings;
      throw error;
    }
  }

  /**
   * 导出设置
   */
  async exportSettings(): Promise<string> {
    return settingsStorage.exportSettings();
  }

  /**
   * 导入设置
   */
  async importSettings(settingsJson: string): Promise<void> {
    try {
      const parsedSettings = JSON.parse(settingsJson);
      
      // 验证导入的设置
      const validationResult = this.validateAllSettings(parsedSettings);
      if (!validationResult.isValid) {
        throw new Error(`Invalid settings: ${validationResult.errors.join(', ')}`);
      }

      const oldSettings = { ...this.currentSettings };
      
      await settingsStorage.importSettings(settingsJson);
      this.currentSettings = await settingsStorage.getSettings();
      
      this.notifyListeners({
        key: 'all',
        value: this.currentSettings,
        oldValue: oldSettings,
      });
    } catch (error) {
      throw new Error(`Failed to import settings: ${error}`);
    }
  }

  /**
   * 添加设置变化监听器
   */
  addListener(listener: SettingsChangeListener): () => void {
    this.listeners.push(listener);
    
    // 返回移除监听器的函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * 获取设置同步状态
   */
  async getSyncStatus() {
    return settingsStorage.getSyncStatus();
  }

  // === 私有方法 ===

  private validateSetting<K extends keyof ExtensionSettings>(
    key: K,
    value: ExtensionSettings[K]
  ): SettingsValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 基础类型检查
    if (value === null || value === undefined) {
      errors.push(`Setting ${String(key)} cannot be null or undefined`);
      return { isValid: false, errors, warnings };
    }

    // 特定验证规则
    if (key === 'behavior' && typeof value === 'object') {
      const behavior = value as ExtensionSettings['behavior'];
      
      if (behavior.minWordLength < 1 || behavior.minWordLength > 10) {
        errors.push('minWordLength must be between 1 and 10');
      }
      
      if (behavior.maxWordLength < 5 || behavior.maxWordLength > 50) {
        errors.push('maxWordLength must be between 5 and 50');
      }
      
      if (behavior.minWordLength >= behavior.maxWordLength) {
        errors.push('minWordLength must be less than maxWordLength');
      }
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  private validateNestedSetting(path: string, value: any): SettingsValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    const rule = SETTINGS_VALIDATION_RULES[path as keyof typeof SETTINGS_VALIDATION_RULES];
    if (rule) {
      if (rule.type === 'number') {
        if (typeof value !== 'number') {
          errors.push(`${path} must be a number`);
        } else {
          if ('min' in rule && value < rule.min) {
            errors.push(`${path} must be at least ${rule.min}`);
          }
          if ('max' in rule && value > rule.max) {
            errors.push(`${path} must be at most ${rule.max}`);
          }
        }
      } else if (rule.type === 'enum') {
        if (!(rule.options as unknown as any[]).includes(value)) {
          errors.push(`${path} must be one of: ${rule.options.join(', ')}`);
        }
      }
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  private validateAllSettings(settings: any): SettingsValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证所有主要设置
    for (const key of Object.keys(DEFAULT_SETTINGS)) {
      if (settings[key] !== undefined) {
        const result = this.validateSetting(key as keyof ExtensionSettings, settings[key]);
        errors.push(...result.errors);
        warnings.push(...result.warnings);
      }
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  private notifyListeners(event: SettingsUpdateEvent): void {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in settings change listener:', error);
      }
    });
  }

  private getValueByPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private setValueByPath(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop();
    if (!lastKey) return;

    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key];
    }, obj);

    target[lastKey] = value;
  }
}

// 导出单例实例
export const settingsManager = SettingsManager.getInstance();