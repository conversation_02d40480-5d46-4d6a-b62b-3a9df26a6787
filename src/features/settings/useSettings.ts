/**
 * useSettings Hook
 * React Hook for managing extension settings
 */

import { useState, useEffect, useCallback } from 'react';
import { ExtensionSettings, SettingsUpdateEvent } from './types';
import { settingsManager } from './settings-manager';

interface UseSettingsReturn {
  settings: ExtensionSettings;
  loading: boolean;
  error: string | null;
  updateSetting: <K extends keyof ExtensionSettings>(key: K, value: ExtensionSettings[K]) => Promise<void>;
  updateNestedSetting: (path: string, value: any) => Promise<void>;
  updateSettings: (updates: Partial<ExtensionSettings>) => Promise<void>;
  resetToDefaults: () => Promise<void>;
  exportSettings: () => Promise<string>;
  importSettings: (settingsJson: string) => Promise<void>;
  refreshSettings: () => Promise<void>;
}

export function useSettings(): UseSettingsReturn {
  const [settings, setSettings] = useState<ExtensionSettings>(settingsManager.getSettings());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 初始化设置管理器
  useEffect(() => {
    const initializeSettings = async () => {
      try {
        setLoading(true);
        setError(null);
        
        await settingsManager.initialize();
        setSettings(settingsManager.getSettings());
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize settings');
        console.error('Settings initialization error:', err);
      } finally {
        setLoading(false);
      }
    };

    initializeSettings();
  }, []);

  // 监听设置变化
  useEffect(() => {
    const removeListener = settingsManager.addListener((event: SettingsUpdateEvent) => {
      if (event.key === 'all') {
        setSettings(event.value as ExtensionSettings);
      } else {
        setSettings(settingsManager.getSettings());
      }
    });

    return removeListener;
  }, []);

  // 更新单个设置
  const updateSetting = useCallback(async <K extends keyof ExtensionSettings>(
    key: K,
    value: ExtensionSettings[K]
  ) => {
    try {
      setError(null);
      await settingsManager.setSetting(key, value);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update setting';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  // 更新嵌套设置
  const updateNestedSetting = useCallback(async (path: string, value: any) => {
    try {
      setError(null);
      await settingsManager.setNestedSetting(path, value);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update nested setting';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  // 批量更新设置
  const updateSettings = useCallback(async (updates: Partial<ExtensionSettings>) => {
    try {
      setError(null);
      await settingsManager.updateSettings(updates);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update settings';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  // 重置为默认设置
  const resetToDefaults = useCallback(async () => {
    try {
      setError(null);
      await settingsManager.resetToDefaults();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reset settings';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  // 导出设置
  const exportSettings = useCallback(async (): Promise<string> => {
    try {
      setError(null);
      return await settingsManager.exportSettings();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export settings';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  // 导入设置
  const importSettings = useCallback(async (settingsJson: string) => {
    try {
      setError(null);
      await settingsManager.importSettings(settingsJson);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to import settings';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  // 刷新设置
  const refreshSettings = useCallback(async () => {
    try {
      setError(null);
      setLoading(true);
      
      // 重新初始化以获取最新设置
      await settingsManager.initialize();
      setSettings(settingsManager.getSettings());
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh settings';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    settings,
    loading,
    error,
    updateSetting,
    updateNestedSetting,
    updateSettings,
    resetToDefaults,
    exportSettings,
    importSettings,
    refreshSettings,
  };
}

/**
 * 获取特定设置的 Hook
 */
export function useSetting<K extends keyof ExtensionSettings>(
  key: K
): [ExtensionSettings[K], (value: ExtensionSettings[K]) => Promise<void>] {
  const { settings, updateSetting } = useSettings();
  
  const setValue = useCallback(async (value: ExtensionSettings[K]) => {
    await updateSetting(key, value);
  }, [key, updateSetting]);

  return [settings[key], setValue];
}

/**
 * 获取嵌套设置的 Hook
 */
export function useNestedSetting<T = any>(
  path: string,
  defaultValue?: T
): [T, (value: T) => Promise<void>] {
  const { settings, updateNestedSetting } = useSettings();
  
  const getValue = useCallback((): T => {
    const keys = path.split('.');
    let current: any = settings;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return defaultValue as T;
      }
    }
    
    return current as T;
  }, [settings, path, defaultValue]);

  const setValue = useCallback(async (value: T) => {
    await updateNestedSetting(path, value);
  }, [path, updateNestedSetting]);

  return [getValue(), setValue];
}