/**
 * Settings Types
 * 扩展设置数据类型定义
 */

// 翻译设置
// 翻译设置
export interface TranslationSettings {
  engine: 'google' | 'deepl' | 'baidu';
  immersiveMode: 'side-by-side' | 'replace' | 'popup';
  hoverTranslate: boolean;
  hoverModifier: 'alt' | 'ctrl' | 'shift' | 'none';
  sourceLanguage: string;
  targetLanguage: string;
  autoTranslation: boolean; // 👈 新增自动翻译设置
}

// 外观设置
export interface AppearanceSettings {
  theme: 'auto' | 'dark' | 'light';
  fontSize: 'small' | 'medium' | 'large';
  language: 'zh-CN' | 'en-US';
}

// 行为设置
export interface BehaviorSettings {
  autoHighlight: boolean;
  minWordLength: number;
  maxWordLength: number;
  showDelay: number;
  hideDelay: number;
  interactive: boolean;
  shortcuts: Record<string, string>;
}

// 账户设置
export interface AccountSettings {
  email: string;
  username: string;
  membershipType: 'free' | 'early-bird' | 'premium';
  expireDate: string;
  isVerified: boolean;
}

// 主扩展设置
export interface ExtensionSettings {
  translation: TranslationSettings;
  appearance: AppearanceSettings;
  behavior: BehaviorSettings;
  account: AccountSettings;
  version: string;
  lastUpdated: number;
}

// 设置更新事件
export interface SettingsUpdateEvent {
  key: keyof ExtensionSettings | string;
  value: any;
  oldValue: any;
}

// 设置验证结果
export interface SettingsValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 设置同步状态
export interface SettingsSyncStatus {
  lastSyncTime: number;
  syncInProgress: boolean;
  syncError?: string;
  deviceId: string;
}