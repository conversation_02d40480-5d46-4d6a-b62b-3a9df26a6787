/**
 * Default Settings
 * 默认配置值
 */

import { ExtensionSettings } from './types';

export const DEFAULT_SETTINGS: ExtensionSettings = {
  translation: {
    engine: 'google',
    immersiveMode: 'side-by-side',
    hoverTranslate: true,
    hoverModifier: 'alt',
    sourceLanguage: 'en',
    targetLanguage: 'zh-CN',
    autoTranslation: false, // 👈 默认关闭自动翻译
  },
  appearance: {
    theme: 'auto',
    fontSize: 'medium',
    language: 'zh-CN',
  },
  behavior: {
    autoHighlight: true,
    minWordLength: 2,
    maxWordLength: 30,
    showDelay: 50,
    hideDelay: 500,
    interactive: true,
    shortcuts: {
      'toggle-slider': 'Ctrl+Shift+S',
      'clear-highlights': 'Ctrl+Shift+C',
      'close-tooltip': 'Escape',
    },
  },
  account: {
    email: '',
    username: '',
    membershipType: 'free',
    expireDate: '',
    isVerified: false,
  },
  version: '1.0.0',
  lastUpdated: Date.now(),
};

// 设置字段的验证规则
export const SETTINGS_VALIDATION_RULES = {
  'behavior.minWordLength': {
    min: 1,
    max: 10,
    type: 'number',
  },
  'behavior.maxWordLength': {
    min: 5,
    max: 50,
    type: 'number',
  },
  'behavior.showDelay': {
    min: 0,
    max: 5000,
    type: 'number',
  },
  'behavior.hideDelay': {
    min: 100,
    max: 10000,
    type: 'number',
  },
  'translation.engine': {
    options: ['google', 'deepl', 'baidu'],
    type: 'enum',
  },
  'appearance.theme': {
    options: ['auto', 'dark', 'light'],
    type: 'enum',
  },
  'translation.autoTranslation': {
    type: 'boolean',
  },
} as const;