/**
 * 字幕翻译功能核心类型定义
 * 
 * 定义了字幕翻译系统中使用的所有接口、枚举和类型
 * 遵循 Lucid Extension 的类型安全和模块化架构原则
 */

// ===== 基础枚举类型 =====

/**
 * 支持的视频平台
 */
export enum SupportedPlatform {
  YOUTUBE = 'youtube',
  NETFLIX = 'netflix',
  PRIME_VIDEO = 'prime_video',
  DISNEY_PLUS = 'disney_plus',
  GENERIC = 'generic'
}

/**
 * 字幕格式
 */
export enum SubtitleFormat {
  VTT = 'vtt',
  SRT = 'srt', 
  YOUTUBE_JSON = 'youtube_json',
  ASS = 'ass',
  UNKNOWN = 'unknown'
}

/**
 * 字幕错误类型
 */
export enum SubtitleErrorType {
  NETWORK_INTERCEPTION_FAILED = 'network_interception_failed',
  UNSUPPORTED_FORMAT = 'unsupported_format',
  PARSING_ERROR = 'parsing_error',
  TRANSLATION_FAILED = 'translation_failed',
  RENDERING_ERROR = 'rendering_error',
  CACHE_ERROR = 'cache_error',
  CONFIG_ERROR = 'config_error'
}

/**
 * 字幕管理器状态
 */
export type SubtitleManagerStatus = 'idle' | 'initializing' | 'ready' | 'running' | 'error' | 'stopped';

/**
 * 翻译进度状态
 */
export type TranslationStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'not_found';

/**
 * 拦截状态
 */
export type InterceptionStatus = 'inactive' | 'starting' | 'active' | 'stopping' | 'error';

// ===== 核心数据结构 =====

/**
 * 字幕位置信息
 */
export interface SubtitlePosition {
  x: number;
  y: number;
  align: 'left' | 'center' | 'right';
  vertical: 'top' | 'middle' | 'bottom';
}

/**
 * 字幕样式配置
 */
export interface SubtitleStyle {
  fontSize: string;
  fontFamily: string;
  color: string;
  backgroundColor?: string;
  borderColor?: string;
  fontWeight?: 'normal' | 'bold';
  textShadow?: string;
}

/**
 * 标准化字幕条目
 */
export interface StandardSubtitle {
  id: string;
  startTime: number;  // 毫秒
  endTime: number;    // 毫秒
  text: string;       // 原始文本
  translatedText?: string; // 翻译文本
  confidence?: number;     // 置信度 (0-1)
  position?: SubtitlePosition;
  style?: SubtitleStyle;
}

/**
 * 字幕数据原始信息
 */
export interface SubtitleData {
  platform: SupportedPlatform;
  format: SubtitleFormat;
  rawData: string;
  url: string;  
  timestamp: number;
  videoId?: string;
}

/**
 * 显示配置
 */
export interface DisplayConfig {
  position: SubtitlePosition;
  style: SubtitleStyle;
  showDuration: number;
  fadeInDuration: number;
  fadeOutDuration: number;
  maxWidth: string;
  zIndex: number;
  showOriginal?: boolean;
  showTranslated?: boolean;
}

/**
 * 字幕翻译配置
 */
export interface SubtitleTranslationConfig {
  enabled: boolean;
  sourceLang: string;
  targetLang: string;
  showOriginal: boolean;
  showTranslated: boolean;
  batchSize: number;
  maxConcurrency: number;
  cacheEnabled: boolean;
  retryCount: number;
  display?: DisplayConfig;
  platforms?: Record<SupportedPlatform, PlatformConfig>;
}

/**
 * 平台特定配置
 */
export interface PlatformConfig {
  interceptUrl: RegExp;
  enabled: boolean;
  customHeaders?: Record<string, string>;
  timeout?: number;
}

// ===== 拦截系统接口 =====

/**
 * 拦截规则
 */
export interface InterceptRule {
  urlPattern: RegExp;
  method: 'GET' | 'POST';
  priority: number;
  extractSubtitleData: (response: Response) => Promise<SubtitleData>;
}

/**
 * 拦截器配置
 */
export interface InterceptorConfig {
  enableSmartFiltering: boolean;
  maxConcurrentRequests: number;
  cachePatterns: boolean;
  timeout?: number;
}

/**
 * 网络拦截器接口
 */
export interface ISubtitleNetworkInterceptor {
  registerInterceptRule(platform: SupportedPlatform, rule: InterceptRule): void;
  startInterception(): Promise<void>;
  stopInterception(): void;
  getInterceptionStatus(): InterceptionStatus;
  setCaptureCallback(callback: (data: SubtitleData) => void): void;
}

// ===== 解析系统接口 =====

/**
 * 解析验证结果
 */
export interface ValidationResult {
  valid: boolean;
  errors: string[];
}

/**
 * 字幕解析器接口
 */
export interface ISubtitleParser {
  parse(data: string): Promise<StandardSubtitle[]>;
  detectFormat?(data: string): SubtitleFormat;
  validate?(subtitles: StandardSubtitle[]): ValidationResult;
}

// ===== 翻译系统接口 =====

/**
 * 翻译选项
 */
export interface SubtitleTranslateOptions {
  sourceLang?: string;
  targetLang?: string;
  batchSize?: number;
  retryCount?: number;
}

/**
 * 翻译进度
 */
export interface TranslationProgress {
  taskId: string;
  total: number;
  completed: number;
  failed: number;
  status: TranslationStatus;
  error?: string;
  startTime?: number;
  endTime?: number;
}

/**
 * 字幕翻译管理器接口
 */
export interface ISubtitleTranslationManager {
  translateSubtitles(
    subtitles: StandardSubtitle[], 
    options: SubtitleTranslateOptions
  ): Promise<StandardSubtitle[]>;
  setTranslationConfig(config: SubtitleTranslationConfig): void;
  getTranslationProgress(taskId?: string): TranslationProgress | TranslationProgress[];
  cancelTranslation(taskId: string): void;
}

// ===== 渲染系统接口 =====

/**
 * 字幕覆盖组件接口
 */
export interface ISubtitleOverlay {
  showSubtitle(subtitle: StandardSubtitle, config: DisplayConfig): void;
  hideSubtitle(): void;
  updateDisplayConfig(config: DisplayConfig): void;
  setVideoContainer(container: HTMLElement): void;
}

// ===== 主管理器接口 =====

/**
 * 字幕管理器接口
 */
export interface ISubtitleManager {
  initialize(): Promise<void>;
  start(): Promise<void>;
  stop(): void;
  updateConfig(config: SubtitleTranslationConfig): void;
  getStatus(): SubtitleManagerStatus;
  destroy(): void;
}

// ===== 错误处理 =====

/**
 * 字幕错误类
 */
export class SubtitleError extends Error {
  constructor(
    public type: SubtitleErrorType,
    public message: string,
    public originalError?: Error,
    public context?: any
  ) {
    super(message);
    this.name = 'SubtitleError';
  }
}

// ===== 工具类型 =====

/**
 * 事件监听器类型
 */
export type SubtitleEventListener<T = any> = (data: T) => void;

/**
 * 事件类型映射
 */
export interface SubtitleEventMap {
  'subtitle-captured': SubtitleData;
  'subtitle-parsed': StandardSubtitle[];
  'subtitle-translated': StandardSubtitle[];
  'subtitle-rendered': StandardSubtitle;
  'translation-progress': TranslationProgress;
  'error': SubtitleError;
}

/**
 * 缓存键生成器类型
 */
export type CacheKeyGenerator = (text: string, sourceLang: string, targetLang: string) => string;

/**
 * 性能监控数据
 */
export interface PerformanceMetrics {
  interceptorLatency: number;
  parseLatency: number;
  translationLatency: number;
  renderLatency: number;
  memoryUsage: number;
  cacheHitRate: number;
}