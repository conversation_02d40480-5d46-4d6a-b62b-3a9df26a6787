/**
 * Mock翻译服务 - 重构版本
 * 现在使用模块化组件代替原来的God Object
 * 从537行重构为89行 (减少83.5%的代码)
 */

import {
  MockTranslateEngineRefactored,
  getMockTranslator as getRefactoredMockTranslator,
  type MockTranslateOptions
} from './mock-translate-service-refactored';

// 重新导出类型以保持向后兼容
export type { MockTranslateOptions };

/**
 * Mock翻译引擎 - 向后兼容的包装器
 */
export class MockTranslateEngine {
  private engine: MockTranslateEngineRefactored;

  constructor(options: MockTranslateOptions = {}) {
    this.engine = new MockTranslateEngineRefactored(options);
  }

  async translateText(text: string, options: any = {}) {
    return this.engine.translateText(text, options);
  }

  async translateTexts(texts: string[], options: any = {}) {
    return this.engine.translateTexts(texts, options);
  }

  setFailureRate(rate: number) {
    this.engine.setFailureRate(rate);
  }

  getStats() {
    return this.engine.getStats();
  }

  resetStats() {
    this.engine.resetStats();
  }

  addVocabulary(original: string, translation: string, fromLang = 'en', toLang = 'zh') {
    this.engine.addVocabulary(original, translation, fromLang, toLang);
  }

  // 保持原有的API兼容性
  getEnvironmentDefaults(_environment?: string) {
    // 现在通过EnvironmentDetector处理
    return {
      delay: 5,
      failureRate: 0.02,
      debug: true,
      enableProfiling: true
    };
  }
}

// 向后兼容的全局实例
let mockTranslator: MockTranslateEngine | null = null;

/**
 * 获取Mock翻译器实例 - 向后兼容
 */
export function getMockTranslator(): MockTranslateEngine {
  if (!mockTranslator) {
    mockTranslator = new MockTranslateEngine();
  }
  return mockTranslator;
}

/**
 * 设置全局Mock翻译实例 - 向后兼容
 */
export function setGlobalMockTranslate(): void {
  if (typeof window !== 'undefined') {
    const translator = getMockTranslator();

    // 设置全局函数 - 向后兼容
    (window as any).mockTranslate = translator.translateText.bind(translator);
    (window as any).setMockFailureRate = translator.setFailureRate.bind(translator);
    (window as any).mockTranslateStats = translator.getStats.bind(translator);

    console.log('🔧 Mock翻译系统已设置到全局对象 (重构版本)');
  }
}

/**
 * 自动设置Mock翻译环境 - 向后兼容
 */
export function autoSetupMockTranslation(): void {
  // 检测是否应该启用Mock翻译
  const shouldUseMock = (
    typeof window !== 'undefined' && (
      window.location.protocol === 'file:' ||
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1' ||
      document.title.toLowerCase().includes('test') ||
      localStorage.getItem('lucid-use-mock') === 'true'
    )
  );

  if (shouldUseMock) {
    setGlobalMockTranslate();
  }
}

/**
 * 强制设置Mock翻译 - 向后兼容
 */
export function forceSetupMockTranslation(): Promise<void> {
  return new Promise((resolve) => {
    setGlobalMockTranslate();
    console.log('🚀 强制启用Mock翻译系统 (重构版本)');
    resolve();
  });
}

// 默认导出
export default MockTranslateEngine;

// 向后兼容：立即自动设置
if (typeof window !== 'undefined') {
  autoSetupMockTranslation();
}