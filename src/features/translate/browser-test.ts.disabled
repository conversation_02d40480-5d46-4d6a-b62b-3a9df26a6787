/**
 * 浏览器控制台翻译测试工具
 * 可以直接在浏览器开发者工具控制台中使用
 */

import { translateService } from './index';

// 全局测试函数
export async function testTranslation(text: string = 'Hello, World!', options: any = {}) {
  console.log('🚀 开始翻译测试');
  console.log('📝 原文:', text);
  console.log('⚙️ 选项:', options);
  
  try {
    const startTime = Date.now();
    const result = await translateService.translateText(text, {
      from: 'auto',
      to: 'zh',
      ...options
    });
    const endTime = Date.now();
    
    console.log('✅ 翻译成功!');
    console.log('🌟 译文:', result.translations[0]);
    console.log('🔧 引擎:', result.engine);
    console.log('🎯 API:', result.api);
    console.log('⏱️ 耗时:', endTime - startTime, 'ms');
    console.log('📊 详细结果:', result);
    
    return result;
  } catch (error) {
    console.error('❌ 翻译失败:', error);
    console.error('🔍 错误详情:', {
      message: error.message,
      type: error.type,
      engine: error.engine
    });
    throw error;
  }
}

export async function testBatchTranslation(texts: string[] = ['Hello', 'World', 'Good morning']) {
  console.log('🚀 开始批量翻译测试');
  console.log('📝 原文列表:', texts);
  
  try {
    const startTime = Date.now();
    const result = await translateService.translateTexts(texts, {
      from: 'auto',
      to: 'zh'
    });
    const endTime = Date.now();
    
    console.log('✅ 批量翻译成功!');
    console.log('📋 翻译对照表:');
    texts.forEach((text, index) => {
      console.log(`  ${index + 1}. "${text}" → "${result.translations[index]}"`);
    });
    
    console.log('📊 统计信息:');
    console.log(`  耗时: ${endTime - startTime}ms`);
    console.log(`  引擎: ${result.engine}`);
    
    return result;
  } catch (error) {
    console.error('❌ 批量翻译失败:', error);
    throw error;
  }
}

export async function showEngineStatus() {
  console.log('🔍 引擎状态检查');
  
  const engines = translateService.getAvailableEngines();
  console.log('可用引擎:', engines);
  
  const recommended = translateService.getRecommendedEngine();
  console.log('推荐引擎:', recommended);
  
  const stats = translateService.getServiceStats();
  console.log('服务统计:', stats);
  
  return { engines, recommended, stats };
}

// 导出到全局对象
if (typeof window !== 'undefined') {
  // 确保全局对象可以访问
  Object.assign(window, {
    testTranslation,
    testBatchTranslation, 
    showEngineStatus,
    translateService
  });
  
  // 也添加到console.log建议的方式
  (window as any).testTranslation = testTranslation;
  (window as any).testBatchTranslation = testBatchTranslation;
  (window as any).showEngineStatus = showEngineStatus;
  (window as any).translateService = translateService;
  
  console.log('🔧 翻译测试工具已加载:');
  console.log('  • testTranslation("Hello") - 单文本翻译');
  console.log('  • testBatchTranslation(["Hello", "World"]) - 批量翻译');
  console.log('  • showEngineStatus() - 引擎状态');
  console.log('  • translateService - 翻译服务实例');
  console.log('');
  console.log('💡 示例用法:');
  console.log('  testTranslation("Hello, World!")');
  console.log('  testBatchTranslation(["Hello", "Good morning"])');
  console.log('  showEngineStatus()');
  
  // 验证函数是否正确暴露
  setTimeout(() => {
    if (typeof (window as any).testTranslation === 'function') {
      console.log('✅ testTranslation 函数已正确加载');
    } else {
      console.error('❌ testTranslation 函数加载失败');
    }
  }, 1000);
}

export default {
  testTranslation,
  testBatchTranslation,
  showEngineStatus,
  translateService
};