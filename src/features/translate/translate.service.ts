/**
 * 翻译统一服务层
 * 提供简洁的翻译接口，隐藏底层引擎管理复杂性
 */

import { TranslateEngineManager } from './engine-manager';
import { TranslateConfigManager, translateConfig } from './config';
import { GoogleTranslateEngine } from './engines/google';
import { MicrosoftTranslateEngine } from './engines/microsoft';
import { translationCache } from './cache-manager';
import {
  TranslateOptions,
  TranslateResponse,
  EngineStatus,
  TranslateError,
  TranslateErrorType,
  TranslateStats
} from './types';

export class TranslateService {
  private engineManager: TranslateEngineManager;
  private configManager: TranslateConfigManager;
  private stats: TranslateStats;

  constructor(configManager?: TranslateConfigManager) {
    this.configManager = configManager || translateConfig;
    this.engineManager = new TranslateEngineManager(this.configManager);
    this.stats = this.initializeStats();

    // 异步加载配置
    this.initialize();
  }

  /**
   * 初始化服务
   */
  private async initialize(): Promise<void> {
    try {
      await this.configManager.loadConfigAsync();

      // 注册Google翻译引擎
      this.registerDefaultEngines();

      console.log('✅ [translate-service|STARTUP] 翻译服务初始化完成');
    } catch (error) {
      console.warn('Failed to initialize TranslateService:', error);
    }
  }

  /**
   * 注册默认引擎
   */
  private registerDefaultEngines(): void {
    const config = this.configManager.getConfig();

    // 注册Google引擎
    if (config.engines.google && config.engines.google.enabled) {
      const googleEngine = new GoogleTranslateEngine(config.engines.google);
      this.engineManager.registerEngine(googleEngine);
    }

    // 注册Microsoft引擎
    if (config.engines.microsoft && config.engines.microsoft.enabled) {
      const microsoftEngine = new MicrosoftTranslateEngine(config.engines.microsoft);
      this.engineManager.registerEngine(microsoftEngine);
    }
  }

  /**
   * 初始化统计数据
   */
  private initializeStats(): TranslateStats {
    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      engineUsage: {},
      errorsByType: {} as Record<TranslateErrorType, number>
    };
  }

  /**
   * 翻译文本数组 - 主要接口
   * 🔧 修复：确保只发送纯文本给翻译引擎，忽略格式参数
   */
  async translateTexts(texts: string[], options: TranslateOptions = {}): Promise<string[]> {
    if (!texts || texts.length === 0) {
      throw new TranslateError(
        TranslateErrorType.INVALID_INPUT,
        'Input texts cannot be empty'
      );
    }

    const startTime = Date.now();

    try {
      // 🔧 修复：相信调用方已提供正确格式的文本，移除冗余转换
      const mergedOptions = this.mergeDefaultOptions({
        ...options,
        format: 'text' // 强制所有翻译请求使用纯文本格式
      });

      // 更新统计
      this.stats.totalRequests++;

      // 🔧 修复：移除冗余的文本清理，相信输入已经是纯文本
      // 检查缓存的文本
      const cachedResults: (string | null)[] = [];
      const uncachedTexts: string[] = [];
      const uncachedIndexes: number[] = [];

      for (let i = 0; i < texts.length; i++) {
        const text = texts[i];
        if (text && text.trim().length > 0) {
          const cached = await translationCache.get(text, mergedOptions.from!, mergedOptions.to!, mergedOptions.engine || 'default');
          if (cached) {
            cachedResults[i] = cached;
          } else {
            cachedResults[i] = null;
            uncachedTexts.push(text);
            uncachedIndexes.push(i);
          }
        } else {
          cachedResults[i] = '';
        }
      }

      // 如果没有需要翻译的文本，直接返回缓存结果
      if (uncachedTexts.length === 0) {
        return cachedResults.map(result => result || '');
      }

      // 🔧 执行翻译（现在确保只有纯文本被发送给引擎）
      const response = await this.engineManager.translate(uncachedTexts, mergedOptions);

      // 缓存新翻译的结果
      for (let i = 0; i < uncachedTexts.length; i++) {
        const text = uncachedTexts[i];
        const translation = response.translations[i];
        if (translation && translation.length > 0) {
          await translationCache.set(text, mergedOptions.from!, mergedOptions.to!, mergedOptions.engine || 'default', translation);
        }
        // 更新结果数组
        cachedResults[uncachedIndexes[i]] = translation;
      }

      // 更新成功统计
      this.updateSuccessStats(response, startTime);

      return cachedResults.map(result => result || '');

    } catch (error) {
      // 更新失败统计
      this.updateFailureStats(error as Error, startTime);
      throw error;
    }
  }

  /**
   * 🔧 确保文本是纯文本格式
   * 移除任何HTML标签，只保留文本内容
   * @private
   */
  /**
   * @deprecated 已废弃：文本清理现在在文本提取阶段完成，翻译服务只接受纯文本
   * 保留方法以防旧代码调用，但建议迁移到在文本提取阶段处理
   */
  private ensurePlainText(text: string): string {
    if (!text) return '';
    
    // 如果文本包含HTML标签，提取纯文本内容
    if (text.includes('<') && text.includes('>')) {
      console.warn('🔧 TranslateService.ensurePlainText: 检测到HTML内容，建议在文本提取阶段处理');
      // 创建临时DOM元素来安全地提取文本
      const tempElement = document.createElement('div');
      tempElement.innerHTML = text;
      return tempElement.textContent || tempElement.innerText || '';
    }
    
    return text;
  }

  /**
   * 翻译单个文本
   */
  async translateText(text: string, options: TranslateOptions = {}): Promise<string> {
    if (!text || text.trim().length === 0) {
      return '';
    }

    const mergedOptions = this.mergeDefaultOptions(options);

    // 检查缓存
    const cacheKey = `${text}_${mergedOptions.from}_${mergedOptions.to}_${mergedOptions.engine}`;
    const cached = await translationCache.get(text, mergedOptions.from!, mergedOptions.to!, mergedOptions.engine || 'default');

    if (cached) {
      return cached;
    }

    // 执行翻译
    const results = await this.translateTexts([text], options);
    const result = results[0] || '';

    // 缓存结果
    if (result && result.length > 0) {
      await translationCache.set(text, mergedOptions.from!, mergedOptions.to!, mergedOptions.engine || 'default', result);
    }

    return result;
  }

  /**
   * 翻译页面 - 高级接口
   */
  async translatePage(options: {
    targetLanguage?: string;
    sourceLanguage?: string;
    excludeSelectors?: string[];
    includeSelectors?: string[];
  } = {}): Promise<{
    translatedCount: number;
    totalCount: number;
    duration: number;
  }> {
    const startTime = Date.now();

    try {
      // 扫描可翻译节点
      const translatableNodes = this.scanTranslatableNodes({
        excludeSelectors: options.excludeSelectors,
        includeSelectors: options.includeSelectors
      });

      if (translatableNodes.length === 0) {
        return { translatedCount: 0, totalCount: 0, duration: 0 };
      }

      // 提取文本
      const texts = translatableNodes.map(node => this.extractTextFromNode(node));

      // 执行翻译
      const translations = await this.translateTexts(texts, {
        to: options.targetLanguage,
        from: options.sourceLanguage
      });

      // 注入翻译结果
      let translatedCount = 0;
      for (let i = 0; i < translatableNodes.length; i++) {
        if (translations[i] && translations[i].trim()) {
          this.injectTranslation(translatableNodes[i], translations[i]);
          translatedCount++;
        }
      }

      // 设置页面状态为双语模式
      document.documentElement.setAttribute('lu-view', 'dual');

      const duration = Date.now() - startTime;
      return { translatedCount, totalCount: translatableNodes.length, duration };

    } catch (error) {
      console.error('Page translation failed:', error);
      throw error;
    }
  }

  /**
   * 扫描可翻译节点
   */
  private scanTranslatableNodes(options: {
    excludeSelectors?: string[];
    includeSelectors?: string[];
  } = {}): HTMLElement[] {
    const {
      excludeSelectors = ['code', 'pre', 'script', 'style', '.lu-skip', '.notranslate'],
      includeSelectors = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'td', 'th', 'div', 'span']
    } = options;

    const nodes: HTMLElement[] = [];

    // 查找包含选择器匹配的节点
    for (const selector of includeSelectors) {
      const elements = document.querySelectorAll(selector);

      for (const element of Array.from(elements)) {
        const htmlElement = element as HTMLElement;

        // 检查是否应该排除
        if (this.shouldExcludeNode(htmlElement, excludeSelectors)) {
          continue;
        }

        // 检查是否有有效的文本内容
        const text = this.extractTextFromNode(htmlElement);
        if (this.isValidTranslationText(text)) {
          nodes.push(htmlElement);
        }
      }
    }

    return nodes;
  }

  /**
   * 检查节点是否应该被排除
   */
  private shouldExcludeNode(node: HTMLElement, excludeSelectors: string[]): boolean {
    // 检查节点本身
    for (const selector of excludeSelectors) {
      if (node.matches(selector)) {
        return true;
      }
    }

    // 检查父节点
    let parent = node.parentElement;
    while (parent) {
      for (const selector of excludeSelectors) {
        if (parent.matches(selector)) {
          return true;
        }
      }
      parent = parent.parentElement;
    }

    // 检查是否已经包含翻译
    if (node.querySelector('.lu-wrapper')) {
      return true;
    }

    return false;
  }

  /**
   * 从节点提取文本
   * 🔧 修复：排除隐藏子元素，避免容器聚合隐藏内容
   */
  private extractTextFromNode(node: HTMLElement): string {
    // 获取直接文本内容，排除子元素中的翻译内容
    const clonedNode = node.cloneNode(true) as HTMLElement;

    // 移除已存在的翻译包装器
    const wrappers = clonedNode.querySelectorAll('.lu-wrapper');
    wrappers.forEach(wrapper => wrapper.remove());

    // 🔧 关键修复：移除隐藏的子元素
    this.removeHiddenElements(clonedNode);

    return clonedNode.textContent?.trim() || '';
  }

  /**
   * 移除隐藏的子元素
   * 🔧 防止容器元素聚合隐藏内容的文本
   */
  private removeHiddenElements(element: HTMLElement): void {
    // 查找所有子元素
    const allChildren = element.querySelectorAll('*');

    for (const child of Array.from(allChildren)) {
      const htmlChild = child as HTMLElement;

      // 检查基本的隐藏状态
      const style = getComputedStyle(htmlChild);
      if (style.display === 'none' || style.visibility === 'hidden') {
        htmlChild.remove();
        continue;
      }

      // 检查 aria-hidden 属性
      if (htmlChild.getAttribute('aria-hidden') === 'true') {
        htmlChild.remove();
        continue;
      }

      // 检查是否有隐藏相关的标记
      if (htmlChild.hasAttribute('data-exclusion-reason')) {
        const reason = htmlChild.getAttribute('data-exclusion-reason');
        if (reason === 'hidden') {
          htmlChild.remove();
          continue;
        }
      }

      // 检查社交分享隐藏元素
      const isInShareDialog = htmlChild.closest('.uni-social-share__dialog, .social-share-dialog');
      if (isInShareDialog) {
        const dialog = htmlChild.closest('.uni-social-share__dialog') as HTMLElement;
        if (dialog) {
          const trigger = dialog.parentElement?.querySelector('[aria-expanded]');
          if (trigger && trigger.getAttribute('aria-expanded') === 'false') {
            htmlChild.remove();
            continue;
          }
        }
      }
    }
  }

  /**
   * 检查文本是否适合翻译
   */
  private isValidTranslationText(text: string): boolean {
    if (!text || text.length < 2) {
      return false;
    }

    // 排除纯数字、纯符号等
    if (/^[\d\s\-_.,;:!?()[\]{}'"]*$/.test(text)) {
      return false;
    }

    // 排除过短的文本
    if (text.length < 3) {
      return false;
    }

    return true;
  }

  /**
   * 注入翻译结果到DOM
   */
  private injectTranslation(node: HTMLElement, translation: string): void {
    // 检查是否已经存在翻译
    if (node.querySelector('.lu-wrapper')) {
      return;
    }

    // 创建翻译包装器
    const wrapper = document.createElement('font');
    wrapper.className = 'notranslate lu-wrapper';
    wrapper.lang = 'zh-CN';

    // 创建换行

    // 创建翻译块
    const block = document.createElement('font');
    block.className = 'notranslate lu-block lu-weak';
    block.textContent = translation;

    // 组装结构
    wrapper.appendChild(block);

    // 注入到原节点
    node.appendChild(wrapper);
  }

  /**
   * 切换页面翻译状态
   */
  setPageViewMode(mode: 'origin' | 'dual' | 'trans'): void {
    document.documentElement.setAttribute('lu-view', mode);
  }

  /**
   * 获取页面翻译状态
   */
  getPageViewMode(): string {
    return document.documentElement.getAttribute('lu-view') || 'origin';
  }

  /**
   * 清除页面翻译
   */
  clearPageTranslations(): void {
    const wrappers = document.querySelectorAll('.lu-wrapper');
    wrappers.forEach(wrapper => wrapper.remove());
    document.documentElement.setAttribute('lu-view', 'origin');
  }

  /**
   * 合并默认选项
   */
  private mergeDefaultOptions(options: TranslateOptions): TranslateOptions {
    const config = this.configManager.getConfig();

    return {
      from: options.from || config.defaultSourceLanguage,
      to: options.to || config.defaultTargetLanguage,
      timeout: options.timeout || config.globalTimeout,
      engine: options.engine
    };
  }

  /**
   * 更新成功统计
   */
  private updateSuccessStats(response: TranslateResponse, startTime: number): void {
    this.stats.successfulRequests++;

    // 更新引擎使用统计
    if (response.engine) {
      this.stats.engineUsage[response.engine] = (this.stats.engineUsage[response.engine] || 0) + 1;
    }

    // 更新平均延迟
    const latency = Date.now() - startTime;
    this.stats.averageLatency = (
      (this.stats.averageLatency * (this.stats.totalRequests - 1) + latency) /
      this.stats.totalRequests
    );
  }

  /**
   * 更新失败统计
   */
  private updateFailureStats(error: Error, startTime: number): void {
    this.stats.failedRequests++;

    // 更新错误类型统计
    const errorType = error instanceof TranslateError
      ? error.type
      : TranslateErrorType.UNKNOWN_ERROR;

    this.stats.errorsByType[errorType] = (this.stats.errorsByType[errorType] || 0) + 1;
  }

  /**
   * 注册引擎到管理器
   */
  registerEngine(engine: any): void {
    this.engineManager.registerEngine(engine);
  }

  /**
   * 获取可用引擎列表
   */
  getAvailableEngines(): string[] {
    return this.engineManager.getAvailableEngines();
  }

  /**
   * 设置引擎优先级
   */
  async setEnginePriority(priority: string[]): Promise<void> {
    await this.configManager.setEnginePriority(priority);
  }

  /**
   * 获取引擎优先级
   */
  getEnginePriority(): string[] {
    return this.configManager.getEnginePriority();
  }

  /**
   * 测试引擎连通性
   */
  async testEngine(engineName: string): Promise<boolean> {
    return await this.engineManager.testEngine(engineName);
  }

  /**
   * 获取所有引擎状态
   */
  async getEnginesStatus(): Promise<Record<string, EngineStatus>> {
    return await this.engineManager.getEnginesStatus();
  }

  /**
   * 获取服务统计信息
   */
  getServiceStats(): TranslateStats & { cacheStats: any } {
    return {
      ...this.stats,
      cacheStats: translationCache.getStats()
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = this.initializeStats();
    this.engineManager.resetEnginesStats();
  }

  /**
   * 清除翻译缓存
   */
  async clearCache(): Promise<void> {
    await translationCache.clear();
  }

  /**
   * 获取推荐引擎
   */
  getRecommendedEngine(): string | null {
    return this.engineManager.getRecommendedEngine();
  }

  /**
   * 获取配置管理器
   */
  getConfigManager(): TranslateConfigManager {
    return this.configManager;
  }

  /**
   * 获取引擎管理器
   */
  getEngineManager(): TranslateEngineManager {
    return this.engineManager;
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.engineManager.destroy();
    console.log('TranslateService destroyed');
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return translationCache.getStats();
  }
}

// 导出默认实例
export const translateService = new TranslateService();