/**
 * 手动测试翻译功能
 * 可以在浏览器控制台中运行的测试脚本
 */

import { translateService } from '../index';

// 声明全局变量用于浏览器控制台访问
declare global {
  interface Window {
    testTranslation: typeof testTranslation;
    testBatchTranslation: typeof testBatchTranslation;
    testTranslationError: typeof testTranslationError;
    testEngineStatus: typeof testEngineStatus;
    testPerformance: typeof testPerformance;
    translateService: typeof translateService;
  }
}

/**
 * 测试单个文本翻译
 */
export async function testTranslation(text: string = 'Hello, World!', options: any = {}) {
  console.log('🚀 开始单文本翻译测试');
  console.log('📝 原文:', text);
  console.log('⚙️ 选项:', options);
  
  try {
    const startTime = Date.now();
    const result = await translateService.translateText(text, {
      from: 'en',
      to: 'zh',
      ...options
    });
    const endTime = Date.now();
    
    console.log('✅ 翻译成功!');
    console.log('🌟 译文:', result.translations[0]);
    console.log('🔧 引擎:', result.engine);
    console.log('🎯 API:', result.api);
    console.log('⏱️ 耗时:', endTime - startTime, 'ms');
    console.log('📊 详细结果:', result);
    
    return result;
  } catch (error) {
    console.error('❌ 翻译失败:', error);
    console.error('🔍 错误详情:', {
      message: error.message,
      type: error.type,
      engine: error.engine,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * 测试批量文本翻译
 */
export async function testBatchTranslation(texts: string[] = ['Hello', 'World', 'Good morning'], options: any = {}) {
  console.log('🚀 开始批量翻译测试');
  console.log('📝 原文列表:', texts);
  console.log('⚙️ 选项:', options);
  
  try {
    const startTime = Date.now();
    const result = await translateService.translateTexts(texts, {
      from: 'en',
      to: 'zh',
      ...options
    });
    const endTime = Date.now();
    
    console.log('✅ 批量翻译成功!');
    console.log('📊 翻译统计:');
    console.log('  📝 原文数量:', texts.length);
    console.log('  🌟 译文数量:', result.translations.length);
    console.log('  🔧 使用引擎:', result.engine);
    console.log('  ⏱️ 总耗时:', endTime - startTime, 'ms');
    console.log('  🚀 平均每条:', Math.round((endTime - startTime) / texts.length), 'ms');
    
    console.log('📋 翻译对照表:');
    texts.forEach((text, index) => {
      console.log(`  ${index + 1}. "${text}" → "${result.translations[index]}"`);
    });
    
    console.log('📊 详细结果:', result);
    
    return result;
  } catch (error) {
    console.error('❌ 批量翻译失败:', error);
    console.error('🔍 错误详情:', {
      message: error.message,
      type: error.type,
      engine: error.engine
    });
    throw error;
  }
}

/**
 * 测试翻译错误处理
 */
export async function testTranslationError() {
  console.log('🚀 开始错误处理测试');
  
  try {
    // 测试空文本
    await translateService.translateText('');
  } catch (error) {
    console.log('✅ 空文本错误处理正常:', error.message);
  }
  
  try {
    // 测试无效引擎
    await translateService.translateText('Hello', { engine: 'invalid-engine' });
  } catch (error) {
    console.log('✅ 无效引擎错误处理正常:', error.message);
  }
  
  try {
    // 测试无效语言
    await translateService.translateText('Hello', { from: 'invalid', to: 'invalid' });
  } catch (error) {
    console.log('✅ 无效语言错误处理正常:', error.message);
  }
  
  console.log('🎉 错误处理测试完成');
}

/**
 * 测试引擎状态
 */
export async function testEngineStatus() {
  console.log('🚀 开始引擎状态测试');
  
  const engines = translateService.getAvailableEngines();
  console.log('🔍 可用引擎:', engines);
  
  const priority = translateService.getEnginePriority();
  console.log('🎯 引擎优先级:', priority);
  
  const recommended = translateService.getRecommendedEngine();
  console.log('⭐ 推荐引擎:', recommended);
  
  console.log('🔧 引擎状态检查:');
  for (const engineName of engines) {
    try {
      const isWorking = await translateService.testEngine(engineName);
      console.log(`  ${engineName}:`, isWorking ? '✅ 正常' : '❌ 异常');
    } catch (error) {
      console.log(`  ${engineName}:`, '❌ 测试失败', error.message);
    }
  }
  
  try {
    const status = await translateService.getEnginesStatus();
    console.log('📊 详细状态:', status);
  } catch (error) {
    console.error('❌ 获取状态失败:', error);
  }
  
  console.log('🎉 引擎状态测试完成');
}

/**
 * 测试性能
 */
export async function testPerformance(count: number = 50) {
  console.log(`🚀 开始性能测试 (${count} 条文本)`);
  
  const texts = Array.from({ length: count }, (_, i) => `Test sentence number ${i + 1}`);
  
  const startTime = Date.now();
  
  try {
    const result = await translateService.translateTexts(texts, {
      from: 'en',
      to: 'zh'
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('✅ 性能测试完成!');
    console.log('📊 性能统计:');
    console.log('  📝 文本数量:', texts.length);
    console.log('  ⏱️ 总耗时:', duration, 'ms');
    console.log('  🚀 平均每条:', Math.round(duration / texts.length), 'ms');
    console.log('  🎯 吞吐量:', Math.round(texts.length / duration * 1000), 'texts/sec');
    console.log('  🔧 使用引擎:', result.engine);
    console.log('  ✅ 成功率:', result.success ? '100%' : '0%');
    
    // 显示服务统计
    const stats = translateService.getServiceStats();
    console.log('📈 服务统计:');
    console.log('  🎯 总请求:', stats.totalRequests);
    console.log('  ✅ 成功请求:', stats.successfulRequests);
    console.log('  ❌ 失败请求:', stats.failedRequests);
    console.log('  ⏱️ 平均延迟:', Math.round(stats.averageLatency), 'ms');
    console.log('  🔧 引擎使用:', stats.engineUsage);
    
    return result;
  } catch (error) {
    console.error('❌ 性能测试失败:', error);
    throw error;
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🎬 开始运行所有翻译测试');
  console.log('=' .repeat(60));
  
  try {
    // 基础翻译测试
    console.log('\n📋 测试 1: 基础翻译');
    await testTranslation('Hello, World!');
    
    // 批量翻译测试
    console.log('\n📋 测试 2: 批量翻译');
    await testBatchTranslation(['Hello', 'Good morning', 'How are you?']);
    
    // 错误处理测试
    console.log('\n📋 测试 3: 错误处理');
    await testTranslationError();
    
    // 引擎状态测试
    console.log('\n📋 测试 4: 引擎状态');
    await testEngineStatus();
    
    // 性能测试
    console.log('\n📋 测试 5: 性能测试');
    await testPerformance(20);
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 所有测试完成!');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 导出到全局对象供浏览器控制台使用
if (typeof window !== 'undefined') {
  window.testTranslation = testTranslation;
  window.testBatchTranslation = testBatchTranslation;
  window.testTranslationError = testTranslationError;
  window.testEngineStatus = testEngineStatus;
  window.testPerformance = testPerformance;
  window.translateService = translateService;
  
  // 添加便捷方法
  (window as any).runAllTranslationTests = runAllTests;
  
  console.log('🔧 翻译测试工具已加载到全局对象:');
  console.log('  • testTranslation(text, options) - 单文本翻译测试');
  console.log('  • testBatchTranslation(texts, options) - 批量翻译测试');
  console.log('  • testTranslationError() - 错误处理测试');
  console.log('  • testEngineStatus() - 引擎状态测试');
  console.log('  • testPerformance(count) - 性能测试');
  console.log('  • runAllTranslationTests() - 运行所有测试');
  console.log('  • translateService - 翻译服务实例');
  console.log('');
  console.log('💡 使用示例:');
  console.log('  testTranslation("Hello, World!")');
  console.log('  testBatchTranslation(["Hello", "World"])');
  console.log('  runAllTranslationTests()');
}

export default {
  testTranslation,
  testBatchTranslation,
  testTranslationError,
  testEngineStatus,
  testPerformance,
  runAllTests
};