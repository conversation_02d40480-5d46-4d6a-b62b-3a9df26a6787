/**
 * MSW API Mock测试
 * 验证MSW handlers是否正确处理翻译API请求
 */

import { describe, it, expect, vi, beforeEach, beforeAll, afterAll } from 'vitest';
import { setupServer } from 'msw/node';
import { handlers } from '../../../test/mocks/handlers';

// 设置MSW server
const server = setupServer(...handlers);

// Mock fetch for the tests
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('MSW API Mock测试', () => {
  beforeAll(() => {
    server.listen({ onUnhandledRequest: 'error' });
  });

  afterAll(() => {
    server.close();
  });

  beforeEach(() => {
    vi.clearAllMocks();
    server.resetHandlers();
  });

  describe('Microsoft API Mock', () => {
    it('应该正确处理Microsoft认证请求', async () => {
      const response = await fetch('https://edge.microsoft.com/translate/auth');
      const token = await response.text();

      expect(response.ok).toBe(true);
      expect(token).toBe('mock-microsoft-auth-token-12345');
    });

    it('应该正确处理Microsoft翻译请求', async () => {
      const requestBody = [
        { Text: 'Hello' },
        { Text: 'World' }
      ];

      const response = await fetch('https://api.cognitive.microsofttranslator.com/translate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const result = await response.json();

      expect(response.ok).toBe(true);
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        translations: [{ text: '你好', to: 'zh-Hans' }]
      });
      expect(result[1]).toEqual({
        translations: [{ text: '世界', to: 'zh-Hans' }]
      });
    });
  });

  describe('Google API Mock', () => {
    it('应该正确处理Google API 1请求', async () => {
      const requestBody = [
        ['Hello', 'World'],
        'en',
        'zh',
        'te_lib'
      ];

      const response = await fetch('https://translate-pa.googleapis.com/v1/translateHtml', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json+protobuf' },
        body: JSON.stringify(requestBody)
      });

      const result = await response.json();

      expect(response.ok).toBe(true);
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(2);
      expect(result[0]).toHaveLength(1);
      expect(result[0][0]).toHaveLength(2);
      expect(result[0][0][0]).toEqual(['你好', 'Hello', null, null, 1]);
      expect(result[0][0][1]).toEqual(['世界', 'World', null, null, 1]);
      expect(result[1]).toBe('te_lib');
    });

    it('应该正确处理Google API 2请求', async () => {
      const formData = new FormData();
      formData.append('q', 'Hello');
      formData.append('q', 'World');

      const response = await fetch('https://translate.googleapis.com/translate_a/t', {
        method: 'POST',
        body: formData
      });

      const resultText = await response.text();
      const result = JSON.parse(resultText);

      expect(response.ok).toBe(true);
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);
      expect(result[0]).toHaveLength(2);
      expect(result[0][0]).toEqual(['你好', 'Hello', null, null, 1]);
      expect(result[0][1]).toEqual(['世界', 'World', null, null, 1]);
    });
  });

  describe('Mock翻译映射', () => {
    it('应该返回正确的翻译映射', async () => {
      // 测试Microsoft API
      const msResponse = await fetch('https://api.cognitive.microsofttranslator.com/translate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify([{ Text: 'Hello, World!' }])
      });

      const msResult = await msResponse.json();
      expect(msResult[0].translations[0].text).toBe('你好，世界！');

      // 测试Google API 1
      const googleResponse = await fetch('https://translate-pa.googleapis.com/v1/translateHtml', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json+protobuf' },
        body: JSON.stringify([['Hello, World!'], 'en', 'zh', 'te_lib'])
      });

      const googleResult = await googleResponse.json();
      expect(googleResult[0][0][0][0]).toBe('你好，世界！');
    });
  });
});