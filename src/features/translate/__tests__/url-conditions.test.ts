/**
 * URL条件功能测试
 */

import { UrlMatcher } from '../../../utils/url-matcher';
import { AdvancedConfigParser } from '../../../core/advanced-config-parser';

describe('URL条件功能测试', () => {
  describe('UrlMatcher', () => {
    test('完整URL匹配', () => {
      const url = 'https://news.google.com/home';
      expect(UrlMatcher.matches(url, 'https://news.google.com/home')).toBe(true);
      expect(UrlMatcher.matches(url, 'https://news.google.com')).toBe(false);
    });

    test('通配符匹配', () => {
      const url = 'https://news.google.com/home';
      expect(UrlMatcher.matches(url, 'https://news.google.com/*')).toBe(true);
      expect(UrlMatcher.matches(url, 'https://*.google.com/*')).toBe(true);
      expect(UrlMatcher.matches(url, 'https://*.example.com/*')).toBe(false);
    });

    test('相对路径匹配', () => {
      const url = 'https://example.com/news/article';
      expect(UrlMatcher.matches(url, '/news/')).toBe(true);
      expect(UrlMatcher.matches(url, '/blog/')).toBe(false);
    });

    test('批量匹配', () => {
      const url = 'https://news.google.com/home';
      const patterns = [
        'https://news.google.com',
        'https://news.google.com/*',
        'https://*.google.com/news/*'
      ];
      expect(UrlMatcher.matchesAny(url, patterns)).toBe(true);

      const nonMatchingPatterns = [
        'https://example.com',
        'https://facebook.com/*'
      ];
      expect(UrlMatcher.matchesAny(url, nonMatchingPatterns)).toBe(false);
    });

    test('空模式处理', () => {
      const url = 'https://example.com';
      expect(UrlMatcher.matchesAny(url, [])).toBe(true); // 空数组应该返回true
      expect(UrlMatcher.matches(url, '')).toBe(false); // 空字符串应该返回false
    });
  });

  describe('配置解析器URL条件解析', () => {
    const parser = new AdvancedConfigParser();

    test('解析单个URL条件', () => {
      const configData = {
        version: '2.0',
        rules: [{
          id: 'test-rule',
          name: 'Test Rule',
          description: 'Test',
          conditions: [{
            url: ['https://news.google.com'],
            tagName: ['div']
          }],
          strategy: 'inline',
          priority: 10,
          enabled: true
        }]
      };

      const rules = parser.parseConfig(configData);
      expect(rules).toHaveLength(1);
      expect(rules[0].conditions[0].url).toEqual(['https://news.google.com']);
    });

    test('解析多个URL条件', () => {
      const configData = {
        version: '2.0',
        rules: [{
          id: 'test-rule',
          name: 'Test Rule',
          description: 'Test',
          conditions: [{
            url: [
              'https://news.google.com',
              'https://news.google.com/*',
              'https://*.google.com/news/*'
            ],
            tagName: ['div']
          }],
          strategy: 'inline',
          priority: 10,
          enabled: true
        }]
      };

      const rules = parser.parseConfig(configData);
      expect(rules[0].conditions[0].url).toHaveLength(3);
    });

    test('字符串形式的URL条件', () => {
      const configData = {
        version: '2.0',
        rules: [{
          id: 'test-rule',
          name: 'Test Rule',
          description: 'Test',
          conditions: [{
            url: 'https://news.google.com',
            tagName: ['div']
          }],
          strategy: 'inline',
          priority: 10,
          enabled: true
        }]
      };

      const rules = parser.parseConfig(configData);
      expect(rules[0].conditions[0].url).toEqual(['https://news.google.com']);
    });
  });
});