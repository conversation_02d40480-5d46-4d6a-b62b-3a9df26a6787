/**
 * 翻译服务测试
 * 测试翻译服务的核心功能：引擎管理、降级策略、页面翻译等
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { TranslateService } from '../translate.service';
import { TranslateConfigManager } from '../config';
import { GoogleTranslateEngine } from '../engines/google';
import { TranslateErrorType } from '../types';

// 检查是否启用网络测试
const NETWORK_TESTS_ENABLED = process.env.NETWORK_TESTS_ENABLED === 'true';

// Access the global browser mock from setup.ts
declare const browser: any;

// Mock DOM
Object.defineProperty(global, 'document', {
  value: {
    documentElement: {
      getAttribute: vi.fn(),
      setAttribute: vi.fn(),
    },
    querySelectorAll: vi.fn(() => []),
    createTextNode: vi.fn(),
    createElement: vi.fn(() => ({
      appendChild: vi.fn(),
      setAttribute: vi.fn(),
      classList: { add: vi.fn() },
    })),
  },
  writable: true,
});

describe('TranslateService', () => {
  let service: TranslateService;
  let mockConfigManager: TranslateConfigManager;

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock storage responses using global browser mock
    vi.mocked(browser.storage.local.get).mockResolvedValue({});
    vi.mocked(browser.storage.local.set).mockResolvedValue(undefined);

    mockConfigManager = new TranslateConfigManager();
    service = new TranslateService(mockConfigManager);
  });

  afterEach(() => {
    vi.resetAllMocks();
    if (service && typeof service.destroy === 'function') {
      service.destroy();
    }
  });

  describe('服务初始化', () => {
    it('应该正确初始化服务', () => {
      expect(service).toBeDefined();
      expect(service.getConfigManager()).toBe(mockConfigManager);
    });

    it('应该注册默认引擎', () => {
      const engines = service.getAvailableEngines();
      expect(engines).toContain('google');
    });

    it('应该初始化统计信息', () => {
      const stats = service.getServiceStats();
      expect(stats.totalRequests).toBe(0);
      expect(stats.successfulRequests).toBe(0);
      expect(stats.failedRequests).toBe(0);
    });
  });

  describe('单文本翻译', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该成功翻译单个文本', async () => {
      // Mock successful translation
      browser.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: [[[['你好', 'Hello', null, null, 1]]]],
        status: 200
      });

      const result = await service.translateText('Hello', { from: 'en', to: 'zh' });
      
      // 新API直接返回翻译后的字符串
      expect(result).toBe('你好');
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该处理翻译失败', async () => {
      browser.runtime.sendMessage.mockRejectedValue(new Error('API error'));
      
      await expect(service.translateText('Hello')).rejects.toThrow();
    });

    it('应该处理空文本', async () => {
      const result = await service.translateText('');
      expect(result).toBe(''); // 空文本应该直接返回空字符串
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该更新成功统计', async () => {
      browser.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: [[[['你好', 'Hello', null, null, 1]]]],
        status: 200
      });

      await service.translateText('Hello');
      
      const stats = service.getServiceStats();
      expect(stats.totalRequests).toBe(1);
      expect(stats.successfulRequests).toBe(1);
      expect(stats.failedRequests).toBe(0);
    });
  });

  describe('批量翻译', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该成功翻译多个文本', async () => {
      browser.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: [
          [
            [['你好', 'Hello', null, null, 1]],
            [['世界', 'World', null, null, 1]]
          ]
        ],
        status: 200
      });

      const result = await service.translateTexts(['Hello', 'World'], { from: 'en', to: 'zh' });
      
      // 新API直接返回字符串数组
      expect(result).toEqual(['你好', '世界']);
    });

    it('应该处理空数组', async () => {
      // translateTexts方法会抛出错误，因为它认为空数组是无效输入
      await expect(service.translateTexts([])).rejects.toThrow('Input texts cannot be empty');
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该处理大量文本', async () => {
      const texts = Array.from({ length: 200 }, (_, i) => `Text ${i}`);
      
      browser.runtime.sendMessage.mockResolvedValue({
        success: true,
        data: [texts.map(text => [[text, text, null, null, 1]])],
        status: 200
      });

      const result = await service.translateTexts(texts);
      
      // 新API直接返回字符串数组
      expect(result).toHaveLength(200);
    });
  });

  describe('页面翻译', () => {
    it.skip('应该翻译页面内容', async () => {
      // 页面翻译功能暂时跳过，因为依赖于DOM操作和复杂的私有方法
    });

    it.skip('应该排除指定的选择器', async () => {
      // 页面翻译功能暂时跳过，因为依赖于DOM操作和复杂的私有方法
    });
  });

  describe('页面视图模式', () => {
    it('应该设置页面视图模式', () => {
      service.setPageViewMode('dual');
      
      expect(document.documentElement.setAttribute).toHaveBeenCalledWith('lu-view', 'dual');
    });

    it('应该获取页面视图模式', () => {
      (document.documentElement.getAttribute as any).mockReturnValue('trans');
      
      const mode = service.getPageViewMode();
      expect(mode).toBe('trans');
    });

    it('应该清除页面翻译', () => {
      const mockWrappers = [
        { remove: vi.fn() },
        { remove: vi.fn() }
      ];
      
      (document.querySelectorAll as any).mockReturnValue(mockWrappers);
      
      service.clearPageTranslations();
      
      expect(mockWrappers[0].remove).toHaveBeenCalled();
      expect(mockWrappers[1].remove).toHaveBeenCalled();
      expect(document.documentElement.setAttribute).toHaveBeenCalledWith('lu-view', 'origin');
    });
  });

  describe('引擎管理', () => {
    it('应该获取可用引擎', () => {
      const engines = service.getAvailableEngines();
      expect(engines).toContain('google');
    });

    it('应该注册新引擎', () => {
      const mockEngine = {
        name: 'test-engine',
        displayName: 'Test Engine',
        maxChunkSize: 1000,
        maxBatchSize: 10,
        translateBatch: vi.fn(),
      };
      
      service.registerEngine(mockEngine);
      
      // 检查引擎是否已注册（通过引擎管理器获取）
      const engineManager = service.getEngineManager();
      const registeredEngines = engineManager.getRegisteredEngines();
      expect(registeredEngines).toContain('test-engine');
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该测试引擎连通性', async () => {
      const result = await service.testEngine('google');
      expect(typeof result).toBe('boolean');
    });

    it('应该获取引擎状态', async () => {
      const status = await service.getEnginesStatus();
      expect(status).toHaveProperty('google');
    });
  });

  describe('配置管理', () => {
    it('应该设置引擎优先级', async () => {
      await service.setEnginePriority(['google', 'microsoft']);
      
      const priority = service.getEnginePriority();
      expect(priority).toEqual(['google', 'microsoft']);
    });

    it('应该获取推荐引擎', () => {
      const recommended = service.getRecommendedEngine();
      expect(recommended).toBe('google');
    });
  });

  describe('统计信息', () => {
    it('应该获取服务统计', () => {
      const stats = service.getServiceStats();
      
      expect(stats).toHaveProperty('totalRequests');
      expect(stats).toHaveProperty('successfulRequests');
      expect(stats).toHaveProperty('failedRequests');
      expect(stats).toHaveProperty('averageLatency');
      expect(stats).toHaveProperty('engineUsage');
      expect(stats).toHaveProperty('errorsByType');
    });

    it('应该重置统计信息', () => {
      service.resetStats();
      
      const stats = service.getServiceStats();
      expect(stats.totalRequests).toBe(0);
      expect(stats.successfulRequests).toBe(0);
      expect(stats.failedRequests).toBe(0);
    });
  });

  describe('错误处理', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该处理网络错误', async () => {
      browser.runtime.sendMessage.mockRejectedValue(new Error('Network error'));
      
      await expect(service.translateText('Hello')).rejects.toThrow();
      
      const stats = service.getServiceStats();
      expect(stats.failedRequests).toBe(1);
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该处理API错误', async () => {
      browser.runtime.sendMessage.mockResolvedValue({
        success: false,
        error: 'API error'
      });
      
      await expect(service.translateText('Hello')).rejects.toThrow();
    });
  });

  describe('服务销毁', () => {
    it('应该正确销毁服务', () => {
      service.destroy();
      
      // 验证清理工作
      expect(service).toBeDefined();
    });
  });
});