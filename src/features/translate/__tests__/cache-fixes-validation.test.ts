/**
 * 🔧 缓存修复效果验证测试
 * 验证4个高优先级和中优先级问题的修复效果
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TranslationCacheManager, CacheConfig, createTranslationCacheManager } from '../cache-manager';

describe('🔧 缓存修复效果验证', () => {
  let cacheManager: TranslationCacheManager;
  let consoleSpy: vi.SpyInstance;
  let consoleWarnSpy: vi.SpyInstance;

  beforeEach(() => {
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'group').mockImplementation(() => {});
    vi.spyOn(console, 'groupEnd').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleSpy.mockRestore();
    consoleWarnSpy.mockRestore();
  });

  describe('🚨 高优先级修复验证：内存使用峰值风险', () => {
    it('✅ 应该使用分块处理大量文本，避免内存溢出', async () => {
      // 使用较小的配置来触发分块处理
      const config: Partial<CacheConfig> = {
        maxBatchSize: 50,  // 较小的批量大小
        memoryCheckInterval: 10
      };
      cacheManager = createTranslationCacheManager(config);

      // 创建超过配置上限的大量文本
      const largeTextArray = Array.from({ length: 100 }, (_, i) => `large text ${i}`);

      console.group('🧪 [cache-fixes-test] 验证内存保护机制');

      const batchStatus = await cacheManager.getBatchStatus(
        largeTextArray, 
        'en', 
        'zh', 
        'google'
      );

      console.groupEnd();

      // 验证分块处理被触发 - 检查console.warn调用
      const warnings = consoleWarnSpy.mock.calls.filter(call => 
        call[0]?.includes?.('文本数量超过上限') || 
        call[0]?.includes?.('启用分块处理')
      );

      expect(warnings.length).toBeGreaterThan(0);
      expect(batchStatus.memoryUsage?.isMemorySafe).toBe(true);
      expect(batchStatus.cachedResults).toHaveLength(100);
      
      console.log('✅ [cache-fixes-test] 内存保护机制验证通过');
    });

    it('🧠 应该正确估算内存使用量', async () => {
      cacheManager = new TranslationCacheManager();
      
      // 创建已知大小的文本数组
      const knownTexts = [
        'a'.repeat(1000),  // 1KB
        'b'.repeat(2000),  // 2KB
        'c'.repeat(500)    // 0.5KB
      ];

      const batchStatus = await cacheManager.getBatchStatus(knownTexts, 'en', 'zh', 'google');

      // 验证内存估算存在且合理
      expect(batchStatus.memoryUsage?.estimatedMB).toBeDefined();
      expect(batchStatus.memoryUsage?.estimatedMB).toBeGreaterThan(0);
      expect(batchStatus.memoryUsage?.estimatedMB).toBeLessThan(1); // 应该小于1MB

      console.log('🧠 [cache-fixes-test] 内存估算验证通过');
    });
  });

  describe('⚡ 中优先级修复验证：统计数据并发安全', () => {
    it('🛡️ 应该使用原子操作更新统计数据', async () => {
      cacheManager = new TranslationCacheManager();

      // 模拟并发访问
      const concurrentPromises = Array.from({ length: 10 }, async (_, i) => {
        await cacheManager.get(`concurrent text ${i}`, 'en', 'zh', 'google');
      });

      await Promise.all(concurrentPromises);

      const stats = cacheManager.getStats();
      
      // 验证统计数据一致性（所有请求都应该是miss）
      expect(stats.misses).toBe(10);
      expect(stats.hits).toBe(0);
      expect(stats.hits + stats.misses).toBe(10);

      console.log('🛡️ [cache-fixes-test] 原子操作验证通过');
    });

    it('🔄 clear操作应该原子性重置统计', async () => {
      cacheManager = new TranslationCacheManager();

      // 先产生一些统计数据
      await cacheManager.set('test1', 'en', 'zh', 'google', 'result1');
      await cacheManager.get('test1', 'en', 'zh', 'google'); // hit
      await cacheManager.get('test2', 'en', 'zh', 'google'); // miss

      let stats = cacheManager.getStats();
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);

      // 清除缓存
      await cacheManager.clear();

      stats = cacheManager.getStats();
      expect(stats.hits).toBe(0);
      expect(stats.misses).toBe(0);

      console.log('🔄 [cache-fixes-test] 原子清除验证通过');
    });
  });

  describe('⚙️ 中优先级修复验证：硬编码配置限制', () => {
    it('🔧 应该支持自定义配置覆盖默认值', () => {
      const customConfig: Partial<CacheConfig> = {
        maxCacheSize: 500,
        batchThreshold: 0.2,
        maxBatchSize: 100,
        memoryCheckInterval: 25
      };

      const customManager = createTranslationCacheManager(customConfig);

      // 通过反射或其他方式验证配置被正确应用
      // 这里我们通过行为验证配置是否生效
      expect(customManager).toBeInstanceOf(TranslationCacheManager);

      console.log('🔧 [cache-fixes-test] 动态配置验证通过');
    });

    it('📊 应该使用动态配置的批量阈值', async () => {
      // 创建高阈值配置（90%）
      const highThresholdConfig: Partial<CacheConfig> = {
        batchThreshold: 0.9
      };
      const highThresholdManager = createTranslationCacheManager(highThresholdConfig);

      // 先缓存部分内容（50%命中率）
      await highThresholdManager.set('cached1', 'en', 'zh', 'google', 'result1');
      await highThresholdManager.set('cached2', 'en', 'zh', 'google', 'result2');

      const testTexts = ['cached1', 'cached2', 'uncached1', 'uncached2'];
      const batchStatus = await highThresholdManager.getBatchStatus(testTexts, 'en', 'zh', 'google');

      // 50%命中率低于90%阈值，应该建议批量翻译
      expect(batchStatus.hitRate).toBe(0.5);
      expect(batchStatus.shouldBatchTranslate).toBe(true);

      console.log('📊 [cache-fixes-test] 动态阈值验证通过');
    });

    it('📏 应该使用动态配置的缓存大小限制', async () => {
      // 创建小缓存大小的配置
      const smallCacheConfig: Partial<CacheConfig> = {
        maxCacheSize: 3  // 只允许3个条目
      };
      const smallCacheManager = createTranslationCacheManager(smallCacheConfig);

      // 添加超过限制的条目，需要逐个等待以确保缓存淘汰正确执行
      await smallCacheManager.set('item1', 'en', 'zh', 'google', 'result1');
      expect(smallCacheManager.getStats().size).toBe(1);
      
      await smallCacheManager.set('item2', 'en', 'zh', 'google', 'result2');
      expect(smallCacheManager.getStats().size).toBe(2);
      
      await smallCacheManager.set('item3', 'en', 'zh', 'google', 'result3');
      expect(smallCacheManager.getStats().size).toBe(3);
      
      // 添加第4个条目时应该触发最旧条目的清理
      await smallCacheManager.set('item4', 'en', 'zh', 'google', 'result4');

      const stats = smallCacheManager.getStats();
      expect(stats.size).toBe(3); // 应该等于配置限制

      console.log('📏 [cache-fixes-test] 动态缓存大小验证通过');
    });
  });

  describe('🔍 中优先级修复验证：内存保护机制', () => {
    it('⚠️ 应该检测并警告内存使用过高', async () => {
      cacheManager = createTranslationCacheManager({
        maxBatchSize: 10 // 很小的限制来触发保护
      });

      // 创建大量大文本
      const largeBatch = Array.from({ length: 20 }, (_, i) => 
        'very long text content '.repeat(100) + i
      );

      console.group('🧪 [cache-fixes-test] 验证内存保护检测');

      await cacheManager.getBatchStatus(largeBatch, 'en', 'zh', 'google');

      console.groupEnd();

      // 验证保护机制被触发 - 检查console.warn调用
      const memoryWarnings = consoleWarnSpy.mock.calls.filter(call => 
        call[0]?.includes?.('预估内存使用过高') || 
        call[0]?.includes?.('启用分块处理') ||
        call[0]?.includes?.('文本数量超过上限')
      );

      expect(memoryWarnings.length).toBeGreaterThan(0);

      console.log('⚠️ [cache-fixes-test] 内存保护检测验证通过');
    });

    it('🚦 应该有合理的内存使用上限', async () => {
      cacheManager = new TranslationCacheManager();

      // 测试正常大小的文本
      const normalTexts = Array.from({ length: 50 }, (_, i) => `normal text ${i}`);
      const normalStatus = await cacheManager.getBatchStatus(normalTexts, 'en', 'zh', 'google');

      expect(normalStatus.memoryUsage?.isMemorySafe).toBe(true);

      console.log('🚦 [cache-fixes-test] 内存上限验证通过');
    });
  })

  describe('🚄 综合性能验证', () => {
    it('⚡ 修复后的缓存应该保持高性能', async () => {
      cacheManager = new TranslationCacheManager();

      const testTexts = Array.from({ length: 100 }, (_, i) => `performance test ${i}`);

      const startTime = performance.now();
      const batchStatus = await cacheManager.getBatchStatus(testTexts, 'en', 'zh', 'google');
      const endTime = performance.now();

      const duration = endTime - startTime;

      // 验证性能指标
      expect(duration).toBeLessThan(100); // 应该在100ms内完成
      expect(batchStatus.cachedResults).toHaveLength(100);
      expect(batchStatus.memoryUsage?.isMemorySafe).toBe(true);

      console.log(`⚡ [cache-fixes-test] 性能验证通过: ${duration.toFixed(2)}ms`);
    });
  });
});