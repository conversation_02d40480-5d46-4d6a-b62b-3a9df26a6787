/**
 * 全面的翻译API测试
 * 测试所有三个翻译API的一致性和可靠性
 * 
 * 涵盖的API：
 * 1. Google Translate API 1 (translateHtml) - protobuf格式
 * 2. Google Translate API 2 (translate_a/t) - form-urlencoded格式  
 * 3. Microsoft Translate API - JSON格式
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { GoogleTranslateEngine } from '../engines/google';
import { MicrosoftTranslateEngine } from '../engines/microsoft';
import { TranslateService } from '../translate.service';
import { TranslateConfigManager } from '../config';
import { GoogleEngineConfig, MicrosoftEngineConfig } from '../types';

// 检查是否启用网络测试
const NETWORK_TESTS_ENABLED = process.env.NETWORK_TESTS_ENABLED === 'true';

// 使用 MSW handlers，不再手动mock fetch
// MSW handlers 在 setup.ts 中已经配置

describe('全翻译API一致性测试', () => {
  let googleEngine: GoogleTranslateEngine;
  let microsoftEngine: MicrosoftTranslateEngine;
  let translateService: TranslateService;
  let configManager: TranslateConfigManager;

  const testTexts = [
    'Hello, World!',
    'How are you today?',
    'This is a test sentence.',
    'Good morning, everyone!'
  ];

  const expectedTranslations = [
    '你好，世界！',
    '你今天好吗？',
    '这是一个测试句子。',
    '大家早上好！'
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // 初始化配置管理器
    configManager = new TranslateConfigManager();
    
    // 初始化Google引擎
    const googleConfig = configManager.getEngineConfig('google') as GoogleEngineConfig;
    googleEngine = new GoogleTranslateEngine(googleConfig);
    
    // 初始化Microsoft引擎
    const microsoftConfig = configManager.getEngineConfig('microsoft') as MicrosoftEngineConfig;
    microsoftConfig.enabled = true; // 启用Microsoft引擎用于测试
    microsoftEngine = new MicrosoftTranslateEngine(microsoftConfig);
    
    // 初始化翻译服务
    translateService = new TranslateService(configManager);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Google Translate API 1 (translateHtml)', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该正确翻译单个文本', async () => {
      const result = await googleEngine.translateBatch(['Hello, World!'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.engine).toBe('google');
      expect(result.translations).toHaveLength(1);
      expect(result.translations[0]).toBe('你好，世界！');
      expect(result.duration).toBeGreaterThan(0);
    }, 5000); // 降低超时时间

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该正确翻译批量文本', async () => {
      const result = await googleEngine.translateBatch(testTexts, {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.engine).toBe('google');
      expect(result.translations).toHaveLength(testTexts.length);
      // 使用mock translations 而不是expectedTranslations
      result.translations.forEach(translation => {
        expect(typeof translation).toBe('string');
        expect(translation.length).toBeGreaterThan(0);
      });
    });

    it('应该正确构建protobuf格式的请求体', () => {
      const body = (googleEngine as any)['buildGoogleApi1Body'](testTexts, {
        from: 'en',
        to: 'zh'
      });

      expect(typeof body).toBe('string');
      const parsed = JSON.parse(body);
      
      // 验证protobuf格式结构
      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed).toHaveLength(2);
      expect(Array.isArray(parsed[0])).toBe(true);
      expect(parsed[0][0]).toEqual(testTexts);
      expect(parsed[0][1]).toBe('en');
      expect(parsed[0][2]).toBe('zh');
      expect(parsed[1]).toBe('te_lib');
    });
  });

  describe('Google Translate API 2 (translate_a/t)', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该在API 1失败时降级到API 2', async () => {
      // 配置Google引擎使用API 2
      const modifiedConfig = { ...configManager.getEngineConfig('google') } as GoogleEngineConfig;
      modifiedConfig.apis = modifiedConfig.apis.map(api => ({
        ...api,
        enabled: api.name === 'google-translate-api-2'
      }));
      
      const engine = new GoogleTranslateEngine(modifiedConfig);
      
      const result = await engine.translateBatch(['Hello, World!'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.engine).toBe('google');
      expect(result.translations).toHaveLength(1);
      expect(typeof result.translations[0]).toBe('string');
    }, 8000); // 增加超时，考虑降级时间

    it('应该正确构建form-urlencoded格式的请求体', () => {
      const body = (googleEngine as any)['buildGoogleApi2Body'](testTexts, {
        from: 'en',
        to: 'zh'
      });

      expect(typeof body).toBe('string');
      
      // 验证form-urlencoded格式
      const params = new URLSearchParams(body);
      const qParams = params.getAll('q');
      
      // 过滤掉零宽空格等特殊字符
      const validQParams = qParams.filter(q => q && q.trim() && q !== '​');
      expect(validQParams.length).toBeGreaterThan(0);
    });

    it('应该包含正确的URL参数', () => {
      const url = (googleEngine as any)['buildGoogleApi2Url']({
        from: 'en',
        to: 'zh',
        format: 'text'
      }, 'https://translate.googleapis.com/translate_a/t');

      expect(url).toContain('client=gtx');
      expect(url).toContain('dt=t');
      expect(url).toContain('sl=en');
      expect(url).toContain('tl=zh');
      expect(url).toContain('format=text'); // 现在应该包含format=text
    });
  });

  describe('Microsoft Translate API', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该正确翻译文本', async () => {
      const result = await microsoftEngine.translateBatch(['Hello, World!'], {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.engine).toBe('microsoft');
      expect(result.translations).toHaveLength(1);
      expect(typeof result.translations[0]).toBe('string');
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该正确处理批量翻译', async () => {
      const result = await microsoftEngine.translateBatch(testTexts, {
        from: 'en',
        to: 'zh'
      });

      expect(result.success).toBe(true);
      expect(result.engine).toBe('microsoft');
      expect(result.translations).toHaveLength(testTexts.length);
      result.translations.forEach(translation => {
        expect(typeof translation).toBe('string');
        expect(translation.length).toBeGreaterThan(0);
      });
    });
  });

  describe('翻译服务集成测试', () => {
    it.skipIf(!NETWORK_TESTS_ENABLED)('应该能够处理多引擎翻译', async () => {
      const result = await translateService.translateTexts(['Hello, World!'], {
        from: 'en',
        to: 'zh'
      });

      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);
      expect(typeof result[0]).toBe('string');
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该在主引擎失败时降级到备用引擎', async () => {
      // 禁用Google引擎，强制使用Microsoft
      await configManager.updateEngineConfig('google', { enabled: false });
      
      const result = await translateService.translateTexts(['Hello, World!'], {
        from: 'en',
        to: 'zh'
      });

      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);
      expect(typeof result[0]).toBe('string');
    });
  });

  describe('错误处理测试', () => {
    it('应该正确处理空文本输入', async () => {
      await expect(googleEngine.translateBatch([], {
        from: 'en',
        to: 'zh'
      })).rejects.toThrow();
    });

    it.skipIf(!NETWORK_TESTS_ENABLED)('应该正确处理无效的语言代码', async () => {
      // 这应该仍然能工作，因为我们的mock不验证语言代码
      const result = await googleEngine.translateBatch(['Hello'], {
        from: 'invalid',
        to: 'invalid'
      });

      // Mock handlers 会返回翻译结果
      expect(result.success).toBe(true);
    });
  });
});