/**
 * 翻译系统集成测试
 * 测试真实的翻译场景，包括控制台输出
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { translateService } from '../index';
import { TranslateService } from '../translate.service';
import { GoogleTranslateEngine } from '../engines/google';

// Mock browser APIs for integration test
const mockBrowserStorage = {
  local: {
    get: vi.fn(),
    set: vi.fn(),
  },
};

const mockBrowserRuntime = {
  sendMessage: vi.fn(),
};

Object.defineProperty(global, 'browser', {
  value: {
    storage: mockBrowserStorage,
    runtime: mockBrowserRuntime,
  },
  writable: true,
});

// Mock console for capturing output
const originalConsoleLog = console.log;
const originalConsoleInfo = console.info;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

let consoleLogs: string[] = [];

function mockConsole() {
  console.log = (...args: any[]) => {
    consoleLogs.push(`[LOG] ${args.join(' ')}`);
    originalConsoleLog(...args);
  };
  
  console.info = (...args: any[]) => {
    consoleLogs.push(`[INFO] ${args.join(' ')}`);
    originalConsoleInfo(...args);
  };
  
  console.warn = (...args: any[]) => {
    consoleLogs.push(`[WARN] ${args.join(' ')}`);
    originalConsoleWarn(...args);
  };
  
  console.error = (...args: any[]) => {
    consoleLogs.push(`[ERROR] ${args.join(' ')}`);
    originalConsoleError(...args);
  };
}

function restoreConsole() {
  console.log = originalConsoleLog;
  console.info = originalConsoleInfo;
  console.warn = originalConsoleWarn;
  console.error = originalConsoleError;
}

describe('Translation Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    consoleLogs = [];
    mockConsole();
    
    // Mock successful storage
    mockBrowserStorage.local.get.mockResolvedValue({});
    mockBrowserStorage.local.set.mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.resetAllMocks();
    restoreConsole();
  });

  describe('基础翻译功能', () => {
    it('应该成功翻译单个文本并输出到控制台', async () => {
      console.log('🚀 开始翻译测试：单个文本');
      
      // Mock successful API response
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: true,
        data: [[[['你好', 'Hello', null, null, 1]]]],
        status: 200,
        statusText: 'OK'
      });

      const result = await translateService.translateText('Hello', {
        from: 'en',
        to: 'zh',
        engine: 'google'
      });

      console.log('✅ 翻译成功！');
      console.log('📝 原文:', 'Hello');
      console.log('🌟 译文:', result.translations[0]);
      console.log('🔧 引擎:', result.engine);
      console.log('⏱️ 耗时:', result.duration, 'ms');
      console.log('🎯 API:', result.api);
      
      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['你好']);
      expect(result.engine).toBe('google');
      
      // 验证控制台输出
      expect(consoleLogs.some(log => log.includes('开始翻译测试'))).toBe(true);
      expect(consoleLogs.some(log => log.includes('翻译成功'))).toBe(true);
      expect(consoleLogs.some(log => log.includes('你好'))).toBe(true);
    });

    it('应该成功翻译多个文本并输出详细信息', async () => {
      console.log('🚀 开始翻译测试：批量文本');
      
      const texts = ['Hello', 'World', 'Good morning', 'How are you?'];
      
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: true,
        data: [
          [
            [['你好', 'Hello', null, null, 1]],
            [['世界', 'World', null, null, 1]],
            [['早上好', 'Good morning', null, null, 1]],
            [['你好吗？', 'How are you?', null, null, 1]]
          ]
        ],
        status: 200,
        statusText: 'OK'
      });

      const result = await translateService.translateTexts(texts, {
        from: 'en',
        to: 'zh',
        engine: 'google'
      });

      console.log('✅ 批量翻译成功！');
      console.log('📊 翻译统计:');
      console.log('  📝 原文数量:', texts.length);
      console.log('  🌟 译文数量:', result.translations.length);
      console.log('  🔧 使用引擎:', result.engine);
      console.log('  ⏱️ 总耗时:', result.duration, 'ms');
      
      console.log('📋 翻译对照表:');
      texts.forEach((text, index) => {
        console.log(`  ${index + 1}. "${text}" → "${result.translations[index]}"`);
      });
      
      expect(result.success).toBe(true);
      expect(result.translations).toHaveLength(4);
      expect(result.translations).toEqual(['你好', '世界', '早上好', '你好吗？']);
      
      // 验证控制台输出
      expect(consoleLogs.some(log => log.includes('批量翻译成功'))).toBe(true);
      expect(consoleLogs.some(log => log.includes('翻译对照表'))).toBe(true);
    });

    it('应该处理翻译错误并输出错误信息', async () => {
      console.log('🚀 开始翻译测试：错误处理');
      
      mockBrowserRuntime.sendMessage.mockRejectedValue(new Error('API服务暂时不可用'));

      try {
        await translateService.translateText('Hello', {
          from: 'en',
          to: 'zh',
          engine: 'google'
        });
      } catch (error) {
        console.error('❌ 翻译失败:', error.message);
        console.log('🔍 错误类型:', error.constructor.name);
        console.log('📋 错误详情:', {
          message: error.message,
          engine: error.engine || 'unknown',
          type: error.type || 'unknown'
        });
      }

      // 验证控制台输出
      expect(consoleLogs.some(log => log.includes('翻译失败'))).toBe(true);
      expect(consoleLogs.some(log => log.includes('API服务暂时不可用'))).toBe(true);
    });
  });

  describe('引擎降级测试', () => {
    it('应该演示引擎降级过程', async () => {
      console.log('🚀 开始翻译测试：引擎降级');
      
      // 第一次调用失败，第二次成功（模拟引擎降级）
      mockBrowserRuntime.sendMessage
        .mockRejectedValueOnce(new Error('Google API 1 失败'))
        .mockResolvedValueOnce({
          success: true,
          data: { data: { translations: [{ translatedText: '你好' }] } },
          status: 200,
          statusText: 'OK'
        });

      const result = await translateService.translateText('Hello', {
        from: 'en',
        to: 'zh'
      });

      console.log('✅ 引擎降级成功！');
      console.log('📝 原文:', 'Hello');
      console.log('🌟 译文:', result.translations[0]);
      console.log('🔧 最终使用引擎:', result.engine);
      console.log('⚠️ 降级过程: Google API 1 失败 → Google API 2 成功');
      
      expect(result.success).toBe(true);
      expect(result.translations[0]).toBe('你好');
    });
  });

  describe('性能测试', () => {
    it('应该测试大量文本翻译性能', async () => {
      console.log('🚀 开始翻译测试：性能测试');
      
      // 生成大量测试文本
      const texts = Array.from({ length: 100 }, (_, i) => `Test sentence ${i + 1}`);
      
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: true,
        data: [texts.map(text => [[`测试句子 ${text.split(' ')[2]}`, text, null, null, 1]])],
        status: 200,
        statusText: 'OK'
      });

      const startTime = Date.now();
      const result = await translateService.translateTexts(texts, {
        from: 'en',
        to: 'zh',
        engine: 'google'
      });
      const endTime = Date.now();

      console.log('✅ 性能测试完成！');
      console.log('📊 性能统计:');
      console.log('  📝 文本数量:', texts.length);
      console.log('  ⏱️ 总耗时:', endTime - startTime, 'ms');
      console.log('  🚀 平均每条:', Math.round((endTime - startTime) / texts.length), 'ms');
      console.log('  🎯 吞吐量:', Math.round(texts.length / (endTime - startTime) * 1000), 'texts/sec');
      
      expect(result.success).toBe(true);
      expect(result.translations).toHaveLength(100);
      
      // 验证控制台输出
      expect(consoleLogs.some(log => log.includes('性能测试完成'))).toBe(true);
      expect(consoleLogs.some(log => log.includes('性能统计'))).toBe(true);
    });
  });

  describe('服务统计', () => {
    it('应该显示服务统计信息', async () => {
      console.log('🚀 开始翻译测试：服务统计');
      
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: true,
        data: [[[['你好', 'Hello', null, null, 1]]]],
        status: 200,
        statusText: 'OK'
      });

      // 执行一些翻译操作
      await translateService.translateText('Hello');
      await translateService.translateText('World');
      
      const stats = translateService.getServiceStats();
      
      console.log('📊 服务统计信息:');
      console.log('  🎯 总请求数:', stats.totalRequests);
      console.log('  ✅ 成功请求:', stats.successfulRequests);
      console.log('  ❌ 失败请求:', stats.failedRequests);
      console.log('  ⏱️ 平均延迟:', Math.round(stats.averageLatency), 'ms');
      console.log('  🔧 引擎使用:', stats.engineUsage);
      console.log('  📈 成功率:', Math.round(stats.successfulRequests / stats.totalRequests * 100), '%');
      
      expect(stats.totalRequests).toBe(2);
      expect(stats.successfulRequests).toBe(2);
      expect(stats.failedRequests).toBe(0);
      
      // 验证控制台输出
      expect(consoleLogs.some(log => log.includes('服务统计信息'))).toBe(true);
      expect(consoleLogs.some(log => log.includes('成功率'))).toBe(true);
    });
  });

  describe('引擎状态监控', () => {
    it('应该监控引擎状态', async () => {
      console.log('🚀 开始翻译测试：引擎状态监控');
      
      const engines = translateService.getAvailableEngines();
      
      console.log('🔍 可用引擎列表:');
      engines.forEach((engine, index) => {
        console.log(`  ${index + 1}. ${engine}`);
      });
      
      for (const engineName of engines) {
        const isWorking = await translateService.testEngine(engineName);
        console.log(`🔧 ${engineName}:`, isWorking ? '✅ 正常' : '❌ 异常');
      }
      
      const recommended = translateService.getRecommendedEngine();
      console.log('🎯 推荐引擎:', recommended);
      
      expect(engines).toContain('google');
      expect(recommended).toBe('google');
      
      // 验证控制台输出
      expect(consoleLogs.some(log => log.includes('可用引擎列表'))).toBe(true);
      expect(consoleLogs.some(log => log.includes('推荐引擎'))).toBe(true);
    });
  });

  describe('完整工作流演示', () => {
    it('应该演示完整的翻译工作流', async () => {
      console.log('🎬 开始完整翻译工作流演示');
      console.log('=' .repeat(50));
      
      // 步骤1：系统初始化
      console.log('📋 步骤1: 系统初始化');
      const engines = translateService.getAvailableEngines();
      console.log('  ✅ 可用引擎:', engines.join(', '));
      
      // 步骤2：配置检查
      console.log('📋 步骤2: 配置检查');
      const config = translateService.getConfigManager().getConfig();
      console.log('  🔧 默认源语言:', config.defaultSourceLanguage);
      console.log('  🎯 默认目标语言:', config.defaultTargetLanguage);
      console.log('  ⏱️ 全局超时:', config.globalTimeout, 'ms');
      
      // 步骤3：执行翻译
      console.log('📋 步骤3: 执行翻译');
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: true,
        data: [
          [
            [['你好，世界！', 'Hello, World!', null, null, 1]],
            [['这是一个测试。', 'This is a test.', null, null, 1]],
            [['翻译系统工作正常。', 'Translation system works fine.', null, null, 1]]
          ]
        ],
        status: 200,
        statusText: 'OK'
      });
      
      const testTexts = [
        'Hello, World!',
        'This is a test.',
        'Translation system works fine.'
      ];
      
      const result = await translateService.translateTexts(testTexts, {
        from: 'en',
        to: 'zh',
        engine: 'google'
      });
      
      console.log('  ✅ 翻译结果:');
      testTexts.forEach((text, index) => {
        console.log(`    "${text}" → "${result.translations[index]}"`);
      });
      
      // 步骤4：统计报告
      console.log('📋 步骤4: 统计报告');
      const stats = translateService.getServiceStats();
      console.log('  📊 请求统计:', `${stats.successfulRequests}/${stats.totalRequests}`);
      console.log('  ⏱️ 平均延迟:', Math.round(stats.averageLatency), 'ms');
      console.log('  🎯 成功率:', Math.round(stats.successfulRequests / stats.totalRequests * 100), '%');
      
      console.log('=' .repeat(50));
      console.log('🎉 完整工作流演示完成！');
      
      expect(result.success).toBe(true);
      expect(result.translations).toHaveLength(3);
      
      // 验证控制台输出
      expect(consoleLogs.some(log => log.includes('完整翻译工作流演示'))).toBe(true);
      expect(consoleLogs.some(log => log.includes('翻译系统工作正常'))).toBe(true);
      expect(consoleLogs.some(log => log.includes('演示完成'))).toBe(true);
    });
  });
});