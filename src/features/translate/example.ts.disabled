/**
 * 翻译系统使用示例
 * 演示如何使用多引擎翻译架构
 */

import { translateService, TranslateEngine, TranslateOptions, EngineConfig } from './index';

/**
 * 基本使用示例
 */
export async function basicUsageExample() {
  try {
    // 1. 翻译单个文本
    const singleResult = await translateService.translateText('Hello world');
    console.log('Single translation:', singleResult);

    // 2. 批量翻译
    const batchResults = await translateService.translateTexts([
      'Hello world',
      'How are you?',
      'Nice to meet you'
    ]);
    console.log('Batch translations:', batchResults);

    // 3. 指定翻译选项
    const customResult = await translateService.translateText('Hello', {
      from: 'en',
      to: 'zh',
      engine: 'google'
    });
    console.log('Custom translation:', customResult);

  } catch (error) {
    console.error('Translation failed:', error);
  }
}

/**
 * 页面翻译示例
 */
export async function pageTranslationExample() {
  try {
    // 翻译整个页面
    const result = await translateService.translatePage({
      targetLanguage: 'zh',
      excludeSelectors: ['.no-translate', 'code', 'pre']
    });

    console.log(`Page translation completed: ${result.translatedCount}/${result.totalCount} nodes`);

    // 切换显示模式
    translateService.setPageViewMode('dual'); // 双语模式

    // 稍后切换回原文
    setTimeout(() => {
      translateService.setPageViewMode('origin');
    }, 5000);

  } catch (error) {
    console.error('Page translation failed:', error);
  }
}

/**
 * 配置管理示例
 */
export async function configurationExample() {
  const configManager = translateService.getConfigManager();

  try {
    // 1. 设置引擎优先级
    await configManager.setEnginePriority(['google', 'microsoft', 'ai']);

    // 2. 启用/禁用引擎
    await configManager.setEngineEnabled('microsoft', true);

    // 3. 设置默认语言
    await configManager.setDefaultLanguages('auto', 'zh');

    // 4. 更新引擎配置
    await configManager.updateEngineConfig('google', {
      timeout: 15000,
      retryCount: 3
    });

    // 5. 获取当前配置
    const config = configManager.getConfig();
    console.log('Current config:', config);

  } catch (error) {
    console.error('Configuration failed:', error);
  }
}

/**
 * 引擎状态监控示例
 */
export async function monitoringExample() {
  try {
    // 1. 获取所有引擎状态
    const status = await translateService.getEnginesStatus();
    console.log('Engines status:', status);

    // 2. 测试特定引擎
    const isGoogleWorking = await translateService.testEngine('google');
    console.log('Google engine working:', isGoogleWorking);

    // 3. 获取服务统计
    const stats = translateService.getServiceStats();
    console.log('Service stats:', stats);

    // 4. 获取推荐引擎
    const recommendedEngine = translateService.getRecommendedEngine();
    console.log('Recommended engine:', recommendedEngine);

  } catch (error) {
    console.error('Monitoring failed:', error);
  }
}

/**
 * 自定义引擎示例（伪代码）
 */
class CustomTranslateEngine extends TranslateEngine {
  readonly name = 'custom';
  readonly displayName = 'Custom Engine';
  readonly maxChunkSize = 5000;
  readonly maxBatchSize = 100;

  constructor(config: EngineConfig) {
    super(config);
  }

  async translateBatch(texts: string[], options: TranslateOptions) {
    // 自定义引擎实现
    return {
      translations: texts.map(text => `[自定义翻译] ${text}`),
      engine: this.name,
      api: 'custom-api',
      success: true,
      duration: 100
    };
  }
}

/**
 * 注册自定义引擎示例
 */
export function registerCustomEngineExample() {
  // 创建自定义引擎配置
  const customConfig: EngineConfig = {
    name: 'custom',
    enabled: true,
    priority: 4,
    maxChunkSize: 5000,
    maxBatchSize: 100,
    timeout: 10000,
    retryCount: 2,
    apis: [{
      name: 'custom-api',
      url: 'https://api.custom-translate.com',
      priority: 1,
      enabled: true
    }]
  };

  // 注册引擎
  const customEngine = new CustomTranslateEngine(customConfig);
  translateService.registerEngine(customEngine);

  console.log('Custom engine registered');
}

/**
 * 错误处理示例
 */
export async function errorHandlingExample() {
  try {
    // 模拟错误情况
    await translateService.translateText('', { engine: 'nonexistent' });
  } catch (error) {
    if (error.type === 'invalid_input') {
      console.log('Input validation error:', error.message);
    } else if (error.type === 'api_error') {
      console.log('API error:', error.message);
      console.log('Engine:', error.engine);
    } else {
      console.log('Unknown error:', error);
    }
  }
}

/**
 * 完整使用流程示例
 */
export async function fullWorkflowExample() {
  console.log('=== Lucid Translation System Demo ===');

  // 1. 配置系统
  console.log('\n1. Configuring system...');
  await configurationExample();

  // 2. 注册自定义引擎
  console.log('\n2. Registering custom engine...');
  registerCustomEngineExample();

  // 3. 基本翻译
  console.log('\n3. Basic translation...');
  await basicUsageExample();

  // 4. 系统监控
  console.log('\n4. System monitoring...');
  await monitoringExample();

  // 5. 页面翻译（如果在浏览器环境中）
  if (typeof document !== 'undefined') {
    console.log('\n5. Page translation...');
    await pageTranslationExample();
  }

  // 6. 错误处理
  console.log('\n6. Error handling...');
  await errorHandlingExample();

  console.log('\n=== Demo completed ===');
}

// 自动运行示例（可选）
// fullWorkflowExample().catch(console.error);