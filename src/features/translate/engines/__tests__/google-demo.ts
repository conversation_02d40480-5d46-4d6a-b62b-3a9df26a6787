/**
 * Google翻译引擎API1+API2演示
 * 展示降级机制的工作原理
 */

import { GoogleTranslateEngine } from '../google';
import { GoogleEngineConfig } from '../../types';

// 创建演示配置
const config: GoogleEngineConfig = {
  name: 'google',
  enabled: true,
  priority: 1,
  maxChunkSize: 5000,
  maxBatchSize: 128,
  timeout: 10000,
  retryCount: 2,
  apis: [
    {
      name: 'google-translate-api-1',
      url: 'https://translate-pa.googleapis.com/v1/translateHtml',
      priority: 1,
      enabled: true,
      timeout: 10000
    },
    {
      name: 'google-translate-api-2',
      url: 'https://translate.googleapis.com/translate_a/t',
      priority: 2,
      enabled: true,
      timeout: 10000
    }
  ]
};

async function demoGoogleApis() {
  console.log('🚀 Google翻译引擎API1+API2演示');
  
  const engine = new GoogleTranslateEngine(config);
  
  console.log('📋 引擎配置:');
  console.log('- API1: POST translateHtml，支持批量，需要API Key');
  console.log('- API2: POST方式，支持批量，免费接口');
  console.log('- 降级机制: API1失败自动切换到API2');
  console.log('- 缓存支持: 避免重复翻译');
  
  console.log('\n🧪 测试案例:');
  
  try {
    // 测试1: 单个文本翻译
    console.log('\n1. 单个文本翻译测试');
    const result1 = await engine.translateBatch(['Hello'], {
      from: 'en',
      to: 'zh'
    });
    console.log(`原文: "Hello" → 译文: "${result1.translations[0]}"`);
    console.log(`使用引擎: ${result1.engine}, API: ${result1.api}`);
    
    // 测试2: 批量翻译
    console.log('\n2. 批量翻译测试');
    const texts = ['Hello', 'World', 'How are you?'];
    const result2 = await engine.translateBatch(texts, {
      from: 'en',
      to: 'zh'
    });
    console.log('批量翻译结果:');
    texts.forEach((text, i) => {
      console.log(`  "${text}" → "${result2.translations[i]}"`);
    });
    
    // 测试3: 语言检测
    console.log('\n3. 语言检测测试');
    const detectedLang = await engine.detectLanguage('Hello world');
    console.log(`检测到的语言: ${detectedLang}`);
    
    // 测试4: 引擎状态
    console.log('\n4. 引擎状态');
    const status = await engine.getStatus();
    console.log(`引擎名称: ${status.name}`);
    console.log(`是否可用: ${status.available}`);
    console.log(`API数量: ${status.apis.length}`);
    
    // 测试5: 统计信息
    console.log('\n5. 统计信息');
    const stats = engine.getStats();
    console.log(`总请求数: ${stats.totalRequests}`);
    console.log(`成功数: ${stats.successCount}`);
    console.log(`失败数: ${stats.errorCount}`);
    
    console.log('\n✅ 所有测试完成！');
    
  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
    console.error('错误详情:', {
      type: error.type,
      engine: error.engine,
      api: error.api
    });
  }
}

// 演示降级机制
async function demoDegradationMechanism() {
  console.log('\n🔄 降级机制演示');
  
  // 配置1: 只启用API1
  const configApi1Only: GoogleEngineConfig = {
    ...config,
    apis: [
      { ...config.apis[0], enabled: true },
      { ...config.apis[1], enabled: false }
    ]
  };
  
  // 配置2: 只启用API2
  const configApi2Only: GoogleEngineConfig = {
    ...config,
    apis: [
      { ...config.apis[0], enabled: false },
      { ...config.apis[1], enabled: true }
    ]
  };
  
  console.log('\n测试API1单独使用:');
  const engine1 = new GoogleTranslateEngine(configApi1Only);
  try {
    const result1 = await engine1.translateBatch(['Test'], { from: 'en', to: 'zh' });
    console.log(`API1结果: "${result1.translations[0]}" (${result1.api})`);
  } catch (error: any) {
    console.log(`API1失败: ${error.message}`);
  }
  
  console.log('\n测试API2单独使用:');
  const engine2 = new GoogleTranslateEngine(configApi2Only);
  try {
    const result2 = await engine2.translateBatch(['Test'], { from: 'en', to: 'zh' });
    console.log(`API2结果: "${result2.translations[0]}" (${result2.api})`);
  } catch (error: any) {
    console.log(`API2失败: ${error.message}`);
  }
  
  console.log('\n测试自动降级 (API1 → API2):');
  const engineBoth = new GoogleTranslateEngine(config);
  try {
    const result3 = await engineBoth.translateBatch(['Test'], { from: 'en', to: 'zh' });
    console.log(`降级结果: "${result3.translations[0]}" (${result3.api})`);
  } catch (error: any) {
    console.log(`降级失败: ${error.message}`);
  }
}

// 导出演示函数
export { demoGoogleApis, demoDegradationMechanism };

// 如果直接运行此文件
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).demoGoogleApis = demoGoogleApis;
  (window as any).demoDegradationMechanism = demoDegradationMechanism;
  
  console.log('🎮 演示函数已加载到全局对象:');
  console.log('- 运行 demoGoogleApis() 查看基本功能');
  console.log('- 运行 demoDegradationMechanism() 查看降级机制');
}