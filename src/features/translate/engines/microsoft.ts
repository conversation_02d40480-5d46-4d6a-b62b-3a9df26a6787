/**
 * Microsoft翻译引擎实现
 * 支持Microsoft Cognitive Services Translator API
 */

import { TranslateEngine } from './base';
import {
  TranslateOptions,
  TranslateResponse,
  MicrosoftEngineConfig,
  ApiEndpoint,
  TranslateError,
  TranslateErrorType,
  EngineStatus
} from '../types';

export class MicrosoftTranslateEngine extends TranslateEngine {
  readonly name = 'microsoft';
  readonly displayName = 'Microsoft Translator';
  readonly maxChunkSize = 5000;
  readonly maxBatchSize = 100;

  private authToken: string | null = null;
  private tokenExpiry: number = 0;

  constructor(config: MicrosoftEngineConfig) {
    super(config);
  }

  /**
   * 批量翻译实现
   */
  async translateBatch(texts: string[], options: TranslateOptions): Promise<TranslateResponse> {
    this.validateTexts(texts);
    const validatedOptions = this.validateOptions(options);

    const startTime = Date.now();

    try {
      // 确保有有效的认证token
      await this.ensureValidToken();

      // 分块处理
      const chunks = this.chunkTexts(texts);
      const allTranslations: string[] = [];

      // 处理每个块
      for (const chunk of chunks) {
        const chunkTranslations = await this.translateChunkWithFallback(chunk, validatedOptions);
        allTranslations.push(...chunkTranslations);
      }

      const duration = Date.now() - startTime;

      return {
        translations: allTranslations,
        engine: this.name,
        api: this.selectBestApi()?.name || 'unknown',
        success: true,
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      throw new TranslateError(
        this.classifyError(error as Error),
        `Microsoft translation failed: ${(error as Error).message}`,
        this.name,
        undefined,
        error as Error
      );
    }
  }

  /**
   * 带降级的分块翻译
   */
  private async translateChunkWithFallback(
    texts: string[],
    options: TranslateOptions
  ): Promise<string[]> {
    const apis = this.getAvailableApis();

    if (apis.length === 0) {
      throw new TranslateError(
        TranslateErrorType.API_ERROR,
        'No available Microsoft APIs',
        this.name
      );
    }

    let lastError: Error | null = null;

    // 尝试每个API端点
    for (const api of apis) {
      try {
        this.debugLog(`Trying Microsoft API: ${api.name}`);
        return await this.callMicrosoftApi(texts, options, api);

      } catch (error) {
        lastError = error as Error;
        this.debugWarn(`Microsoft API ${api.name} failed:`, error);
        
        // 如果是认证错误，尝试刷新token
        if (this.isAuthError(error as Error)) {
          this.authToken = null;
          this.tokenExpiry = 0;
        }
      }
    }

    // 所有API都失败了
    throw new TranslateError(
      this.classifyError(lastError!),
      `All Microsoft APIs failed. Last error: ${lastError!.message}`,
      this.name,
      undefined,
      lastError!
    );
  }

  /**
   * 调用Microsoft翻译API
   */
  private async callMicrosoftApi(
    texts: string[],
    options: TranslateOptions,
    api: ApiEndpoint
  ): Promise<string[]> {
    // 构建请求
    const url = this.buildMicrosoftUrl(options, api.url);
    const body = this.buildMicrosoftBody(texts);
    const headers = this.buildMicrosoftHeaders();

    this.debugLog(`Microsoft API request to ${url}`);

    // 发送请求
    const response = await this.makeHttpRequest(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
      timeout: api.timeout || this.config.timeout
    });

    // 解析响应
    const data = await response.json();
    return this.parseMicrosoftResponse(data, texts);
  }

  /**
   * 构建Microsoft API URL
   */
  private buildMicrosoftUrl(options: TranslateOptions, baseUrl: string): string {
    const params = new URLSearchParams({
      'api-version': '3.0',
      'from': options.from || 'auto',
      'to': options.to || 'zh-Hans'
    });

    return `${baseUrl}?${params.toString()}`;
  }

  /**
   * 构建Microsoft API请求体
   */
  private buildMicrosoftBody(texts: string[]): Array<{ Text: string }> {
    return texts.map(text => ({ Text: text }));
  }

  /**
   * 构建Microsoft API请求头
   */
  private buildMicrosoftHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': '*/*',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36'
    };

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    return headers;
  }

  /**
   * 解析Microsoft API响应
   */
  private parseMicrosoftResponse(data: any, originalTexts: string[]): string[] {
    if (!Array.isArray(data)) {
      throw new Error('Invalid Microsoft API response format');
    }

    const translations: string[] = [];

    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      
      if (!item || !item.translations || !Array.isArray(item.translations)) {
        this.debugWarn(`Invalid translation item at index ${i}:`, item);
        translations.push(originalTexts[i] || '');
        continue;
      }

      const translation = item.translations[0];
      if (!translation || typeof translation.text !== 'string') {
        this.debugWarn(`Invalid translation at index ${i}:`, translation);
        translations.push(originalTexts[i] || '');
        continue;
      }

      translations.push(translation.text);
    }

    return translations;
  }

  /**
   * 确保有有效的认证token
   */
  private async ensureValidToken(): Promise<void> {
    // 如果token还没过期，直接使用
    if (this.authToken && Date.now() < this.tokenExpiry) {
      return;
    }

    // 获取新的token
    await this.fetchAuthToken();
  }

  /**
   * 获取Microsoft认证token
   */
  private async fetchAuthToken(): Promise<void> {
    try {
      this.debugLog('Fetching Microsoft auth token...');

      const response = await this.makeHttpRequest('https://edge.microsoft.com/translate/auth', {
        method: 'GET',
        headers: {
          'Accept': '*/*',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36'
        },
        timeout: 10000
      });

      this.authToken = await response.text();
      
      // Token通常有效1小时，提前5分钟刷新
      this.tokenExpiry = Date.now() + (55 * 60 * 1000);

      this.debugLog('Microsoft auth token obtained successfully');

    } catch (error) {
      throw new TranslateError(
        TranslateErrorType.AUTHENTICATION_ERROR,
        `Failed to get Microsoft auth token: ${(error as Error).message}`,
        this.name,
        undefined,
        error as Error
      );
    }
  }

  /**
   * 检查是否是认证错误
   */
  private isAuthError(error: Error): boolean {
    const message = error.message.toLowerCase();
    return message.includes('unauthorized') || 
           message.includes('forbidden') || 
           message.includes('auth') ||
           message.includes('token') ||
           message.includes('401') ||
           message.includes('403');
  }

  /**
   * 获取引擎状态
   */
  async getStatus(): Promise<EngineStatus> {
    try {
      const startTime = Date.now();
      
      // 尝试获取认证token
      await this.ensureValidToken();
      
      // 测试简单翻译
      await this.translateBatch(['test'], { from: 'en', to: 'zh' });
      
      const latency = Date.now() - startTime;
      return { 
        name: this.name,
        available: true,
        lastCheck: new Date(),
        apis: this.getAvailableApis(),
        errorCount: this.stats.errorCount,
        successCount: this.stats.successCount
      };

    } catch (error) {
      return { 
        name: this.name,
        available: false,
        lastCheck: new Date(),
        apis: this.getAvailableApis(),
        errorCount: this.stats.errorCount,
        successCount: this.stats.successCount
      };
    }
  }

  /**
   * 分类错误类型
   */
  protected classifyError(error: Error): TranslateErrorType {
    const message = error.message.toLowerCase();

    if (this.isAuthError(error)) {
      return TranslateErrorType.AUTHENTICATION_ERROR;
    }

    if (message.includes('timeout') || message.includes('time out')) {
      return TranslateErrorType.TIMEOUT;
    }

    if (message.includes('network') || message.includes('connection')) {
      return TranslateErrorType.NETWORK_ERROR;
    }

    if (message.includes('rate limit') || message.includes('quota')) {
      return TranslateErrorType.QUOTA_EXCEEDED;
    }

    if (message.includes('api') || message.includes('server')) {
      return TranslateErrorType.API_ERROR;
    }

    return TranslateErrorType.UNKNOWN_ERROR;
  }

  /**
   * 发送HTTP请求
   */
  private async makeHttpRequest(url: string, options: RequestInit & { timeout?: number }): Promise<Response> {
    const { timeout = 10000, ...fetchOptions } = options;

    // 在浏览器扩展环境中，尝试通过background script代理请求
    const runtimeAPI = this.getRuntimeAPI();
    
    if (runtimeAPI) {
      try {
        return await this.makeRequestViaBackground(url, fetchOptions, runtimeAPI);
      } catch (error) {
        this.debugWarn('Background Script request failed:', error);
        // 继续使用直接fetch作为降级
      }
    }

    // 直接发送请求（带超时）
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...fetchOptions,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * 获取Runtime API
   */
  private getRuntimeAPI(): any {
    if (typeof browser !== 'undefined' && browser.runtime) {
      return browser.runtime;
    }
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      return chrome.runtime;
    }
    return null;
  }

  /**
   * 通过background script代理请求
   */
  private async makeRequestViaBackground(url: string, options: RequestInit, runtimeAPI: any): Promise<Response> {
    try {
      const response = await new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error('Request timeout'));
        }, 15000);

        runtimeAPI.sendMessage({
          type: 'PROXY_REQUEST',
          url,
          options: {
            method: options.method || 'GET',
            headers: options.headers,
            body: options.body
          }
        }, (response: any) => {
          clearTimeout(timeoutId);
          
          if (runtimeAPI.lastError) {
            reject(new Error(runtimeAPI.lastError.message));
            return;
          }

          if (!response) {
            reject(new Error('No response from background script'));
            return;
          }

          if (!response.success) {
            reject(new Error(response.error || 'Request failed'));
            return;
          }

          // 创建Response对象
          const responseObj = new Response(response.body, {
            status: response.status,
            statusText: response.statusText,
            headers: response.headers
          });

          resolve(responseObj);
        });
      });

      return response as Response;
    } catch (error) {
      throw new TranslateError(
        TranslateErrorType.NETWORK_ERROR,
        `Background Script request failed: ${(error as Error).message}`,
        this.name
      );
    }
  }
}