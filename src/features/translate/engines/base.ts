/**
 * 翻译引擎抽象基类
 * 定义所有翻译引擎的统一接口
 */

import { 
  TranslateOptions, 
  TranslateResponse, 
  EngineConfig, 
  ApiEndpoint, 
  EngineStatus, 
  TranslateError, 
  TranslateErrorType 
} from '../types';
// debugContent 导入已移除，使用手动 console.log

export abstract class TranslateEngine {
  // 引擎基本信息
  abstract readonly name: string;
  abstract readonly displayName: string;
  abstract readonly maxChunkSize: number;
  abstract readonly maxBatchSize: number;
  
  protected config: EngineConfig;
  protected stats = {
    successCount: 0,
    errorCount: 0,
    totalRequests: 0,
    lastUsed: new Date()
  };

  constructor(config: EngineConfig) {
    this.config = config;
  }

  /**
   * 批量翻译接口 - 子类必须实现
   */
  abstract translateBatch(
    texts: string[], 
    options: TranslateOptions
  ): Promise<TranslateResponse>;

  /**
   * 获取可用的API端点列表
   */
  getAvailableApis(): ApiEndpoint[] {
    return this.config.apis.filter(api => api.enabled);
  }

  /**
   * 选择最佳API端点
   */
  selectBestApi(): ApiEndpoint | null {
    const availableApis = this.getAvailableApis();
    if (availableApis.length === 0) {
      return null;
    }

    // 按优先级排序，选择最高优先级的API
    return availableApis.sort((a, b) => a.priority - b.priority)[0];
  }

  /**
   * 文本分块处理
   */
  protected chunkTexts(texts: string[]): string[][] {
    const chunks: string[][] = [];
    let currentChunk: string[] = [];
    let currentChunkSize = 0;

    for (const text of texts) {
      // 检查单个文本是否超过最大长度
      if (text.length > this.maxChunkSize) {
        // 如果当前块不为空，先处理当前块
        if (currentChunk.length > 0) {
          chunks.push([...currentChunk]);
          currentChunk = [];
          currentChunkSize = 0;
        }
        
        // 将超长文本分割处理
        const splitTexts = this.splitLongText(text);
        for (const splitText of splitTexts) {
          chunks.push([splitText]);
        }
        continue;
      }

      // 检查是否需要开始新的块
      if (currentChunk.length >= this.maxBatchSize || 
          currentChunkSize + text.length > this.maxChunkSize) {
        if (currentChunk.length > 0) {
          chunks.push([...currentChunk]);
          currentChunk = [];
          currentChunkSize = 0;
        }
      }

      currentChunk.push(text);
      currentChunkSize += text.length;
    }

    // 处理最后一个块
    if (currentChunk.length > 0) {
      chunks.push(currentChunk);
    }

    return chunks;
  }

  /**
   * 分割超长文本
   */
  protected splitLongText(text: string): string[] {
    const chunks: string[] = [];
    const maxSize = this.maxChunkSize - 100; // 留一些缓冲

    for (let i = 0; i < text.length; i += maxSize) {
      chunks.push(text.slice(i, i + maxSize));
    }

    return chunks;
  }

  /**
   * 带降级的API调用
   */
  protected async callWithFallback<T>(
    operation: (api: ApiEndpoint) => Promise<T>,
    retryCount: number = this.config.retryCount
  ): Promise<T> {
    const apis = this.getAvailableApis();
    let lastError: Error | null = null;

    for (const api of apis) {
      let attempts = 0;
      
      while (attempts <= retryCount) {
        try {
          const result = await this.withTimeout(
            operation(api), 
            api.timeout || this.config.timeout
          );
          
          // 成功时更新统计
          this.stats.successCount++;
          this.stats.totalRequests++;
          this.stats.lastUsed = new Date();
          
          return result;
        } catch (error) {
          lastError = error as Error;
          attempts++;
          
          // 如果不是最后一次尝试，等待后重试
          if (attempts <= retryCount) {
            await this.delay(1000 * attempts); // 递增延迟
          }
        }
      }
    }

    // 所有API和重试都失败了
    this.stats.errorCount++;
    this.stats.totalRequests++;
    
    throw new TranslateError(
      this.classifyError(lastError),
      `All APIs failed for engine ${this.name}`,
      this.name,
      undefined,
      lastError || undefined
    );
  }

  /**
   * 添加超时控制
   */
  protected async withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new TranslateError(
          TranslateErrorType.TIMEOUT,
          `Operation timed out after ${timeoutMs}ms`,
          this.name
        ));
      }, timeoutMs);
    });

    return Promise.race([promise, timeoutPromise]);
  }

  /**
   * 延迟函数
   */
  protected delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 错误分类
   */
  protected classifyError(error: Error | null): TranslateErrorType {
    if (!error) return TranslateErrorType.UNKNOWN_ERROR;

    const message = error.message.toLowerCase();
    
    if (message.includes('timeout')) {
      return TranslateErrorType.TIMEOUT;
    }
    if (message.includes('network') || message.includes('fetch')) {
      return TranslateErrorType.NETWORK_ERROR;
    }
    if (message.includes('quota') || message.includes('limit')) {
      return TranslateErrorType.QUOTA_EXCEEDED;
    }
    if (message.includes('auth') || message.includes('unauthorized')) {
      return TranslateErrorType.AUTHENTICATION_ERROR;
    }
    if (message.includes('invalid') || message.includes('bad request')) {
      return TranslateErrorType.INVALID_INPUT;
    }

    return TranslateErrorType.API_ERROR;
  }

  /**
   * 验证输入文本
   */
  protected validateTexts(texts: string[]): void {
    if (!Array.isArray(texts) || texts.length === 0) {
      throw new TranslateError(
        TranslateErrorType.INVALID_INPUT,
        'Input texts must be a non-empty array',
        this.name
      );
    }

    for (const text of texts) {
      if (typeof text !== 'string') {
        throw new TranslateError(
          TranslateErrorType.INVALID_INPUT,
          'All texts must be strings',
          this.name
        );
      }
    }
  }

  /**
   * 验证翻译选项
   */
  protected validateOptions(options: TranslateOptions): TranslateOptions {
    const validated: TranslateOptions = {
      from: options.from || 'auto',
      to: options.to || 'zh',
      timeout: options.timeout || this.config.timeout
    };

    return validated;
  }

  /**
   * 获取引擎状态
   */
  async getStatus(): Promise<EngineStatus> {
    const apis = this.getAvailableApis();
    
    return {
      name: this.name,
      available: apis.length > 0 && this.config.enabled,
      lastCheck: new Date(),
      apis: apis,
      errorCount: this.stats.errorCount,
      successCount: this.stats.successCount
    };
  }

  /**
   * 测试引擎连通性
   */
  async testConnection(): Promise<boolean> {
    try {
      const testResult = await this.translateBatch(
        ['Hello'],
        { from: 'en', to: 'zh' }
      );
      return testResult.success;
    } catch {
      return false;
    }
  }

  /**
   * 获取引擎统计信息
   */
  getStats() {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      successCount: 0,
      errorCount: 0,
      totalRequests: 0,
      lastUsed: new Date()
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<EngineConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  getConfig(): EngineConfig {
    return { ...this.config };
  }

  /**
   * 调试日志方法
   */
  protected debugLog(message: string, ...args: any[]): void {
    console.log(`🔧 [translate-engine-${this.name}|DEBUG] ${message}`, ...args);
  }

  protected debugWarn(message: string, ...args: any[]): void {
    console.log(`⚠️ [translate-engine-${this.name}|WARN] ${message}`, ...args);
  }

  protected debugError(message: string, ...args: any[]): void {
    console.log(`❌ [translate-engine-${this.name}|ERROR] ${message}`, ...args);
  }
}