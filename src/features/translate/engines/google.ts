/**
 * Google翻译引擎实现
 * 支持多API端点降级策略
 */

import { TranslateEngine } from './base';
import {
  TranslateOptions,
  TranslateResponse,
  GoogleEngineConfig,
  ApiEndpoint,
  TranslateError,
  TranslateErrorType
} from '../types';

export class GoogleTranslateEngine extends TranslateEngine {
  readonly name = 'google';
  readonly displayName = 'Google Translate';
  readonly maxChunkSize = 5000;
  readonly maxBatchSize = 128;

  constructor(config: GoogleEngineConfig) {
    super(config);
  }

  /**
   * 批量翻译实现
   */
  async translateBatch(texts: string[], options: TranslateOptions): Promise<TranslateResponse> {
    this.validateTexts(texts);
    const validatedOptions = this.validateOptions(options);

    const startTime = Date.now();

    try {
      // 分块处理
      const chunks = this.chunkTexts(texts);
      const allTranslations: string[] = [];

      // 处理每个块
      for (const chunk of chunks) {
        const chunkTranslations = await this.translateChunkWithFallback(chunk, validatedOptions);
        allTranslations.push(...chunkTranslations);
      }

      const duration = Date.now() - startTime;

      return {
        translations: allTranslations,
        engine: this.name,
        api: this.selectBestApi()?.name || 'unknown',
        success: true,
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      throw new TranslateError(
        this.classifyError(error as Error),
        `Google translation failed: ${(error as Error).message}`,
        this.name,
        undefined,
        error as Error
      );
    }
  }

  /**
   * 带降级的分块翻译
   */
  private async translateChunkWithFallback(
    texts: string[],
    options: TranslateOptions
  ): Promise<string[]> {
    const apis = this.getAvailableApis();

    if (apis.length === 0) {
      throw new TranslateError(
        TranslateErrorType.API_ERROR,
        'No available Google APIs',
        this.name
      );
    }

    let lastError: Error | null = null;

    // 尝试每个API端点
    for (const api of apis) {
      try {
        this.debugLog(`Trying Google API: ${api.name}`);

        if (api.name === 'google-translate-api-1') {
          return await this.callGoogleApi1(texts, options, api);
        } else if (api.name === 'google-translate-api-2') {
          return await this.callGoogleApi2(texts, options, api);
        }

      } catch (error) {
        lastError = error as Error;
        this.debugWarn(`Google API ${api.name} failed:`, error);

        // 如果是认证错误，不继续尝试其他API
        if (this.classifyError(error as Error) === TranslateErrorType.AUTHENTICATION_ERROR) {
          break;
        }

        continue;
      }
    }

    // 所有API都失败了
    throw new TranslateError(
      this.classifyError(lastError),
      'All Google APIs failed',
      this.name,
      undefined,
      lastError || undefined
    );
  }

  /**
   * Google API 1 调用实现
   * 🔧 修改：使用普通translate端点而不是translateHtml，避免CORS问题
   */
  private async callGoogleApi1(
    texts: string[],
    options: TranslateOptions,
    api: ApiEndpoint
  ): Promise<string[]> {
    const body = this.buildGoogleApi1Body(texts, options);

    const response = await this.withTimeout(
      this.makeRequest(api.url, {
        method: 'POST',
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Content-Type': 'application/json+protobuf',
          'Origin': 'chrome-extension://bpoadfkcbjbfhfodiogcnhhhpibjhbnh',
          'Priority': 'u=1, i',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'none',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
          'x-goog-api-key': 'AIzaSyATBXajvzQLTDHEQbcpq0Ihe0vWDHmO520'
        },
        body
      }),
      api.timeout || this.config.timeout
    );

    if (!response.ok) {
      throw new TranslateError(
        TranslateErrorType.API_ERROR,
        `Google API 1 HTTP ${response.status}: ${response.statusText}`,
        this.name,
        api.name
      );
    }

    const data = await response.json();
    return this.parseGoogleApi1Response(data, texts);
  }

  /**
   * Google API 2 调用实现
   * 使用 /translate_a/t 端点，支持批量POST请求
   */
  private async callGoogleApi2(
    texts: string[],
    options: TranslateOptions,
    api: ApiEndpoint
  ): Promise<string[]> {
    // API2支持批量请求，但仍需要合理分块
    const translations: string[] = [];
    const chunkSize = Math.min(texts.length, 10); // API2每次最多处理10个文本

    for (let i = 0; i < texts.length; i += chunkSize) {
      const chunk = texts.slice(i, i + chunkSize);
      const chunkTranslations = await this.callGoogleApi2Batch(chunk, options, api);
      translations.push(...chunkTranslations);
    }

    return translations;
  }

  /**
   * Google API 2 批量调用
   */
  private async callGoogleApi2Batch(
    texts: string[],
    options: TranslateOptions,
    api: ApiEndpoint
  ): Promise<string[]> {
    const url = this.buildGoogleApi2Url(options, api.url);
    const body = this.buildGoogleApi2Body(texts, options);

    const response = await this.withTimeout(
      this.makeRequest(url, {
        method: 'POST',
        headers: {
          'Accept': '*/*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          // 🔧 修复CORS问题：移除Cache-Control和Pragma头部
          // 'Cache-Control': 'no-cache',  // 被Google API的CORS政策禁止
          'Content-Type': 'application/x-www-form-urlencoded',
          'Origin': 'chrome-extension://mjdbhokoopacimoekfgkcoogikbfgngb',
          // 'Pragma': 'no-cache',  // 被Google API的CORS政策禁止
          'Priority': 'u=1, i',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'none',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36'
        },
        body
      }),
      api.timeout || this.config.timeout
    );

    if (!response.ok) {
      throw new TranslateError(
        TranslateErrorType.API_ERROR,
        `Google API 2 HTTP ${response.status}: ${response.statusText}`,
        this.name,
        api.name
      );
    }

    const data = await response.json();
    return this.parseGoogleApi2Response(data, texts);
  }

  /**
   * 构建Google API 1请求体 (纯文本格式)
   * 🔧 修复：移除HTML格式支持，只处理纯文本
   */
  private buildGoogleApi1Body(texts: string[], options: TranslateOptions): string {
    // 🔧 修复：忽略格式参数，始终使用纯文本格式
    if (options.format === 'html') {
      console.warn('🔧 Google引擎: HTML格式已被忽略，强制使用纯文本格式以确保架构清晰性');
    }

    // 始终使用标准纯文本格式
    const requestData = [
      [
        texts,
        options.from || 'en',
        options.to || 'zh-CN'
      ],
      'te_lib'  // 使用标准翻译API
    ];

    if ((this.config as any).debug) {
      console.log('🔗 构建纯文本翻译请求:', {
        textsCount: texts.length,
        format: 'text', // 强制显示为text
        from: options.from || 'en',
        to: options.to || 'zh-CN',
        firstText: texts[0]?.slice(0, 100) + '...'
      });
    }

    return JSON.stringify(requestData);
  }

  /**
   * 构建Google API 2请求URL
   * 🔧 修复：强制使用纯文本格式
   */
  private buildGoogleApi2Url(options: TranslateOptions, baseUrl: string): string {
    // 🔧 修复：忽略格式参数，始终使用纯文本
    if (options.format === 'html') {
      console.warn('🔧 Google引擎 API2: HTML格式已被忽略，强制使用纯文本格式');
    }

    const params = new URLSearchParams({
      client: 'gtx',
      dt: 't',
      sl: options.from || 'auto',
      tl: options.to || 'zh-CN',
      format: 'text' // 强制使用纯文本格式
    });
    return `${baseUrl}?${params}`;
  }

  /**
   * 构建Google API 2请求体
   */
  private buildGoogleApi2Body(texts: string[], options: TranslateOptions): string {
    const params = new URLSearchParams();

    // 添加每个文本作为q参数
    texts.forEach(text => {
      params.append('q', text);
    });

    // 如果只有一个文本且很短，添加一个空的q参数（模拟原始请求）
    if (texts.length === 1 && texts[0].length < 100) {
      params.append('q', '​'); // 零宽空格
    }

    return params.toString();
  }

  /**
   * 解析Google API 1响应 (普通translate格式)
   */
  private parseGoogleApi1Response(data: any, originalTexts: string[]): string[] {
    try {
      // Google translate API的响应格式解析
      if (!data || !Array.isArray(data) || data.length === 0) {
        this.debugWarn('Invalid API 1 response format, returning original texts');
        return originalTexts;
      }

      if ((this.config as any).debug) {
        console.log('🔗 解析Google API 1响应:', {
          dataType: typeof data,
          isArray: Array.isArray(data),
          dataLength: data.length,
          firstItemType: data[0] ? typeof data[0] : 'undefined',
          originalTextsCount: originalTexts.length
        });
      }

      const translations: string[] = [];

      // 🔧 修复：改进普通translate API响应解析逻辑
      // 基于实际API响应格式进行解析
      if (Array.isArray(data) && data.length > 0) {
        // 情况1：响应是字符串数组 ["翻译结果1", "翻译结果2"]
        if (typeof data[0] === 'string') {
          data.forEach((translation, index) => {
            const translatedText = translation || originalTexts[index] || '';
            translations.push(translatedText);

            if ((this.config as any).debug && index < 3) { // 只显示前3个，避免日志过多
              console.log(`🔗 翻译结果 ${index + 1}:`, {
                original: originalTexts[index]?.slice(0, 50) + '...',
                translated: translatedText.slice(0, 50) + '...',
                hasHtml: translatedText.includes('<a ')
              });
            }
          });
        }
        // 情况2：响应是嵌套数组格式 [["翻译结果1", "翻译结果2"]]
        else if (Array.isArray(data[0])) {
          data[0].forEach((translation: string, index: number) => {
            const translatedText = translation || originalTexts[index] || '';
            translations.push(translatedText);

            if ((this.config as any).debug && index < 3) {
              console.log(`🔗 翻译结果 ${index + 1}:`, {
                original: originalTexts[index]?.slice(0, 50) + '...',
                translated: translatedText.slice(0, 50) + '...',
                hasHtml: translatedText.includes('<a ')
              });
            }
          });
        }
        // 情况3：复杂的嵌套结构，尝试深度解析
        else {
          this.debugWarn('Unexpected API 1 response structure, attempting deep parse');
          // 尝试递归查找翻译结果
          const extractedTranslations = this.extractTranslationsFromComplexResponse(data, originalTexts);
          translations.push(...extractedTranslations);
        }
      }

      // 确保返回的翻译数量与原文数量一致
      while (translations.length < originalTexts.length) {
        translations.push(originalTexts[translations.length]);
      }

      const result = translations.slice(0, originalTexts.length);

      if ((this.config as any).debug) {
        console.log('🔗 API 1解析完成:', {
          inputCount: originalTexts.length,
          outputCount: result.length,
          hasHtmlResults: result.some(t => t.includes('<a '))
        });
      }

      return result;

    } catch (error) {
      this.debugWarn('Failed to parse Google API 1 response, returning original texts:', error);
      return originalTexts;
    }
  }

  /**
   * 从复杂响应结构中提取翻译结果
   * 🔧 处理Google API可能返回的各种复杂嵌套结构
   */
  private extractTranslationsFromComplexResponse(data: any, originalTexts: string[]): string[] {
    const translations: string[] = [];

    // 递归搜索翻译结果
    const findTranslations = (obj: any, depth: number = 0): string[] => {
      if (depth > 5) return []; // 防止无限递归

      if (typeof obj === 'string') {
        return [obj];
      }

      if (Array.isArray(obj)) {
        const results: string[] = [];
        for (const item of obj) {
          results.push(...findTranslations(item, depth + 1));
        }
        return results;
      }

      if (typeof obj === 'object' && obj !== null) {
        const results: string[] = [];
        for (const value of Object.values(obj)) {
          results.push(...findTranslations(value, depth + 1));
        }
        return results;
      }

      return [];
    };

    const foundTranslations = findTranslations(data);

    // 过滤出可能的翻译结果（长度合理的字符串）
    const validTranslations = foundTranslations.filter(t =>
      typeof t === 'string' && t.length > 0 && t.length < 10000
    );

    // 取前N个结果，N为原文数量
    for (let i = 0; i < originalTexts.length && i < validTranslations.length; i++) {
      translations.push(validTranslations[i]);
    }

    if ((this.config as any).debug) {
      console.log('🔗 复杂响应解析结果:', {
        foundCount: foundTranslations.length,
        validCount: validTranslations.length,
        extractedCount: translations.length
      });
    }

    return translations;
  }

  /**
   * 解析Google API 2响应
   */
  private parseGoogleApi2Response(data: any, originalTexts: string[]): string[] {
    try {
      // API 2的响应格式通常与API 1相同：嵌套数组格式
      if (!data || !Array.isArray(data) || data.length === 0) {
        this.debugWarn('Invalid API 2 response format, returning original texts');
        return originalTexts;
      }

      const translations: string[] = [];

      if ((this.config as any).debug) {
        console.log('🔗 解析Google API 2响应:', {
          dataType: typeof data,
          isArray: Array.isArray(data),
          dataLength: data.length,
          firstItemType: data[0] ? typeof data[0] : 'undefined',
          originalTextsCount: originalTexts.length
        });
      }

      // Google API 2的响应格式通常是: [[翻译结果数组]]
      if (Array.isArray(data[0])) {
        // 处理标准的嵌套数组响应格式
        data[0].forEach((translation: any, index: number) => {
          if (Array.isArray(translation) && translation.length > 0) {
            // 格式: ['翻译结果', '原文', null, null, 1]
            const translatedText = translation[0] || originalTexts[index] || '';
            translations.push(translatedText);
          } else if (typeof translation === 'string') {
            // 直接字符串格式
            translations.push(translation);
          } else {
            // 无法解析，使用原文
            translations.push(originalTexts[index] || '');
          }

          if ((this.config as any).debug && index < 3) {
            console.log(`🔗 API 2 翻译结果 ${index + 1}:`, {
              original: originalTexts[index]?.slice(0, 50) + '...',
              translated: translations[index]?.slice(0, 50) + '...'
            });
          }
        });
      } else {
        // 非标准格式，尝试从复杂响应中提取
        this.debugWarn('Unexpected API 2 response structure, attempting extraction');
        const extractedTranslations = this.extractTranslationsFromComplexResponse(data, originalTexts);
        translations.push(...extractedTranslations);
      }

      // 确保返回的翻译数量与原文数量一致
      while (translations.length < originalTexts.length) {
        translations.push(originalTexts[translations.length]);
      }

      const result = translations.slice(0, originalTexts.length);

      if ((this.config as any).debug) {
        console.log('🔗 API 2解析完成:', {
          inputCount: originalTexts.length,
          outputCount: result.length
        });
      }

      return result;

    } catch (error) {
      this.debugWarn('Failed to parse Google API 2 response, returning original texts:', error);
      return originalTexts;
    }
  }

  /**
   * 发送HTTP请求
   */
  private async makeRequest(url: string, options: RequestInit): Promise<Response> {
    // 在浏览器扩展环境中，优先尝试通过background script代理请求
    const runtimeAPI = this.getRuntimeAPI();
    if (runtimeAPI) {
      // 实施重试机制，最多重试3次
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          return await this.makeRequestViaBackground(url, options, runtimeAPI);
        } catch (error) {
          this.debugWarn(`Background Script request failed (attempt ${attempt}/3):`, error);
          
          // 如果是最后一次尝试，抛出明确的错误而不是降级为直接fetch
          if (attempt === 3) {
            throw new TranslateError(
              TranslateErrorType.NETWORK_ERROR,
              `Background script communication failed after 3 attempts. This may be due to the service worker being terminated. Please try refreshing the page or restarting the extension.`,
              this.name
            );
          }
          
          // 在重试之间添加短暂延迟，以给service worker重启时间
          await new Promise(resolve => setTimeout(resolve, 100 * attempt));
        }
      }
    }

    // 直接发送请求（仅用于测试或非扩展环境）
    // 注意：在内容脚本中这会被CORS阻止，但在测试环境中可能有效
    return await fetch(url, options);
  }

  /**
   * 获取运行时API (兼容browser和chrome)
   */
  private getRuntimeAPI(): any {
    if (typeof browser !== 'undefined' && browser.runtime) {
      return browser.runtime;
    }
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      return chrome.runtime;
    }
    return null;
  }

  /**
   * 通过background script代理请求
   */
  private async makeRequestViaBackground(url: string, options: RequestInit, runtimeAPI: any): Promise<Response> {
    try {
      const response = await new Promise((resolve, reject) => {
        runtimeAPI.sendMessage({
          type: 'TRANSLATE_REQUEST',
          payload: {
            url,
            options: {
              method: options.method,
              headers: options.headers,
              body: options.body
            }
          }
        }, (response: any) => {
          if (runtimeAPI.lastError) {
            reject(new Error(runtimeAPI.lastError.message));
          } else {
            resolve(response);
          }
        });
      });

      if (!(response as any).success) {
        throw new Error((response as any).error || 'Background request failed');
      }

      // 构造Response对象
      return new Response(JSON.stringify((response as any).data), {
        status: (response as any).status || 200,
        statusText: (response as any).statusText || 'OK',
        headers: (response as any).headers || {}
      });

    } catch (error) {
      throw new TranslateError(
        TranslateErrorType.NETWORK_ERROR,
        `Background request failed: ${(error as Error).message}`,
        this.name
      );
    }
  }

  /**
   * 获取语言检测结果
   */
  async detectLanguage(text: string): Promise<string> {
    if (!text || text.trim().length === 0) {
      return 'auto';
    }

    try {
      const response = await this.translateBatch([text], {
        from: 'auto',
        to: 'en'
      });

      // 从响应中提取检测到的语言
      // 这里简化实现，实际需要根据API响应格式来解析
      return 'auto';

    } catch (error) {
      this.debugWarn('Language detection failed:', error);
      return 'auto';
    }
  }

  /**
   * 获取支持的语言列表
   */
  getSupportedLanguages(): string[] {
    return [
      'auto', 'en', 'zh', 'zh-cn', 'zh-tw', 'ja', 'ko', 'fr', 'de', 'es', 'it', 'pt', 'ru',
      'ar', 'hi', 'th', 'vi', 'id', 'ms', 'fil', 'nl', 'sv', 'da', 'no', 'fi', 'pl', 'cs',
      'sk', 'hu', 'ro', 'bg', 'hr', 'sr', 'sl', 'et', 'lv', 'lt', 'mt', 'cy', 'ga', 'eu',
      'ca', 'gl', 'ast', 'oc', 'br', 'co', 'fy', 'gd', 'is', 'lb', 'mk', 'sq', 'az', 'be',
      'ka', 'ky', 'kk', 'mn', 'tg', 'tk', 'uz', 'hy', 'he', 'yi', 'fa', 'ur', 'ps', 'sd',
      'ne', 'si', 'bn', 'gu', 'kn', 'ml', 'or', 'pa', 'ta', 'te', 'as', 'mni', 'my', 'km',
      'lo', 'ka', 'am', 'sw', 'zu', 'af', 'sq', 'eu', 'be', 'bn', 'bg', 'ca', 'hr', 'cs',
      'da', 'nl', 'en', 'et', 'fi', 'fr', 'gl', 'de', 'el', 'gu', 'ht', 'he', 'hi', 'hu',
      'is', 'id', 'ga', 'it', 'ja', 'kn', 'ko', 'lv', 'lt', 'mk', 'ms', 'mt', 'no', 'fa',
      'pl', 'pt', 'ro', 'ru', 'sr', 'sk', 'sl', 'es', 'sw', 'sv', 'ta', 'te', 'th', 'tr',
      'uk', 'ur', 'vi', 'cy', 'yi'
    ];
  }

  /**
   * 验证语言代码
   */
  private isValidLanguageCode(code: string): boolean {
    return this.getSupportedLanguages().includes(code);
  }

  /**
   * 验证翻译选项
   */
  protected validateOptions(options: TranslateOptions): TranslateOptions {
    const validated = super.validateOptions(options);

    // 验证语言代码
    if (validated.from && validated.from !== 'auto' && !this.isValidLanguageCode(validated.from)) {
      this.debugWarn(`Invalid source language: ${validated.from}, using 'auto'`);
      validated.from = 'auto';
    }

    if (validated.to && !this.isValidLanguageCode(validated.to)) {
      this.debugWarn(`Invalid target language: ${validated.to}, using 'zh'`);
      validated.to = 'zh';
    }

    return validated;
  }

  /**
   * 获取引擎特定的配置
   */
  getGoogleConfig(): GoogleEngineConfig {
    return this.config as GoogleEngineConfig;
  }

  /**
   * 更新Google特定配置
   */
  updateGoogleConfig(updates: Partial<GoogleEngineConfig>): void {
    this.updateConfig(updates);
  }
}