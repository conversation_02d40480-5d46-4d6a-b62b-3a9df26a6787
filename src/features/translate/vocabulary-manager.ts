/**
 * 词汇管理器
 * 负责管理翻译词汇库和语言映射
 */

export interface VocabularyEntry {
  /** 原文 */
  original: string;
  /** 翻译 */
  translation: string;
  /** 语言对 (如 'en-zh') */
  languagePair: string;
  /** 词性 */
  partOfSpeech?: string;
  /** 使用频率 */
  frequency?: number;
}

export class VocabularyManager {
  private vocabularies = new Map<string, Map<string, VocabularyEntry>>();
  private phoneticMappings = new Map<string, string>();

  constructor() {
    this.initializeDefaultVocabularies();
  }

  /**
   * 初始化默认词汇库
   */
  private initializeDefaultVocabularies(): void {
    // 英译中基础词汇
    const enZhVocab = new Map<string, VocabularyEntry>();
    
    const basicWords = [
      ['hello', '你好'], ['world', '世界'], ['test', '测试'], ['page', '页面'],
      ['google', '谷歌'], ['chrome', '浏览器'], ['extension', '扩展'],
      ['favorite', '最喜欢的'], ['our', '我们的'], ['extensions', '扩展程序'],
      ['the', '的'], ['and', '和'], ['to', '到'], ['of', '的'], ['a', '一个'],
      ['in', '在'], ['is', '是'], ['you', '你'], ['that', '那个'], ['it', '它'],
      ['with', '与'], ['for', '为了'], ['as', '作为'], ['was', '是'], ['on', '在'],
      ['are', '是'], ['this', '这个'], ['be', '是'], ['at', '在'], ['by', '通过'],
      ['not', '不'], ['or', '或者'], ['have', '有'], ['from', '从'], ['they', '他们'],
      ['we', '我们'], ['an', '一个'], ['your', '你的'], ['all', '所有'], ['can', '能够'],
      ['but', '但是'], ['will', '将'], ['if', '如果'], ['about', '关于'], ['up', '向上'],
      ['out', '出去'], ['time', '时间'], ['get', '得到'], ['when', '当'], ['new', '新的'],
      ['now', '现在'], ['way', '方式'], ['may', '可能'], ['say', '说'], ['use', '使用'],
      ['how', '如何'], ['work', '工作'], ['first', '第一'], ['good', '好的'], ['people', '人们'],
      ['over', '超过'], ['other', '其他'], ['make', '制作'], ['day', '天'], ['see', '看见'],
      ['him', '他'], ['only', '只有'], ['its', '它的'], ['after', '之后'], ['back', '回来'],
      ['man', '男人'], ['year', '年'], ['years', '年'], ['come', '来'], ['could', '能够'],
      ['know', '知道'], ['take', '拿'], ['than', '比'], ['find', '找到'], ['think', '思考'],
      ['also', '也'], ['go', '去'], ['well', '好'], ['two', '二'], ['want', '想要'],
      ['look', '看'], ['right', '右'], ['where', '哪里'], ['still', '仍然'], ['like', '喜欢'],
      ['give', '给'], ['should', '应该'], ['being', '存在'], ['through', '通过'], ['last', '最后'],
      ['before', '之前'], ['here', '这里'], ['turn', '转'], ['why', '为什么'], ['ask', '问'],
      ['men', '男人们'], ['old', '老的'], ['try', '尝试'], ['tell', '告诉'], ['call', '呼叫'],
      ['hand', '手'], ['high', '高'], ['keep', '保持'], ['move', '移动'], ['own', '自己的'],
      ['under', '在下面'], ['leave', '离开'], ['never', '从不'], ['help', '帮助'], ['home', '家'],
      ['around', '周围'], ['while', '当'], ['place', '地方'], ['made', '制作'], ['great', '伟大的'],
      ['small', '小的'], ['put', '放'], ['end', '结束'], ['why', '为什么'], ['again', '再次'],
      ['both', '两个'], ['does', '做'], ['another', '另一个'], ['even', '甚至'], ['must', '必须'],
      ['because', '因为'], ['same', '相同的'], ['those', '那些'], ['feel', '感觉'], ['three', '三'],
      ['state', '状态'], ['never', '从不'], ['become', '成为'], ['between', '之间'], ['high', '高'],
      ['really', '真的'], ['something', '某事'], ['most', '最多'], ['another', '另一个'], ['much', '很多'],
      ['family', '家庭'], ['own', '自己的'], ['out', '出去'], ['leave', '离开'], ['put', '放'],
      ['old', '老的'], ['while', '当'], ['mean', '意思'], ['on', '在'], ['keep', '保持'],
      ['student', '学生'], ['why', '为什么'], ['let', '让'], ['great', '伟大的'], ['same', '相同的'],
      ['big', '大的'], ['group', '组'], ['begin', '开始'], ['seem', '似乎'], ['country', '国家'],
      ['help', '帮助'], ['talk', '谈话'], ['where', '哪里'], ['turn', '转'], ['problem', '问题'],
      ['every', '每个'], ['start', '开始'], ['hand', '手'], ['might', '可能'], ['american', '美国的'],
      ['show', '显示'], ['part', '部分'], ['about', '关于'], ['against', '反对'], ['place', '地方'],
      ['over', '超过'], ['such', '这样的'], ['again', '再次'], ['few', '少数'], ['case', '情况'],
      ['most', '最多'], ['week', '周'], ['company', '公司'], ['where', '哪里'], ['system', '系统'],
      ['each', '每个'], ['right', '右'], ['program', '程序'], ['hear', '听'], ['so', '所以'],
      ['question', '问题'], ['during', '期间'], ['work', '工作'], ['play', '玩'], ['government', '政府'],
      ['run', '跑'], ['small', '小的'], ['number', '数字'], ['off', '关闭'], ['always', '总是']
    ];

    basicWords.forEach(([en, zh]) => {
      enZhVocab.set(en.toLowerCase(), {
        original: en,
        translation: zh,
        languagePair: 'en-zh',
        frequency: 1
      });
    });

    this.vocabularies.set('en-zh', enZhVocab);

    // 语音映射（英文到中文音译）
    const phoneticMappings = [
      ['google', '谷歌'], ['facebook', '脸书'], ['twitter', '推特'],
      ['youtube', '油管'], ['amazon', '亚马逊'], ['microsoft', '微软'],
      ['apple', '苹果'], ['tesla', '特斯拉'], ['uber', '优步'],
      ['starbucks', '星巴克'], ['mcdonalds', '麦当劳'], ['kfc', 'KFC']
    ];

    phoneticMappings.forEach(([en, zh]) => {
      this.phoneticMappings.set(en.toLowerCase(), zh);
    });
  }

  /**
   * 查找翻译
   */
  findTranslation(text: string, fromLang: string = 'en', toLang: string = 'zh'): string | null {
    const languagePair = `${fromLang}-${toLang}`;
    const vocab = this.vocabularies.get(languagePair);
    
    if (!vocab) {
      return null;
    }

    const entry = vocab.get(text.toLowerCase());
    return entry ? entry.translation : null;
  }

  /**
   * 查找语音映射
   */
  findPhoneticMapping(text: string): string | null {
    return this.phoneticMappings.get(text.toLowerCase()) || null;
  }

  /**
   * 生成中文翻译（包含词汇查找和音译）
   */
  generateChineseTranslation(text: string): string {
    // 清理文本
    const cleanText = text.trim().toLowerCase();
    
    if (!cleanText) {
      return text;
    }

    // 处理简单的标点和数字
    if (/^[0-9.,\s\-+=%$@#!?;:'"()\[\]{}\/\\]+$/.test(cleanText)) {
      return text; // 保持原样
    }

    // 分词处理
    const words = cleanText.split(/\s+/);
    const translatedWords: string[] = [];

    for (const word of words) {
      if (!word.trim()) continue;

      // 清理单词中的标点
      const cleanWord = word.replace(/[^\w]/g, '');
      if (!cleanWord) {
        translatedWords.push(word); // 保持标点
        continue;
      }

      // 1. 查找词汇库
      const vocabTranslation = this.findTranslation(cleanWord);
      if (vocabTranslation) {
        translatedWords.push(vocabTranslation);
        continue;
      }

      // 2. 查找语音映射
      const phoneticTranslation = this.findPhoneticMapping(cleanWord);
      if (phoneticTranslation) {
        translatedWords.push(phoneticTranslation);
        continue;
      }

      // 3. 生成音译或标记
      if (cleanWord.length <= 2) {
        // 短词直接标记
        translatedWords.push(`[${cleanWord}]`);
      } else if (cleanWord.length <= 5) {
        // 中等长度词尝试音译
        translatedWords.push(this.generatePhoneticTranslation(cleanWord));
      } else {
        // 长词标记翻译
        translatedWords.push(`[${cleanWord}译]`);
      }
    }

    return translatedWords.join(' ');
  }

  /**
   * 生成语音翻译
   */
  private generatePhoneticTranslation(word: string): string {
    // 简单的语音映射规则
    const phoneticRules = new Map([
      ['tion', '申'], ['ing', '英'], ['er', '尔'], ['ly', '利'],
      ['al', '尔'], ['or', '尔'], ['ic', '克'], ['ed', '德'],
      ['le', '勒'], ['ty', '蒂'], ['ry', '里'], ['ny', '尼'],
      ['th', '思'], ['ch', '奇'], ['sh', '什'], ['ph', '夫'],
      ['gh', '格'], ['ck', '克'], ['ng', '恩'], ['st', '斯特']
    ]);

    let result = word;
    
    // 应用语音规则
    for (const [pattern, replacement] of phoneticRules) {
      if (result.endsWith(pattern)) {
        result = result.slice(0, -pattern.length) + replacement;
        break;
      }
    }

    // 如果没有匹配，简单标记
    if (result === word) {
      result = `[${word}音]`;
    }

    return result;
  }

  /**
   * 添加自定义词汇
   */
  addVocabulary(
    original: string, 
    translation: string, 
    fromLang: string = 'en', 
    toLang: string = 'zh'
  ): void {
    const languagePair = `${fromLang}-${toLang}`;
    
    if (!this.vocabularies.has(languagePair)) {
      this.vocabularies.set(languagePair, new Map());
    }

    const vocab = this.vocabularies.get(languagePair)!;
    vocab.set(original.toLowerCase(), {
      original,
      translation,
      languagePair,
      frequency: 1
    });
  }

  /**
   * 添加语音映射
   */
  addPhoneticMapping(original: string, phonetic: string): void {
    this.phoneticMappings.set(original.toLowerCase(), phonetic);
  }

  /**
   * 获取词汇统计
   */
  getVocabularyStats(): {
    totalVocabularies: number;
    totalEntries: number;
    phoneticMappings: number;
    languagePairs: string[];
  } {
    const totalEntries = Array.from(this.vocabularies.values())
      .reduce((total, vocab) => total + vocab.size, 0);

    return {
      totalVocabularies: this.vocabularies.size,
      totalEntries,
      phoneticMappings: this.phoneticMappings.size,
      languagePairs: Array.from(this.vocabularies.keys())
    };
  }

  /**
   * 清空所有词汇
   */
  clear(): void {
    this.vocabularies.clear();
    this.phoneticMappings.clear();
    this.initializeDefaultVocabularies();
  }
}