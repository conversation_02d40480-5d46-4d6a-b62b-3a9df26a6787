/**
 * 翻译引擎管理器
 * 统一管理所有翻译引擎，处理引擎注册、选择、降级等逻辑
 */

import { TranslateEngine } from './engines/base';
import { TranslateConfigManager } from './config';
import { 
  TranslateOptions, 
  TranslateResponse, 
  EngineStatus, 
  TranslateError, 
  TranslateErrorType 
} from './types';

export class TranslateEngineManager {
  private engines: Map<string, TranslateEngine> = new Map();
  private configManager: TranslateConfigManager;
  private healthCheckInterval: number | null = null;
  private readonly HEALTH_CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟

  constructor(configManager: TranslateConfigManager) {
    this.configManager = configManager;
    // 不自动启动健康检查，等待视图模式控制器决定
  }

  /**
   * 注册翻译引擎
   */
  registerEngine(engine: TranslateEngine): void {
    this.engines.set(engine.name, engine);
    console.log(`✅ [translate-engine|REGISTER] 翻译引擎已注册: ${engine.name}`);
  }

  /**
   * 注销翻译引擎
   */
  unregisterEngine(engineName: string): void {
    this.engines.delete(engineName);
    console.log(`Translate engine unregistered: ${engineName}`);
  }

  /**
   * 获取引擎实例
   */
  getEngine(engineName: string): TranslateEngine | null {
    return this.engines.get(engineName) || null;
  }

  /**
   * 获取所有已注册的引擎名称
   */
  getRegisteredEngines(): string[] {
    return Array.from(this.engines.keys());
  }

  /**
   * 获取可用的引擎列表（已注册且已启用）
   */
  getAvailableEngines(): string[] {
    const enabledEngines = this.configManager.getEnabledEngines();
    return enabledEngines.filter(name => this.engines.has(name));
  }

  /**
   * 按优先级获取引擎列表
   */
  getEnginesByPriority(): string[] {
    const available = this.getAvailableEngines();
    const priority = this.configManager.getEnginePriority();
    
    // 按配置的优先级排序
    return priority.filter(name => available.includes(name));
  }

  /**
   * 主要翻译接口 - 带引擎降级
   */
  async translate(texts: string[], options: TranslateOptions = {}): Promise<TranslateResponse> {
    if (!texts || texts.length === 0) {
      throw new TranslateError(
        TranslateErrorType.INVALID_INPUT,
        'Input texts cannot be empty'
      );
    }

    const enginePriority = options.engine 
      ? [options.engine]  // 如果指定了引擎，只使用该引擎
      : this.getEnginesByPriority();

    if (enginePriority.length === 0) {
      throw new TranslateError(
        TranslateErrorType.API_ERROR,
        'No available translation engines'
      );
    }

    let lastError: Error | null = null;

    // 按优先级尝试每个引擎
    for (const engineName of enginePriority) {
      const engine = this.engines.get(engineName);
      if (!engine) {
        console.warn(`Engine ${engineName} not found, skipping`);
        continue;
      }

      try {
        console.log(`Attempting translation with engine: ${engineName}`);
        const startTime = Date.now();
        
        const response = await engine.translateBatch(texts, options);
        
        const duration = Date.now() - startTime;
        console.log(`Translation completed with ${engineName} in ${duration}ms`);

        return {
          ...response,
          engine: engineName,
          duration
        };

      } catch (error) {
        lastError = error as Error;
        console.warn(`Engine ${engineName} failed:`, error);
        
        // 如果明确指定了引擎且失败，不继续尝试其他引擎
        if (options.engine) {
          break;
        }

        // 如果禁用了降级功能，不继续尝试
        if (!this.configManager.getConfig().enableFallback) {
          break;
        }

        // 某些错误类型不适合降级（如输入错误）
        if (this.shouldSkipFallback(error as Error)) {
          break;
        }

        continue;
      }
    }

    // 所有引擎都失败了
    const errorMessage = options.engine 
      ? `Translation failed with engine ${options.engine}`
      : 'All translation engines failed';

    throw new TranslateError(
      lastError ? this.classifyError(lastError) : TranslateErrorType.UNKNOWN_ERROR,
      errorMessage,
      undefined,
      undefined,
      lastError || undefined
    );
  }

  /**
   * 批量翻译（处理大量文本）
   */
  async translateBatch(
    textBatches: string[][], 
    options: TranslateOptions = {}
  ): Promise<TranslateResponse[]> {
    const results: TranslateResponse[] = [];
    const config = this.configManager.getConfig();
    
    for (let i = 0; i < textBatches.length; i++) {
      const batch = textBatches[i];
      
      try {
        const result = await this.translate(batch, options);
        results.push(result);
        
        // 添加请求间延迟，避免频率限制
        if (i < textBatches.length - 1 && config.requestDelay > 0) {
          await this.delay(config.requestDelay);
        }
      } catch (error) {
        // 批量处理中的错误处理
        results.push({
          translations: batch.map(() => ''), // 返回空翻译
          engine: 'unknown',
          api: 'unknown',
          success: false,
          error: (error as Error).message,
          duration: 0
        });
      }
    }

    return results;
  }

  /**
   * 判断是否应该跳过降级
   */
  private shouldSkipFallback(error: Error): boolean {
    const errorType = this.classifyError(error);
    
    // 这些错误类型通常不适合降级到其他引擎
    return [
      TranslateErrorType.INVALID_INPUT,
      TranslateErrorType.AUTHENTICATION_ERROR
    ].includes(errorType);
  }

  /**
   * 错误分类
   */
  private classifyError(error: Error): TranslateErrorType {
    if (error instanceof TranslateError) {
      return error.type;
    }

    const message = error.message.toLowerCase();
    
    if (message.includes('timeout')) {
      return TranslateErrorType.TIMEOUT;
    }
    if (message.includes('network') || message.includes('fetch')) {
      return TranslateErrorType.NETWORK_ERROR;
    }
    if (message.includes('quota') || message.includes('limit')) {
      return TranslateErrorType.QUOTA_EXCEEDED;
    }
    if (message.includes('auth') || message.includes('unauthorized')) {
      return TranslateErrorType.AUTHENTICATION_ERROR;
    }
    if (message.includes('invalid') || message.includes('bad request')) {
      return TranslateErrorType.INVALID_INPUT;
    }

    return TranslateErrorType.API_ERROR;
  }

  /**
   * 获取所有引擎状态
   */
  async getEnginesStatus(): Promise<Record<string, EngineStatus>> {
    const status: Record<string, EngineStatus> = {};
    
    for (const [name, engine] of this.engines) {
      try {
        status[name] = await engine.getStatus();
      } catch (error) {
        status[name] = {
          name,
          available: false,
          lastCheck: new Date(),
          apis: [],
          errorCount: 0,
          successCount: 0
        };
      }
    }

    return status;
  }

  /**
   * 测试特定引擎
   */
  async testEngine(engineName: string): Promise<boolean> {
    const engine = this.engines.get(engineName);
    if (!engine) {
      return false;
    }

    try {
      return await engine.testConnection();
    } catch {
      return false;
    }
  }

  /**
   * 测试所有引擎
   */
  async testAllEngines(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    
    const testPromises = Array.from(this.engines.keys()).map(async (name) => {
      results[name] = await this.testEngine(name);
    });

    await Promise.all(testPromises);
    return results;
  }

  /**
   * 获取引擎统计信息
   */
  getEnginesStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    for (const [name, engine] of this.engines) {
      stats[name] = engine.getStats();
    }

    return stats;
  }

  /**
   * 重置所有引擎统计
   */
  resetEnginesStats(): void {
    for (const engine of this.engines.values()) {
      engine.resetStats();
    }
  }

  /**
   * 启动健康检查
   */
  startHealthCheck(): void {
    if (this.healthCheckInterval) {
      return;
    }

    this.healthCheckInterval = window.setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.warn('Health check failed:', error);
      }
    }, this.HEALTH_CHECK_INTERVAL);

    console.log('Translation engines health check started');
  }

  /**
   * 停止健康检查
   */
  stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log('Translation engines health check stopped');
    }
  }

  /**
   * 执行健康检查
   */
  private async performHealthCheck(): Promise<void> {
    const availableEngines = this.getAvailableEngines();
    const testPromises = availableEngines.map(async (name) => {
      const isHealthy = await this.testEngine(name);
      if (!isHealthy) {
        console.warn(`Engine ${name} health check failed`);
      }
      return { name, healthy: isHealthy };
    });

    const results = await Promise.all(testPromises);
    const healthyEngines = results.filter(r => r.healthy).map(r => r.name);
    
    if (healthyEngines.length === 0 && availableEngines.length > 0) {
      console.error('All translation engines are unhealthy!');
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stopHealthCheck();
    this.engines.clear();
    console.log('TranslateEngineManager destroyed');
  }

  /**
   * 更新引擎配置
   */
  async updateEngineConfig(engineName: string, config: any): Promise<void> {
    const engine = this.engines.get(engineName);
    if (engine) {
      engine.updateConfig(config);
      await this.configManager.updateEngineConfig(engineName, config);
    }
  }

  /**
   * 获取推荐引擎（基于历史表现）
   */
  getRecommendedEngine(): string | null {
    const stats = this.getEnginesStats();
    const availableEngines = this.getAvailableEngines();
    
    if (availableEngines.length === 0) {
      return null;
    }

    // 简单的推荐算法：成功率最高的引擎
    let bestEngine = availableEngines[0];
    let bestScore = 0;

    for (const engineName of availableEngines) {
      const engineStats = stats[engineName];
      if (engineStats && engineStats.totalRequests > 0) {
        const successRate = engineStats.successCount / engineStats.totalRequests;
        if (successRate > bestScore) {
          bestScore = successRate;
          bestEngine = engineName;
        }
      }
    }

    return bestEngine;
  }
}