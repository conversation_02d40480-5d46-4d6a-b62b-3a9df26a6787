/**
 * 翻译系统类型定义
 */

// 翻译内容格式
export type TranslateFormat = 'text' | 'html';

// 翻译选项
export interface TranslateOptions {
  from?: string;        // 源语言，默认 'auto'
  to?: string;          // 目标语言，默认 'zh'
  engine?: string;      // 指定引擎，不指定则使用优先级
  timeout?: number;     // 超时时间（毫秒）
  format?: TranslateFormat; // 内容格式，默认 'text'
}

// API端点配置
export interface ApiEndpoint {
  name: string;         // API名称
  url: string;          // API地址
  priority: number;     // 优先级 (1-10, 1最高)
  enabled: boolean;     // 是否启用
  timeout?: number;     // 超时时间
}

// 翻译请求
export interface TranslateRequest {
  texts: string[];
  options: TranslateOptions;
  id: string;          // 请求唯一标识
}

// 翻译响应
export interface TranslateResponse {
  translations: string[];
  engine: string;      // 实际使用的引擎
  api: string;         // 实际使用的API
  success: boolean;
  error?: string;
  duration: number;    // 翻译耗时（毫秒）
}

// 引擎状态
export interface EngineStatus {
  name: string;
  available: boolean;
  lastCheck: Date;
  apis: ApiEndpoint[];
  errorCount: number;
  successCount: number;
}

// 引擎配置
export interface EngineConfig {
  name: string;
  enabled: boolean;
  priority: number;
  maxChunkSize: number;    // 单个文本最大长度
  maxBatchSize: number;    // 批量翻译最大数量
  timeout: number;         // 超时时间
  retryCount: number;      // 重试次数
  apis: ApiEndpoint[];
}

// Google引擎配置
export interface GoogleEngineConfig extends EngineConfig {
  apiKey?: string;
  projectId?: string;
}

// Microsoft引擎配置
export interface MicrosoftEngineConfig extends EngineConfig {
  apiKey?: string;
  region?: string;
}

// AI引擎配置
export interface AIEngineConfig extends EngineConfig {
  apiKey?: string;
  model?: string;
  baseUrl?: string;
}

// 翻译配置
export interface TranslateConfig {
  // 基础配置
  defaultSourceLanguage: string;
  defaultTargetLanguage: string;
  enableFallback: boolean;
  globalTimeout: number;
  
  // 引擎配置
  enginePriority: string[];
  engines: {
    google: GoogleEngineConfig;
    microsoft: MicrosoftEngineConfig;
    ai: AIEngineConfig;
  };
  
  // 高级配置
  maxConcurrentRequests: number;
  requestDelay: number;
  enableDebugMode: boolean;
}

// 翻译错误类型
export enum TranslateErrorType {
  NETWORK_ERROR = 'network_error',
  API_ERROR = 'api_error',
  TIMEOUT = 'timeout',
  INVALID_INPUT = 'invalid_input',
  QUOTA_EXCEEDED = 'quota_exceeded',
  AUTHENTICATION_ERROR = 'authentication_error',
  UNKNOWN_ERROR = 'unknown_error'
}

// 翻译错误
export class TranslateError extends Error {
  constructor(
    public type: TranslateErrorType,
    public message: string,
    public engine?: string,
    public api?: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'TranslateError';
  }
}

// 翻译统计
export interface TranslateStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageLatency: number;
  engineUsage: Record<string, number>;
  errorsByType: Record<TranslateErrorType, number>;
}

// 批处理配置
export interface BatchConfig {
  batchSize: number;
  batchDelay: number;
  maxRetries: number;
  retryDelay: number;
}