/**
 * 重构后的Mock翻译服务
 * 使用模块化组件，大幅简化原有的God Object
 */

import { TranslationEngine, type EngineTranslationOptions, type EngineTranslationResult } from './translation-engine';
import { EnvironmentDetector } from './environment-detector';
import { VocabularyManager } from './vocabulary-manager';

export interface MockTranslateOptions {
  delay?: number;
  failureRate?: number;
  debug?: boolean;
  environment?: 'development' | 'testing' | 'production';
  enableProfiling?: boolean;
}

/**
 * 重构后的Mock翻译引擎 - 只有54行vs原来的537行
 */
export class MockTranslateEngineRefactored {
  private translationEngine: TranslationEngine;
  private environmentDetector: EnvironmentDetector;

  constructor(options: MockTranslateOptions = {}) {
    this.environmentDetector = EnvironmentDetector.getInstance();
    
    // 如果指定了环境，强制设置
    if (options.environment) {
      this.environmentDetector.forceEnvironment(options.environment);
    }

    this.translationEngine = new TranslationEngine();

    // 应用自定义配置
    if (options.delay !== undefined || options.failureRate !== undefined) {
      this.translationEngine.updateConfig({
        delay: options.delay,
        failureRate: options.failureRate,
        debug: options.debug,
        enableProfiling: options.enableProfiling
      });
    }
  }

  /**
   * 翻译文本
   */
  async translateText(text: string, options: EngineTranslationOptions = {}): Promise<EngineTranslationResult> {
    return this.translationEngine.translateText(text, options);
  }

  /**
   * 批量翻译
   */
  async translateTexts(texts: string[], options: EngineTranslationOptions = {}): Promise<EngineTranslationResult> {
    return this.translationEngine.translateTexts(texts, options);
  }

  /**
   * 设置失败率
   */
  setFailureRate(rate: number): void {
    this.translationEngine.setFailureRate(rate);
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return this.translationEngine.getStats();
  }

  /**
   * 重置统计
   */
  resetStats(): void {
    this.translationEngine.resetStats();
  }

  /**
   * 添加自定义词汇
   */
  addVocabulary(original: string, translation: string, fromLang = 'en', toLang = 'zh'): void {
    this.translationEngine.addCustomVocabulary(original, translation, fromLang, toLang);
  }
}

// 全局实例 - 向后兼容
let globalMockTranslator: MockTranslateEngineRefactored | null = null;

/**
 * 获取全局Mock翻译器实例
 */
export function getMockTranslator(): MockTranslateEngineRefactored {
  if (!globalMockTranslator) {
    globalMockTranslator = new MockTranslateEngineRefactored();
  }
  return globalMockTranslator;
}

/**
 * 重置全局Mock翻译器
 */
export function resetMockTranslator(): void {
  globalMockTranslator = null;
}

/**
 * 设置全局Mock翻译函数 - 向后兼容
 */
export function setGlobalMockTranslate(): void {
  if (typeof window !== 'undefined') {
    const translator = getMockTranslator();

    // 设置全局函数 - 向后兼容
    (window as any).mockTranslate = translator.translateText.bind(translator);
    (window as any).setMockFailureRate = translator.setFailureRate.bind(translator);
    (window as any).mockTranslateStats = translator.getStats.bind(translator);

    console.log('🔧 Mock翻译系统已设置到全局对象 (重构版本)');
  }
}

/**
 * 自动设置Mock翻译环境 - 向后兼容
 */
export function autoSetupMockTranslation(): void {
  // 检测是否应该启用Mock翻译
  const shouldUseMock = (
    typeof window !== 'undefined' && (
      window.location.protocol === 'file:' ||
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1' ||
      document.title.toLowerCase().includes('test') ||
      localStorage.getItem('lucid-use-mock') === 'true'
    )
  );

  if (shouldUseMock) {
    setGlobalMockTranslate();
  }
}

// 向后兼容 - getMockTranslator 已在上面导出

// 默认导出
export default MockTranslateEngineRefactored;