/**
 * 环境检测器
 * 负责检测运行环境并提供相应的配置
 */

export type Environment = 'development' | 'testing' | 'production';

export interface EnvironmentConfig {
  /** 模拟延迟 (ms) */
  delay: number;
  /** 失败率 (0-1) */
  failureRate: number;
  /** 是否启用调试 */
  debug: boolean;
  /** 是否启用性能统计 */
  enableProfiling: boolean;
}

export class EnvironmentDetector {
  private static instance: EnvironmentDetector | null = null;
  private detectedEnvironment: Environment | null = null;

  /**
   * 单例模式获取实例
   */
  static getInstance(): EnvironmentDetector {
    if (!EnvironmentDetector.instance) {
      EnvironmentDetector.instance = new EnvironmentDetector();
    }
    return EnvironmentDetector.instance;
  }

  /**
   * 检测当前运行环境
   */
  detectEnvironment(): Environment {
    if (this.detectedEnvironment) {
      return this.detectedEnvironment;
    }

    // 检查URL协议
    if (typeof window !== 'undefined') {
      const location = window.location;
      
      // 文件协议 - 通常是测试
      if (location.protocol === 'file:') {
        this.detectedEnvironment = 'testing';
        return this.detectedEnvironment;
      }

      // 本地开发服务器
      if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
        this.detectedEnvironment = 'development';
        return this.detectedEnvironment;
      }

      // 测试域名
      if (location.hostname.includes('test') || location.hostname.includes('staging')) {
        this.detectedEnvironment = 'testing';
        return this.detectedEnvironment;
      }
    }

    // 检查Node.js环境变量
    if (typeof process !== 'undefined' && process.env) {
      const nodeEnv = process.env.NODE_ENV;
      if (nodeEnv === 'test' || nodeEnv === 'testing') {
        this.detectedEnvironment = 'testing';
        return this.detectedEnvironment;
      }
      if (nodeEnv === 'development') {
        this.detectedEnvironment = 'development';
        return this.detectedEnvironment;
      }
      if (nodeEnv === 'production') {
        this.detectedEnvironment = 'production';
        return this.detectedEnvironment;
      }
    }

    // 检查localStorage设置
    if (typeof localStorage !== 'undefined') {
      const forcedEnv = localStorage.getItem('lucid-force-env');
      if (forcedEnv && ['development', 'testing', 'production'].includes(forcedEnv)) {
        this.detectedEnvironment = forcedEnv as Environment;
        return this.detectedEnvironment;
      }

      // 检查调试模式
      if (localStorage.getItem('lucid-debug') === 'true') {
        this.detectedEnvironment = 'development';
        return this.detectedEnvironment;
      }

      // 检查Mock模式
      if (localStorage.getItem('lucid-use-mock') === 'true') {
        this.detectedEnvironment = 'testing';
        return this.detectedEnvironment;
      }
    }

    // 检查页面标题和内容
    if (typeof document !== 'undefined') {
      const title = document.title.toLowerCase();
      if (title.includes('test') || title.includes('demo')) {
        this.detectedEnvironment = 'testing';
        return this.detectedEnvironment;
      }
    }

    // 默认为生产环境
    this.detectedEnvironment = 'production';
    return this.detectedEnvironment;
  }

  /**
   * 获取环境配置
   */
  getEnvironmentConfig(environment?: Environment): EnvironmentConfig {
    const env = environment || this.detectEnvironment();

    switch (env) {
      case 'production':
        return {
          delay: 50,
          failureRate: 0, // 生产环境不允许失败
          debug: false,
          enableProfiling: false
        };

      case 'testing':
        return {
          delay: 2, // 测试环境：模拟快速响应 (非真实API性能)
          failureRate: 0.1, // 测试环境高失败率
          debug: true,
          enableProfiling: true
        };

      case 'development':
      default:
        return {
          delay: 5, // 开发环境：模拟延迟 (真实Google API为800-1200ms)
          failureRate: 0.02, // 开发环境低失败率
          debug: true,
          enableProfiling: true
        };
    }
  }

  /**
   * 强制设置环境 (用于测试)
   */
  forceEnvironment(environment: Environment): void {
    this.detectedEnvironment = environment;
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('lucid-force-env', environment);
    }
  }

  /**
   * 重置环境检测
   */
  reset(): void {
    this.detectedEnvironment = null;
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('lucid-force-env');
    }
  }

  /**
   * 获取环境信息
   */
  getEnvironmentInfo(): {
    environment: Environment;
    config: EnvironmentConfig;
    detectionMethod: string;
  } {
    const environment = this.detectEnvironment();
    const config = this.getEnvironmentConfig(environment);
    
    let detectionMethod = 'default';
    
    if (typeof window !== 'undefined') {
      if (window.location.protocol === 'file:') {
        detectionMethod = 'file-protocol';
      } else if (window.location.hostname === 'localhost') {
        detectionMethod = 'localhost';
      } else if (window.location.hostname.includes('test')) {
        detectionMethod = 'test-domain';
      }
    }

    if (typeof localStorage !== 'undefined' && localStorage.getItem('lucid-force-env')) {
      detectionMethod = 'localStorage-forced';
    }

    return {
      environment,
      config,
      detectionMethod
    };
  }
}