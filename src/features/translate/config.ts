/**
 * 翻译系统配置管理
 * 处理引擎优先级、参数配置、持久化存储等
 */

import { 
  TranslateConfig, 
  GoogleEngineConfig, 
  MicrosoftEngineConfig, 
  AIEngineConfig,
  ApiEndpoint 
} from './types';

export class TranslateConfigManager {
  private config: TranslateConfig;
  private readonly STORAGE_KEY = 'lucid-translate-config';

  constructor() {
    this.config = this.loadConfig();
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): TranslateConfig {
    return {
      // 基础配置
      defaultSourceLanguage: 'auto',
      defaultTargetLanguage: 'zh',
      enableFallback: true,
      globalTimeout: 30000,

      // 引擎优先级
      enginePriority: ['google', 'microsoft', 'ai'],

      // 引擎具体配置
      engines: {
        google: {
          name: 'google',
          enabled: true,
          priority: 1,
          maxChunkSize: 5000,
          maxBatchSize: 128,
          timeout: 10000,
          retryCount: 2,
          apis: [
            {
              name: 'google-translate-api-1',
              url: 'https://translate-pa.googleapis.com/v1/translateHtml', // 保持使用translateHTML
              priority: 1,
              enabled: true,
              timeout: 10000
            },
            {
              name: 'google-translate-api-2',
              url: 'https://translate.googleapis.com/translate_a/t',
              priority: 2,
              enabled: true,
              timeout: 10000
            }
          ]
        },
        microsoft: {
          name: 'microsoft',
          enabled: false, // 默认关闭，需要用户配置API密钥
          priority: 2,
          maxChunkSize: 10000,
          maxBatchSize: 100,
          timeout: 15000,
          retryCount: 2,
          apis: [
            {
              name: 'azure-translator',
              url: 'https://api.cognitive.microsofttranslator.com/translate',
              priority: 1,
              enabled: true,
              timeout: 15000
            }
          ],
          region: 'global'
        },
        ai: {
          name: 'ai',
          enabled: false, // 默认关闭，需要用户配置API密钥
          priority: 3,
          maxChunkSize: 8000,
          maxBatchSize: 50,
          timeout: 20000,
          retryCount: 1,
          apis: [
            {
              name: 'openai-translate',
              url: 'https://api.openai.com/v1/chat/completions',
              priority: 1,
              enabled: true,
              timeout: 20000
            }
          ],
          model: 'gpt-3.5-turbo',
          baseUrl: 'https://api.openai.com'
        }
      },

      // 高级配置
      maxConcurrentRequests: 3,
      requestDelay: 100,
      enableDebugMode: false
    };
  }

  /**
   * 从存储加载配置
   */
  private loadConfig(): TranslateConfig {
    try {
      // 优先从 extension storage 加载
      const stored = this.loadFromExtensionStorage();
      if (stored) {
        return this.mergeWithDefaults(stored);
      }

      // 回退到 localStorage
      const localStored = this.loadFromLocalStorage();
      if (localStored) {
        return this.mergeWithDefaults(localStored);
      }

      return this.getDefaultConfig();
    } catch (error) {
      console.warn('Failed to load translate config, using defaults:', error);
      return this.getDefaultConfig();
    }
  }

  /**
   * 从扩展存储加载
   */
  private loadFromExtensionStorage(): TranslateConfig | null {
    try {
      // 这里会异步加载，暂时返回null，后续通过 loadConfigAsync 处理
      return null;
    } catch {
      return null;
    }
  }

  /**
   * 从本地存储加载
   */
  private loadFromLocalStorage(): TranslateConfig | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  }

  /**
   * 异步加载配置（用于扩展存储）
   */
  async loadConfigAsync(): Promise<void> {
    try {
      if (typeof browser !== 'undefined' && browser.storage?.local) {
        const result = await browser.storage.local.get(this.STORAGE_KEY);
        if (result[this.STORAGE_KEY]) {
          this.config = this.mergeWithDefaults(result[this.STORAGE_KEY]);
        }
      }
    } catch (error) {
      console.warn('Failed to load config from extension storage:', error);
    }
  }

  /**
   * 与默认配置合并
   */
  private mergeWithDefaults(stored: Partial<TranslateConfig>): TranslateConfig {
    const defaults = this.getDefaultConfig();
    
    return {
      ...defaults,
      ...stored,
      engines: {
        google: { ...defaults.engines.google, ...stored.engines?.google },
        microsoft: { ...defaults.engines.microsoft, ...stored.engines?.microsoft },
        ai: { ...defaults.engines.ai, ...stored.engines?.ai }
      }
    };
  }

  /**
   * 保存配置
   */
  async saveConfig(): Promise<void> {
    try {
      // 优先保存到扩展存储
      if (typeof browser !== 'undefined' && browser.storage?.local) {
        await browser.storage.local.set({ [this.STORAGE_KEY]: this.config });
      } else {
        // 回退到本地存储
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.config));
      }
    } catch (error) {
      console.error('Failed to save translate config:', error);
      // 尝试本地存储作为备份
      try {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.config));
      } catch (localError) {
        console.error('Failed to save to localStorage as well:', localError);
      }
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): TranslateConfig {
    return { ...this.config };
  }

  /**
   * 获取引擎优先级
   */
  getEnginePriority(): string[] {
    return [...this.config.enginePriority];
  }

  /**
   * 设置引擎优先级
   */
  async setEnginePriority(priority: string[]): Promise<void> {
    this.config.enginePriority = [...priority];
    await this.saveConfig();
  }

  /**
   * 获取引擎配置
   */
  getEngineConfig(engineName: string): GoogleEngineConfig | MicrosoftEngineConfig | AIEngineConfig | null {
    return this.config.engines[engineName as keyof typeof this.config.engines] || null;
  }

  /**
   * 更新引擎配置
   */
  async updateEngineConfig(
    engineName: string, 
    updates: Partial<GoogleEngineConfig | MicrosoftEngineConfig | AIEngineConfig>
  ): Promise<void> {
    const currentConfig = this.getEngineConfig(engineName);
    if (currentConfig) {
      this.config.engines[engineName as keyof typeof this.config.engines] = {
        ...currentConfig,
        ...updates
      } as any;
      await this.saveConfig();
    }
  }

  /**
   * 启用/禁用引擎
   */
  async setEngineEnabled(engineName: string, enabled: boolean): Promise<void> {
    await this.updateEngineConfig(engineName, { enabled });
  }

  /**
   * 获取启用的引擎列表
   */
  getEnabledEngines(): string[] {
    return Object.entries(this.config.engines)
      .filter(([_, config]) => config.enabled)
      .map(([name]) => name)
      .sort((a, b) => {
        const aPriority = this.config.enginePriority.indexOf(a);
        const bPriority = this.config.enginePriority.indexOf(b);
        return aPriority - bPriority;
      });
  }

  /**
   * 添加API端点
   */
  async addApiEndpoint(engineName: string, endpoint: ApiEndpoint): Promise<void> {
    const engineConfig = this.getEngineConfig(engineName);
    if (engineConfig) {
      engineConfig.apis.push(endpoint);
      await this.updateEngineConfig(engineName, { apis: engineConfig.apis });
    }
  }

  /**
   * 移除API端点
   */
  async removeApiEndpoint(engineName: string, endpointName: string): Promise<void> {
    const engineConfig = this.getEngineConfig(engineName);
    if (engineConfig) {
      engineConfig.apis = engineConfig.apis.filter(api => api.name !== endpointName);
      await this.updateEngineConfig(engineName, { apis: engineConfig.apis });
    }
  }

  /**
   * 更新API端点
   */
  async updateApiEndpoint(
    engineName: string, 
    endpointName: string, 
    updates: Partial<ApiEndpoint>
  ): Promise<void> {
    const engineConfig = this.getEngineConfig(engineName);
    if (engineConfig) {
      const apiIndex = engineConfig.apis.findIndex(api => api.name === endpointName);
      if (apiIndex >= 0) {
        engineConfig.apis[apiIndex] = { ...engineConfig.apis[apiIndex], ...updates };
        await this.updateEngineConfig(engineName, { apis: engineConfig.apis });
      }
    }
  }

  /**
   * 设置默认语言
   */
  async setDefaultLanguages(source: string, target: string): Promise<void> {
    this.config.defaultSourceLanguage = source;
    this.config.defaultTargetLanguage = target;
    await this.saveConfig();
  }

  /**
   * 设置全局超时
   */
  async setGlobalTimeout(timeout: number): Promise<void> {
    this.config.globalTimeout = timeout;
    await this.saveConfig();
  }

  /**
   * 启用/禁用降级功能
   */
  async setFallbackEnabled(enabled: boolean): Promise<void> {
    this.config.enableFallback = enabled;
    await this.saveConfig();
  }

  /**
   * 设置调试模式
   */
  async setDebugMode(enabled: boolean): Promise<void> {
    this.config.enableDebugMode = enabled;
    await this.saveConfig();
  }

  /**
   * 重置为默认配置
   */
  async resetToDefaults(): Promise<void> {
    this.config = this.getDefaultConfig();
    await this.saveConfig();
  }

  /**
   * 导出配置
   */
  exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }

  /**
   * 导入配置
   */
  async importConfig(configJson: string): Promise<void> {
    try {
      const importedConfig = JSON.parse(configJson);
      this.config = this.mergeWithDefaults(importedConfig);
      await this.saveConfig();
    } catch (error) {
      throw new Error('Invalid configuration format');
    }
  }

  /**
   * 验证配置
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证引擎优先级
    if (!Array.isArray(this.config.enginePriority)) {
      errors.push('Engine priority must be an array');
    }

    // 验证引擎配置
    for (const [engineName, engineConfig] of Object.entries(this.config.engines)) {
      if (!engineConfig.apis || !Array.isArray(engineConfig.apis)) {
        errors.push(`Engine ${engineName} must have APIs array`);
      }
      
      if (engineConfig.maxChunkSize <= 0) {
        errors.push(`Engine ${engineName} maxChunkSize must be positive`);
      }
      
      if (engineConfig.maxBatchSize <= 0) {
        errors.push(`Engine ${engineName} maxBatchSize must be positive`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// 导出单例实例
export const translateConfig = new TranslateConfigManager();