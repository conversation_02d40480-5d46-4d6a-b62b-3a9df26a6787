/**
 * 翻译缓存管理器
 * 提供内存缓存和持久化存储，提高翻译性能
 */

import { errorLogger, CacheError, ValidationError, ErrorContext } from '../../utils/error-logger';

export interface CacheEntry<T = string> {
  text: string;
  result: T;
  timestamp: number;
  from: string;
  to: string;
  engine: string;
}

export interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  hitRate: number;
}

export interface CacheConfig {
  maxCacheSize: number;
  batchThreshold: number; // 命中率阈值
  maxBatchSize: number;   // 单次批量处理最大文本数
  memoryCheckInterval: number; // 内存检查间隔
  cacheTtlHours: number; // 缓存过期时间（小时）
  cleanupIntervalHours: number; // 清理间隔（小时）
  maxMemoryMB: number; // 单次操作最大内存使用（MB）
  spinLockTimeoutMs: number; // 自旋锁超时时间（毫秒）
}

export interface BatchCacheStatus {
  hitRate: number;
  shouldBatchTranslate: boolean;
  cachedResults: (string | null)[];
  uncachedTexts: string[];
  uncachedIndexes: number[];
  stats: {
    total: number;
    hits: number;
    misses: number;
  };
  memoryUsage?: {
    estimatedMB: number;
    isMemorySafe: boolean;
  };
}

export class TranslationCacheManager {
  private memoryCache: Map<string, CacheEntry> = new Map();
  private readonly STORAGE_KEY = 'lucid_translation_cache';
  private cleanupInterval?: NodeJS.Timeout;
  private saveInProgress = false;
  private loadInProgress = false;
  
  // 🔧 可配置参数，解决硬编码问题
  private config: CacheConfig;
  
  // 🔧 原子操作保护统计数据
  private statsLock = false;
  private stats = {
    hits: 0,
    misses: 0
  };

  constructor(config?: Partial<CacheConfig>) {
    // 🔧 默认配置，支持自定义覆盖
    this.config = this.validateAndMergeConfig(config);
    
    this.loadFromStorage();

    // 定期清理过期缓存
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredEntries();
    }, this.config.cleanupIntervalHours * 60 * 60 * 1000);
  }

  /**
   * 🔧 验证并合并配置
   */
  private validateAndMergeConfig(userConfig?: Partial<CacheConfig>): CacheConfig {
    const defaultConfig: CacheConfig = {
      maxCacheSize: 1000,
      batchThreshold: 0.1,     // 10%命中率阈值
      maxBatchSize: 200,       // 单次最多处理200个文本
      memoryCheckInterval: 50, // 每50个文本检查一次内存
      cacheTtlHours: 24,       // 24小时过期
      cleanupIntervalHours: 1, // 1小时清理一次
      maxMemoryMB: 50,         // 最大50MB内存使用
      spinLockTimeoutMs: 100   // 自旋锁超时100ms
    };

    const merged = { ...defaultConfig, ...userConfig };

    // 验证配置合法性
    if (merged.maxCacheSize <= 0) throw new Error('maxCacheSize must be positive');
    if (merged.batchThreshold < 0 || merged.batchThreshold > 1) {
      throw new Error('batchThreshold must be between 0 and 1');
    }
    if (merged.maxBatchSize <= 0) throw new Error('maxBatchSize must be positive');
    if (merged.cacheTtlHours <= 0) throw new Error('cacheTtlHours must be positive');
    if (merged.cleanupIntervalHours <= 0) throw new Error('cleanupIntervalHours must be positive');
    if (merged.maxMemoryMB <= 0) throw new Error('maxMemoryMB must be positive');
    if (merged.spinLockTimeoutMs <= 0) throw new Error('spinLockTimeoutMs must be positive');

    return merged;
  }

  /**
   * 🧹 销毁缓存管理器，清理资源
   */
  async destroy(): Promise<void> {
    try {
      // ✅ 停止新的定时任务
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
        this.cleanupInterval = undefined;
      }
      
      // ✅ 等待进行中的异步操作完成
      const maxWaitTime = 5000; // 5秒最大等待时间
      const startWait = Date.now();
      
      while ((this.saveInProgress || this.loadInProgress) && (Date.now() - startWait < maxWaitTime)) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
      
      if (this.saveInProgress || this.loadInProgress) {
        const context: ErrorContext = {
          method: 'TranslationCacheManager.destroy',
          component: 'TranslationCacheManager',
          data: { saveInProgress: this.saveInProgress, loadInProgress: this.loadInProgress }
        };
        errorLogger.warn('异步操作未能在超时前完成，强制销毁', undefined, context);
      }
      
      // 清空内存缓存
      this.memoryCache.clear();
      
      // 重置统计数据和状态
      this.stats = { hits: 0, misses: 0 };
      this.statsLock = false;
      this.saveInProgress = false;
      this.loadInProgress = false;
      
      errorLogger.info('缓存管理器已安全销毁', {
        method: 'TranslationCacheManager.destroy',
        component: 'TranslationCacheManager'
      });
    } catch (error) {
      const context: ErrorContext = {
        method: 'TranslationCacheManager.destroy',
        component: 'TranslationCacheManager'
      };
      errorLogger.error('Failed to destroy TranslationCacheManager', error as Error, context);
      throw new CacheError('Failed to destroy cache manager', 'CLEAR', error as Error, context);
    }
  }

  /**
   * 🛡️ 原子操作更新统计数据，解决并发安全问题
   */
  private async updateStatsAtomic(hitsDelta: number, missesDelta: number): Promise<void> {
    const startTime = Date.now();
    // 简单的自旋锁，确保统计更新的原子性
    while (this.statsLock) {
      await new Promise(resolve => setTimeout(resolve, 1));
      // 防止无限等待
      if (Date.now() - startTime > this.config.spinLockTimeoutMs) {
        console.warn('⚠️ [🟦translation-cache🟦|WARN] 统计更新自旋锁超时，跳过本次更新');
        return; // ✅ 超时后直接返回，避免数据不一致
      }
    }
    
    this.statsLock = true;
    try {
      this.stats.hits += hitsDelta;
      this.stats.misses += missesDelta;
    } finally {
      this.statsLock = false;
    }
  }

  /**
   * 🧠 估算内存使用量（MB）
   */
  private estimateMemoryUsage(texts: string[], results: (string | null)[]): number {
    const textsSizeBytes = texts.reduce((sum, text) => sum + (text?.length || 0) * 2, 0); // UTF-16
    const resultsSizeBytes = results.reduce((sum, result) => sum + (result?.length || 0) * 2, 0);
    const arrayOverheadBytes = (texts.length + results.length) * 8; // 指针开销
    
    return (textsSizeBytes + resultsSizeBytes + arrayOverheadBytes) / (1024 * 1024);
  }

  /**
   * 🔍 检查内存是否安全
   */
  private isMemorySafe(estimatedMB: number, textCount: number): boolean {
    // 内存安全检查规则
    const maxTextCount = this.config.maxBatchSize;
    
    return estimatedMB <= this.config.maxMemoryMB && textCount <= maxTextCount;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(text: string, from: string, to: string, engine: string): string {
    // 标准化文本（去除多余空格，转换为小写）
    const normalizedText = text.trim().toLowerCase();
    // 对长文本使用哈希避免键过长
    const textKey = normalizedText.length > 100
      ? this.hashString(normalizedText)
      : normalizedText;
    return `${textKey}_${from}_${to}_${engine}`;
  }

  /**
   * 简单的字符串哈希函数
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 获取缓存
   */
  async get(text: string, from: string, to: string, engine: string): Promise<any | null> {
    const key = this.generateCacheKey(text, from, to, engine);
    const entry = this.memoryCache.get(key);

    if (entry) {
      const cacheTtl = this.config.cacheTtlHours * 60 * 60 * 1000;
      // 检查是否过期
      if (Date.now() - entry.timestamp > cacheTtl) {
        this.memoryCache.delete(key);
        await this.updateStatsAtomic(0, 1); // 🛡️ 原子更新
        console.log('🔧 [🟦translation-cache🟦|DEBUG] Translation cache expired', { key });
        return null;
      }

      await this.updateStatsAtomic(1, 0); // 🛡️ 原子更新
      console.log('✅ [🟦translation-cache🟦|DEBUG] Translation cache hit', { key, age: Date.now() - entry.timestamp });
      return entry.result;
    }

    await this.updateStatsAtomic(0, 1); // 🛡️ 原子更新
    console.log('🔧 [🟦translation-cache🟦|DEBUG] Translation cache miss', { key });
    return null;
  }

  /**
   * 🚀 批量检查缓存状态 - 内存优化版本，支持分块处理
   */
  async getBatchStatus(texts: string[], from: string, to: string, engine: string): Promise<BatchCacheStatus> {
    // 🛡️ 输入验证和内存预检查
    if (!texts || texts.length === 0) {
      return this.createEmptyBatchStatus();
    }

    // 🔍 内存安全检查 - 如果文本数量超过配置上限，使用分块处理
    if (texts.length > this.config.maxBatchSize) {
      console.warn(`⚠️ [translation-cache|MEMORY] 文本数量超过上限 (${texts.length} > ${this.config.maxBatchSize})，启用分块处理`);
      return await this.getBatchStatusChunked(texts, from, to, engine);
    }

    // 🧠 预估内存使用量
    const estimatedMemoryMB = this.estimateMemoryUsage(texts, new Array(texts.length).fill(null));
    const isMemorySafe = this.isMemorySafe(estimatedMemoryMB, texts.length);

    if (!isMemorySafe) {
      console.warn(`⚠️ [translation-cache|MEMORY] 预估内存使用过高 (${estimatedMemoryMB.toFixed(2)}MB)，启用分块处理`);
      return await this.getBatchStatusChunked(texts, from, to, engine);
    }

    // 🚀 正常批量处理
    return await this.getBatchStatusDirect(texts, from, to, engine, estimatedMemoryMB, isMemorySafe);
  }

  /**
   * 🔄 分块处理版本 - 解决大量文本的内存问题
   */
  private async getBatchStatusChunked(texts: string[], from: string, to: string, engine: string): Promise<BatchCacheStatus> {
    const chunkSize = Math.min(this.config.maxBatchSize, 100); // 保守的分块大小
    const totalChunks = Math.ceil(texts.length / chunkSize);
    
    // 初始化结果数组
    const cachedResults: (string | null)[] = new Array(texts.length);
    const uncachedTexts: string[] = [];
    const uncachedIndexes: number[] = [];
    let totalHits = 0;
    let totalMisses = 0;

    console.group(`🔄 [translation-cache|CHUNK] 分块处理 ${texts.length}个文本，共${totalChunks}块`);

    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const startIndex = chunkIndex * chunkSize;
      const endIndex = Math.min(startIndex + chunkSize, texts.length);
      const chunk = texts.slice(startIndex, endIndex);
      
      console.log(`📦 [translation-cache|CHUNK] 处理第${chunkIndex + 1}/${totalChunks}块 (${chunk.length}个文本)`);

      // 处理当前块
      const chunkResult = await this.getBatchStatusDirect(chunk, from, to, engine, 0, true);
      
      // 合并结果
      for (let i = 0; i < chunk.length; i++) {
        const globalIndex = startIndex + i;
        cachedResults[globalIndex] = chunkResult.cachedResults[i];
      }
      
      // 调整uncachedIndexes到全局索引
      chunkResult.uncachedTexts.forEach((text, i) => {
        uncachedTexts.push(text);
        uncachedIndexes.push(startIndex + chunkResult.uncachedIndexes[i]);
      });
      
      totalHits += chunkResult.stats.hits;
      totalMisses += chunkResult.stats.misses;

      // 🕒 让出执行权，避免阻塞主线程
      if (chunkIndex < totalChunks - 1) {
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    }

    console.groupEnd();

    const hitRate = texts.length > 0 ? totalHits / texts.length : 0;
    const shouldBatchTranslate = hitRate < this.config.batchThreshold;

    // 🛡️ 原子更新统计
    await this.updateStatsAtomic(totalHits, totalMisses);

    console.log('🔄 [🟦translation-cache🟦|CHUNK] 分块处理完成', {
      total: texts.length,
      chunks: totalChunks,
      hits: totalHits,
      misses: totalMisses,
      hitRate: `${(hitRate * 100).toFixed(1)}%`,
      shouldBatchTranslate,
      strategy: shouldBatchTranslate ? '跳过逐个查询，直接批量翻译' : '混合模式处理'
    });

    return {
      hitRate,
      shouldBatchTranslate,
      cachedResults,
      uncachedTexts,
      uncachedIndexes,
      stats: {
        total: texts.length,
        hits: totalHits,
        misses: totalMisses
      },
      memoryUsage: {
        estimatedMB: 0, // 分块处理无法准确估算总内存
        isMemorySafe: true
      }
    };
  }

  /**
   * 🎯 直接批量处理版本 - 针对内存安全的情况
   */
  private async getBatchStatusDirect(
    texts: string[], 
    from: string, 
    to: string, 
    engine: string,
    estimatedMemoryMB: number = 0,
    isMemorySafe: boolean = true
  ): Promise<BatchCacheStatus> {
    const cachedResults: (string | null)[] = [];
    const uncachedTexts: string[] = [];
    const uncachedIndexes: number[] = [];
    let hits = 0;
    let misses = 0;

    const now = Date.now();

    for (let i = 0; i < texts.length; i++) {
      const text = texts[i];
      if (!text || text.trim().length === 0) {
        cachedResults[i] = '';
        continue;
      }

      const key = this.generateCacheKey(text, from, to, engine);
      const entry = this.memoryCache.get(key);

      const cacheTtl = this.config.cacheTtlHours * 60 * 60 * 1000;
      if (entry && (now - entry.timestamp <= cacheTtl)) {
        // 缓存命中且未过期
        cachedResults[i] = entry.result;
        hits++;
      } else {
        // 缓存未命中或已过期
        if (entry && (now - entry.timestamp > cacheTtl)) {
          // 清理过期条目
          this.memoryCache.delete(key);
        }
        cachedResults[i] = null;
        uncachedTexts.push(text);
        uncachedIndexes.push(i);
        misses++;
      }
    }

    const hitRate = texts.length > 0 ? hits / texts.length : 0;
    const shouldBatchTranslate = hitRate < this.config.batchThreshold;

    // 🛡️ 原子更新统计（仅在非分块模式下）
    if (estimatedMemoryMB > 0) {
      await this.updateStatsAtomic(hits, misses);
    }

    // 🔧 批量汇总日志
    if (estimatedMemoryMB > 0) { // 只在顶级调用时输出日志
      console.log('🔧 [🟦translation-cache🟦|BATCH] 缓存批量查询统计', {
        total: texts.length,
        hits,
        misses,
        hitRate: `${(hitRate * 100).toFixed(1)}%`,
        shouldBatchTranslate,
        memoryEstimate: `${estimatedMemoryMB.toFixed(2)}MB`,
        memorySafe: isMemorySafe,
        strategy: shouldBatchTranslate ? '跳过逐个查询，直接批量翻译' : '混合模式处理'
      });
    }

    // 🔧 修复：确保所有路径都能正确返回estimatedMemoryMB
    const finalEstimatedMemoryMB = estimatedMemoryMB > 0 ? estimatedMemoryMB : this.estimateMemoryUsage(texts, cachedResults);

    return {
      hitRate,
      shouldBatchTranslate,
      cachedResults,
      uncachedTexts,
      uncachedIndexes,
      stats: {
        total: texts.length,
        hits,
        misses
      },
      memoryUsage: {
        estimatedMB: finalEstimatedMemoryMB,
        isMemorySafe
      }
    };
  }

  /**
   * 🏗️ 创建空的批量状态对象
   */
  private createEmptyBatchStatus(): BatchCacheStatus {
    return {
      hitRate: 0,
      shouldBatchTranslate: false,
      cachedResults: [],
      uncachedTexts: [],
      uncachedIndexes: [],
      stats: { total: 0, hits: 0, misses: 0 },
      memoryUsage: { estimatedMB: 0, isMemorySafe: true }
    };
  }

  /**
   * 设置缓存 - 🔧 修复：使用动态配置和原子操作
   */
  async set(text: string, from: string, to: string, engine: string, result: any): Promise<void> {
    const key = this.generateCacheKey(text, from, to, engine);
    const entry: CacheEntry = {
      text,
      result,
      timestamp: Date.now(),
      from,
      to,
      engine
    };

    // 🔧 使用动态配置的缓存大小限制
    if (this.memoryCache.size >= this.config.maxCacheSize) {
      const oldestKey = this.findOldestEntry();
      if (oldestKey) {
        this.memoryCache.delete(oldestKey);
        console.log('🔧 [🟦translation-cache🟦|DEBUG] Cache evicted oldest entry', { key: oldestKey });
      }
    }

    this.memoryCache.set(key, entry);
    console.log('✅ [🟦translation-cache🟦|INFO] Translation cached', { key, size: this.memoryCache.size });

    // 异步保存到存储
    this.saveToStorage();
  }

  /**
   * 找到最旧的缓存条目 - 🔧 修复：正确初始化oldestTime
   */
  private findOldestEntry(): string | null {
    let oldestKey: string | null = null;
    let oldestTime = Number.MAX_SAFE_INTEGER;

    this.memoryCache.forEach((entry, key) => {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    });

    return oldestKey;
  }

  /**
   * 清理过期条目
   */
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];
    const cacheTtl = this.config.cacheTtlHours * 60 * 60 * 1000;

    this.memoryCache.forEach((entry, key) => {
      if (now - entry.timestamp > cacheTtl) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => this.memoryCache.delete(key));

    if (keysToDelete.length > 0) {
      console.log('🧹 [🟦translation-cache🟦|INFO] Cleaned up expired cache entries', { count: keysToDelete.length });
      this.saveToStorage();
    }
  }

  /**
   * 从存储加载缓存 - 🛡️ 修复竞态条件
   */
  private async loadFromStorage(): Promise<void> {
    if (this.loadInProgress) {
      console.warn('⚠️ [🟦translation-cache🟦|WARN] 加载操作已在进行中，跳过');
      return;
    }

    this.loadInProgress = true;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        if (typeof chrome !== 'undefined' && chrome.storage) {
          const result = await chrome.storage.local.get([this.STORAGE_KEY]);
          const cached = result[this.STORAGE_KEY];

          if (cached && Array.isArray(cached)) {
            const now = Date.now();
            const cacheTtl = this.config.cacheTtlHours * 60 * 60 * 1000;
            let loadedCount = 0;

            for (const entry of cached) {
              // 只加载未过期的条目
              if (entry && entry.text && entry.timestamp && (now - entry.timestamp <= cacheTtl)) {
                const key = this.generateCacheKey(entry.text, entry.from, entry.to, entry.engine);
                this.memoryCache.set(key, entry);
                loadedCount++;
              }
            }

            console.log('✅ [🟦translation-cache🟦|INFO] Loaded translation cache from storage', {
              loaded: loadedCount,
              total: cached.length
            });
          }
        }
        break; // 成功则退出循环
      } catch (error) {
        retryCount++;
        console.warn(`⚠️ [🟦translation-cache🟦|WARN] 加载缓存失败（第${retryCount}/${maxRetries}次重试）`, error);
        
        if (retryCount >= maxRetries) {
          console.error('❌ [🟦translation-cache🟦|ERROR] 加载缓存最终失败，使用默认状态', error);
          // 优雅降级：使用空缓存继续运行
        } else {
          // 指数退避重试
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 100));
        }
      }
    }
    
    this.loadInProgress = false;
  }

  /**
   * 保存缓存到存储 - 🛡️ 修复竞态条件
   */
  private async saveToStorage(): Promise<void> {
    if (this.saveInProgress) {
      return; // 防止并发保存
    }

    this.saveInProgress = true;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        if (typeof chrome !== 'undefined' && chrome.storage) {
          const cacheArray = Array.from(this.memoryCache.values());
          await chrome.storage.local.set({
            [this.STORAGE_KEY]: cacheArray
          });

          console.log('✅ [🟦translation-cache🟦|DEBUG] Saved translation cache to storage', { size: cacheArray.length });
        }
        break; // 成功则退出循环
      } catch (error) {
        retryCount++;
        console.warn(`⚠️ [🟦translation-cache🟦|WARN] 保存缓存失败（第${retryCount}/${maxRetries}次重试）`, error);
        
        if (retryCount >= maxRetries) {
          console.error('❌ [🟦translation-cache🟦|ERROR] 保存缓存最终失败', error);
        } else {
          // 指数退避重试
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 100));
        }
      }
    }
    
    this.saveInProgress = false;
  }

  /**
   * 清除所有缓存 - 🔧 修复：使用原子操作更新统计
   */
  async clear(): Promise<void> {
    this.memoryCache.clear();
    
    // 🛡️ 原子操作重置统计数据
    await this.updateStatsAtomic(-this.stats.hits, -this.stats.misses);

    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.remove([this.STORAGE_KEY]);
      }
    } catch (error) {
      console.log('❌ [🟦translation-cache🟦|ERROR] Failed to clear translation cache from storage', error);
    }

    console.log('✅ [🟦translation-cache🟦|INFO] Translation cache cleared');
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    const hitRate = this.stats.hits + this.stats.misses > 0
      ? (this.stats.hits / (this.stats.hits + this.stats.misses)) * 100
      : 0;

    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      size: this.memoryCache.size,
      hitRate: Math.round(hitRate * 100) / 100
    };
  }

  /**
   * 📊 获取健康监控指标
   */
  public getHealthMetrics(): {
    memoryUsage: number;
    hitRate: number;
    cacheEfficiency: number;
    staleCacheRatio: number;
    configStatus: string;
  } {
    const now = Date.now();
    const cacheTtl = this.config.cacheTtlHours * 60 * 60 * 1000;
    let staleCount = 0;

    this.memoryCache.forEach((entry) => {
      if (now - entry.timestamp > cacheTtl * 0.8) {
        staleCount++;
      }
    });

    const keys = Array.from(this.memoryCache.keys());
    const values = Array.from(this.memoryCache.values()).map(v => v.result || '');
    const memoryUsage = this.estimateMemoryUsage(keys, values);
    const stats = this.getStats();

    return {
      memoryUsage,
      hitRate: stats.hitRate,
      cacheEfficiency: this.stats.hits / (this.stats.hits + this.stats.misses + 1),
      staleCacheRatio: this.memoryCache.size > 0 ? staleCount / this.memoryCache.size : 0,
      configStatus: `Size: ${this.memoryCache.size}/${this.config.maxCacheSize}, TTL: ${this.config.cacheTtlHours}h`
    };
  }

  /**
   * 🚀 批量更新统计数据
   */
  private async batchUpdateStats(operations: Array<{hits: number, misses: number}>): Promise<void> {
    const totalHits = operations.reduce((sum, op) => sum + op.hits, 0);
    const totalMisses = operations.reduce((sum, op) => sum + op.misses, 0);
    await this.updateStatsAtomic(totalHits, totalMisses);
  }

  /**
   * 预热缓存 - 预加载常用翻译
   */
  async preloadCommonTranslations(): Promise<void> {
    const commonPhrases = [
      'Hello',
      'Thank you',
      'Please',
      'Yes',
      'No',
      'How are you?',
      'Good morning',
      'Good evening',
      'Goodbye'
    ];

    console.log('🔧 [🟦translation-cache🟦|DEBUG] Preloading common translations', { count: commonPhrases.length });

    // 这里可以添加预加载逻辑，但需要翻译服务实例
    // 通常在服务层调用
  }
}

// 🔧 可配置的单例实例
export const translationCache = new TranslationCacheManager();

/**
 * 🔧 创建自定义配置的缓存管理器实例
 * @param config 自定义配置
 */
export function createTranslationCacheManager(config?: Partial<CacheConfig>): TranslationCacheManager {
  return new TranslationCacheManager(config);
}