/**
 * 翻译引擎
 * 负责核心翻译逻辑和失败模拟
 */

import { VocabularyManager } from './vocabulary-manager';
import { EnvironmentDetector, type EnvironmentConfig } from './environment-detector';

export interface EngineTranslationOptions {
  /** 源语言 */
  from?: string;
  /** 目标语言 */
  to?: string;
  /** 格式 */
  format?: 'text' | 'html';
  /** 是否启用调试 */
  debug?: boolean;
}

export interface EngineTranslationResult {
  /** 翻译成功 */
  success: boolean;
  /** 翻译结果 */
  translations: string[];
  /** 使用的引擎 */
  engine: string;
  /** API标识 */
  api: string;
  /** 错误信息 */
  error?: string;
  /** 处理时间 */
  duration?: number;
}

export class TranslationEngine {
  private vocabularyManager: VocabularyManager;
  private environmentDetector: EnvironmentDetector;
  private config: EnvironmentConfig;

  // 统计信息
  private stats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    totalTime: 0
  };

  constructor() {
    this.vocabularyManager = new VocabularyManager();
    this.environmentDetector = EnvironmentDetector.getInstance();
    this.config = this.environmentDetector.getEnvironmentConfig();
  }

  /**
   * 翻译单个文本
   */
  async translateText(
    text: string, 
    options: EngineTranslationOptions = {}
  ): Promise<EngineTranslationResult> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    const {
      from = 'auto',
      to = 'zh',
      debug = this.config.debug
    } = options;

    try {
      // 模拟网络延迟
      await this.delay(this.config.delay);

      // 模拟失败
      if (this.shouldSimulateFailure()) {
        throw new Error('Mock translation service failure');
      }

      // 执行翻译
      const translation = this.performTranslation(text, from, to);
      
      const duration = Date.now() - startTime;
      this.stats.successfulRequests++;
      this.stats.totalTime += duration;

      if (debug) {
        console.log(`Mock翻译成功: "${text}" → "${translation}" (${duration}ms)`);
      }

      return {
        success: true,
        translations: [translation],
        engine: 'mock-translator',
        api: 'mock-v1',
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      this.stats.failedRequests++;
      this.stats.totalTime += duration;

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      if (debug) {
        console.error(`Mock翻译失败: "${text}" - ${errorMessage} (${duration}ms)`);
      }

      return {
        success: false,
        translations: [],
        engine: 'mock-translator',
        api: 'mock-v1',
        error: errorMessage,
        duration
      };
    }
  }

  /**
   * 翻译多个文本
   */
  async translateTexts(
    texts: string[], 
    options: EngineTranslationOptions = {}
  ): Promise<EngineTranslationResult> {
    const startTime = Date.now();
    this.stats.totalRequests++;

    const {
      from = 'auto',
      to = 'zh',
      debug = this.config.debug
    } = options;

    try {
      // 模拟批量翻译延迟（比单个翻译稍快）
      const batchDelay = Math.max(this.config.delay * 0.7, 1);
      await this.delay(batchDelay);

      // 模拟失败
      if (this.shouldSimulateFailure()) {
        throw new Error('Mock batch translation service failure');
      }

      // 执行批量翻译
      const translations = texts.map(text => this.performTranslation(text, from, to));
      
      const duration = Date.now() - startTime;
      this.stats.successfulRequests++;
      this.stats.totalTime += duration;

      if (debug) {
        console.log(`Mock批量翻译成功: ${texts.length}个文本 (${duration}ms)`);
      }

      return {
        success: true,
        translations,
        engine: 'mock-translator',
        api: 'mock-batch-v1',
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      this.stats.failedRequests++;
      this.stats.totalTime += duration;

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      if (debug) {
        console.error(`Mock批量翻译失败: ${texts.length}个文本 - ${errorMessage} (${duration}ms)`);
      }

      return {
        success: false,
        translations: [],
        engine: 'mock-translator',
        api: 'mock-batch-v1',
        error: errorMessage,
        duration
      };
    }
  }

  /**
   * 执行实际翻译
   */
  private performTranslation(text: string, from: string, to: string): string {
    // 基本验证
    if (!text || !text.trim()) {
      return '';
    }

    const cleanText = text.trim();

    // 检查是否启用"返回原文"模式（用于调试）
    if (typeof window !== 'undefined' && 
        (localStorage.getItem('lucid-mock-return-original') === 'true' ||
         localStorage.getItem('lucid-debug-original') === 'true')) {
      console.log(`🔧 Mock翻译(返回原文模式): "${cleanText}"`);
      return cleanText; // 直接返回原文
    }

    // 如果目标语言是中文，使用词汇管理器
    if (to === 'zh' || to === 'zh-CN') {
      return this.vocabularyManager.generateChineseTranslation(cleanText);
    }

    // 其他语言的简单处理
    if (from === 'zh' || from === 'zh-CN') {
      // 中译英：简单的反向映射
      return this.translateFromChinese(cleanText, to);
    }

    // 默认处理：添加标记
    return `[${cleanText}→${to}]`;
  }

  /**
   * 中文翻译到其他语言
   */
  private translateFromChinese(text: string, to: string): string {
    // 简单的中英映射
    const chineseToEnglish = new Map([
      ['你好', 'hello'], ['世界', 'world'], ['测试', 'test'],
      ['页面', 'page'], ['谷歌', 'google'], ['浏览器', 'browser'],
      ['扩展', 'extension'], ['程序', 'program'], ['应用', 'application']
    ]);

    const words = text.split(/\s+/);
    const translatedWords = words.map(word => {
      const translation = chineseToEnglish.get(word.trim());
      return translation || `[${word}→${to}]`;
    });

    return translatedWords.join(' ');
  }

  /**
   * 是否应该模拟失败
   */
  private shouldSimulateFailure(): boolean {
    return Math.random() < this.config.failureRate;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<EnvironmentConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 设置失败率
   */
  setFailureRate(rate: number): void {
    this.config.failureRate = Math.max(0, Math.min(1, rate));
  }

  /**
   * 获取统计信息
   */
  getStats(): typeof this.stats & {
    vocabulary: ReturnType<VocabularyManager['getVocabularyStats']>;
    environment: ReturnType<EnvironmentDetector['getEnvironmentInfo']>;
    averageTime: number;
    successRate: number;
  } {
    const averageTime = this.stats.totalRequests > 0 
      ? this.stats.totalTime / this.stats.totalRequests 
      : 0;
    
    const successRate = this.stats.totalRequests > 0 
      ? this.stats.successfulRequests / this.stats.totalRequests 
      : 0;

    return {
      ...this.stats,
      vocabulary: this.vocabularyManager.getVocabularyStats(),
      environment: this.environmentDetector.getEnvironmentInfo(),
      averageTime,
      successRate
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalTime: 0
    };
  }

  /**
   * 添加自定义词汇
   */
  addCustomVocabulary(original: string, translation: string, fromLang = 'en', toLang = 'zh'): void {
    this.vocabularyManager.addVocabulary(original, translation, fromLang, toLang);
  }

  /**
   * 获取可用引擎列表
   */
  getAvailableEngines(): string[] {
    return ['mock-translator'];
  }

  /**
   * 获取推荐引擎
   */
  getRecommendedEngine(): string {
    return 'mock-translator';
  }
}