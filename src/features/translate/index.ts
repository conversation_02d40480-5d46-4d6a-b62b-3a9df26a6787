/**
 * 翻译模块统一导出文件
 */

// 核心服务
export { TranslateService, translateService } from './translate.service';
export { TranslateEngineManager } from './engine-manager';
export { TranslateConfigManager, translateConfig } from './config';

// 引擎相关
export { TranslateEngine } from './engines/base';
export * from './engines';

// 类型定义
export * from './types';

// 默认导出主服务
import { translateService } from './translate.service';
export default translateService;