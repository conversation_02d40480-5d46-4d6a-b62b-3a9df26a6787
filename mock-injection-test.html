<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock Smart Injection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .demo-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .control-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 1000;
            min-width: 200px;
        }
        .control-panel button {
            display: block;
            width: 100%;
            margin-bottom: 10px;
            padding: 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-test { background: #4CAF50; color: white; }
        .btn-clear { background: #f44336; color: white; }
        .btn-stats { background: #2196F3; color: white; }
        .btn-inject { background: #FF9800; color: white; }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #e8f5e8;
            border-left: 4px solid #4CAF50;
            border-radius: 4px;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .error {
            background: #ffeaea;
            border-left-color: #f44336;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        /* Smart injection visualization */
        .lu-wrapper {
            border: 2px dashed #4CAF50;
            margin: 5px 0;
            padding: 5px;
            background: rgba(76, 175, 80, 0.1);
        }
        .lu-translation {
            color: #2E7D32;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="control-panel">
        <h4>🎯 Mock Smart Injection</h4>
        <button class="btn-test" onclick="testMockTranslation()">Test Mock Translation</button>
        <button class="btn-inject" onclick="testDirectInjection()">Test Direct Injection</button>
        <button class="btn-stats" onclick="showExtensionStatus()">Extension Status</button>
        <button class="btn-clear" onclick="clearAll()">Clear All</button>
        <div id="result-panel"></div>
    </div>

    <header>
        <h1 id="main-title">Mock Smart Injection Test</h1>
        <p id="subtitle">Testing smart injection with mock translations</p>
    </header>

    <main>
        <div class="demo-section">
            <h2>Test Case 1: Heading Elements (Should use BLOCK strategy)</h2>
            <h3 id="test-heading">Important Announcement</h3>
            <p>Expected: Block injection below the heading with green border</p>
        </div>

        <div class="demo-section">
            <h2>Test Case 2: Short Inline Elements (Should use INLINE strategy)</h2>
            <p>Here is <span id="short-span">hello world</span> and <a href="#" id="test-link">click here</a> in a sentence.</p>
            <p>Expected: Inline injection after the elements</p>
        </div>

        <div class="demo-section">
            <h2>Test Case 3: Long Paragraph (Should use BLOCK strategy)</h2>
            <p id="long-paragraph">This is a comprehensive paragraph that contains substantial content and should definitely use the block injection strategy because it has enough content to warrant being displayed below the original text rather than inline with it for better readability and user experience throughout the entire reading process.</p>
            <p>Expected: Block injection below the paragraph</p>
        </div>

        <div class="demo-section">
            <h2>Test Case 4: List Elements (Should use BLOCK strategy)</h2>
            <ul>
                <li id="list-item-1">First important task</li>
                <li id="list-item-2">Second task</li>
            </ul>
            <p>Expected: Block injection for list items</p>
        </div>

        <div class="demo-section">
            <h2>Test Case 5: Navigation Elements (Should use INLINE strategy)</h2>
            <nav>
                <a href="#" id="nav-home">Home</a> |
                <a href="#" id="nav-about">About</a> |
                <a href="#" id="nav-contact">Contact</a>
            </nav>
            <p>Expected: Inline injection to preserve navigation layout</p>
        </div>
    </main>

    <script>
        console.log('🚀 Mock Smart Injection Test loaded');
        
        // Mock translation function
        function mockTranslate(text) {
            // Simple mock translations for demonstration
            const translations = {
                'Important Announcement': '重要公告',
                'hello world': '你好世界', 
                'click here': '点击这里',
                'First important task': '第一个重要任务',
                'Second task': '第二个任务',
                'Home': '首页',
                'About': '关于',
                'Contact': '联系我们'
            };
            
            // For longer texts, just add Chinese prefix
            if (text.length > 50) {
                return '【中文翻译】' + text;
            }
            
            return translations[text] || `【译】${text}`;
        }
        
        // Test mock translation without extension
        function testMockTranslation() {
            console.log('🧪 Testing mock translation system...');
            showResult('Starting mock translation test...', 'info');
            
            try {
                // Get all test elements
                const testElements = [
                    'test-heading',
                    'short-span', 
                    'test-link',
                    'long-paragraph',
                    'list-item-1',
                    'list-item-2',
                    'nav-home',
                    'nav-about', 
                    'nav-contact'
                ];
                
                let processed = 0;
                let injected = 0;
                
                testElements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element && element.textContent) {
                        processed++;
                        const originalText = element.textContent.trim();
                        const translatedText = mockTranslate(originalText);
                        
                        // Determine injection strategy based on element
                        let strategy = 'INLINE';
                        const tagName = element.tagName.toLowerCase();
                        const textLength = originalText.length;
                        
                        if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
                            strategy = 'BLOCK';
                        } else if (tagName === 'p' && textLength > 50) {
                            strategy = 'BLOCK'; 
                        } else if (tagName === 'li') {
                            strategy = 'BLOCK';
                        } else if (textLength <= 50) {
                            strategy = 'INLINE';
                        }
                        
                        // Create translation wrapper
                        const wrapper = document.createElement('div');
                        wrapper.className = 'lu-wrapper';
                        wrapper.setAttribute('data-lu-strategy', strategy);
                        wrapper.innerHTML = `
                            <div class="lu-translation">
                                ${translatedText} 
                                <small>(${strategy})</small>
                            </div>
                        `;
                        
                        // Apply injection strategy
                        if (strategy === 'BLOCK') {
                            // Insert after the element
                            element.parentNode.insertBefore(wrapper, element.nextSibling);
                        } else if (strategy === 'INLINE') {
                            // Insert inline after the element
                            wrapper.style.display = 'inline';
                            wrapper.style.marginLeft = '10px';
                            element.parentNode.insertBefore(wrapper, element.nextSibling);
                        }
                        
                        injected++;
                        console.log(`✅ ${id}: "${originalText}" → "${translatedText}" (${strategy})`);
                    }
                });
                
                const resultMessage = `Mock translation completed!\nProcessed: ${processed} elements\nInjected: ${injected} translations\n\nCheck the page to see different injection strategies.`;
                showResult(resultMessage, 'success');
                
            } catch (error) {
                console.error('❌ Mock translation failed:', error);
                showResult(`Mock translation failed: ${error.message}`, 'error');
            }
        }
        
        // Test direct injection using the extension if available
        function testDirectInjection() {
            console.log('🔧 Testing direct injection...');
            showResult('Testing direct injection with extension...', 'info');
            
            // Check for extension functions
            const extensionFunctions = [
                'LucidTranslation',
                'translatePage',
                'testLightweightPageTranslation',
                'testBackgroundScript'
            ];
            
            let foundFunction = null;
            for (const funcName of extensionFunctions) {
                if (window[funcName]) {
                    foundFunction = funcName;
                    break;
                }
                if (window.LucidTranslation && window.LucidTranslation[funcName]) {
                    foundFunction = `LucidTranslation.${funcName}`;
                    break;
                }
            }
            
            if (foundFunction) {
                showResult(`Found extension function: ${foundFunction}\nTrying to use it...`, 'info');
                
                try {
                    // Try to call the function
                    if (foundFunction.includes('.')) {
                        const [obj, method] = foundFunction.split('.');
                        if (typeof window[obj][method] === 'function') {
                            window[obj][method]().then(result => {
                                showResult(`Extension function called successfully:\n${JSON.stringify(result, null, 2)}`, 'success');
                            }).catch(error => {
                                showResult(`Extension function failed:\n${error.message}`, 'error');
                            });
                        }
                    } else {
                        if (typeof window[foundFunction] === 'function') {
                            window[foundFunction]().then(result => {
                                showResult(`Extension function called successfully:\n${JSON.stringify(result, null, 2)}`, 'success');
                            }).catch(error => {
                                showResult(`Extension function failed:\n${error.message}`, 'error');
                            });
                        }
                    }
                } catch (error) {
                    showResult(`Direct injection failed:\n${error.message}`, 'error');
                }
            } else {
                showResult('No extension functions found.\nUsing mock translation instead.', 'warning');
                setTimeout(testMockTranslation, 1000);
            }
        }
        
        function showExtensionStatus() {
            console.log('📊 Checking extension status...');
            
            const status = {
                extensionDetected: false,
                availableFunctions: [],
                globalObjects: []
            };
            
            // Check for extension objects
            const checkObjects = [
                'LucidTranslation',
                'translatePage', 
                'testLightweightPageTranslation',
                'testBackgroundScript',
                'translateService',
                'clearLightweightTranslations'
            ];
            
            checkObjects.forEach(objName => {
                if (window[objName]) {
                    status.extensionDetected = true;
                    status.availableFunctions.push(objName);
                    status.globalObjects.push(`${objName}: ${typeof window[objName]}`);
                }
            });
            
            // Check LucidTranslation methods
            if (window.LucidTranslation) {
                const methods = Object.keys(window.LucidTranslation);
                methods.forEach(method => {
                    status.availableFunctions.push(`LucidTranslation.${method}`);
                    status.globalObjects.push(`LucidTranslation.${method}: ${typeof window.LucidTranslation[method]}`);
                });
            }
            
            const statusText = `Extension Status:\n${JSON.stringify(status, null, 2)}`;
            console.log('📈 Extension status:', status);
            showResult(statusText, status.extensionDetected ? 'success' : 'warning');
        }
        
        function clearAll() {
            console.log('🧹 Clearing all translations...');
            
            // Remove all injection wrappers
            const wrappers = document.querySelectorAll('.lu-wrapper');
            let cleared = 0;
            wrappers.forEach(wrapper => {
                wrapper.remove();
                cleared++;
            });
            
            showResult(`Cleared ${cleared} mock translations`, 'success');
        }
        
        function showResult(message, type = 'info') {
            const panel = document.getElementById('result-panel');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            if (type === 'error') resultDiv.className += ' error';
            if (type === 'warning') resultDiv.className += ' warning';
            
            resultDiv.innerHTML = `
                <strong>${type.toUpperCase()}:</strong><br>
                <pre>${message}</pre>
            `;
            
            panel.innerHTML = '';
            panel.appendChild(resultDiv);
            
            // Auto-clear after 15 seconds for non-error messages
            if (type !== 'error') {
                setTimeout(() => {
                    if (panel.contains(resultDiv)) {
                        panel.removeChild(resultDiv);
                    }
                }, 15000);
            }
        }
        
        // Initialize
        window.addEventListener('load', () => {
            console.log('📖 Mock test page ready');
            showResult('Mock test page loaded.\nClick "Test Mock Translation" to see smart injection strategies.', 'info');
            
            // Auto-check extension status
            setTimeout(showExtensionStatus, 2000);
        });
    </script>
</body>
</html>