# 翻译系统Debug模式配置指南

本文档介绍如何启用和配置翻译系统的调试模式，以便查看详细的扫描结果和翻译结果日志。

## 🚀 快速启用Debug模式

### 方法1: 通过全局API启用（推荐）

在浏览器控制台中执行：

```javascript
// 启用调试模式
LucidTranslation.config.enableDebug();

// 或者直接使用调试工具
LucidTranslation.config.debug.enableDebugForSession();
```

### 方法2: 通过配置对象启用

```javascript
// 更新配置
LucidTranslation.config.update({ debug: true });

// 或者在初始化时启用
LucidTranslation.initialize({ debug: true });
```

### 方法3: 通过代码导入启用

```javascript
import { enableDebugMode, configDebugTools } from './src/features/translation_pipeline/config';

// 启用调试模式
enableDebugMode();

// 或者使用调试工具
configDebugTools.enableDebugForSession();
```

## 🔧 调试工具功能

系统提供了丰富的调试工具：

```javascript
// 查看当前配置
LucidTranslation.config.debug.printConfig();

// 查看环境信息
LucidTranslation.config.debug.printEnvironment();

// 使用预设配置
LucidTranslation.config.debug.presets.debug(); // 调试模式
LucidTranslation.config.debug.presets.development(); // 开发模式
LucidTranslation.config.debug.presets.performance(); // 性能模式
```

## 📊 Debug日志输出

启用debug模式后，翻译过程中会输出以下详细日志：

### 扫描结果日志
```
🔍 扫描结果 - 需要翻译的节点详情: {
  totalNodes: 5,
  scanStats: {...},
  nodes: [
    {
      index: 0,
      element: "P.content",
      text: "Hello world...",
      textLength: 25,
      hasHtmlStructure: false,
      linksCount: 0,
      links: []
    }
    // ... 更多节点
  ]
}
```

### 翻译结果日志
```
🔍 翻译结果详情: {
  totalNodes: 5,
  successful: 4,
  failed: 1,
  results: [
    {
      index: 0,
      success: true,
      originalText: "Hello world...",
      translatedText: "你好世界...",
      format: "text",
      duration: 150.25
    }
    // ... 更多结果
  ],
  successfulTexts: [...],
  failedTexts: [...]
}
```

## 🔍 排查翻译问题

当翻译有内容遗漏时，可以通过debug日志排查：

1. **检查扫描结果**：查看 `🔍 扫描结果` 日志，确认需要翻译的内容是否被正确识别
2. **检查翻译结果**：查看 `🔍 翻译结果详情` 日志，确认哪些内容翻译成功/失败
3. **对比差异**：对比扫描到的节点数量和实际翻译的结果数量

## ⚙️ 配置管理

### 查看当前配置
```javascript
const config = LucidTranslation.config.get();
console.table(config);
```

### 检查调试状态
```javascript
const isDebugEnabled = LucidTranslation.config.isDebugEnabled();
console.log('Debug模式:', isDebugEnabled ? '已启用' : '已关闭');
```

### 重置配置
```javascript
// 重置为默认配置
LucidTranslation.config.debug.presets.development();

// 或者完全重置
import { resetTranslationConfig } from './src/features/translation_pipeline/config';
resetTranslationConfig();
```

## 🌍 环境自动配置

系统会根据环境自动配置debug模式：

- **开发环境**: 自动启用debug模式
- **生产环境**: 自动关闭debug模式  
- **测试环境**: 关闭debug输出

环境检测基于：
- `process.env.NODE_ENV`
- 浏览器扩展manifest版本信息
- 开发工具的存在

## 🔄 会话级临时启用

如果只想在当前会话中启用调试：

```javascript
// 临时启用（不会持久化）
LucidTranslation.config.debug.enableDebugForSession();

// 页面刷新后会恢复默认配置
```

## 📝 注意事项

1. **性能影响**: Debug模式会增加日志输出，可能影响翻译性能
2. **日志量**: 大量内容翻译时会产生大量日志，注意控制台输出
3. **生产环境**: 建议在生产环境中关闭debug模式
4. **内存使用**: Debug模式可能增加内存使用量

## 🛠️ 高级调试

### 开发者工具集成

```javascript
// 在开发环境中，调试工具会自动暴露到全局
if (window.translationConfigDebug) {
  // 使用开发者专用调试工具
  translationConfigDebug.printConfig();
  translationConfigDebug.printEnvironment();
}
```

### 自定义日志输出

```javascript
// 监听翻译事件
LucidTranslation.initialize({
  debug: true,
  onProgress: (progress) => {
    console.log('翻译进度:', progress);
  },
  onError: (error, context) => {
    console.error('翻译错误:', error, '上下文:', context);
  }
});
```