# 🔧 浏览器插件Debug模式启用指南

这是一个浏览器插件项目，启用debug模式非常简单！

## ✅ Debug模式已启用

**配置已设置完成！**

在 `src/features/translation_pipeline/config.ts` 文件第34行：

```typescript
const DEFAULT_CONFIG: GlobalTranslationConfig = {
  debug: true, // ✅ 已启用debug模式
  // ... 其他配置
};
```

**Debug模式现在默认开启！**

## 🔍 查看Debug日志

启用debug模式后，打开浏览器的开发者工具（F12），在Console标签页中可以看到详细的翻译日志：

### 扫描阶段日志
```
🔍 扫描结果 - 需要翻译的节点详情:
- 总节点数: 5
- 节点详情: 每个需要翻译的元素信息
- 文本内容预览
- HTML结构信息
```

### 翻译阶段日志  
```
🔍 翻译结果详情:
- 成功数量: 4
- 失败数量: 1  
- 每个节点的翻译结果
- 原文/译文对比
- 错误信息（如果有）
```

## 🚀 开发流程

1. **修改配置**: 设置 `debug: true`
2. **重新构建**: 运行 `pnpm run build`
3. **重新加载插件**: 在浏览器扩展管理页面重新加载插件
4. **打开开发者工具**: F12 → Console
5. **使用翻译功能**: 触发翻译，查看日志输出

## 🎯 排查翻译问题

当发现翻译有遗漏时：

1. **查看扫描日志**: 确认需要翻译的内容是否被正确识别
2. **查看翻译日志**: 确认哪些内容翻译成功/失败
3. **对比数量**: 扫描到的节点数 vs 实际翻译的结果数

## 📝 注意事项

- ⚠️ **生产环境**: 发布时记得关闭debug模式
- 📊 **性能**: Debug模式会增加日志输出，可能影响性能
- 🔄 **热更新**: 修改配置后需要重新构建和加载插件

## 🛠️ 其他Debug方法

如果需要动态控制，可以在代码中添加：

```typescript
// 在需要调试的地方添加
import { enableDebugMode } from './features/translation_pipeline/config';
enableDebugMode(); // 临时启用
```

---

**总结**: 对于浏览器插件，最简单的方法就是直接修改 `config.ts` 中的 `debug: true`，然后重新构建插件！