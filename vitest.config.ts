import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// 读取代理配置环境变量
const HTTP_PROXY = process.env.HTTP_PROXY || process.env.http_proxy;
const HTTPS_PROXY = process.env.HTTPS_PROXY || process.env.https_proxy;

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'happy-dom',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,
    include: ['src/**/*.{test,spec}.{js,ts,jsx,tsx}'],
    exclude: ['node_modules', '.output', 'dist'],
    testTimeout: 15000, // 增加超时以支持网络请求和代理
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        '.output/',
        'dist/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.ts',
        '**/*.spec.ts',
        '**/*.test.ts'
      ]
    },
    // 为真实网络测试添加环境变量
    pool: 'forks',
    env: {
      HTTP_PROXY,
      HTTPS_PROXY,
      // 标记测试环境，让代码知道这是在测试中
      VITEST: 'true',
      // 网络测试配置
      NETWORK_TESTS_ENABLED: process.env.NETWORK_TESTS_ENABLED || 'false',
      // 调试配置
      DEBUG_NETWORK: process.env.DEBUG_NETWORK || 'false'
    }
  },
  resolve: {
    alias: {
      // 与 wxt.config.ts 和 tsconfig.json 完全一致的路径别名
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@features': resolve(__dirname, 'src/features'),
      '@services': resolve(__dirname, 'src/services'),
      '@ui-manager': resolve(__dirname, 'src/ui-manager'),
      '@styles': resolve(__dirname, 'src/styles'),
      '@test': resolve(__dirname, 'src/test'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@constants': resolve(__dirname, 'src/constants'),
      '@content': resolve(__dirname, 'src/content'),

      // 具体的子目录别名，便于快速导入（与 tsconfig.json 保持一致）
      '@tooltip': resolve(__dirname, 'src/components/Tooltip'),
      '@dynamic-tooltip': resolve(__dirname, 'src/components/DynamicTooltip'),
      '@slider': resolve(__dirname, 'src/components/Slider'),
      '@dictionary': resolve(__dirname, 'src/features/dictionary'),
      '@settings': resolve(__dirname, 'src/features/settings'),
      '@translate': resolve(__dirname, 'src/features/translate'),

      // 特殊别名处理
      'webextension-polyfill': 'webextension-polyfill'
    }
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:4000',
        changeOrigin: true,
      },
    },
  }
});