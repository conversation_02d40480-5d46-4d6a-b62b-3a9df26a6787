<div data-layer="Frame 4" class="Frame4" style="width: 659px; height: 540px; position: relative; background: #1E1E1E; overflow: hidden">
  <div data-layer="Overlay+Border+Shadow+OverlayBlur" class="OverlayBorderShadowOverlayblur" style="width: 282px; padding: 1px; left: 189px; top: 114px; position: absolute; background: rgba(28, 28, 28, 0.90); box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.30); overflow: hidden; border-radius: 12px; outline: 1px rgba(255, 255, 255, 0.10) solid; outline-offset: -1px; backdrop-filter: blur(5px); flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
    <div data-layer="Container" class="Container" style="width: 560px; justify-content: flex-start; align-items: flex-start; display: inline-flex">
      <div data-layer="Container" class="Container" style="width: 280px; align-self: stretch; padding-bottom: 4px; flex-direction: column; justify-content: flex-start; align-items: center; gap: 4px; display: inline-flex">
        <div data-layer="HorizontalBorder" class="Horizontalborder" style="align-self: stretch; padding-bottom: 1px; border-bottom: 1px rgba(255, 255, 255, 0.08) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="Component 8" data-:hover="false" data-variant="1" class="Component8" style="align-self: stretch; height: 40px; padding-left: 12px; padding-right: 8px; justify-content: flex-start; align-items: center; display: inline-flex">
            <div data-layer="Margin" class="Margin" style="width: 28px; height: 20px; padding-right: 8px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Container" class="Container" style="width: 20px; height: 20px; justify-content: center; align-items: center; display: inline-flex">
                <div data-svg-wrapper data-layer="Component 1" data-variant="16" class="Component1" style="position: relative">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4.16667 16.6666C3.70833 16.6666 3.31597 16.5034 2.98958 16.177C2.66319 15.8506 2.5 15.4583 2.5 14.9999V4.99992C2.5 4.54159 2.66319 4.14922 2.98958 3.82284C3.31597 3.49645 3.70833 3.33325 4.16667 3.33325H15.8333C16.2917 3.33325 16.684 3.49645 17.0104 3.82284C17.3368 4.14922 17.5 4.54159 17.5 4.99992V14.9999C17.5 15.4583 17.3368 15.8506 17.0104 16.177C16.684 16.5034 16.2917 16.6666 15.8333 16.6666H4.16667ZM4.16667 14.9999H15.8333V4.99992H4.16667V14.9999ZM5.83333 12.4999H8.33333C8.56944 12.4999 8.76736 12.4201 8.92708 12.2603C9.08681 12.1006 9.16667 11.9027 9.16667 11.6666V11.2499C9.16667 11.1249 9.125 11.0208 9.04167 10.9374C8.95833 10.8541 8.85417 10.8124 8.72917 10.8124H8.35417C8.22917 10.8124 8.125 10.8541 8.04167 10.9374C7.95833 11.0208 7.91667 11.1249 7.91667 11.2499H6.25V8.74992H7.91667C7.91667 8.87492 7.95833 8.97909 8.04167 9.06242C8.125 9.14575 8.22917 9.18742 8.35417 9.18742H8.72917C8.85417 9.18742 8.95833 9.14575 9.04167 9.06242C9.125 8.97909 9.16667 8.87492 9.16667 8.74992V8.33325C9.16667 8.09714 9.08681 7.89922 8.92708 7.7395C8.76736 7.57978 8.56944 7.49992 8.33333 7.49992H5.83333C5.59722 7.49992 5.39931 7.57978 5.23958 7.7395C5.07986 7.89922 5 8.09714 5 8.33325V11.6666C5 11.9027 5.07986 12.1006 5.23958 12.2603C5.39931 12.4201 5.59722 12.4999 5.83333 12.4999ZM14.1667 7.49992H11.6667C11.4306 7.49992 11.2326 7.57978 11.0729 7.7395C10.9132 7.89922 10.8333 8.09714 10.8333 8.33325V11.6666C10.8333 11.9027 10.9132 12.1006 11.0729 12.2603C11.2326 12.4201 11.4306 12.4999 11.6667 12.4999H14.1667C14.4028 12.4999 14.6007 12.4201 14.7604 12.2603C14.9201 12.1006 15 11.9027 15 11.6666V11.2499C15 11.1249 14.9583 11.0208 14.875 10.9374C14.7917 10.8541 14.6875 10.8124 14.5625 10.8124H14.1875C14.0625 10.8124 13.9583 10.8541 13.875 10.9374C13.7917 11.0208 13.75 11.1249 13.75 11.2499H12.0833V8.74992H13.75C13.75 8.87492 13.7917 8.97909 13.875 9.06242C13.9583 9.14575 14.0625 9.18742 14.1875 9.18742H14.5625C14.6875 9.18742 14.7917 9.14575 14.875 9.06242C14.9583 8.97909 15 8.87492 15 8.74992V8.33325C15 8.09714 14.9201 7.89922 14.7604 7.7395C14.6007 7.57978 14.4028 7.49992 14.1667 7.49992Z" fill="white" fill-opacity="0.8"/>
                  </svg>
                </div>
              </div>
            </div>
            <div data-layer="Container" class="Container" style="justify-content: flex-start; align-items: center; gap: 4px; display: flex">
              <div data-layer="Lucid 字幕" class="Lucid" style="justify-content: center; display: flex; flex-direction: column; color: rgba(255, 255, 255, 0.80); font-size: 12.90px; font-family: Roboto; font-weight: 400; line-height: 13px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">Lucid 字幕</div>
              <div data-layer="Container" class="Container" style="justify-content: center; align-items: center; display: flex">
                <div data-svg-wrapper data-layer="Component 1" data-variant="17" class="Component1" style="position: relative">
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7.00008 12.8334C3.77842 12.8334 1.16675 10.2217 1.16675 7.00008C1.16675 3.77842 3.77842 1.16675 7.00008 1.16675C10.2217 1.16675 12.8334 3.77842 12.8334 7.00008C12.8334 10.2217 10.2217 12.8334 7.00008 12.8334ZM7.00008 11.6667C9.57742 11.6667 11.6667 9.57742 11.6667 7.00008C11.6667 4.42275 9.57742 2.33341 7.00008 2.33341C4.42275 2.33341 2.33341 4.42275 2.33341 7.00008C2.33341 9.57742 4.42275 11.6667 7.00008 11.6667ZM6.41675 4.08341H7.58341V5.25008H6.41675V4.08341ZM6.41675 6.41675H7.58341V9.91675H6.41675V6.41675Z" fill="white" fill-opacity="0.6"/>
                  </svg>
                </div>
              </div>
            </div>
            <div data-layer="Container" class="Container" style="width: 149.97px; justify-content: flex-end; align-items: center; display: flex">
              <div data-layer="Container" class="Container" style="width: 56px; height: 38px; padding: 12px; position: relative; overflow: hidden; justify-content: center; align-items: flex-start; display: flex">
                <div data-svg-wrapper data-layer="Background" class="Background">
                  <svg width="33" height="14" viewBox="0 0 33 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect opacity="0.5" x="0.969971" width="32" height="14" rx="7" fill="#FFC862"/>
                  </svg>
                </div>
                <div data-layer="Component 7" data-:hover="false" data-variant="1" class="Component7" style="height: 38px; padding: 10px; left: 20px; top: 0px; position: absolute; border-radius: 19px; justify-content: center; align-items: center; display: flex">
                  <div data-svg-wrapper data-layer="Background+Shadow" class="BackgroundShadow" style="position: relative">
                    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#filter0_ddd_42_749)">
                    <rect x="3.96997" y="2" width="18" height="18" rx="9" fill="#FFC862"/>
                    </g>
                    <defs>
                    <filter id="filter0_ddd_42_749" x="0.969971" y="0" width="24" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                    <feOffset dy="1"/>
                    <feGaussianBlur stdDeviation="1.5"/>
                    <feComposite in2="hardAlpha" operator="out"/>
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_42_749"/>
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                    <feOffset dy="1"/>
                    <feGaussianBlur stdDeviation="0.5"/>
                    <feComposite in2="hardAlpha" operator="out"/>
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.14 0"/>
                    <feBlend mode="normal" in2="effect1_dropShadow_42_749" result="effect2_dropShadow_42_749"/>
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                    <feMorphology radius="1" operator="erode" in="SourceAlpha" result="effect3_dropShadow_42_749"/>
                    <feOffset dy="2"/>
                    <feGaussianBlur stdDeviation="0.5"/>
                    <feComposite in2="hardAlpha" operator="out"/>
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                    <feBlend mode="normal" in2="effect2_dropShadow_42_749" result="effect3_dropShadow_42_749"/>
                    <feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_42_749" result="shape"/>
                    </filter>
                    </defs>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div data-layer="HorizontalBorder" class="Horizontalborder" style="align-self: stretch; padding-bottom: 1px; border-bottom: 1px rgba(255, 255, 255, 0.08) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="Component 8" data-:hover="false" data-variant="3" class="Component8" style="align-self: stretch; height: 40px; padding-left: 12px; padding-right: 8px; justify-content: flex-start; align-items: center; display: inline-flex">
            <div data-layer="Margin" class="Margin" style="width: 28px; height: 20px; padding-right: 8px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Container" class="Container" style="width: 20px; height: 20px; justify-content: center; align-items: center; display: inline-flex">
                <div data-svg-wrapper data-layer="Component 1" data-variant="19" class="Component1" style="position: relative">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10.0001 18.3334C8.86119 18.3334 7.7848 18.1147 6.77092 17.6772C5.75703 17.2397 4.87161 16.6424 4.11467 15.8855C3.35772 15.1286 2.7605 14.2431 2.323 13.2292C1.8855 12.2154 1.66675 11.139 1.66675 10.0001C1.66675 8.8473 1.8855 7.76744 2.323 6.7605C2.7605 5.75355 3.35772 4.87161 4.11467 4.11467C4.87161 3.35772 5.75703 2.7605 6.77092 2.323C7.7848 1.8855 8.86119 1.66675 10.0001 1.66675C11.1529 1.66675 12.2327 1.8855 13.2397 2.323C14.2466 2.7605 15.1286 3.35772 15.8855 4.11467C16.6424 4.87161 17.2397 5.75355 17.6772 6.7605C18.1147 7.76744 18.3334 8.8473 18.3334 10.0001C18.3334 11.139 18.1147 12.2154 17.6772 13.2292C17.2397 14.2431 16.6424 15.1286 15.8855 15.8855C15.1286 16.6424 14.2466 17.2397 13.2397 17.6772C12.2327 18.1147 11.1529 18.3334 10.0001 18.3334ZM10.0001 16.6251C10.3612 16.1251 10.6737 15.6042 10.9376 15.0626C11.2015 14.5209 11.4167 13.9445 11.5834 13.3334H8.41675C8.58342 13.9445 8.79869 14.5209 9.06258 15.0626C9.32647 15.6042 9.63897 16.1251 10.0001 16.6251ZM7.83342 16.2917C7.58342 15.8334 7.36467 15.3577 7.17717 14.8647C6.98967 14.3716 6.83342 13.8612 6.70842 13.3334H4.25008C4.65286 14.0279 5.15633 14.632 5.7605 15.1459C6.36467 15.6598 7.05564 16.0417 7.83342 16.2917ZM12.1667 16.2917C12.9445 16.0417 13.6355 15.6598 14.2397 15.1459C14.8438 14.632 15.3473 14.0279 15.7501 13.3334H13.2917C13.1667 13.8612 13.0105 14.3716 12.823 14.8647C12.6355 15.3577 12.4167 15.8334 12.1667 16.2917ZM3.54175 11.6667H6.37508C6.33342 11.389 6.30217 11.1147 6.28133 10.8438C6.2605 10.573 6.25008 10.2917 6.25008 10.0001C6.25008 9.70842 6.2605 9.42717 6.28133 9.15633C6.30217 8.8855 6.33342 8.61119 6.37508 8.33342H3.54175C3.4723 8.61119 3.42022 8.8855 3.3855 9.15633C3.35078 9.42717 3.33341 9.70842 3.33341 10.0001C3.33341 10.2917 3.35078 10.573 3.3855 10.8438C3.42022 11.1147 3.4723 11.389 3.54175 11.6667ZM8.04175 11.6667H11.9584C12.0001 11.389 12.0313 11.1147 12.0522 10.8438C12.073 10.573 12.0834 10.2917 12.0834 10.0001C12.0834 9.70842 12.073 9.42717 12.0522 9.15633C12.0313 8.8855 12.0001 8.61119 11.9584 8.33342H8.04175C8.00008 8.61119 7.96883 8.8855 7.948 9.15633C7.92717 9.42717 7.91675 9.70842 7.91675 10.0001C7.91675 10.2917 7.92717 10.573 7.948 10.8438C7.96883 11.1147 8.00008 11.389 8.04175 11.6667ZM13.6251 11.6667H16.4584C16.5279 11.389 16.5799 11.1147 16.6147 10.8438C16.6494 10.573 16.6667 10.2917 16.6667 10.0001C16.6667 9.70842 16.6494 9.42717 16.6147 9.15633C16.5799 8.8855 16.5279 8.61119 16.4584 8.33342H13.6251C13.6667 8.61119 13.698 8.8855 13.7188 9.15633C13.7397 9.42717 13.7501 9.70842 13.7501 10.0001C13.7501 10.2917 13.7397 10.573 13.7188 10.8438C13.698 11.1147 13.6667 11.389 13.6251 11.6667ZM13.2917 6.66675H15.7501C15.3473 5.9723 14.8438 5.36814 14.2397 4.85425C13.6355 4.34036 12.9445 3.95841 12.1667 3.70841C12.4167 4.16675 12.6355 4.64244 12.823 5.1355C13.0105 5.62855 13.1667 6.13897 13.2917 6.66675ZM8.41675 6.66675H11.5834C11.4167 6.05564 11.2015 5.47925 10.9376 4.93758C10.6737 4.39592 10.3612 3.87508 10.0001 3.37508C9.63897 3.87508 9.32647 4.39592 9.06258 4.93758C8.79869 5.47925 8.58342 6.05564 8.41675 6.66675ZM4.25008 6.66675H6.70842C6.83342 6.13897 6.98967 5.62855 7.17717 5.1355C7.36467 4.64244 7.58342 4.16675 7.83342 3.70841C7.05564 3.95841 6.36467 4.34036 5.7605 4.85425C5.15633 5.36814 4.65286 5.9723 4.25008 6.66675Z" fill="white" fill-opacity="0.8"/>
                  </svg>
                </div>
              </div>
            </div>
            <div data-layer="主字幕" style="justify-content: center; display: flex; flex-direction: column; color: rgba(255, 255, 255, 0.80); font-size: 13px; font-family: Roboto; font-weight: 400; line-height: 13px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">主字幕</div>
            <div data-layer="Container" class="Container" style="flex: 1 1 0; justify-content: flex-end; align-items: center; gap: 4px; display: flex">
              <div data-layer="Margin" class="Margin" style="max-width: 110px; padding-right: 4px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                <div data-layer="Container" class="Container" style="max-width: 106px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                  <div data-layer="English" class="English" style="justify-content: center; display: flex; flex-direction: column; color: rgba(255, 255, 255, 0.90); font-size: 12.90px; font-family: Roboto; font-weight: 400; line-height: 15.60px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">English</div>
                </div>
              </div>
              <div data-svg-wrapper data-layer="Component 1" data-variant="20" class="Component1" style="position: relative">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fill-opacity="0.6"/>
                </svg>
              </div>
            </div>
          </div>
          <div data-layer="Component 8" data-:hover="false" data-variant="4" class="Component8" style="align-self: stretch; height: 40px; padding-left: 12px; padding-right: 8px; justify-content: flex-start; align-items: center; display: inline-flex">
            <div data-layer="Margin" class="Margin" style="width: 28px; height: 20px; padding-right: 8px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Container" class="Container" style="width: 20px; height: 20px; justify-content: center; align-items: center; display: inline-flex">
                <div data-svg-wrapper data-layer="Component 1" data-variant="21" class="Component1" style="position: relative">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16.4279 6.77334L16.2224 7.24474C16.0721 7.58985 15.5947 7.58985 15.4443 7.24474L15.2389 6.77334C14.8727 5.93281 14.213 5.26359 13.3898 4.89749L12.7567 4.61594C12.4145 4.46369 12.4145 3.96559 12.7567 3.81335L13.3544 3.54754C14.1987 3.17201 14.8702 2.47803 15.2302 1.60894L15.4412 1.09953C15.5882 0.744493 16.0786 0.744493 16.2256 1.09953L16.4366 1.60894C16.7966 2.47803 17.4681 3.17201 18.3124 3.54754L18.91 3.81335C19.2523 3.96559 19.2523 4.46369 18.91 4.61594L18.277 4.89749C17.4538 5.26359 16.7942 5.93281 16.4279 6.77334ZM4.16675 14.1666V12.4999H2.50008V14.1666C2.50008 16.0075 3.99246 17.4999 5.83341 17.4999H8.33341V15.8333H5.83341L5.70903 15.8287C4.84665 15.7651 4.16675 15.0453 4.16675 14.1666ZM18.6667 17.4999L15.0001 8.33325H13.3334L9.66758 17.4999H11.4626L12.4617 14.9999H15.8701L16.8709 17.4999H18.6667ZM13.1276 13.3333L14.1667 10.7376L15.2042 13.3333H13.1276ZM6.66675 3.33325V1.66659H5.00008V3.33325H1.66675V9.16659H5.00008V11.6666H6.66675V9.16659H10.0001V3.33325H6.66675ZM3.33341 4.99992H5.00008V7.49992H3.33341V4.99992ZM6.66675 4.99992H8.33341V7.49992H6.66675V4.99992Z" fill="white" fill-opacity="0.8"/>
                  </svg>
                </div>
              </div>
            </div>
            <div data-layer="翻译字幕" style="justify-content: center; display: flex; flex-direction: column; color: rgba(255, 255, 255, 0.80); font-size: 13px; font-family: Roboto; font-weight: 400; line-height: 13px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">翻译字幕</div>
            <div data-layer="Container" class="Container" style="flex: 1 1 0; justify-content: flex-end; align-items: center; gap: 4px; display: flex">
              <div data-layer="Margin" class="Margin" style="max-width: 110px; padding-right: 4px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                <div data-layer="Container" class="Container" style="max-width: 106px; min-width: 26px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                  <div data-layer="中文" style="justify-content: center; display: flex; flex-direction: column; color: rgba(255, 255, 255, 0.90); font-size: 13px; font-family: Roboto; font-weight: 400; line-height: 15.60px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">中文</div>
                </div>
              </div>
              <div data-svg-wrapper data-layer="Component 1" data-variant="20" class="Component1" style="position: relative">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fill-opacity="0.6"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
        <div data-layer="HorizontalBorder" class="Horizontalborder" style="align-self: stretch; padding-bottom: 9px; border-bottom: 1px rgba(255, 255, 255, 0.08) solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
          <div data-layer="Component 8" data-:hover="false" data-variant="5" class="Component8" style="align-self: stretch; height: 40px; padding-left: 12px; padding-right: 8px; justify-content: flex-start; align-items: center; display: inline-flex">
            <div data-layer="Margin" class="Margin" style="width: 28px; height: 20px; padding-right: 8px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Container" class="Container" style="width: 20px; height: 20px; justify-content: center; align-items: center; display: inline-flex">
                <div data-svg-wrapper data-layer="Component 1" data-variant="22" class="Component1" style="position: relative">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M17.0557 7.24474L17.2612 6.77334C17.6275 5.93281 18.2872 5.26359 19.1103 4.89749L19.7433 4.61594C20.0857 4.46369 20.0857 3.96559 19.7433 3.81335L19.1457 3.54754C18.3014 3.17201 17.6299 2.47803 17.2699 1.60894L17.0589 1.09953C16.9119 0.744493 16.4216 0.744493 16.2745 1.09953L16.0635 1.60894C15.7036 2.47803 15.0321 3.17201 14.1877 3.54754L13.5901 3.81335C13.2478 3.96559 13.2478 4.46369 13.5901 4.61594L14.2232 4.89749C15.0463 5.26359 15.706 5.93281 16.0722 6.77334L16.2777 7.24474C16.4281 7.58985 16.9054 7.58985 17.0557 7.24474ZM4.83336 13.3333H6.62841L7.12841 12.0833H9.53833L10.0383 13.3333H11.8333L9.16666 6.66659H7.50002L4.83336 13.3333ZM8.33336 9.07092L8.87166 10.4166H7.79507L8.33336 9.07092ZM12.5001 13.3333V6.66659H14.1667V13.3333H12.5001ZM2.50008 2.49992C2.03985 2.49992 1.66675 2.87302 1.66675 3.33325V16.6666C1.66675 17.1268 2.03985 17.4999 2.50008 17.4999H17.5001C17.9603 17.4999 18.3334 17.1268 18.3334 16.6666V9.16659H16.6667V15.8333H3.33341V4.16659H11.6667V2.49992H2.50008Z" fill="white" fill-opacity="0.8"/>
                  </svg>
                </div>
              </div>
            </div>
            <div data-layer="翻译引擎" style="justify-content: center; display: flex; flex-direction: column; color: rgba(255, 255, 255, 0.80); font-size: 13px; font-family: Roboto; font-weight: 400; line-height: 13px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">翻译引擎</div>
            <div data-layer="Container" class="Container" style="flex: 1 1 0; justify-content: flex-end; align-items: center; gap: 4px; display: flex">
              <div data-layer="Margin" class="Margin" style="max-width: 110px; padding-right: 4px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                <div data-layer="Container" class="Container" style="max-width: 106px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                  <div data-layer="Microsoft" class="Microsoft" style="justify-content: center; display: flex; flex-direction: column; color: rgba(255, 255, 255, 0.90); font-size: 12.29px; font-family: Roboto; font-weight: 400; line-height: 15.60px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">Microsoft</div>
                </div>
              </div>
              <div data-svg-wrapper data-layer="Component 1" data-variant="20" class="Component1" style="position: relative">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fill-opacity="0.6"/>
                </svg>
              </div>
            </div>
          </div>
          <div data-layer="Component 8" data-:hover="false" data-variant="6" class="Component8" style="align-self: stretch; height: 40px; padding-left: 12px; padding-right: 8px; justify-content: flex-start; align-items: center; display: inline-flex">
            <div data-layer="Margin" class="Margin" style="width: 28px; height: 20px; padding-right: 8px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Container" class="Container" style="width: 20px; height: 20px; justify-content: center; align-items: center; display: inline-flex">
                <div data-svg-wrapper data-layer="Component 1" data-variant="23" class="Component1" style="position: relative">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5.00008 13.3333H11.6667V11.6666H5.00008V13.3333ZM13.3334 13.3333H15.0001V11.6666H13.3334V13.3333ZM5.00008 9.99992H6.66675V8.33325H5.00008V9.99992ZM8.33342 9.99992H15.0001V8.33325H8.33342V9.99992ZM3.33341 16.6666C2.87508 16.6666 2.48272 16.5034 2.15633 16.177C1.82994 15.8506 1.66675 15.4583 1.66675 14.9999V4.99992C1.66675 4.54159 1.82994 4.14922 2.15633 3.82284C2.48272 3.49645 2.87508 3.33325 3.33341 3.33325H16.6667C17.1251 3.33325 17.5174 3.49645 17.8438 3.82284C18.1702 4.14922 18.3334 4.54159 18.3334 4.99992V14.9999C18.3334 15.4583 18.1702 15.8506 17.8438 16.177C17.5174 16.5034 17.1251 16.6666 16.6667 16.6666H3.33341ZM3.33341 14.9999H16.6667V4.99992H3.33341V14.9999Z" fill="white" fill-opacity="0.8"/>
                  </svg>
                </div>
              </div>
            </div>
            <div data-layer="字幕显示" style="justify-content: center; display: flex; flex-direction: column; color: rgba(255, 255, 255, 0.80); font-size: 13px; font-family: Roboto; font-weight: 400; line-height: 13px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">字幕显示</div>
            <div data-layer="Container" class="Container" style="flex: 1 1 0; justify-content: flex-end; align-items: center; gap: 4px; display: flex">
              <div data-layer="Margin" class="Margin" style="max-width: 110px; padding-right: 4px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                <div data-layer="Container" class="Container" style="max-width: 106px; min-width: 52px; overflow: hidden; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
                  <div data-layer="双语字幕" style="justify-content: center; display: flex; flex-direction: column; color: rgba(255, 255, 255, 0.90); font-size: 13px; font-family: Roboto; font-weight: 400; line-height: 15.60px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">双语字幕</div>
                </div>
              </div>
              <div data-svg-wrapper data-layer="Component 1" data-variant="20" class="Component1" style="position: relative">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fill-opacity="0.6"/>
                </svg>
              </div>
            </div>
          </div>
          <div data-layer="Component 8" data-:hover="false" data-variant="7" class="Component8" style="align-self: stretch; height: 40px; padding-left: 12px; padding-right: 8px; justify-content: flex-start; align-items: center; display: inline-flex">
            <div data-layer="Margin" class="Margin" style="width: 28px; height: 20px; padding-right: 8px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Container" class="Container" style="width: 20px; height: 20px; justify-content: center; align-items: center; display: inline-flex">
                <div data-svg-wrapper data-layer="Component 1" data-variant="24" class="Component1" style="position: relative">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M6.66675 9.99992V8.33325H5.00008V9.99992H6.66675ZM8.91675 9.99992C9.06953 9.68047 9.24661 9.38186 9.448 9.10409C9.64939 8.82631 9.87508 8.56936 10.1251 8.33325H8.33342V9.99992H8.91675ZM8.41675 13.3333C8.38897 13.1944 8.37161 13.0589 8.36467 12.927C8.35772 12.7951 8.35425 12.6527 8.35425 12.4999C8.35425 12.3471 8.35772 12.2048 8.36467 12.0728C8.37161 11.9409 8.38897 11.8055 8.41675 11.6666H5.00008V13.3333H8.41675ZM10.1042 16.6666H3.33341C2.87508 16.6666 2.48272 16.5034 2.15633 16.177C1.82994 15.8506 1.66675 15.4583 1.66675 14.9999V4.99992C1.66675 4.54159 1.82994 4.14922 2.15633 3.82284C2.48272 3.49645 2.87508 3.33325 3.33341 3.33325H16.6667C17.1251 3.33325 17.5174 3.49645 17.8438 3.82284C18.1702 4.14922 18.3334 4.54159 18.3334 4.99992V8.45825C18.0973 8.20825 17.8404 7.98256 17.5626 7.78117C17.2848 7.57978 16.9862 7.4027 16.6667 7.24992V4.99992H3.33341V14.9999H8.91675C9.06953 15.3194 9.24314 15.618 9.43758 15.8958C9.63203 16.1735 9.85425 16.4305 10.1042 16.6666ZM15.0001 16.6666H13.3334L13.0834 15.4166C12.9167 15.3471 12.7605 15.2742 12.6147 15.1978C12.4688 15.1214 12.3195 15.0277 12.1667 14.9166L10.9584 15.2916L10.1251 13.8749L11.0834 13.0416C11.0556 12.861 11.0417 12.6805 11.0417 12.4999C11.0417 12.3194 11.0556 12.1388 11.0834 11.9583L10.1251 11.1249L10.9584 9.70825L12.1667 10.0833C12.3195 9.97214 12.4688 9.87839 12.6147 9.802C12.7605 9.72561 12.9167 9.6527 13.0834 9.58325L13.3334 8.33325H15.0001L15.2501 9.58325C15.4167 9.6527 15.5765 9.73256 15.7292 9.82284C15.882 9.91311 16.0279 10.0138 16.1667 10.1249L17.3751 9.70825L18.2084 11.1666L17.2501 11.9999C17.2779 12.1805 17.2917 12.3541 17.2917 12.5208C17.2917 12.6874 17.2779 12.861 17.2501 13.0416L18.2084 13.8749L17.3751 15.2916L16.1667 14.9166C16.014 15.0277 15.8647 15.1214 15.7188 15.1978C15.573 15.2742 15.4167 15.3471 15.2501 15.4166L15.0001 16.6666ZM14.1667 14.1666C14.6251 14.1666 15.0174 14.0034 15.3438 13.677C15.6702 13.3506 15.8334 12.9583 15.8334 12.4999C15.8334 12.0416 15.6702 11.6492 15.3438 11.3228C15.0174 10.9964 14.6251 10.8333 14.1667 10.8333C13.7084 10.8333 13.3161 10.9964 12.9897 11.3228C12.6633 11.6492 12.5001 12.0416 12.5001 12.4999C12.5001 12.9583 12.6633 13.3506 12.9897 13.677C13.3161 14.0034 13.7084 14.1666 14.1667 14.1666Z" fill="white" fill-opacity="0.8"/>
                  </svg>
                </div>
              </div>
            </div>
            <div data-layer="字幕样式" style="justify-content: center; display: flex; flex-direction: column; color: rgba(255, 255, 255, 0.80); font-size: 13px; font-family: Roboto; font-weight: 400; line-height: 13px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">字幕样式</div>
            <div data-layer="Container" class="Container" style="flex: 1 1 0; justify-content: flex-end; align-items: center; display: flex">
              <div data-svg-wrapper data-layer="Component 1" data-variant="20" class="Component1" style="position: relative">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fill-opacity="0.6"/>
                </svg>
              </div>
            </div>
          </div>
          <div data-layer="Component 8" data-:hover="false" data-variant="8" class="Component8" style="align-self: stretch; height: 40px; padding-left: 12px; padding-right: 8px; justify-content: flex-start; align-items: center; display: inline-flex">
            <div data-layer="Margin" class="Margin" style="width: 28px; height: 20px; padding-right: 8px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
              <div data-layer="Container" class="Container" style="width: 20px; height: 20px; justify-content: center; align-items: center; display: inline-flex">
                <div data-svg-wrapper data-layer="Component 1" data-variant="25" class="Component1" style="position: relative">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M3.66659 4.45833V15.5417H16.3333V4.45833H3.66659ZM2.87492 2.875H17.1249C17.5622 2.875 17.9166 3.22945 17.9166 3.66667V16.3333C17.9166 16.7706 17.5622 17.125 17.1249 17.125H2.87492C2.4377 17.125 2.08325 16.7706 2.08325 16.3333V3.66667C2.08325 3.22945 2.4377 2.875 2.87492 2.875ZM5.24992 6.04167H6.83325V7.625H5.24992V6.04167ZM5.24992 9.20833H6.83325V10.7917H5.24992V9.20833ZM5.24992 12.375H14.7499V13.9583H5.24992V12.375ZM9.20825 9.20833H10.7916V10.7917H9.20825V9.20833ZM9.20825 6.04167H10.7916V7.625H9.20825V6.04167ZM13.1666 6.04167H14.7499V7.625H13.1666V6.04167ZM13.1666 9.20833H14.7499V10.7917H13.1666V9.20833Z" fill="white" fill-opacity="0.8"/>
                  </svg>
                </div>
              </div>
            </div>
            <div data-layer="设置快捷键" style="justify-content: center; display: flex; flex-direction: column; color: rgba(255, 255, 255, 0.80); font-size: 13px; font-family: Roboto; font-weight: 400; line-height: 13px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">设置快捷键</div>
            <div data-layer="Container" class="Container" style="flex: 1 1 0; justify-content: flex-end; align-items: center; display: flex">
              <div data-svg-wrapper data-layer="Component 1" data-variant="20" class="Component1" style="position: relative">
                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.4501 9.0001L6.5251 6.0751C6.3876 5.9376 6.31885 5.7626 6.31885 5.5501C6.31885 5.3376 6.3876 5.1626 6.5251 5.0251C6.6626 4.8876 6.8376 4.81885 7.0501 4.81885C7.2626 4.81885 7.4376 4.8876 7.5751 5.0251L11.0251 8.4751C11.1001 8.5501 11.1532 8.63135 11.1845 8.71885C11.2157 8.80635 11.2313 8.9001 11.2313 9.0001C11.2313 9.1001 11.2157 9.19385 11.1845 9.28135C11.1532 9.36885 11.1001 9.4501 11.0251 9.5251L7.5751 12.9751C7.4376 13.1126 7.2626 13.1813 7.0501 13.1813C6.8376 13.1813 6.6626 13.1126 6.5251 12.9751C6.3876 12.8376 6.31885 12.6626 6.31885 12.4501C6.31885 12.2376 6.3876 12.0626 6.5251 11.9251L9.4501 9.0001Z" fill="white" fill-opacity="0.6"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
        <div data-layer="Component 11" data-:hover="false" data-variant="1" class="Component11" style="width: 264px; height: 32px; padding-left: 9px; padding-right: 9px; padding-top: 1px; padding-bottom: 1px; position: relative; background: rgba(255, 255, 255, 0.09); border-radius: 6px; outline: 1px rgba(255, 255, 255, 0.12) solid; outline-offset: -1px; justify-content: center; align-items: center; gap: 11px; display: inline-flex">
          <div data-layer="Container" class="Container" style="width: 30px; height: 30px; left: 233px; top: 1px; position: absolute; justify-content: center; align-items: center; display: flex">
            <div data-svg-wrapper data-layer="Component 1" data-variant="26" class="Component1" style="position: relative">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 14.25C4.54822 14.25 1.75 11.4517 1.75 8C1.75 4.54822 4.54822 1.75 8 1.75C11.4517 1.75 14.25 4.54822 14.25 8C14.25 11.4517 11.4517 14.25 8 14.25ZM8 13C10.7614 13 13 10.7614 13 8C13 5.23857 10.7614 3 8 3C5.23857 3 3 5.23857 3 8C3 10.7614 5.23857 13 8 13ZM7.375 4.875H8.625V6.125H7.375V4.875ZM7.375 7.375H8.625V11.125H7.375V7.375Z" fill="white" fill-opacity="0.5"/>
              </svg>
            </div>
          </div>
          <div data-svg-wrapper data-layer="Frame 6" class="Frame6" style="position: relative">
            <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="1.23684" y="0.736842" width="18.5263" height="18.5263" rx="2.63158" stroke="url(#paint0_linear_42_922)" stroke-width="1.47368"/>
            <path d="M11.7441 8.31592V12.3122C11.7441 12.7598 11.9238 13.189 12.2437 13.5055C12.5635 13.822 12.9973 13.9998 13.4497 13.9998H14.1319C14.5842 13.9998 15.018 13.822 15.3379 13.5055C15.6577 13.189 15.8374 12.7598 15.8374 12.3122V8.31592" stroke="url(#paint1_linear_42_922)" stroke-width="1.62008" stroke-linejoin="round"/>
            <path d="M5.55273 5.47388V13.4739H10.1843" stroke="url(#paint2_linear_42_922)" stroke-width="1.89474" stroke-linejoin="round"/>
            <defs>
            <linearGradient id="paint0_linear_42_922" x1="-8.5" y1="-3.5" x2="-18.5" y2="12" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FFB700"/>
            <stop offset="1" stop-color="#996E00"/>
            </linearGradient>
            <linearGradient id="paint1_linear_42_922" x1="13.7908" y1="8.31592" x2="13.7908" y2="13.9998" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FFB700"/>
            <stop offset="1" stop-color="#996E00"/>
            </linearGradient>
            <linearGradient id="paint2_linear_42_922" x1="7.86852" y1="5.47388" x2="7.86852" y2="13.4739" gradientUnits="userSpaceOnUse">
            <stop stop-color="#FFB700"/>
            <stop offset="1" stop-color="#996E00"/>
            </linearGradient>
            </defs>
            </svg>
          </div>
          <div data-layer="Container" class="Container" style="justify-content: flex-start; align-items: center; display: flex">
            <div data-layer="Text" class="Text" style="justify-content: center; display: flex; flex-direction: column; color: rgba(255, 255, 255, 0.80); font-size: 14px; font-family: Roboto; font-weight: 400; line-height: 14px; word-wrap: break-word; text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.50)">学习模式</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>