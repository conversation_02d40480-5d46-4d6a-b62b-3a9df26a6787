# 字幕翻译功能需求文档

## 项目概述

基于现有 Lucid Extension 浏览器扩展，开发字幕翻译功能，通过捕获、解析和自定义显示字幕，来增强用户在各大流媒体平台上的视频观看体验。该功能将作为扩展的新核心功能模块，与现有的文本翻译和词汇学习功能协同工作。

## 功能目标

### 主要目标
- 智能检测并捕获流媒体平台的原生字幕数据
- 实时翻译字幕内容并精确同步视频播放
- 提供自定义字幕显示界面，支持多语言翻译
- 无缝集成到现有的 Lucid Extension 架构中

### 预期价值
- 提升非母语用户的视频观看体验
- 支持语言学习场景，提供双语字幕显示
- 扩展现有翻译能力到视频内容领域
- 增强扩展的用户价值和使用粘性

## 技术需求

### 核心架构要求

#### 1. 平台兼容性

> **注：当前版本只需要支持和测试适配 YouTube，代码上需预留其他平台扩展能力。**

- **YouTube**: 拦截 `/api/timedtext` API 请求获取字幕数据
- **Netflix**: 重写 `JSON.parse` 函数捕获嵌套字幕信息
- **其他平台**: 支持 `.vtt`、`.srt` 等标准字幕格式检测
- **通用方案**: 提供 AI 语音识别备用方案（未来扩展）

#### 2. 字幕获取策略
```
优先级顺序：
1. 原生字幕捕获（网络请求拦截）
2. DOM 字幕元素监听（备用方案，暂不实现）
3. AI 语音识别（备用方案，暂不实现）
```

#### 3. 网络拦截机制
- **Fetch API 拦截**: `NetworkInterceptor.onFetch()`
- **XMLHttpRequest 拦截**: `NetworkInterceptor.onXHR()`
- **JSON.parse 重写**: 针对 Netflix 等复杂平台
- **请求过滤**: 智能识别字幕相关的网络请求

#### 4. 字幕解析引擎
- **多格式支持**: SRT, VTT, ASS 等格式
- **正则表达式引擎**: 精确提取时间戳和文本内容
- **时间格式转换**: 统一转换为毫秒精度
- **说话人标签处理**: 解析 `<v ...>` 等标签

### 集成架构设计

#### 1. 模块目录结构
```
src/features/subtitle-translation/
├── index.ts                      # 统一导出入口
├── subtitle-manager.ts           # 字幕管理核心服务
├── network-interceptor.ts        # 网络请求拦截器
├── parsers/                      # 字幕解析器
│   ├── index.ts
│   ├── srt-parser.ts
│   ├── vtt-parser.ts
│   └── netflix-parser.ts
├── platforms/                    # 平台适配器
│   ├── index.ts
│   ├── youtube-adapter.ts
│   ├── netflix-adapter.ts
│   └── generic-adapter.ts
├── types.ts                      # 类型定义
├── config.ts                     # 配置管理
└── __tests__/                    # 测试文件
```

#### 2. 组件集成
```
src/components/SubtitleOverlay/
├── index.ts
├── SubtitleOverlay.tsx           # 字幕显示组件
├── SubtitleOverlay.module.css    # 组件样式
├── SubtitleSettings.tsx          # 字幕设置面板
└── SubtitleSync.tsx              # 同步控制组件
```

#### 3. 内容脚本集成
```
src/content/
├── subtitle-manager.ts           # 字幕管理器（新增）
├── view-controller.ts            # 更新：集成字幕管理器
└── interaction-handlers.ts       # 更新：添加字幕交互
```

## 功能规格

### 1. 字幕检测与捕获

#### 平台识别
- 通过 `window.location.href` 检测当前平台
- 存储平台标识符于 `wc.platform` 变量
- 调用平台专属处理器的 `initPlayer()` 方法

#### 网络拦截
```typescript
interface NetworkInterceptor {
  onFetch(request: Request): Promise<Response | null>
  onXHR(xhr: XMLHttpRequest): void
  onJSONParse(text: string): any
}
```

#### 字幕数据捕获
- YouTube: 监听包含 `/api/timedtext` 的请求
- Netflix: 拦截 JSON 解析过程获取嵌套数据
- 通用: 检测 `.vtt`、`.srt` 文件请求

### 2. 字幕解析与处理

#### 解析器接口
```typescript
interface SubtitleParser {
  format: 'srt' | 'vtt' | 'ass' | 'json'
  parse(rawText: string): SubtitleEntry[]
  validate(rawText: string): boolean
}

interface SubtitleEntry {
  text: string
  start: number    // 毫秒
  end: number      // 毫秒
  speaker?: string // 说话人标识
}
```

#### 时间同步机制
- 监听视频播放器时间更新事件
- 高频触发的同步处理（优化性能）
- 精确匹配当前播放时间的字幕行

### 3. 翻译集成

#### 翻译服务集成
- 复用现有的 `@features/translate` 模块
- 支持批量翻译字幕条目
- 缓存翻译结果避免重复请求

#### 翻译配置
```typescript
interface SubtitleTranslationConfig {
  enabled: boolean
  sourceLanguage: string
  targetLanguage: string
  showOriginal: boolean
  showTranslation: boolean
  translationEngine: 'google' | 'microsoft' | 'ai'
}
```

### 4. 用户界面

#### 字幕显示组件
- React 组件：`lucid-caption-window` 作为字幕组件
- React 组件: 配置面板
  - 在youtube播放器的 ytp-chrome-bottom>ytp-chrome-controls>ytp-right-controls 里面添加一个button (svg我会提供)
  - 点击弹开配置面板 `lucid-subtitle-setting` 
    - 可配置显示位置和样式 (前期版本只留位置)
- 支持双语显示 译文 原文

#### 视觉设计要求
- 遵循现有 Lucid UI 主题
- 只有一个颜色体系
- 响应式字体大小调整
- 可配置透明度和位置

## 技术实现规范

### 1. 代码规范遵循

#### 项目结构规范
- 遵循现有的 `src/features/` 模块化结构
- 使用路径别名：`@features/subtitle-translation`
- 统一导出模式：每个模块包含 `index.ts`

#### 组件开发规范
- React 19 + TypeScript 严格类型检查
- CSS Modules 模块化样式
- 组件命名：`lu-` 前缀

#### 管理器模式
- 集成到现有的管理器分层结构
- `subtitle-manager.ts` 专门处理字幕逻辑
- 通过 `view-controller.ts` 统一协调

### 2. 性能优化要求

#### 网络拦截优化
- 智能过滤，避免拦截无关请求
- 异步处理，不阻塞页面加载
- 内存管理，及时清理拦截器

#### 解析优化
- 正则表达式预编译
- 增量解析大型字幕文件
- 缓存解析结果

#### 渲染优化
- 虚拟化长字幕列表
- 防抖处理频繁的时间更新
- 批量更新 DOM 操作

### 3. 错误处理

#### 容错机制
- 字幕获取失败的优雅降级
- 解析错误的兜底处理
- 翻译服务异常的重试机制

#### 调试支持
- 集成现有的 `debugLog` 系统
- 字幕数据的可视化调试
- 性能监控和错误上报

## 测试要求

### 1. 单元测试
- 字幕解析器测试：各种格式的解析准确性
- 网络拦截器测试：模拟不同平台的请求
- 翻译集成测试：批量翻译功能

### 2. 集成测试
- 端到端字幕流程测试
- 多平台兼容性测试
- 性能压力测试

### 3. 用户界面测试
- 组件渲染测试
- 交互功能测试
- 样式隔离测试

## 部署和配置

### 1. 权限要求
```json
{
  "permissions": [
    "storage", "tts", "contextMenus", "activeTab",
    "webRequest"  // 新增：网络请求拦截
  ],
  "host_permissions": [
    "<all_urls>",
    "https://www.youtube.com/*",
    "https://www.netflix.com/*"
  ]
}
```

### 2. 构建配置
- 更新 `wxt.config.ts` 添加字幕资源
- 配置 Web Accessible Resources
- 添加内容脚本匹配规则

### 3. 用户配置
- 存储键前缀：`lucid-subtitle-`
- 配置验证规则集成
- 实时配置保存机制

## 开发里程碑

### 阶段一：基础架构（2周）
- [ ] 创建功能模块目录结构
- [ ] 实现网络请求拦截器
- [ ] 开发字幕解析引擎核心

### 阶段二：平台适配（2周）
- [ ] YouTube 平台适配器
- [ ] Netflix 平台适配器
- [ ] 通用平台支持

### 阶段三：UI集成（1周）
- [ ] 字幕显示组件开发
- [ ] 设置面板集成
- [ ] 样式和主题适配

### 阶段四：测试和优化（1周）
- [ ] 单元测试完善
- [ ] 性能优化
- [ ] 用户体验测试

## 风险评估

### 技术风险
- **网络拦截复杂性**: 不同平台的字幕获取机制差异较大
- **性能影响**: 网络拦截可能影响页面加载性能
- **兼容性问题**: 平台更新可能破坏字幕获取逻辑

### 缓解策略
- 实现多种获取策略的优雅降级
- 性能监控和优化机制
- 建立平台兼容性测试套件

## 成功指标

### 功能指标
- 支持平台数量：YouTube、Netflix + 2个额外平台
- 字幕格式支持：SRT、VTT、ASS
- 翻译准确性：与现有翻译功能一致

### 性能指标
- 字幕捕获成功率 > 95%
- 翻译响应时间 < 2秒
- 页面性能影响 < 5%

### 用户体验指标
- 字幕同步精度 < 100ms 误差
- 界面响应流畅性
- 设置配置的易用性