<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Toolfull Dictionary – Components Demo</title>
    <style>
      :root {
        --glass-bg: rgba(30, 30, 30, 0.45);
        --glass-border: rgba(255, 255, 255, 0.15);
        --text-gray: #dadada;
        --text-white: #ffffff;
        font-size: 16px;
      }
      body {
        display: flex;
        flex-direction: column;
        gap: 40px;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: #000
          url("https://images.unsplash.com/photo-1517694712202-14dd9538aa97?auto=format&fit=crop&w=2400&q=80")
          center/cover fixed;
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI",
          "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue",
          Helvetica, Arial, sans-serif;
        color: var(--text-gray);
      }
      /* ================= 大卡片 ================= */
      .toolfull-card {
        backdrop-filter: blur(22px) saturate(160%);
        -webkit-backdrop-filter: blur(22px) saturate(160%);
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 14px;
        width: 360px;
        padding: 24px 28px;
        box-shadow: 0 4px 32px rgba(0, 0, 0, 0.45);
        position: relative;
      }

      /* -- 省略此前已实现的 card 内部样式与脚本 (保留 but 折叠) -- */
      /* ... 之前代码已被用户确认可用，若需查看请展开 ... */

      /* ================= 迷你 Tooltip ================= */
      .lu-tooltip {
        backdrop-filter: blur(14px) saturate(160%);
        -webkit-backdrop-filter: blur(14px) saturate(160%);
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 8px;
        padding: 4px 12px;
        font-size: 0.88em;
        line-height: 1.45;
        color: var(--text-gray);
        display: inline-block;
        white-space: nowrap;
        transition: color 0.15s ease-out;
      }
      .lu-tooltip:hover {
        color: var(--text-white);
      }
    </style>
  </head>
  <body>
    <!-- 迷你 tooltip 仅一行，无按钮 -->
    <span class="lu-tooltip">n.&nbsp;概述&nbsp;&nbsp;v.&nbsp;总结,概述</span>
  </body>
</html>
