<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Toolfull Dictionary Card – overview</title>
    <style>
      :root {
        --glass-bg: rgba(30, 30, 30, 0.45);
        --glass-border: rgba(255, 255, 255, 0.15);
        --text-gray: #dadada;
        --text-white: #ffffff;
        font-size: 16px;
      }
      body {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: #000
          url("https://images.unsplash.com/photo-1517694712202-14dd9538aa97?auto=format&fit=crop&w=2400&q=80")
          center/cover fixed;
        margin: 0;
        font-family:
          -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
          "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica,
          Arial, sans-serif;
        color: var(--text-gray);
      }
      .toolfull-card {
        backdrop-filter: blur(22px) saturate(160%);
        -webkit-backdrop-filter: blur(22px) saturate(160%);
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 14px;
        width: 360px;
        padding: 24px 28px;
        box-shadow: 0 4px 32px rgba(0, 0, 0, 0.45);
        position: relative;
      }
      .word-line {
        font-size: 1.6em;
        color: var(--text-white);
        letter-spacing: 0.02em;
        margin-bottom: 8px;
      }
      .word-line .dot {
        opacity: 0.55;
      }

      /* Phonetics */
      .phonetics {
        display: flex;
        gap: 10px;
        margin-bottom: 12px;
      }
      .phonetic {
        cursor: pointer;
        font-size: 0.85em;
        color: var(--text-gray);
        transition:
          color 0.15s ease-out,
          max-width 0.3s ease-out,
          opacity 0.3s ease-out;
        white-space: nowrap;
        overflow: hidden;
        display: flex;
        align-items: center;
      }
      .phonetic:hover {
        color: var(--text-white);
      }
      .tag {
        text-transform: uppercase;
        font-weight: 600;
        font-size: 0.7em;
        padding: 2px 5px;
        border-radius: 3px;
        margin-right: 5px;
        border: 1px solid var(--glass-border);
        background: rgba(255, 255, 255, 0.07);
        color: var(--text-white);
      }

      /* 默认仅 US 可见 */
      .phonetic.uk {
        max-width: 0;
        opacity: 0;
      }
      .phonetics:hover .phonetic.uk {
        max-width: 160px;
        opacity: 1;
      }

      /* Definitions */
      .definition {
        line-height: 1.5;
        margin: 8px 0;
        position: relative;
      }
      .definition .hoverable {
        cursor: pointer;
        transition: color 0.15s ease-out;
      }
      .definition .hoverable:hover {
        color: var(--text-white);
      }

      .detail {
        display: none;
        margin-top: 4px;
        font-size: 0.88em;
        line-height: 1.6;
        color: var(--text-gray);
        white-space: pre-wrap;
      }
      .detail.show {
        display: block;
      }

      /* token words */
      .token {
        transition: color 0.15s ease-out;
        cursor: pointer;
      }
      .token:hover {
        color: var(--text-white);
      }

      /* Action buttons */
      .action-buttons {
        position: absolute;
        top: 16px;
        right: 16px;
        display: flex;
        gap: 8px;
      }
      .action-btn {
        width: 24px;
        height: 24px;
        cursor: pointer;
        border: none;
        background: none;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        transition:
          transform 0.15s ease-out,
          opacity 0.15s ease-out;
        opacity: 0.7;
      }
      .action-btn:hover {
        transform: scale(1.1);
        opacity: 1;
      }

      .action-btn svg {
        width: 100%;
        height: 100%;
        color: var(--text-gray);
        fill: var(--text-gray);
        transition:
          fill 0.15s ease-out,
          color 0.15s ease-out;
      }

      .action-btn:hover svg {
        color: var(--text-white);
        fill: var(--text-white);
      }
      .action-btn.favorite.active svg {
        fill: #ff4757;
      }
      .action-btn.favorite.active:hover svg {
        fill: #ff3838;
      }
    </style>
  </head>
  <body>
    <div class="toolfull-card" id="card">
      <!-- Action buttons -->
      <div class="action-buttons">
        <!-- Reduce frequency button -->
        <button class="action-btn reduce" title="减少生词查询数">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8 12.4854L12.2426 16.728L20.727 8.24268M3 12.4854L7.24264 16.728M15.7279 8.24268L12.5 11.5001"
              stroke="white"
              fill="none"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
        <!-- Favorite button -->
        <button class="action-btn favorite active" title="收藏生词">
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"
            />
          </svg>
        </button>
      </div>

      <div class="word-line">
        o<span class="dot">·</span>ver<span class="dot">·</span>view
      </div>

      <div class="phonetics">
        <span class="phonetic us" data-voice="en-US"
          ><span class="tag">us</span>/ˈoʊvɚˌvjuː/</span
        >
        <span class="phonetic uk" data-voice="en-GB"
          ><span class="tag">uk</span>/ˈəʊvəvjuː/</span
        >
      </div>

      <p class="definition zh">
        n.&nbsp;<span class="hoverable">概述</span>
        <span
          class="detail"
          data-text="noun a general review or summary of a subject: a critical overview of the scientific issues of our time."
        ></span>
      </p>

      <p class="definition zh">
        v.&nbsp;<span class="hoverable">总结,概述</span>
        <span
          class="detail"
          data-text="verb [with object] give a general review or summary of: the report overviews the needs of the community."
        ></span>
      </p>
    </div>

    <script>
      /* 发音功能 for phonetics */
      document.querySelectorAll(".phonetic").forEach(function (span) {
        span.addEventListener("click", function () {
          const utter = new SpeechSynthesisUtterance("overview");
          utter.lang = this.dataset.voice;
          speechSynthesis.speak(utter);
        });
      });

      /* hover 1.2s 展开英文解释并使用打字机效果；出现后不再隐藏 */
      document.querySelectorAll(".definition").forEach(function (def) {
        const hoverable = def.querySelector(".hoverable");
        const detail = def.querySelector(".detail");
        let timer;
        hoverable.addEventListener("mouseenter", () => {
          if (detail.classList.contains("show")) return; // 已展开
          timer = setTimeout(() => {
            typeWriterTokens(detail, detail.dataset.text, 25);
          }, 800);
        });
        hoverable.addEventListener("mouseleave", () => {
          clearTimeout(timer);
        });
      });

      function typeWriterTokens(elem, text, speed) {
        elem.classList.add("show");
        let i = 0;
        let buffer = "";
        function typing() {
          if (i < text.length) {
            buffer += text.charAt(i);
            elem.textContent = buffer;
            i++;
            setTimeout(typing, speed);
          } else {
            // 打字完成后把文本拆分成 token span
            elem.innerHTML = text
              .split(" ")
              .map((w) => `<span class="token">${w}</span>`)
              .join(" ");
          }
        }
        typing();
      }

      /* 监听 token 点击发音 */
      document.addEventListener("click", function (e) {
        if (e.target.classList.contains("token")) {
          const word = e.target.textContent.replace(/[^a-zA-Z'-]/g, "");
          if (word) {
            const utter = new SpeechSynthesisUtterance(word);
            utter.lang = "en-US";
            speechSynthesis.speak(utter);
          }
        }
      });

      /* Action buttons functionality */
      document
        .querySelector(".action-btn.reduce")
        .addEventListener("click", function () {
          // 减少生词查询数功能
          console.log("Reduce word frequency clicked");
          // 这里可以添加实际的减少功能逻辑
        });

      document
        .querySelector(".action-btn.favorite")
        .addEventListener("click", function () {
          // 切换收藏状态
          this.classList.toggle("active");
          const isFavorited = this.classList.contains("active");

          // 更新 SVG 路径以显示实心或空心爱心
          const svg = this.querySelector("svg");
          if (isFavorited) {
            // 实心爱心
            svg.innerHTML =
              '<path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>';
          } else {
            // 空心爱心
            svg.innerHTML =
              '<path d="M16.5 3c-1.74 0-3.41.81-4.5 2.09C10.91 3.81 9.24 3 7.5 3 4.42 3 2 5.42 2 8.5c0 3.78 3.4 6.86 8.55 11.54L12 21.35l1.45-1.32C18.6 15.36 22 12.28 22 8.5 22 5.42 19.58 3 16.5 3zm-4.4 15.55l-.1.1-.1-.1C7.14 14.24 4 11.39 4 8.5 4 6.5 5.5 5 7.5 5c1.54 0 3.04.99 3.57 2.36h1.87C13.46 5.99 14.96 5 16.5 5c2 0 3.5 1.5 3.5 3.5 0 2.89-3.14 5.74-7.9 10.05z"/>';
          }

          console.log("Favorite toggled:", isFavorited);
          // 这里可以添加实际的收藏功能逻辑
        });
    </script>
  </body>
</html>
