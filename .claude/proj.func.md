lucid/
├── entrypoints/
│ ├── content.ts # 内容脚本主入口
│ ├── background.ts # 后台脚本主入口
│ └── popup.html # 插件弹出页面 (如果需要)
│
└── src/
| ├── components/ # 可复用的 UI 组件
│ ├── ui/
│ └── Tooltip.tsx
│ └── Tooltip.module.css
│ └── Toolfull.tsx
│ └── Toolfull.module.css
│ └── Slider.tsx
│ └── Slider.module.css
├── ui-manager/ # 【核心】统一的 UI 管理器
│ ├── ShadowView.ts # 【新】管理单个 Shadow DOM 实例的类
│ └── uiManager.ts # 【新】管理所有 Shadow DOM 实例的单例 │
├── features/ # 核心功能模块
│ ├── dictionary/ # 查词功能 (用于 Tooltip/Toolfull)
│ ├── dictionary.data.ts # 封装查词 API 和缓存
│ └── useDictionary.ts # React Hook
│ │
│ ├── translator/ # 翻译功能 (用于大段文本)
│ ├── translator.api.ts # 封装翻译 API (如 Google/DeepL)
│ ├── translator.dom.ts # DOM 操作：文本提取、节点替换
│ └── useTranslator.ts # React Hook
│ │
│ ├── highlight/ # 高亮功能
│ ├── highlight.ts
│ └── useHighlight.ts
│ │
│ └── slider/ # 侧边栏功能
│ ├── sliderManager.ts
│ └── useSlider.ts
│
├── services/ # 共享服务
│ ├── storage.ts
│ └── eventBus.ts
│
├── styles/ # 全局样式
│ └── main.css
│
└── types/ # 全球类型定义
└── index.ts

---

功能

- 点击插件图标 弹出 slider 用 Shadow DOM 包裹

  - 登录
  - 生词本 今日生词
  - 单词高亮设置
    - 高亮样式
    - hover
      - 弹出 tooltip 还是 在高亮单词后面 的中文解释 从模糊到显示
  - 翻译设置
  - 当前页面生词

- shift 选择单词 -> 高亮所有这个单词 -> 弹出 toolfull
- hover 已经高亮的单词 -> 弹出 tooltip -> hover 在 tooltip 下面 2s -> 扩张成 toolfull
- 点击其他地方 -> 关闭 toolfull
-

toolfull 参考 .claude/design/toolfull.html
带音节的单词
音标
pos chinese_short chinese_short (列表展示所有同 pos 的 Chinese 空格分隔)
→ 点击 chinese_short 下面扩展英文 definition 点击不同的 chinese_short 可以更改展示的 definition
pos chinese_short → 点击下面扩展英文 definition

tooltip 参考 .claude/design/tooltip.html
音标 chinese_short chinese_short (列表展示所有同 pos 的 Chinese) 空格 音标 chinese_short chinese_short (列表展示所有同 pos 的 Chinese)

使用方法：

🧹 清除所有高亮

// 控制台中执行
clearLucidHighlights()

// 或按快捷键: Ctrl+Shift+C

🎯 设置单个词汇高亮

// 控制台中执行
setLucidWord("chrome")

🔍 查看调试信息

// 控制台中执行  
 lucidDebug()
