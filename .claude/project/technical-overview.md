# Lucid Browser Extension - 完整技术设计概览

## 项目愿景

构建一个现代化的浏览器扩展，为英语学习者提供智能的单词查询、翻译和学习功能。通过 Shadow DOM 技术确保在任何网页上都能稳定运行，不影响原始页面样式。

## 技术栈

- **框架**: WXT (Browser Extension Framework)
- **UI**: React 18 + TypeScript
- **样式**: CSS Modules + CSS Variables
- **隔离**: Shadow DOM
- **存储**: Browser Extension Storage API
- **构建**: Vite (由 WXT 集成)

## 架构设计

### 1. 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        Lucid Extension                          │
├─────────────────────────────────────────────────────────────────┤
│  Entry Points                                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │  Popup      │  │   Content   │  │ Background  │              │
│  │   (WXT)     │  │   Script    │  │   Script    │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
├─────────────────────────────────────────────────────────────────┤
│  Core Systems                                                   │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                 UI Manager                                  │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │ │
│  │  │ ShadowView  │  │ Animations  │  │ uiManager   │        │ │
│  │  │   (Class)   │  │  Manager    │  │ (Singleton) │        │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘        │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                Feature Modules                              │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │ │
│  │  │ Dictionary  │  │ Translator  │  │  Highlight  │        │ │
│  │  │   Feature   │  │   Feature   │  │   Feature   │        │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘        │ │
│  │                                                             │ │
│  │  ┌─────────────┐  ┌─────────────┐                          │ │
│  │  │  Settings   │  │   Storage   │                          │ │
│  │  │   Feature   │  │   Service   │                          │ │
│  │  └─────────────┘  └─────────────┘                          │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  React Components                                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   Tooltip   │  │  Toolfull   │  │   Slider    │              │
│  │ (Mini Info) │  │ (Detailed)  │  │ (Settings)  │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
```

### 2. 数据流设计

```
User Action → Event Detection → Feature Processing → UI Rendering → Shadow DOM
     ↓              ↓                ↓                  ↓             ↓
 鼠标悬停      highlight.manager   dictionary.api    uiManager    ShadowView
 文本选择   →  eventBus.emit()  →  lookupWord()  →  showTooltip() → render()
 快捷键                                                              ↓
                                                                 网页显示
```

## 核心功能模块

### 1. Shadow DOM UI 系统

**核心类**:
- `ShadowView`: 单个 Shadow DOM 实例管理
- `UIManager`: 全局 UI 协调器
- `AnimationManager`: 动画效果处理

**特性**:
- 完全样式隔离
- 流畅动画过渡
- 主题支持 (明/暗)
- 智能定位和尺寸适配

### 2. 智能查词系统

**组件**:
- `DictionaryAPI`: 多源 API 接口
- `DictionaryCache`: 本地缓存管理
- `useDictionary`: React Hook

**特性**:
- 多 API 降级策略
- 智能缓存系统
- 离线数据支持
- 错误处理和重试

### 3. 高亮交互系统

**流程**:
1. 文本选择检测
2. 单词提取和验证
3. 页面高亮渲染
4. 悬停交互处理
5. Tooltip ↔ Toolfull 转换

**特性**:
- 智能单词边界检测
- 非侵入式高亮样式
- 精确的时机控制
- 流畅的组件切换

## 组件设计详解

### Tooltip 组件
```
┌──────────────────────────────────┐
│  [音标]  简释1  简释2  简释3     │
│  /ˈəʊvəvjuː/  概述  总结  概况   │
└──────────────────────────────────┘
```
- **目的**: 快速预览单词信息
- **触发**: 悬停高亮单词 200ms
- **时长**: 悬停 2 秒后自动扩展

### Toolfull 组件
```
┌─────────────────────────────────────────┐
│  o·ver·view                      [❤] [✓] │
│                                         │
│  [US] /ˈoʊvɚˌvjuː/  [UK] /ˈəʊvəvjuː/  │
│                                         │
│  n. 概述 总结                           │
│     ↳ a general review or summary...    │
│                                         │
│  v. 概述 总结                           │
│     ↳ give a general review...          │
└─────────────────────────────────────────┘
```
- **目的**: 详细的单词学习界面
- **功能**: 音节、音标、多义项、例句、收藏
- **交互**: 点击音标发音、点击释义展开

### Slider 组件
```
┌────────────────────────────────┐
│  Lucid English Helper    [×]   │
├────────────────────────────────┤
│  📚 生词本 (12)                │
│  📖 今日新词 (3)               │
│  🎯 学习目标                   │
│  ⚙️  设置                      │
│  │  ✓ 自动高亮                │
│  │  ✓ 悬停显示 Tooltip        │
│  │  📱 主题: 暗色             │
│  │  🔊 发音: 美音             │
│  └─ 当前页面: 5 个生词          │
└────────────────────────────────┘
```
- **目的**: 扩展设置和数据管理
- **位置**: 页面右侧，固定定位
- **功能**: 用户设置、学习统计、生词管理

## 性能优化策略

### 1. 渲染性能
- **延迟加载**: 组件按需创建
- **虚拟化**: 大列表使用虚拟滚动
- **动画优化**: GPU 加速和帧率控制
- **内存管理**: 及时清理无用的 Shadow DOM

### 2. 网络性能
- **请求去重**: 相同查词请求合并
- **智能缓存**: 本地存储 + 过期策略
- **API 降级**: 主要 → 备用 → 本地
- **预加载**: 常用词汇预缓存

### 3. 交互性能
- **防抖节流**: 高频事件的性能优化
- **时机控制**: 精确的显示/隐藏时机
- **状态管理**: 避免不必要的重渲染
- **事件委托**: 减少事件监听器数量

## 开发工作流

### 1. 开发环境设置
```bash
# 安装依赖
npm install

# 开发服务器 (Chrome)
npm run dev

# Firefox 开发
npm run dev:firefox

# 类型检查
npm run compile
```

### 2. 项目结构映射
```
实际开发结构                  →   运行时结构
─────────────────────────────────────────────────
src/components/Tooltip.tsx    →   Shadow DOM 中渲染
src/ui-manager/uiManager.ts   →   content.ts 调用
src/features/dictionary/      →   独立功能模块
entrypoints/content.ts        →   注入到网页
.output/chrome-mv3/           →   浏览器加载
```

### 3. 构建和部署
```bash
# 生产构建
npm run build

# 打包分发
npm run zip        # Chrome
npm run zip:firefox # Firefox

# 发布到商店
# .output/[browser]-mv3-prod.zip
```

## 安全考虑

### 1. 内容安全策略
- **CSP 兼容**: 避免内联脚本和样式
- **域名限制**: 只访问必要的 API 域名
- **权限最小化**: 只请求必需的浏览器权限

### 2. 数据隐私
- **本地优先**: 敏感数据不上传
- **加密存储**: 用户偏好加密保存
- **匿名化**: API 请求不包含用户标识

### 3. 注入安全
- **Shadow DOM 隔离**: 防止样式污染
- **事件隔离**: 避免与页面事件冲突
- **DOM 操作限制**: 只修改必要的 DOM

## 测试策略

### 1. 单元测试
- React 组件测试
- API 功能测试
- 工具函数测试

### 2. 集成测试
- Shadow DOM 创建和销毁
- 组件间通信
- 缓存和存储

### 3. 端到端测试
- 真实网页环境测试
- 多浏览器兼容性
- 性能基准测试

## 部署和维护

### 1. 版本管理
- 语义化版本号
- 变更日志维护
- 向后兼容性保证

### 2. 监控和分析
- 错误报告收集
- 性能指标监控
- 用户行为分析（匿名）

### 3. 持续改进
- 用户反馈收集
- 功能迭代计划
- 性能优化路线图

---

这个技术设计为 Lucid 扩展提供了完整的开发指南，确保项目能够高质量地交付，同时保持代码的可维护性和扩展性。所有设计都考虑了个人开发的实际情况，在功能完整性和复杂度之间找到了最佳平衡点。