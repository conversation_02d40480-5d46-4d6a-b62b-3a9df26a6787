# Shadow DOM 实现方案 - 最小可维护单体

## 核心设计理念

**最小可维护单体**：能跑、好改、文件少
- 不搞 DDD / 多层 Onion，只保留「入口 → 功能 → UI」三段
- 所有文件握在 `src/` 一层，不再多级嵌套
- 若某个改动会让文件数翻倍，就先不做

## 极简架构设计

### 目录结构（最终版 - 文件数最少）
```
src/
├── components/           # 3个组件文件夹
│   ├── Tooltip/         
│   │   ├── Tooltip.tsx   
│   │   └── tooltip.css   
│   ├── Toolfull/        
│   │   ├── Toolfull.tsx  
│   │   └── toolfull.css  
│   └── Slider/          
│       ├── Slider.tsx    
│       └── slider.css    
├── features/            # 3个纯函数模块
│   ├── dictionary.ts    # 查词逻辑 + 缓存
│   ├── highlight.ts     # 高亮捕获
│   └── settings.ts      # 配置管理
├── ui-manager/          # 只要2个文件
│   ├── ShadowView.ts    # Shadow DOM + 动画（合并版）
│   └── uiManager.ts     # Map管理 + 生命周期
├── services/            # 2个基础服务
│   ├── storage.ts       # 双层缓存一体化
│   └── eventBus.ts      # 简单事件（可选）
├── styles/              # 2个样式文件
│   ├── main.css         # CSS变量 + 基础样式
│   └── animations.css   # 所有动画定义
└── types.ts             # 全局类型定义
```

## 核心实现方案

### 1. ShadowView 类 - 极简合并版

```typescript
// src/ui-manager/ShadowView.ts
import { createRoot, Root } from "react-dom/client";
import React from "react";

export interface ShadowViewOptions {
  id: string;
  component: React.ReactElement;
  styles: string[];           // 支持多个样式文件
  position?: { x: number; y: number };
  theme?: 'light' | 'dark';   // 主题支持
  animation?: string;         // 动画类型: fade-in | slide-in | expand
  hostClass?: string;
  zIndex?: number;
  closeOnClickOutside?: boolean;
}

export class ShadowView {
  public readonly id: string;
  private hostElement: HTMLElement;
  private shadowRoot: ShadowRoot;
  private reactRoot: Root;
  private isVisible = false;
  private isDestroyed = false;
  private options: ShadowViewOptions;
  private clickHandler?: (event: MouseEvent) => void; // 防止内存泄漏

  constructor(options: ShadowViewOptions) {
    this.options = options;
    this.id = options.id;
    
    this.createHost();
    this.createShadowRoot();
    this.injectStyles();
    this.setupReactRoot();
    this.setupEventListeners();
  }

  private createHost(): void {
    this.hostElement = document.createElement("div");
    this.hostElement.id = `lucid-shadow-${this.id}`;
    this.hostElement.className = `lucid-shadow-host ${this.options.hostClass || ''}`;
    
    // 设置基本样式
    this.hostElement.style.position = 'fixed';
    this.hostElement.style.zIndex = String(this.options.zIndex || 10000);
    this.hostElement.style.pointerEvents = 'none';
    this.hostElement.style.transition = 'all 0.2s ease-out';
    
    if (this.options.position) {
      this.updatePosition(this.options.position.x, this.options.position.y);
    }
    
    document.body.appendChild(this.hostElement);
  }

  private createShadowRoot(): void {
    this.shadowRoot = this.hostElement.attachShadow({ mode: "open" });
  }

  private injectStyles(): void {
    // 简化样式注入 - 使用 CSS 变量统一管理
    const isDark = this.options.theme !== 'light';
    const baseStyles = `
      :host {
        /* 主题变量 */
        --lucid-glass-bg: ${isDark ? 'rgba(30, 30, 30, 0.45)' : 'rgba(255, 255, 255, 0.45)'};
        --lucid-glass-border: ${isDark ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)'};
        --lucid-text-gray: ${isDark ? '#dadada' : '#333333'};
        --lucid-text-white: ${isDark ? '#ffffff' : '#000000'};
        --lucid-shadow: ${isDark ? '0 4px 32px rgba(0, 0, 0, 0.45)' : '0 4px 32px rgba(0, 0, 0, 0.15)'};
        
        /* 字体 */
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", 
                     "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", 
                     "Helvetica Neue", Helvetica, Arial, sans-serif;
      }

      * { box-sizing: border-box; }

      .lucid-container {
        pointer-events: auto;
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
        transition: all 0.2s ease-out;
      }

      .lucid-container.visible {
        opacity: 1;
        transform: scale(1) translateY(0);
      }

      /* 玻璃态效果 */
      .lucid-glass {
        backdrop-filter: blur(22px) saturate(160%);
        -webkit-backdrop-filter: blur(22px) saturate(160%);
        background: var(--lucid-glass-bg);
        border: 1px solid var(--lucid-glass-border);
        border-radius: 14px;
        box-shadow: var(--lucid-shadow);
      }

      /* 内联动画 - 减少文件数 */
      @keyframes lucidFadeIn {
        from { opacity: 0; transform: scale(0.95) translateY(-10px); }
        to { opacity: 1; transform: scale(1) translateY(0); }
      }
      @keyframes lucidSlideIn {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
      }
      @keyframes lucidExpand {
        from { width: 200px; height: 40px; opacity: 0.8; }
        to { width: 360px; height: auto; opacity: 1; }
      }
      .lucid-animate-fade-in { animation: lucidFadeIn 0.2s ease-out; }
      .lucid-animate-slide-in { animation: lucidSlideIn 0.3s ease-out; }
      .lucid-animate-expand { animation: lucidExpand 0.4s ease-out; }
    `;

    // 合并用户样式
    const allStyles = [baseStyles, ...this.options.styles].join('\n');
    
    const styleElement = document.createElement("style");
    styleElement.textContent = allStyles;
    this.shadowRoot.appendChild(styleElement);
  }

  private setupReactRoot(): void {
    const mountPoint = document.createElement("div");
    mountPoint.className = "lucid-container";
    this.shadowRoot.appendChild(mountPoint);
    
    this.reactRoot = createRoot(mountPoint);
    this.reactRoot.render(this.options.component);
  }

  private setupEventListeners(): void {
    if (this.options.closeOnClickOutside) {
      this.clickHandler = (event: MouseEvent) => {
        if (!this.hostElement.contains(event.target as Node)) {
          this.hide();
        }
      };
      document.addEventListener('click', this.clickHandler);
    }
  }

  public show(): void {
    if (this.isVisible || this.isDestroyed) return;
    
    this.isVisible = true;
    this.hostElement.style.pointerEvents = 'auto';
    
    requestAnimationFrame(() => {
      const container = this.shadowRoot.querySelector('.lucid-container') as HTMLElement;
      if (container) {
        container.classList.add('visible');
        if (this.options.animation) {
          container.classList.add(`lucid-animate-${this.options.animation}`);
        }
      }
    });
  }

  public hide(): Promise<void> {
    if (!this.isVisible || this.isDestroyed) return Promise.resolve();
    
    this.isVisible = false;
    this.hostElement.style.pointerEvents = 'none';
    
    return new Promise((resolve) => {
      const container = this.shadowRoot.querySelector('.lucid-container') as HTMLElement;
      if (container) {
        container.classList.remove('visible');
        if (this.options.animation) {
          container.classList.add(`lucid-animate-${this.options.animation}-out`);
        }
      }
      
      setTimeout(() => {
        resolve();
      }, 200);
    });
  }

  public updatePosition(x: number, y: number): void {
    if (this.isDestroyed) return;
    
    this.hostElement.style.left = `${x}px`;
    this.hostElement.style.top = `${y}px`;
  }

  public updateComponent(component: React.ReactElement): void {
    if (this.isDestroyed) return;
    
    this.reactRoot.render(component);
  }

  public async destroy(): Promise<void> {
    if (this.isDestroyed) return;
    
    this.isDestroyed = true;
    
    // 移除事件监听器 - 防止内存泄漏
    if (this.clickHandler) {
      document.removeEventListener('click', this.clickHandler);
      this.clickHandler = undefined;
    }
    
    // 执行退出动画
    await this.hide();
    
    // 清理 React
    this.reactRoot.unmount();
    
    // 移除 DOM
    this.hostElement.remove();
  }
}
```

### 2. 极简 UI 管理器

```typescript
// src/ui-manager/uiManager.ts
import { ShadowView } from "./ShadowView";
import React from "react";

// 极简接口定义
interface UIOptions {
  word: string;
  position: { x: number; y: number };
  theme?: 'light' | 'dark';
  data?: any;
}

class UIManager {
  private views = new Map<string, ShadowView>();
  private currentTheme: 'light' | 'dark' = 'dark';

  // 单例模式
  private static instance: UIManager;
  static getInstance(): UIManager {
    if (!UIManager.instance) {
      UIManager.instance = new UIManager();
    }
    return UIManager.instance;
  }

  // 主题管理
  setTheme(theme: 'light' | 'dark'): void {
    this.currentTheme = theme;
  }

  // 通用显示方法 - Tooltip → Toolfull 复用同一个 ShadowView
  async show(type: 'tooltip' | 'toolfull' | 'slider', options: UIOptions): Promise<void> {
    const id = `${type}-${options.word || 'main'}`;
    
    // 复用逻辑：Tooltip → Toolfull 用同一个实例，只更新组件
    if (type === 'toolfull') {
      const tooltipId = `tooltip-${options.word}`;
      const tooltipView = this.views.get(tooltipId);
      if (tooltipView) {
        // 复用 tooltip 的 ShadowView，只更新组件内容
        const ToolfullComponent = await import('../components/Toolfull/Toolfull');
        tooltipView.updateComponent(
          React.createElement(ToolfullComponent.default, {
            word: options.word,
            data: options.data,
            theme: options.theme || this.currentTheme,
            onClose: () => this.hide(options.word)
          })
        );
        // 更改 ID 映射
        this.views.delete(tooltipId);
        this.views.set(id, tooltipView);
        return;
      }
    }

    // 新建视图
    let Component, styles, animation = 'fade-in';
    
    switch (type) {
      case 'tooltip':
        Component = (await import('../components/Tooltip/Tooltip')).default;
        styles = [await import('../components/Tooltip/tooltip.css?inline')];
        break;
      case 'toolfull':
        Component = (await import('../components/Toolfull/Toolfull')).default;
        styles = [await import('../components/Toolfull/toolfull.css?inline')];
        animation = 'expand';
        break;
      case 'slider':
        Component = (await import('../components/Slider/Slider')).default;
        styles = [await import('../components/Slider/slider.css?inline')];
        animation = 'slide-in';
        options.position = { x: window.innerWidth - 400, y: 20 };
        break;
    }

    // 清理同类型的其他实例
    await this.hideType(type);

    const view = new ShadowView({
      id,
      component: React.createElement(Component, {
        ...options,
        theme: options.theme || this.currentTheme,
        onClose: () => this.hide(options.word || 'main'),
        onExpand: type === 'tooltip' ? () => this.show('toolfull', options) : undefined
      }),
      styles,
      position: options.position,
      theme: options.theme || this.currentTheme,
      animation,
      closeOnClickOutside: type !== 'tooltip',
      zIndex: type === 'slider' ? 10000 : 9999
    });

    this.views.set(id, view);
    view.show();
  }

  // 隐藏方法
  async hide(word: string): Promise<void> {
    const patterns = [`tooltip-${word}`, `toolfull-${word}`, `slider-${word}`];
    
    for (const pattern of patterns) {
      const view = this.views.get(pattern);
      if (view) {
        await view.hide();
        view.destroy();
        this.views.delete(pattern);
      }
    }
  }

  // 按类型隐藏
  private async hideType(type: string): Promise<void> {
    const toHide: Promise<void>[] = [];
    
    for (const [id, view] of this.views.entries()) {
      if (id.startsWith(`${type}-`)) {
        toHide.push(
          view.hide().then(() => {
            view.destroy();
            this.views.delete(id);
          })
        );
      }
    }
    
    await Promise.all(toHide);
  }

  // 清理所有 UI
  async cleanup(): Promise<void> {
    const all = Array.from(this.views.values()).map(view => 
      view.hide().then(() => view.destroy())
    );
    await Promise.all(all);
    this.views.clear();
  }
}

export const uiManager = UIManager.getInstance();

// 简化的使用方式
// uiManager.show('tooltip', { word: 'hello', position: { x: 100, y: 200 } });
// uiManager.show('toolfull', { word: 'hello', position: { x: 100, y: 200 }, data: {...} });
// uiManager.show('slider', { word: 'main', position: { x: 0, y: 0 } });
```

### 3. 双层缓存系统

```typescript
// src/services/storage.ts
class CacheManager {
  private memCache = new Map<string, { data: any; expiry: number }>();
  private readonly MEM_TTL = 10 * 60 * 1000; // 10分钟
  private readonly STORAGE_TTL = 7 * 24 * 60 * 60 * 1000; // 7天

  async get<T>(key: string): Promise<T | null> {
    // 1. 检查内存缓存
    const memItem = this.memCache.get(key);
    if (memItem && memItem.expiry > Date.now()) {
      return memItem.data;
    }

    // 2. 检查 storage 缓存
    const storageItem = await chrome.storage.local.get(key);
    if (storageItem[key] && storageItem[key].expiry > Date.now()) {
      // 回写内存缓存
      this.memCache.set(key, {
        data: storageItem[key].data,
        expiry: Date.now() + this.MEM_TTL
      });
      return storageItem[key].data;
    }

    return null;
  }

  async set<T>(key: string, data: T): Promise<void> {
    const now = Date.now();
    
    // 写入内存缓存
    this.memCache.set(key, { data, expiry: now + this.MEM_TTL });
    
    // 写入 storage 缓存
    await chrome.storage.local.set({
      [key]: { data, expiry: now + this.STORAGE_TTL }
    });
  }

  // 清理过期缓存
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.memCache.entries()) {
      if (item.expiry <= now) {
        this.memCache.delete(key);
      }
    }
  }
}

export const cache = new CacheManager();

// 定期清理
setInterval(() => cache.cleanup(), 5 * 60 * 1000); // 5分钟清理一次
```

### 4. 数据流简化

```typescript
// src/features/dictionary.ts
import { cache } from '../services/storage';

export async function lookup(word: string): Promise<any> {
  // 1. 尝试缓存
  const cached = await cache.get(`dict:${word}`);
  if (cached) return cached;

  // 2. 远程 API (3s 超时)
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 3000);

  try {
    const response = await fetch(`/api/lookup?word=${word}`, {
      signal: controller.signal
    });
    const data = await response.json();
    
    // 3. 写回缓存
    await cache.set(`dict:${word}`, data);
    return data;
  } catch (error) {
    return { error: '网络错误' };
  } finally {
    clearTimeout(timeoutId);
  }
}

// src/features/highlight.ts
export function setupHighlight(callback: (word: string, pos: {x: number, y: number}) => void) {
  let debounceTimer: number;

  const handleMouseMove = (e: MouseEvent) => {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
      const word = getWordAt(e.target as Element, e.clientX, e.clientY);
      if (word) {
        callback(word, { x: e.clientX, y: e.clientY });
      }
    }, 200); // 200ms 防抖
  };

  document.addEventListener('mousemove', handleMouseMove);
  return () => document.removeEventListener('mousemove', handleMouseMove);
}

function getWordAt(element: Element, x: number, y: number): string | null {
  // 获取鼠标位置的单词逻辑
  return 'example'; // 简化示例
}
```

## 核心优化原则

### 1. **文件数最少**
- ui-manager/ 只要 2 个文件
- 动画内联到 ShadowView，不单独文件
- 样式通过 CSS 变量统一管理

### 2. **内存管理**
- 事件监听器正确清理
- ShadowView 复用（Tooltip → Toolfull）
- 缓存定期清理过期数据

### 3. **性能关键点**
- `mousemove` 200ms 防抖
- `fetch` 3s 超时 + AbortController
- 双层缓存避免重复请求

### 4. **数据流直接化**
```typescript
// 不用 EventEmitter，直接函数调用
mouseover → highlight.getWord() → dictionary.lookup() → uiManager.show()
```

### 5. **预留扩展点**
```typescript
// TODO: OAuth login()
// TODO: 学习计划 features/study.ts
// TODO: 更多词典源
```

**宗旨：能跑、好改、文件少**