# API 设计文档 - 优化版

## 架构概览

基于三级缓存系统的 API 设计，提供高性能、高可靠性的词典和翻译服务。

```
┌─────────────────────────────────────────────────────────────────┐
│                        API Layer Architecture                   │
├─────────────────────────────────────────────────────────────────┤
│  React Hook Layer                                               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  useDictionary  │  │ useTranslator   │  │   useSettings   │  │
│  │     Hook        │  │     Hook        │  │     Hook        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│  Service Layer                                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ getWordDefinition│  │ translateText   │  │ ConfigManager   │  │
│  │   (统一入口)     │  │   (翻译服务)    │  │   (配置管理)    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│  Cache Layer (Three-Level)                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   L1 Memory     │  │  L2 IndexedDB   │  │   L3 Remote     │  │
│  │    Cache        │  │     Cache       │  │     API         │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## 1. 数据类型定义

```typescript
// src/types/dictionary.ts
export interface WordData {
  word: string;
  explain: WordExplanation[];
  wordFormats: WordFormat[];
  phonetic: PhoneticData;
  // 扩展元数据
  frequency?: number; // 词频 1-10
  difficulty?: DifficultyLevel; // 难度等级
  tags?: string[]; // 自定义标签
  // 缓存元数据
  cachedAt: number;
  source: DataSource;
  version: string; // 数据版本，用于缓存失效
}

export interface WordExplanation {
  pos: string; // 词性
  definitions: Definition[];
}

export interface Definition {
  definition: string; // 英文定义
  chinese: string; // 详细中文释义
  chinese_short: string; // 简短中文释义
  examples?: Example[]; // 例句
  synonyms?: string[]; // 同义词
  antonyms?: string[]; // 反义词
}

export interface Example {
  english: string;
  chinese: string;
  source?: string; // 例句来源
}

export interface WordFormat {
  name: string; // 变形名称
  form: string; // 变形形式
  phonetic?: string; // 变形音标
}

export interface PhoneticData {
  us: string; // 美式音标
  uk: string; // 英式音标
  audio?: {
    // 音频文件
    us?: string;
    uk?: string;
  };
}

export type DifficultyLevel = "basic" | "intermediate" | "advanced" | "expert";
export type DataSource = "l1" | "l2" | "api" | "local" | "fallback";

// API 响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: ApiError;
  source: DataSource;
  cached: boolean;
  timestamp: number;
  responseTime: number; // 响应时间 (ms)
}

export interface ApiError {
  code: ErrorCode;
  message: string;
  details?: Record<string, any>;
  retryable: boolean;
  retryAfter?: number; // 重试延迟 (ms)
}

export type ErrorCode =
  | "NETWORK_ERROR"
  | "TIMEOUT"
  | "RATE_LIMIT"
  | "WORD_NOT_FOUND"
  | "INVALID_REQUEST"
  | "SERVICE_UNAVAILABLE"
  | "CACHE_ERROR"
  | "PARSE_ERROR";
```

## 2. 统一 API 服务层

```typescript
// src/features/dictionary/DictionaryService.ts
import { l1Cache } from "./cache/L1MemoryCache";
import { l2Cache } from "./cache/L2IndexedDBCache";
import { l3Fetcher } from "./api/L3APIFetcher";
import { RequestManager } from "../../utils/RequestManager";
import { ApiErrorHandler } from "../../services/ErrorHandler";
import type { WordData, ApiResponse } from "../../types/dictionary";

export class DictionaryService {
  private requestManager: RequestManager;
  private errorHandler: ApiErrorHandler;

  constructor() {
    this.requestManager = new RequestManager();
    this.errorHandler = new ApiErrorHandler();
  }

  /**
   * 主要查词入口 - 集成三级缓存
   */
  public async getWordDefinition(word: string): Promise<ApiResponse<WordData>> {
    const startTime = performance.now();
    const normalizedWord = this.normalizeWord(word);

    // 防重复请求
    return this.requestManager.dedupe(`lookup:${normalizedWord}`, async () => {
      try {
        const result = await this.performLookup(normalizedWord);
        const responseTime = performance.now() - startTime;

        return {
          ...result,
          responseTime,
          timestamp: Date.now(),
        };
      } catch (error) {
        return this.handleLookupError(error, normalizedWord, startTime);
      }
    });
  }

  private async performLookup(word: string): Promise<ApiResponse<WordData>> {
    // L1: 内存缓存检查
    const l1Result = l1Cache.get(word);
    if (l1Result) {
      return {
        success: true,
        data: l1Result,
        source: "l1",
        cached: true,
      };
    }

    // L2: IndexedDB 检查
    const l2Result = await l2Cache.get(word);
    if (l2Result) {
      // 回写到 L1
      l1Cache.set(word, l2Result);
      return {
        success: true,
        data: l2Result,
        source: "l2",
        cached: true,
      };
    }

    // L3: 远程 API 获取
    const l3Result = await l3Fetcher.fetch(word);
    if (l3Result) {
      // 回写到缓存
      await this.writeback(word, l3Result);
      return {
        success: true,
        data: l3Result,
        source: "api",
        cached: false,
      };
    }

    // 所有方法都失败
    throw new Error(`Unable to find definition for: ${word}`);
  }

  private async writeback(word: string, data: WordData): Promise<void> {
    try {
      // 并行写入 L1 和 L2
      await Promise.all([l1Cache.set(word, data), l2Cache.set(word, data)]);
    } catch (error) {
      console.warn("[DICTIONARY] Cache writeback failed:", error);
      // 缓存写入失败不影响主流程
    }
  }

  private handleLookupError(
    error: any,
    word: string,
    startTime: number
  ): ApiResponse<WordData> {
    const responseTime = performance.now() - startTime;
    const apiError = this.errorHandler.parseError(error);

    return {
      success: false,
      error: apiError,
      source: "api",
      cached: false,
      responseTime,
      timestamp: Date.now(),
    };
  }

  private normalizeWord(word: string): string {
    return word
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-']/g, "");
  }

  /**
   * 批量预加载常用单词
   */
  public async preloadCommonWords(words: string[]): Promise<void> {
    const batchSize = 5;
    for (let i = 0; i < words.length; i += batchSize) {
      const batch = words.slice(i, i + batchSize);
      await Promise.allSettled(
        batch.map((word) => this.getWordDefinition(word))
      );
      // 避免 API 限流
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }

  /**
   * 清理过期缓存
   */
  public async cleanup(): Promise<void> {
    await Promise.all([l1Cache.clear(), l2Cache.cleanup?.()]);
  }
}

export const dictionaryService = new DictionaryService();
```

## 3. 增强的 L3 API 获取器

```typescript
// src/features/dictionary/api/L3APIFetcher.ts
export class L3APIFetcher {
  private readonly ENDPOINTS = {
    primary: {
      url: "https://api.dictionaryapi.dev/api/v2/entries/en/",
      timeout: 5000,
      rateLimit: { requests: 100, window: 60000 }, // 100 requests/minute
    },
    fallback: {
      url: "https://dict.youdao.com/jsonapi",
      timeout: 3000,
      rateLimit: { requests: 50, window: 60000 },
    },
    backup: {
      url: browser.runtime.getURL("data/dictionary-backup.json"),
      timeout: 1000,
      rateLimit: null,
    },
  };

  private rateLimiters = new Map<string, RateLimiter>();

  constructor() {
    // 初始化速率限制器
    Object.entries(this.ENDPOINTS).forEach(([key, config]) => {
      if (config.rateLimit) {
        this.rateLimiters.set(key, new RateLimiter(config.rateLimit));
      }
    });
  }

  public async fetch(word: string): Promise<WordData | null> {
    const strategies = [
      () => this.fetchFromEndpoint("primary", word),
      () => this.fetchFromEndpoint("fallback", word),
      () => this.fetchFromEndpoint("backup", word),
    ];

    for (const strategy of strategies) {
      try {
        const result = await strategy();
        if (result) {
          return result;
        }
      } catch (error) {
        console.warn(`[L3API] Strategy failed:`, error);
        // 继续尝试下一个策略
      }
    }

    return null;
  }

  private async fetchFromEndpoint(
    endpoint: string,
    word: string
  ): Promise<WordData | null> {
    const config = this.ENDPOINTS[endpoint];
    const rateLimiter = this.rateLimiters.get(endpoint);

    // 检查速率限制
    if (rateLimiter && !rateLimiter.canMakeRequest()) {
      throw new Error(`Rate limit exceeded for ${endpoint}`);
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout);

    try {
      let response: Response;

      if (endpoint === "primary") {
        response = await this.fetchPrimary(word, controller);
      } else if (endpoint === "fallback") {
        response = await this.fetchFallback(word, controller);
      } else {
        response = await this.fetchBackup(word, controller);
      }

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return this.transformApiData(data, word, endpoint);
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private async fetchPrimary(
    word: string,
    controller: AbortController
  ): Promise<Response> {
    return fetch(`${this.ENDPOINTS.primary.url}${word}`, {
      signal: controller.signal,
      headers: {
        Accept: "application/json",
        "User-Agent": "Lucid Extension 1.0",
        "Cache-Control": "no-cache",
      },
    });
  }

  private async fetchFallback(
    word: string,
    controller: AbortController
  ): Promise<Response> {
    const params = new URLSearchParams({
      jsonversion: "2",
      q: word,
      dicts: JSON.stringify({
        count: 1,
        dicts: [["ec"]], // 英中词典
      }),
    });

    return fetch(`${this.ENDPOINTS.fallback.url}?${params}`, {
      signal: controller.signal,
      headers: {
        Accept: "application/json",
        Referer: "https://dict.youdao.com/",
      },
    });
  }

  private async fetchBackup(
    word: string,
    controller: AbortController
  ): Promise<Response> {
    // 从本地备份文件中查找
    return fetch(this.ENDPOINTS.backup.url, {
      signal: controller.signal,
    });
  }

  private transformApiData(
    apiData: any,
    word: string,
    source: string
  ): WordData {
    switch (source) {
      case "primary":
        return this.transformDictionaryApiData(apiData, word);
      case "fallback":
        return this.transformYoudaoData(apiData, word);
      case "backup":
        return this.transformBackupData(apiData, word);
      default:
        throw new Error(`Unknown data source: ${source}`);
    }
  }

  private transformDictionaryApiData(apiData: any, word: string): WordData {
    const data = Array.isArray(apiData) ? apiData[0] : apiData;

    // 提取音标
    const phonetic = this.extractPhonetics(data.phonetics || []);

    // 转换释义
    const explain: WordExplanation[] = (data.meanings || []).map(
      (meaning: any) => ({
        pos: meaning.partOfSpeech,
        definitions: (meaning.definitions || [])
          .slice(0, 3)
          .map((def: any) => ({
            definition: def.definition,
            chinese: this.translateDefinition(def.definition),
            chinese_short: this.extractShortTranslation(def.definition),
            examples: def.example
              ? [
                  {
                    english: def.example,
                    chinese: this.translateExample(def.example),
                  },
                ]
              : [],
            synonyms: def.synonyms || [],
            antonyms: def.antonyms || [],
          })),
      })
    );

    // 生成词形变化
    const wordFormats = this.generateWordFormats(word, explain);

    return {
      word,
      explain,
      wordFormats,
      phonetic,
      frequency: this.calculateFrequency(word),
      difficulty: this.calculateDifficulty(word, explain),
      cachedAt: Date.now(),
      source: "api",
      version: "1.0",
    };
  }

  private transformYoudaoData(apiData: any, word: string): WordData {
    // 有道词典数据转换
    const ecDict = apiData?.ec?.word?.[0];
    if (!ecDict) {
      throw new Error("No valid data from Youdao API");
    }

    const phonetic = {
      us: ecDict.usphone ? `/${ecDict.usphone}/` : "",
      uk: ecDict.ukphone ? `/${ecDict.ukphone}/` : "",
    };

    const explain: WordExplanation[] = (ecDict.trs || []).map((tr: any) => {
      const pos = tr.pos || "unknown";
      const definitions =
        tr.tr?.map((t: any) => ({
          definition: t.l?.i?.[0] || "",
          chinese: t.l?.i?.[0] || "",
          chinese_short: this.extractShortTranslation(t.l?.i?.[0] || ""),
        })) || [];

      return { pos, definitions };
    });

    return {
      word,
      explain,
      wordFormats: this.generateWordFormats(word, explain),
      phonetic,
      frequency: this.calculateFrequency(word),
      difficulty: this.calculateDifficulty(word, explain),
      cachedAt: Date.now(),
      source: "fallback",
      version: "1.0",
    };
  }

  private transformBackupData(apiData: any, word: string): WordData {
    // 从本地备份数据中查找
    const wordData = apiData.words?.find(
      (w: any) => w.word.toLowerCase() === word.toLowerCase()
    );
    if (!wordData) {
      throw new Error(`Word not found in backup data: ${word}`);
    }

    return {
      ...wordData,
      cachedAt: Date.now(),
      source: "local",
      version: "1.0",
    };
  }

  private extractPhonetics(phonetics: any[]): PhoneticData {
    const result = { us: "", uk: "", audio: {} };

    for (const p of phonetics) {
      if (!p.text) continue;

      if (p.audio?.includes("-us") || p.audio?.includes("_us")) {
        result.us = p.text;
        result.audio.us = p.audio;
      } else if (p.audio?.includes("-uk") || p.audio?.includes("_gb")) {
        result.uk = p.text;
        result.audio.uk = p.audio;
      } else if (!result.us) {
        // 默认使用第一个音标作为美音
        result.us = p.text;
        if (p.audio) result.audio.us = p.audio;
      }
    }

    return result;
  }

  private generateWordFormats(
    word: string,
    explain: WordExplanation[]
  ): WordFormat[] {
    const formats: WordFormat[] = [{ name: "原型", form: word }];

    // 根据词性生成变形
    for (const exp of explain) {
      switch (exp.pos) {
        case "verb":
          formats.push(
            { name: "第三人称单数", form: this.getThirdPersonSingular(word) },
            { name: "过去式", form: this.getPastTense(word) },
            { name: "过去分词", form: this.getPastParticiple(word) },
            { name: "现在分词", form: this.getPresentParticiple(word) }
          );
          break;
        case "noun":
          const plural = this.getPlural(word);
          if (plural !== word) {
            formats.push({ name: "复数", form: plural });
          }
          break;
        case "adjective":
          formats.push(
            { name: "比较级", form: this.getComparative(word) },
            { name: "最高级", form: this.getSuperlative(word) }
          );
          break;
      }
    }

    return formats;
  }

  // 简化的词形变化规则
  private getThirdPersonSingular(word: string): string {
    if (word.endsWith("y")) return word.slice(0, -1) + "ies";
    if (
      word.endsWith("s") ||
      word.endsWith("sh") ||
      word.endsWith("ch") ||
      word.endsWith("x") ||
      word.endsWith("z")
    ) {
      return word + "es";
    }
    return word + "s";
  }

  private getPastTense(word: string): string {
    if (word.endsWith("e")) return word + "d";
    if (word.endsWith("y")) return word.slice(0, -1) + "ied";
    return word + "ed";
  }

  private getPastParticiple(word: string): string {
    return this.getPastTense(word); // 简化处理
  }

  private getPresentParticiple(word: string): string {
    if (word.endsWith("e")) return word.slice(0, -1) + "ing";
    return word + "ing";
  }

  private getPlural(word: string): string {
    if (word.endsWith("y")) return word.slice(0, -1) + "ies";
    if (
      word.endsWith("s") ||
      word.endsWith("sh") ||
      word.endsWith("ch") ||
      word.endsWith("x") ||
      word.endsWith("z")
    ) {
      return word + "es";
    }
    return word + "s";
  }

  private getComparative(word: string): string {
    if (word.length <= 6) return word + "er";
    return `more ${word}`;
  }

  private getSuperlative(word: string): string {
    if (word.length <= 6) return word + "est";
    return `most ${word}`;
  }

  private translateDefinition(definition: string): string {
    // TODO: 集成翻译服务
    return definition; // 暂时返回原文
  }

  private extractShortTranslation(text: string): string {
    // 提取简短翻译
    const words = text.split(/[,，;；]/)[0].trim();
    return words.split(" ").slice(0, 2).join(" ");
  }

  private translateExample(example: string): string {
    // TODO: 集成翻译服务
    return example; // 暂时返回原文
  }

  private calculateFrequency(word: string): number {
    // 基于词长和常见度的简单计算
    const commonWords = new Set([
      "the",
      "be",
      "to",
      "of",
      "and",
      "a",
      "in",
      "that",
      "have",
      "it",
    ]);
    if (commonWords.has(word.toLowerCase())) return 10;

    if (word.length <= 4) return 8;
    if (word.length <= 6) return 6;
    if (word.length <= 8) return 4;
    return 2;
  }

  private calculateDifficulty(
    word: string,
    explain: WordExplanation[]
  ): DifficultyLevel {
    // 基于多个因素的难度计算
    let score = 0;

    // 词长因素
    if (word.length > 12) score += 3;
    else if (word.length > 8) score += 2;
    else if (word.length > 6) score += 1;

    // 释义复杂度
    const avgDefLength =
      explain.reduce(
        (acc, exp) =>
          acc +
          exp.definitions.reduce((sum, def) => sum + def.definition.length, 0),
        0
      ) /
      Math.max(
        explain.reduce((acc, exp) => acc + exp.definitions.length, 0),
        1
      );

    if (avgDefLength > 100) score += 2;
    else if (avgDefLength > 50) score += 1;

    // 词性数量
    if (explain.length > 3) score += 1;

    if (score >= 5) return "expert";
    if (score >= 3) return "advanced";
    if (score >= 1) return "intermediate";
    return "basic";
  }
}

// 速率限制器
class RateLimiter {
  private requests: number[] = [];
  private maxRequests: number;
  private windowMs: number;

  constructor(config: { requests: number; window: number }) {
    this.maxRequests = config.requests;
    this.windowMs = config.window;
  }

  canMakeRequest(): boolean {
    const now = Date.now();
    // 清理过期请求
    this.requests = this.requests.filter((time) => now - time < this.windowMs);

    if (this.requests.length < this.maxRequests) {
      this.requests.push(now);
      return true;
    }

    return false;
  }
}
```

## 4. 优化的 React Hooks

```typescript
// src/features/dictionary/useDictionary.ts
import { useState, useCallback, useRef, useEffect } from "react";
import { dictionaryService } from "./DictionaryService";
import type { WordData, ApiResponse } from "../../types/dictionary";

interface DictionaryState {
  loading: boolean;
  data: WordData | null;
  error: string | null;
  source?: string;
  cached?: boolean;
  responseTime?: number;
}

export const useDictionary = (
  options: {
    autoRetry?: boolean;
    retryAttempts?: number;
    debounceMs?: number;
  } = {}
) => {
  const { autoRetry = true, retryAttempts = 2, debounceMs = 300 } = options;

  const [state, setState] = useState<DictionaryState>({
    loading: false,
    data: null,
    error: null,
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef(0);

  const lookup = useCallback(
    async (word: string, immediate = false) => {
      if (!word.trim()) {
        setState({ loading: false, data: null, error: null });
        return;
      }

      // 取消之前的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // 清除防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      const performLookup = async () => {
        setState((prev) => ({ ...prev, loading: true, error: null }));
        retryCountRef.current = 0;

        const abortController = new AbortController();
        abortControllerRef.current = abortController;

        try {
          const response = await dictionaryService.getWordDefinition(word);

          // 检查是否被取消
          if (abortController.signal.aborted) return;

          if (response.success && response.data) {
            setState({
              loading: false,
              data: response.data,
              error: null,
              source: response.source,
              cached: response.cached,
              responseTime: response.responseTime,
            });
          } else {
            throw new Error(response.error?.message || "查询失败");
          }
        } catch (error) {
          if (abortController.signal.aborted) return;

          const errorMessage =
            error instanceof Error ? error.message : "未知错误";

          // 自动重试
          if (autoRetry && retryCountRef.current < retryAttempts) {
            retryCountRef.current++;
            console.log(
              `[DICTIONARY] Retrying (${retryCountRef.current}/${retryAttempts}): ${word}`
            );

            setTimeout(() => {
              if (!abortController.signal.aborted) {
                performLookup();
              }
            }, 1000 * retryCountRef.current); // 指数退避
            return;
          }

          setState({
            loading: false,
            data: null,
            error: errorMessage,
          });
        }
      };

      if (immediate || debounceMs === 0) {
        performLookup();
      } else {
        debounceTimerRef.current = setTimeout(performLookup, debounceMs);
      }
    },
    [autoRetry, retryAttempts, debounceMs]
  );

  const clear = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    setState({ loading: false, data: null, error: null });
  }, []);

  const retry = useCallback(() => {
    if (state.error && !state.loading) {
      const lastWord = state.data?.word || "";
      if (lastWord) {
        lookup(lastWord, true);
      }
    }
  }, [state.error, state.loading, state.data?.word, lookup]);

  // 清理
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return {
    ...state,
    lookup,
    clear,
    retry,
  };
};

// 预加载 Hook
export const useDictionaryPreloader = () => {
  const [preloadStatus, setPreloadStatus] = useState<{
    loading: boolean;
    progress: number;
    total: number;
  }>({ loading: false, progress: 0, total: 0 });

  const preloadCommonWords = useCallback(async (words: string[]) => {
    setPreloadStatus({ loading: true, progress: 0, total: words.length });

    try {
      for (let i = 0; i < words.length; i++) {
        await dictionaryService.getWordDefinition(words[i]);
        setPreloadStatus((prev) => ({ ...prev, progress: i + 1 }));
      }
    } catch (error) {
      console.error("[PRELOADER] Failed:", error);
    } finally {
      setPreloadStatus((prev) => ({ ...prev, loading: false }));
    }
  }, []);

  return {
    preloadStatus,
    preloadCommonWords,
  };
};
```

## 5. 错误处理和监控

```typescript
// src/services/ErrorHandler.ts
export class ApiErrorHandler {
  private errorCounts = new Map<ErrorCode, number>();
  private lastErrors = new Map<ErrorCode, number>();

  public parseError(error: any): ApiError {
    let code: ErrorCode;
    let message: string;
    let retryable = false;
    let retryAfter: number | undefined;

    if (error instanceof TypeError && error.message.includes("fetch")) {
      code = "NETWORK_ERROR";
      message = "网络连接失败";
      retryable = true;
      retryAfter = 2000;
    } else if (error.name === "AbortError") {
      code = "TIMEOUT";
      message = "请求超时";
      retryable = true;
      retryAfter = 1000;
    } else if (error.message?.includes("rate limit")) {
      code = "RATE_LIMIT";
      message = "请求过于频繁，请稍后重试";
      retryable = true;
      retryAfter = 5000;
    } else if (error.message?.includes("not found")) {
      code = "WORD_NOT_FOUND";
      message = "未找到该单词";
      retryable = false;
    } else {
      code = "SERVICE_UNAVAILABLE";
      message = "服务暂时不可用";
      retryable = true;
      retryAfter = 3000;
    }

    this.recordError(code);

    return {
      code,
      message,
      retryable,
      retryAfter,
      details: {
        originalError: error.message,
        errorCount: this.errorCounts.get(code) || 0,
      },
    };
  }

  private recordError(code: ErrorCode): void {
    const count = this.errorCounts.get(code) || 0;
    this.errorCounts.set(code, count + 1);
    this.lastErrors.set(code, Date.now());

    // 清理旧错误记录
    const cleanupTime = Date.now() - 60000; // 1分钟前
    for (const [errorCode, timestamp] of this.lastErrors.entries()) {
      if (timestamp < cleanupTime) {
        this.lastErrors.delete(errorCode);
        this.errorCounts.delete(errorCode);
      }
    }
  }

  public getErrorStats(): Record<ErrorCode, number> {
    return Object.fromEntries(this.errorCounts) as Record<ErrorCode, number>;
  }
}
```

## 6. 性能监控和指标

```typescript
// src/services/MetricsCollector.ts
export class MetricsCollector {
  private metrics = {
    cacheHits: { l1: 0, l2: 0 },
    cacheMisses: { l1: 0, l2: 0 },
    apiCalls: { success: 0, failure: 0 },
    responseTimes: [] as number[],
    errorCounts: new Map<ErrorCode, number>(),
  };

  public recordCacheHit(level: "l1" | "l2"): void {
    this.metrics.cacheHits[level]++;
  }

  public recordCacheMiss(level: "l1" | "l2"): void {
    this.metrics.cacheMisses[level]++;
  }

  public recordApiCall(success: boolean, responseTime: number): void {
    if (success) {
      this.metrics.apiCalls.success++;
    } else {
      this.metrics.apiCalls.failure++;
    }

    this.metrics.responseTimes.push(responseTime);

    // 只保留最近100次的响应时间
    if (this.metrics.responseTimes.length > 100) {
      this.metrics.responseTimes.shift();
    }
  }

  public getStats() {
    const totalRequests =
      this.metrics.cacheHits.l1 +
      this.metrics.cacheHits.l2 +
      this.metrics.cacheMisses.l1 +
      this.metrics.cacheMisses.l2;

    const l1HitRate =
      totalRequests > 0 ? this.metrics.cacheHits.l1 / totalRequests : 0;
    const l2HitRate =
      totalRequests > 0 ? this.metrics.cacheHits.l2 / totalRequests : 0;

    const avgResponseTime =
      this.metrics.responseTimes.length > 0
        ? this.metrics.responseTimes.reduce((a, b) => a + b, 0) /
          this.metrics.responseTimes.length
        : 0;

    return {
      cacheHitRates: {
        l1: (l1HitRate * 100).toFixed(1) + "%",
        l2: (l2HitRate * 100).toFixed(1) + "%",
      },
      apiStats: this.metrics.apiCalls,
      avgResponseTime: Math.round(avgResponseTime) + "ms",
      totalRequests,
    };
  }
}

export const metricsCollector = new MetricsCollector();
```

这个优化后的 API 设计提供了：

1. **完整的三级缓存集成** - 与缓存系统无缝协作
2. **多源 API 降级策略** - 主 API → 备用 API → 本地数据
3. **智能错误处理** - 自动重试、速率限制、优雅降级
4. **性能监控** - 详细的缓存命中率和响应时间统计
5. **数据格式增强** - 支持更丰富的词典信息
6. **React Hook 优化** - 防抖、取消请求、自动重试
7. **生产级可靠性** - 完整的错误处理和监控机制

这个设计确保了你的 Lucid 扩展能够提供快速、可靠的词典服务。
