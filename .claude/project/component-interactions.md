# 组件交互流程设计 - 优化版

## 交互流程概览

基于三级缓存系统和优化后的 API 设计，提供流畅的用户交互体验。

```
┌─────────────────────────────────────────────────────────────────┐
│                     Component Interaction Flow                  │
├─────────────────────────────────────────────────────────────────┤
│  User Action Level                                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │  Click Icon │  │ Select Text │  │ Hover Word  │              │
│  │     ↓       │  │     ↓       │  │     ↓       │              │
│  │   Slider    │  │  Highlight  │  │   Tooltip   │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
├─────────────────────────────────────────────────────────────────┤
│  Hook Layer                                                     │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │               useDictionary Hook                            │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │ │
│  │  │  debounce   │ │ autoRetry   │ │ abortSignal │           │ │
│  │  │   300ms     │ │   2 times   │ │   cancel    │           │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘           │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  Cache & API Layer                                             │
│  ┌─────────────┐ → ┌─────────────┐ → ┌─────────────┐           │
│  │ L1 Memory   │   │ L2 IndexedDB│   │ L3 Remote   │           │
│  │ <1ms        │   │ 5-20ms      │   │ 200-2000ms  │           │
│  └─────────────┘   └─────────────┘   └─────────────┘           │
└─────────────────────────────────────────────────────────────────┘
```

## 核心交互场景

### 1. 插件激活 → Slider 显示

```typescript
// 完整的激活流程
async function handleExtensionIconClick() {
  // 1. 检查页面状态
  const isContentScriptReady = await checkContentScriptStatus();
  if (!isContentScriptReady) {
    await injectContentScript();
  }

  // 2. 发送消息到 content script
  await browser.tabs.sendMessage(tabId, {
    type: 'TOGGLE_SLIDER',
    timestamp: Date.now()
  });
}

// content.ts 中的处理
onMessage('TOGGLE_SLIDER', async (message) => {
  try {
    await uiManager.toggleSlider();
    
    // 预加载常用单词（后台进行）
    if (!preloadCompleted) {
      dictionaryService.preloadCommonWords(COMMON_WORDS)
        .then(() => {
          preloadCompleted = true;
          console.log('[PRELOAD] Common words cached');
        })
        .catch(console.warn);
    }
  } catch (error) {
    console.error('[SLIDER] Toggle failed:', error);
  }
});
```

**时序图：**
```
User Click → Background → Content → UIManager → ShadowView → DOM
    ↓           ↓          ↓          ↓           ↓         ↓
   50ms       20ms       10ms       5ms        2ms     渲染完成
```

### 2. 文本选择 → 高亮 → Toolfull

```typescript
// 增强的文本选择处理
class TextSelectionHandler {
  private selectionTimeout: NodeJS.Timeout | null = null;
  private readonly SELECTION_DELAY = 300; // 防抖延迟

  constructor() {
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    document.addEventListener('selectionchange', this.handleSelectionChange.bind(this));
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
  }

  private handleSelectionChange(): void {
    // 清除之前的定时器
    if (this.selectionTimeout) {
      clearTimeout(this.selectionTimeout);
    }

    // 防抖处理
    this.selectionTimeout = setTimeout(() => {
      this.processSelection();
    }, this.SELECTION_DELAY);
  }

  private handleKeyDown(event: KeyboardEvent): void {
    // Shift + 选择文本的特殊处理
    if (event.shiftKey && !event.ctrlKey && !event.altKey) {
      this.shiftSelectionMode = true;
    }
  }

  private async processSelection(): Promise<void> {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed) return;

    const selectedText = selection.toString().trim();
    if (!selectedText || selectedText.length > 50) return;

    // 验证是否为有效单词
    const word = this.extractWord(selectedText);
    if (!word) return;

    try {
      // 1. 高亮所有相同单词
      const highlightPositions = await highlightManager.highlightWord(word);
      
      // 2. 获取选择位置
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      const position = {
        x: rect.left + rect.width / 2,
        y: rect.top - 10
      };

      // 3. 显示 Toolfull（直接显示，不经过 Tooltip）
      await uiManager.showToolfull({
        word,
        position,
        expandFrom: 'selection',
        highlightPositions
      });

      // 4. 记录用户行为
      userBehaviorTracker.recordWordSelection(word, highlightPositions.length);

    } catch (error) {
      console.error('[SELECTION] Processing failed:', error);
      // 显示简单的错误提示
      this.showErrorToast('查词失败，请重试');
    }
  }

  private extractWord(text: string): string | null {
    // 智能单词提取
    const cleanText = text.replace(/[^\w\s'-]/g, '').trim();
    const words = cleanText.split(/\s+/);
    
    // 只处理单个单词
    if (words.length === 1) {
      return words[0].toLowerCase();
    }
    
    // 如果是短语，尝试提取主要单词
    if (words.length <= 3) {
      return words.find(w => w.length > 3) || words[0];
    }
    
    return null;
  }

  private showErrorToast(message: string): void {
    // 简单的错误提示
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(255, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      z-index: 999999;
      font-size: 14px;
    `;
    document.body.appendChild(toast);
    
    setTimeout(() => toast.remove(), 3000);
  }
}
```

### 3. 高亮单词悬停 → Tooltip → Toolfull 渐进展开

这是最复杂的交互流程，需要精确的状态管理和时机控制。

```typescript
// 高亮悬停处理器
class HighlightHoverHandler {
  private hoverState = new Map<string, HoverState>();
  private activeTooltip: string | null = null;
  private expansionTimer: NodeJS.Timeout | null = null;

  private readonly TOOLTIP_DELAY = 200;      // Tooltip 显示延迟
  private readonly EXPANSION_DELAY = 2000;   // 扩展到 Toolfull 的延迟
  private readonly HIDE_DELAY = 300;         // 鼠标离开后的隐藏延迟

  public handleWordHover(word: string, element: HTMLElement, event: MouseEvent): void {
    const position = this.calculateTooltipPosition(element, event);
    
    // 清理之前的状态
    this.clearHoverState(word);
    
    // 创建新的悬停状态
    const state: HoverState = {
      word,
      element,
      position,
      stage: 'hovering',
      timers: {}
    };

    // 阶段 1: 延迟显示 Tooltip
    state.timers.showTooltip = setTimeout(async () => {
      if (this.hoverState.get(word)?.stage !== 'hovering') return;
      
      try {
        // 显示 Tooltip
        await this.showTooltip(word, position);
        
        // 更新状态
        const currentState = this.hoverState.get(word);
        if (currentState) {
          currentState.stage = 'tooltip_shown';
          this.activeTooltip = word;
        }

        // 阶段 2: 准备扩展到 Toolfull
        state.timers.expandToolfull = setTimeout(async () => {
          const expandState = this.hoverState.get(word);
          if (expandState?.stage === 'tooltip_shown') {
            await this.expandToToolfull(word, position);
            expandState.stage = 'toolfull_shown';
          }
        }, this.EXPANSION_DELAY);

      } catch (error) {
        console.error('[HOVER] Tooltip display failed:', error);
        this.clearHoverState(word);
      }
    }, this.TOOLTIP_DELAY);

    this.hoverState.set(word, state);
  }

  public handleWordLeave(word: string): void {
    const state = this.hoverState.get(word);
    if (!state) return;

    // 清除显示定时器
    if (state.timers.showTooltip) {
      clearTimeout(state.timers.showTooltip);
    }

    // 如果 Tooltip 已显示，延迟隐藏
    if (state.stage === 'tooltip_shown') {
      state.timers.hideTooltip = setTimeout(() => {
        if (this.hoverState.get(word)?.stage === 'tooltip_shown') {
          this.hideTooltip(word);
          this.clearHoverState(word);
        }
      }, this.HIDE_DELAY);
    } else {
      // 直接清理
      this.clearHoverState(word);
    }
  }

  public handleTooltipHover(word: string): void {
    const state = this.hoverState.get(word);
    if (!state) return;

    // 取消隐藏定时器
    if (state.timers.hideTooltip) {
      clearTimeout(state.timers.hideTooltip);
      delete state.timers.hideTooltip;
    }
  }

  public handleTooltipLeave(word: string): void {
    const state = this.hoverState.get(word);
    if (!state || state.stage !== 'tooltip_shown') return;

    // 延迟隐藏
    state.timers.hideTooltip = setTimeout(() => {
      if (this.hoverState.get(word)?.stage === 'tooltip_shown') {
        this.hideTooltip(word);
        this.clearHoverState(word);
      }
    }, this.HIDE_DELAY);
  }

  private async showTooltip(word: string, position: { x: number; y: number }): Promise<void> {
    // 隐藏其他 Tooltip
    if (this.activeTooltip && this.activeTooltip !== word) {
      await this.hideTooltip(this.activeTooltip);
      this.clearHoverState(this.activeTooltip);
    }

    // 显示新 Tooltip
    await uiManager.showTooltip({
      word,
      position,
      theme: await settingsManager.getTheme(),
      onHover: () => this.handleTooltipHover(word),
      onLeave: () => this.handleTooltipLeave(word),
      onExpand: () => this.expandToToolfull(word, position)
    });

    console.log(`[HOVER] Tooltip shown for: ${word}`);
  }

  private async expandToToolfull(word: string, position: { x: number; y: number }): Promise<void> {
    try {
      // 清除扩展定时器
      const state = this.hoverState.get(word);
      if (state?.timers.expandToolfull) {
        clearTimeout(state.timers.expandToolfull);
      }

      // 执行渐进扩展动画
      await uiManager.expandToToolfull({
        word,
        position,
        animationType: 'smooth_expand'
      });

      // 更新状态
      if (state) {
        state.stage = 'toolfull_shown';
      }

      console.log(`[HOVER] Expanded to Toolfull: ${word}`);

    } catch (error) {
      console.error('[HOVER] Expansion failed:', error);
    }
  }

  private async hideTooltip(word: string): Promise<void> {
    await uiManager.hideTooltip(word);
    if (this.activeTooltip === word) {
      this.activeTooltip = null;
    }
  }

  private calculateTooltipPosition(element: HTMLElement, event: MouseEvent): { x: number; y: number } {
    const rect = element.getBoundingClientRect();
    const x = rect.left + rect.width / 2;
    const y = rect.top - 10;

    // 边界检查和调整
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    return {
      x: Math.max(50, Math.min(x, viewportWidth - 200)),
      y: Math.max(50, Math.min(y, viewportHeight - 100))
    };
  }

  private clearHoverState(word: string): void {
    const state = this.hoverState.get(word);
    if (!state) return;

    // 清除所有定时器
    Object.values(state.timers).forEach(timer => {
      if (timer) clearTimeout(timer);
    });

    this.hoverState.delete(word);
  }

  // 清理所有状态
  public cleanup(): void {
    for (const [word, state] of this.hoverState.entries()) {
      this.clearHoverState(word);
    }
    this.activeTooltip = null;
  }
}

interface HoverState {
  word: string;
  element: HTMLElement;
  position: { x: number; y: number };
  stage: 'hovering' | 'tooltip_shown' | 'toolfull_shown';
  timers: {
    showTooltip?: NodeJS.Timeout;
    expandToolfull?: NodeJS.Timeout;
    hideTooltip?: NodeJS.Timeout;
  };
}
```

## 渐进展开动画实现

```typescript
// UIManager 中的扩展方法
export class UIManager {
  public async expandToToolfull(options: {
    word: string;
    position: { x: number; y: number };
    animationType?: 'smooth_expand' | 'fade_transition';
  }): Promise<void> {
    const { word, position, animationType = 'smooth_expand' } = options;
    const tooltipId = `tooltip-${word}`;
    const toolfullId = `toolfull-${word}`;
    
    const tooltipView = this.views.get(tooltipId);
    if (!tooltipView) {
      // 如果没有 Tooltip，直接显示 Toolfull
      return this.showToolfull({ word, position });
    }

    try {
      // 阶段 1: 准备扩展动画
      await this.prepareExpansion(tooltipView, word);
      
      // 阶段 2: 执行扩展动画
      if (animationType === 'smooth_expand') {
        await this.performSmoothExpansion(tooltipView, word, position);
      } else {
        await this.performFadeTransition(tooltipView, word, position);
      }
      
      // 阶段 3: 清理旧视图
      tooltipView.destroy();
      this.views.delete(tooltipId);

    } catch (error) {
      console.error('[UIMANAGER] Expansion failed:', error);
      // 降级到直接显示 Toolfull
      await tooltipView.hide();
      tooltipView.destroy();
      this.views.delete(tooltipId);
      await this.showToolfull({ word, position });
    }
  }

  private async prepareExpansion(tooltipView: ShadowView, word: string): Promise<void> {
    // 预加载 Toolfull 数据
    const { data } = await dictionaryService.getWordDefinition(word);
    
    if (!data) {
      throw new Error(`No data available for word: ${word}`);
    }

    // 预处理数据，确保所有必需字段都存在
    this.preprocessWordData(data);
  }

  private async performSmoothExpansion(
    tooltipView: ShadowView,
    word: string,
    position: { x: number; y: number }
  ): Promise<void> {
    // 获取当前 Tooltip 的尺寸和位置
    const tooltipElement = tooltipView['hostElement'];
    const currentRect = tooltipElement.getBoundingClientRect();
    
    // 创建 Toolfull 组件
    const toolfullComponent = React.createElement(Toolfull, {
      word,
      theme: this.currentTheme,
      onClose: () => this.hideToolfull(word),
      expandAnimation: true
    });

    // 创建新的 ShadowView，初始尺寸与 Tooltip 相同
    const toolfullView = new ShadowView({
      id: `toolfull-${word}`,
      component: toolfullComponent,
      styles: [globalStyles, toolfullStyles],
      position: { x: currentRect.left, y: currentRect.top },
      theme: this.currentTheme,
      animation: 'expand',
      hostClass: "lucid-toolfull-host",
      zIndex: 9999,
      closeOnClickOutside: true
    });

    // 设置初始样式（Tooltip 尺寸）
    const hostElement = toolfullView['hostElement'];
    hostElement.style.width = `${currentRect.width}px`;
    hostElement.style.height = `${currentRect.height}px`;
    hostElement.style.transition = 'all 0.4s cubic-bezier(0.4, 0.0, 0.2, 1)';

    // 隐藏原 Tooltip
    await tooltipView.hide();

    // 显示 Toolfull（初始为 Tooltip 尺寸）
    toolfullView.show();
    this.views.set(`toolfull-${word}`, toolfullView);

    // 执行扩展动画
    await new Promise<void>((resolve) => {
      requestAnimationFrame(() => {
        // 扩展到目标尺寸
        hostElement.style.width = '360px';
        hostElement.style.height = 'auto';
        hostElement.style.minHeight = '200px';
        
        // 调整位置以保持居中
        const newX = position.x - 180; // 360px / 2
        const newY = position.y - 20;
        hostElement.style.left = `${newX}px`;
        hostElement.style.top = `${newY}px`;

        // 动画完成后解决 Promise
        hostElement.addEventListener('transitionend', () => resolve(), { once: true });
      });
    });

    console.log(`[UIMANAGER] Smooth expansion completed: ${word}`);
  }

  private async performFadeTransition(
    tooltipView: ShadowView,
    word: string,
    position: { x: number; y: number }
  ): Promise<void> {
    // 渐隐当前 Tooltip
    await tooltipView.hide();
    
    // 稍微延迟后显示 Toolfull
    await new Promise(resolve => setTimeout(resolve, 100));
    
    await this.showToolfull({
      word,
      position,
      animationType: 'fade_in'
    });
  }

  private preprocessWordData(data: WordData): void {
    // 确保数据完整性
    if (!data.explain || data.explain.length === 0) {
      data.explain = [{
        pos: 'unknown',
        definitions: [{
          definition: 'No definition available',
          chinese: '暂无释义',
          chinese_short: '暂无'
        }]
      }];
    }

    // 限制释义数量以提高性能
    data.explain = data.explain.slice(0, 3);
    data.explain.forEach(exp => {
      exp.definitions = exp.definitions.slice(0, 2);
    });
  }
}
```

## 状态管理和错误处理

```typescript
// 全局交互状态管理器
class InteractionStateManager {
  private state = {
    activeInteractions: new Map<string, InteractionContext>(),
    globalSettings: {
      tooltipDelay: 200,
      expansionDelay: 2000,
      hideDelay: 300,
      maxConcurrentTooltips: 1,
      enableSmoothExpansion: true
    },
    performanceMetrics: {
      tooltipShowTime: [] as number[],
      expansionTime: [] as number[],
      cacheHitRate: 0
    }
  };

  public startInteraction(word: string, type: InteractionType): string {
    const id = `${type}_${word}_${Date.now()}`;
    
    this.state.activeInteractions.set(id, {
      id,
      word,
      type,
      startTime: performance.now(),
      stage: 'started'
    });

    return id;
  }

  public updateInteraction(id: string, stage: InteractionStage): void {
    const interaction = this.state.activeInteractions.get(id);
    if (interaction) {
      interaction.stage = stage;
      interaction.lastUpdate = performance.now();
    }
  }

  public endInteraction(id: string, success: boolean): void {
    const interaction = this.state.activeInteractions.get(id);
    if (!interaction) return;

    const duration = performance.now() - interaction.startTime;
    
    // 记录性能指标
    if (interaction.type === 'tooltip' && success) {
      this.state.performanceMetrics.tooltipShowTime.push(duration);
    } else if (interaction.type === 'expansion' && success) {
      this.state.performanceMetrics.expansionTime.push(duration);
    }

    this.state.activeInteractions.delete(id);
    
    // 清理旧指标
    this.cleanupMetrics();
  }

  private cleanupMetrics(): void {
    const metrics = this.state.performanceMetrics;
    
    // 只保留最近 50 次的指标
    if (metrics.tooltipShowTime.length > 50) {
      metrics.tooltipShowTime = metrics.tooltipShowTime.slice(-50);
    }
    if (metrics.expansionTime.length > 50) {
      metrics.expansionTime = metrics.expansionTime.slice(-50);
    }
  }

  public getPerformanceReport(): PerformanceReport {
    const metrics = this.state.performanceMetrics;
    
    return {
      avgTooltipTime: this.calculateAverage(metrics.tooltipShowTime),
      avgExpansionTime: this.calculateAverage(metrics.expansionTime),
      activeInteractions: this.state.activeInteractions.size,
      cacheHitRate: metrics.cacheHitRate
    };
  }

  private calculateAverage(numbers: number[]): number {
    return numbers.length > 0 
      ? numbers.reduce((a, b) => a + b, 0) / numbers.length 
      : 0;
  }
}

interface InteractionContext {
  id: string;
  word: string;
  type: InteractionType;
  stage: InteractionStage;
  startTime: number;
  lastUpdate?: number;
}

type InteractionType = 'tooltip' | 'toolfull' | 'expansion' | 'highlight';
type InteractionStage = 'started' | 'loading' | 'displayed' | 'expanding' | 'completed' | 'error';

interface PerformanceReport {
  avgTooltipTime: number;
  avgExpansionTime: number;
  activeInteractions: number;
  cacheHitRate: number;
}

export const interactionState = new InteractionStateManager();
```

这个优化后的组件交互设计提供了：

1. **精确的时机控制** - 防抖、延迟、状态管理
2. **流畅的动画效果** - Tooltip → Toolfull 渐进展开
3. **智能错误处理** - 降级策略、错误恢复
4. **性能监控** - 交互指标、响应时间统计
5. **用户体验优化** - 边界检查、状态反馈
6. **内存管理** - 及时清理、状态复用

这个设计确保了用户在使用 Lucid 扩展时能够获得极致流畅的交互体验。