好的，我们来深入梳理原项目的高亮设计，并将其精华部分提炼、简化，然后编排进我们正在设计的新体系中。

原项目的高亮设计非常强大和灵活，但层次过多，耦合也有些复杂。我们的目标是：**保留其核心的动态样式能力，但用更简洁、更直接的方式来组织和调用它。**

---

### 1. **原项目高亮设计的核心思想解析**

原项目的高亮系统可以分解为这几个核心部分：

1.  **数据驱动样式 (`LucidStyleManager`)**:

    - **核心理念**: 样式不是写死的，而是根据数据（查询次数 `count`、文本颜色 `isDarkText`）动态计算生成的。
    - **颜色级别 (`ColorLevelCalculator`)**: 将 1-10 的查询次数映射到 L1-L5 的五个颜色级别。这是实现“越熟的词颜色越深”的关键。
    - **颜色混合 (`color-mix()`)**: 使用现代 CSS 的 `color-mix()` 函数，根据文本是深色还是浅色，微调高亮的基础色，以保证在任何背景下都有良好的对比度。
    - **用户效果 (`UserEffectsManager`)**: 允许用户自由组合多种视觉效果（如下划线、闪光），而不是提供单一固定的样式。

2.  **DOM 操作 (`HighlightDomTraverser`)**:

    - 负责在页面上查找文本节点，并用自定义的 `<lucid>` 标签包裹它们。
    - 处理了避免重复包裹、更新现有高亮等复杂 DOM 场景。

3.  **业务逻辑 (`HighlightWordUseCase`)**:

    - 负责管理单词的查询次数（`count`），并决定是增加次数还是新增高亮。
    - 与存储层交互，持久化单词的查询次数。

4.  **编排层 (`HighlightCoordinator`)**:
    - 作为“胶水”，将上述所有部分粘合在一起。它响应用户操作，调用 Use Case 更新数据，调用 Traverser 操作 DOM，并最终触发样式应用。

---

### 2. **在新体系中的重新编排**

在新体系中，我们将抛弃 `UseCase` 和 `Coordinator`，将它们的职责合并到更精简的 `highlight.ts` 模块和 `useHighlight` Hook 中。

#### **项目结构**

```
lucid/
└── src/
    ├── features/
    │   ├── highlight/
    │   │   ├── highlight.dom.ts      # 【新】DOM 操作层 (原Traverser)
    │   │   ├── highlight.storage.ts  # 【新】存储层 (管理单词count)
    │   │   ├── highlight.styles.ts   # 【新】样式逻辑层 (原StyleManager)
    │   │   └── useHighlight.ts       # 【新】React Hook (原Coordinator+UseCase)
    │
    └── styles/
        └── highlight.css         # 【新】所有高亮相关的静态CSS规则
```

**设计思路**:

- **按职责拆分文件**: 将原项目中庞大的 `Highlight...` 类拆分成更小的、职责单一的 `.ts` 文件。
  - `highlight.dom.ts`: 只管 DOM，不管数据和样式。
  - `highlight.storage.ts`: 只管数据持久化，不管 DOM。
  - `highlight.styles.ts`: 只管样式计算和类名生成，不管 DOM 和数据。
- **`useHighlight.ts` 作为总指挥**: 这个 Hook 将成为高亮功能的大脑，负责调用其他三个模块，完成整个业务流程。

---

### 3. **核心模块的详细设计**

#### **`styles/highlight.css` (静态样式基础)**

这个文件定义所有高亮效果的“规则”，但不决定“应用哪个规则”。它使用 CSS 变量来实现动态化。

```css
/* src/styles/highlight.css */

/* 1. 定义颜色变量和混合基础 */
:root {
  --lucid-highlight-start: #f97316; /* orange-500 */
  --lucid-highlight-end: #dc2626; /* red-600 */
  --lucid-current-color: var(--lucid-highlight-start); /* 默认颜色 */
}

/* 2. 定义颜色级别 (L1-L5) */
.lu-level-1 {
  --lucid-current-color: color-mix(
    in srgb,
    var(--lucid-highlight-start) 100%,
    var(--lucid-highlight-end) 0%
  );
}
.lu-level-2 {
  --lucid-current-color: color-mix(
    in srgb,
    var(--lucid-highlight-start) 80%,
    var(--lucid-highlight-end) 20%
  );
}
.lu-level-3 {
  --lucid-current-color: color-mix(
    in srgb,
    var(--lucid-highlight-start) 60%,
    var(--lucid-highlight-end) 40%
  );
}
.lu-level-4 {
  --lucid-current-color: color-mix(
    in srgb,
    var(--lucid-highlight-start) 40%,
    var(--lucid-highlight-end) 60%
  );
}
.lu-level-5 {
  --lucid-current-color: color-mix(
    in srgb,
    var(--lucid-highlight-start) 20%,
    var(--lucid-highlight-end) 80%
  );
}

/* 3. 定义所有可选的视觉效果 */
lucid-highlight.lu-gradient {
  background: linear-gradient(135deg, var(--lucid-current-color), #ffba8a);
  -webkit-background-clip: text;
  color: transparent;
}

lucid-highlight.lu-underline {
  text-decoration: underline;
  text-underline-offset: 3px;
  text-decoration-thickness: 2px;
  text-decoration-color: var(--lucid-current-color);
}

lucid-highlight.lu-bold {
  font-weight: bold;
}

lucid-highlight.lu-shadow {
  text-shadow: 1px 1px 2px var(--lucid-current-color);
}

/* ... 其他效果 ... */
```

#### **`features/highlight/highlight.styles.ts` (样式逻辑层)**

这个模块负责根据数据计算出应该应用哪些 CSS class。

```typescript
// src/features/highlight/highlight.styles.ts

// 沿用原设计的优秀理念
const LEVEL_THRESHOLD = 2;

export interface LucidStyleConfig {
  count: number;
  isDarkText: boolean; // 虽然CSS里没直接用，但保留用于未来可能的JS逻辑
  userEffects?: string[]; // e.g., ['lu-gradient', 'lu-underline']
}

/**
 * 根据查询次数计算颜色级别 (L1-L5)
 */
function calculateLevel(count: number): number {
  if (count <= 0) return 1;
  const level = Math.ceil(count / LEVEL_THRESHOLD);
  return Math.min(level, 5);
}

/**
 * 根据配置生成需要应用到 DOM 元素的 CSS class 列表
 */
export function generateClasses(config: LucidStyleConfig): string[] {
  const classes = new Set<string>();

  // 1. 添加级别 class
  const level = calculateLevel(config.count);
  classes.add(`lu-level-${level}`);

  // 2. 添加用户选择的效果 class
  const effects = config.userEffects || ["lu-gradient", "lu-underline"]; // 默认效果
  effects.forEach((effect) => classes.add(effect));

  return Array.from(classes);
}
```

#### **`features/highlight/highlight.storage.ts` (存储层)**

这个模块只负责一件事：管理单词的查询次数。

```typescript
// src/features/highlight/highlight.storage.ts
import { storage } from "../../services/storage"; // 假设 storage.ts 封装了 chrome.storage

const WORD_COUNT_KEY = "lucid-word-counts";

async function getCounts(): Promise<Map<string, number>> {
  const data = await storage.get(WORD_COUNT_KEY);
  return new Map(Object.entries(data || {}));
}

async function saveCounts(counts: Map<string, number>): Promise<void> {
  await storage.set(WORD_COUNT_KEY, Object.fromEntries(counts));
}

/**
 * 增加一个单词的查询次数，并返回新的次数
 */
export async function incrementWordCount(word: string): Promise<number> {
  const counts = await getCounts();
  const newCount = (counts.get(word) || 0) + 1;
  counts.set(word, newCount);
  await saveCounts(counts);
  return newCount;
}

/**
 * 获取一个单词的查询次数
 */
export async function getWordCount(word: string): Promise<number> {
  const counts = await getCounts();
  return counts.get(word) || 0;
}
```

#### **`features/highlight/highlight.dom.ts` (DOM 操作层)**

这个模块只负责纯粹的 DOM 查找和修改。

```typescript
// src/features/highlight/highlight.dom.ts
import { generateClasses, LucidStyleConfig } from "./highlight.styles";

/**
 * 在指定的 Range 内包裹单词
 */
export function applyHighlight(
  range: Range,
  config: LucidStyleConfig
): HTMLElement {
  const word = range.toString();
  const highlightElement = document.createElement("lucid-highlight");
  highlightElement.textContent = word;

  // 应用样式
  updateHighlight(highlightElement, config);

  // 包裹
  range.deleteContents();
  range.insertNode(highlightElement);

  return highlightElement;
}

/**
 * 更新一个已存在高亮元素的样式
 */
export function updateHighlight(
  element: HTMLElement,
  config: LucidStyleConfig
): void {
  // 移除所有旧的 lu- class
  element.className = Array.from(element.classList)
    .filter((c) => !c.startsWith("lu-"))
    .join(" ");

  // 添加新的 class
  const newClasses = generateClasses(config);
  element.classList.add(...newClasses);

  // 存储 count 以便后续读取
  element.dataset.count = config.count.toString();
}

/**
 * 移除高亮，恢复成普通文本节点
 */
export function removeHighlight(element: HTMLElement): void {
  const parent = element.parentNode;
  if (parent) {
    const textNode = document.createTextNode(element.textContent || "");
    parent.replaceChild(textNode, element);
  }
}
```

#### **`features/highlight/useHighlight.ts` (总指挥 Hook)**

这个 Hook 是所有逻辑的编排中心。

```typescript
// src/features/highlight/useHighlight.ts
import { useCallback, useEffect } from "react";
import * as dom from "./highlight.dom";
import * as storage from "./highlight.storage";

export function useHighlight() {
  const handleSelection = useCallback(async () => {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed) return;

    const range = selection.getRangeAt(0);
    const word = range.toString().trim().toLowerCase();
    if (!word) return;

    // 1. 从存储获取/更新数据
    const newCount = await storage.incrementWordCount(word);

    // 2. 准备样式配置
    const styleConfig: LucidStyleConfig = {
      count: newCount,
      isDarkText: false, // 这个可以进一步实现颜色检测逻辑
      userEffects: ["lu-gradient", "lu-underline"], // 从用户设置读取
    };

    // 3. 操作 DOM
    const highlightElement = dom.applyHighlight(range, styleConfig);

    // 4. (可选) 发出事件，通知其他部分（如 Tooltip）
    // eventBus.emit('highlight:created', { element: highlightElement, word });
  }, []);

  useEffect(() => {
    // 监听鼠标抬起事件来触发高亮
    document.addEventListener("mouseup", handleSelection);
    return () => document.removeEventListener("mouseup", handleSelection);
  }, [handleSelection]);

  // 还可以暴露其他方法，如 remove, update 等
  const remove = async (element: HTMLElement) => {
    // ... 调用 storage.resetWordCount() 和 dom.removeHighlight() ...
  };

  return { highlight: handleSelection, remove };
}
```

### 4. **流程总结**

1.  **用户划词**: `useHighlight` Hook 监听到 `mouseup` 事件。
2.  **数据处理**: Hook 调用 `highlight.storage.ts` 中的 `incrementWordCount`，将单词 "escalade" 的计数从 0 更新到 1，并返回新计数 `1`。
3.  **样式决策**: Hook 创建 `LucidStyleConfig` 对象 `{ count: 1, isDarkText: false, userEffects: [...] }`。
4.  **DOM 操作**: Hook 调用 `highlight.dom.ts` 的 `applyHighlight`，并传入 `range` 和 `styleConfig`。
5.  **`applyHighlight` 内部**:
    a. 创建一个 `<lucid-highlight>` 元素。
    b. 调用 `updateHighlight`。
    c. **`updateHighlight` 内部**:
    i. 调用 `highlight.styles.ts` 的 `generateClasses({ count: 1, ... })`。
    ii. `generateClasses` 调用 `calculateLevel(1)`，返回 `1`。
    iii. `generateClasses` 返回 `['lu-level-1', 'lu-gradient', 'lu-underline']`。
    iv. `updateHighlight` 将这些 class 添加到 `<lucid-highlight>` 元素上。
    d. `applyHighlight` 将元素插入 DOM。
6.  **CSS 生效**: 浏览器根据 `styles/highlight.css` 中的规则，将 `.lu-level-1`, `.lu-gradient`, `.lu-underline` 对应的样式应用到元素上，最终呈现出 L1 级别的渐变+下划线效果。

这个新设计**保留了原设计的精髓**（数据驱动的动态、可组合样式），但**极大地简化了代码结构**，使其更符合单人开发的直观性和效率，同时保持了良好的模块化和可维护性。
