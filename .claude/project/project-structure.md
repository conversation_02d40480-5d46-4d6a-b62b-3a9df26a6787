# Lucid Browser Extension - 优化后的项目结构设计

## 目录结构优化

### 原设计问题
- `components/ui/` 层级冗余，增加不必要的嵌套
- `features/slider/` 与 `components/Slider.tsx` 功能重复
- 缺少明确的样式组织策略

### 优化后的目录结构

```
lucid/
├── entrypoints/
│   ├── content.ts          # 内容脚本主入口
│   ├── background.ts       # 后台脚本主入口
│   └── popup.html          # 插件弹出页面 (如果需要)
│
└── src/
    ├── components/         # 【优化】扁平化的 UI 组件
    │   ├── Tooltip/
    │   │   ├── Tooltip.tsx
    │   │   └── Tooltip.module.css
    │   ├── Toolfull/
    │   │   ├── Toolfull.tsx
    │   │   └── Toolfull.module.css
    │   └── Slider/
    │       ├── Slider.tsx
    │       └── Slider.module.css
    │
    ├── ui-manager/         # 【核心】统一的 UI 管理器
    │   ├── ShadowView.ts   # 管理单个 Shadow DOM 实例的类
    │   ├── uiManager.ts    # 管理所有 Shadow DOM 实例的单例
    │   └── animations.ts   # 【新增】动画和过渡效果处理
    │
    ├── features/           # 核心功能模块
    │   ├── dictionary/     # 查词功能
    │   │   ├── dictionary.api.ts    # API 接口
    │   │   ├── dictionary.cache.ts  # 缓存管理
    │   │   ├── dictionary.types.ts  # 类型定义
    │   │   └── useDictionary.ts     # React Hook
    │   │
    │   ├── translator/     # 翻译功能
    │   │   ├── translator.api.ts
    │   │   ├── translator.dom.ts
    │   │   └── useTranslator.ts
    │   │
    │   ├── highlight/      # 高亮功能
    │   │   ├── highlight.manager.ts  # 高亮管理器
    │   │   ├── highlight.types.ts    # 高亮相关类型
    │   │   └── useHighlight.ts       # React Hook
    │   │
    │   └── settings/       # 【新增】设置管理
    │       ├── settings.manager.ts
    │       ├── settings.types.ts
    │       └── useSettings.ts
    │
    ├── services/           # 共享服务
    │   ├── storage.ts      # 存储服务
    │   ├── eventBus.ts     # 事件总线
    │   └── logger.ts       # 【新增】日志服务
    │
    ├── styles/             # 【优化】样式组织
    │   ├── globals/        # 全局样式
    │   │   ├── variables.css      # CSS 变量
    │   │   ├── reset.css          # 重置样式
    │   │   └── animations.css     # 动画样式
    │   └── themes/         # 主题样式
    │       ├── light.css
    │       └── dark.css
    │
    └── types/              # 全局类型定义
        ├── index.ts        # 通用类型
        ├── components.ts   # 组件类型
        └── api.ts          # API 类型
```

## 设计原则

### 1. 扁平化组件结构
- 移除 `components/ui/` 中间层
- 每个组件独立目录，包含 TSX 和 CSS Module
- 便于查找和维护

### 2. 功能模块化
- 每个 feature 包含完整的功能实现
- 清晰的依赖关系
- 便于测试和复用

### 3. 样式统一管理
- 全局样式集中管理
- 主题支持
- CSS 变量统一定义

### 4. 类型安全
- 详细的类型定义
- 分类组织类型文件
- 提供良好的 TypeScript 支持

## 主要改进点

1. **消除重复**: 移除 `features/slider/` 与 `components/Slider.tsx` 的重复
2. **层级优化**: 简化目录嵌套，提高可读性
3. **功能完整**: 增加缺失的设置管理和日志服务
4. **样式组织**: 更好的样式文件组织和主题支持
5. **类型定义**: 更细粒度的类型定义和组织

这个优化后的结构保持了原有的功能完整性，同时提高了代码的可维护性和开发效率。