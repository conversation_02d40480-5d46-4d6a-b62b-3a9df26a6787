# 测试文件组织结构

## 📁 目录说明

### 🚀 Performance Tests (`performance/`)
性能测试相关的HTML页面
- `test-lightweight-performance.html` - 轻量化翻译系统性能测试
- `test-performance-check.html` - 通用性能检查测试
- `test-optimized-injection.html` - DOM注入优化测试

### 🧪 HTML Feature Tests (`html/`)
HTML功能测试页面
- `test-highlight.html` - 文本高亮功能测试
- `test-tooltip.html` - 提示框功能测试
- `test-style-verification.html` - 样式验证测试
- `test-security-fix.html` - 安全修复验证测试

### 🔗 Integration Tests (`integration/`)
集成测试和交互测试
- `test-exclusion-debug.html` - 排除标记调试测试
- `test-exclusion-menu.html` - 排除菜单集成测试
- `test-right-click-menu.html` - 右键菜单功能测试

## 🛠️ 使用方法

### 1. 开发环境测试
1. 启动开发服务器: `pnpm run dev`
2. 加载扩展到Chrome: `.output/chrome-mv3/`
3. 打开相应的测试HTML文件
4. 使用扩展的右键菜单测试功能

### 2. 性能测试流程
1. 打开 `performance/test-lightweight-performance.html`
2. 右键选择 "🧪 测试页面翻译 (轻量化)"
3. 观察控制台的性能指标输出
4. 验证成功率是否达到85%+

### 3. 功能验证流程
1. 打开相应的功能测试页面
2. 按照页面上的说明操作
3. 检查控制台日志确认功能正常

## 📊 测试覆盖

### 核心功能
- ✅ 轻量化翻译系统
- ✅ 文本高亮功能
- ✅ 动态提示框
- ✅ 右键菜单集成
- ✅ 排除标记调试

### 性能指标
- ✅ 翻译速度 (目标: <0.5秒)
- ✅ 成功率 (目标: 85%+)
- ✅ 内存使用优化
- ✅ DOM注入性能

### 安全验证
- ✅ 消息验证机制
- ✅ 域名白名单检查
- ✅ 输入sanitization
- ✅ 权限范围验证

## 🐛 调试说明

### 常用调试命令
在浏览器控制台中可以使用以下全局函数:
- `testLightweightTranslation()` - 测试轻量化翻译
- `testLightweightPageTranslation()` - 测试页面翻译
- `getLightweightTranslationStats()` - 获取翻译统计
- `clearLightweightTranslations()` - 清除翻译内容

### 性能分析
每次翻译操作都会输出详细的性能统计，包括:
- 处理元素数量
- 成功/失败比例
- 耗时统计
- 缓存命中率

---

*所有测试文件都已经集成了完整的调试功能和性能监控* 🔍