<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exclusion Debug Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .hidden-element {
            display: none;
        }
        .invisible-element {
            visibility: hidden;
        }
        .transparent-element {
            opacity: 0;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        code {
            background: #f0f0f0;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .notranslate {
            color: #666;
        }
        .monaco-editor {
            background: #1e1e1e;
            color: white;
            padding: 10px;
        }
    </style>
</head>
<body>
    <!-- 正常可翻译元素 -->
    <h1>Exclusion Debug Test Page</h1>
    <p>This is a test page to demonstrate the exclusion debugging functionality.</p>
    
    <!-- 标题元素（应该被包含） -->
    <div class="test-section">
        <h2>Headers That Should Be Included</h2>
        <h3>Subtitle for testing</h3>
        <p>Regular paragraph that should be translatable.</p>
        <li>List item that should be translatable</li>
    </div>

    <!-- 代码相关元素（应该被排除） -->
    <div class="test-section">
        <h2>Code Elements That Should Be Excluded</h2>
        <p>Here are some code elements:</p>
        <pre><code>function hello() { return "world"; }</code></pre>
        <p>Inline code: <code>console.log('test')</code></p>
        <script>console.log('This script should be excluded');</script>
        <style>.test { color: red; }</style>
        <kbd>Ctrl+C</kbd>
        <samp>Sample output</samp>
        <var>variableName</var>
    </div>

    <!-- 表单控件元素（应该被排除） -->
    <div class="test-section">
        <h2>Form Elements That Should Be Excluded</h2>
        <input type="text" placeholder="Text input" />
        <textarea placeholder="Textarea"></textarea>
        <select>
            <option>Option 1</option>
            <option>Option 2</option>
        </select>
        <button>Button text</button>
    </div>

    <!-- 媒体和交互元素（应该被排除） -->
    <div class="test-section">
        <h2>Media Elements That Should Be Excluded</h2>
        <svg width="100" height="50">
            <text x="10" y="25">SVG Text</text>
        </svg>
        <canvas width="100" height="50">Canvas fallback text</canvas>
        <iframe src="about:blank" width="200" height="100"></iframe>
    </div>

    <!-- 导航和元数据元素（应该被排除） -->
    <nav class="test-section">
        <h2>Navigation That Should Be Excluded</h2>
        <ul>
            <li><a href="#">Home</a></li>
            <li><a href="#">About</a></li>
            <li><a href="#">Contact</a></li>
        </ul>
    </nav>

    <header class="test-section">
        <h2>Header Content That Should Be Excluded</h2>
        <p>This is header content</p>
    </header>

    <footer class="test-section">
        <h2>Footer Content That Should Be Excluded</h2>
        <p>This is footer content</p>
    </footer>

    <aside class="test-section">
        <h2>Sidebar Content That Should Be Excluded</h2>
        <p>This is sidebar content</p>
    </aside>

    <!-- 排除标记元素（应该被排除） -->
    <div class="test-section">
        <h2>Elements With Exclusion Markers</h2>
        <p class="notranslate">This has notranslate class</p>
        <p data-no-translate>This has data-no-translate attribute</p>
        <p translate="no">This has translate="no" attribute</p>
        <div class="lu-skip">
            <p>This is inside lu-skip class</p>
        </div>
    </div>

    <!-- 编辑器相关元素（应该被排除） -->
    <div class="test-section">
        <h2>Editor Elements That Should Be Excluded</h2>
        <div contenteditable="true">
            <p>This is editable content</p>
        </div>
        <div class="editor">
            <p>This is inside editor class</p>
        </div>
        <div class="monaco-editor">
            <p>This is Monaco editor content</p>
        </div>
    </div>

    <!-- 隐藏元素（应该被排除） -->
    <div class="test-section">
        <h2>Hidden Elements That Should Be Excluded</h2>
        <p class="hidden-element">This element has display: none</p>
        <p class="invisible-element">This element has visibility: hidden</p>
        <p class="transparent-element">This element has opacity: 0</p>
    </div>

    <!-- 已翻译元素（应该被排除） -->
    <div class="test-section">
        <h2>Already Translated Elements</h2>
        <p data-lu-translated="true">This element is already translated</p>
    </div>

    <!-- 短文本和纯数字（可能被TextFilter排除） -->
    <div class="test-section">
        <h2>Text Filter Test Elements</h2>
        <p>Hi</p> <!-- 太短 -->
        <p>123</p> <!-- 纯数字 -->
        <p>!!!</p> <!-- 纯标点 -->
        <p><EMAIL></p> <!-- 邮箱格式 -->
        <p>$19.99</p> <!-- 价格格式 -->
        <p>#FF5733</p> <!-- 颜色代码 -->
        <p>https://example.com</p> <!-- URL -->
        <p>example.com</p> <!-- 域名 -->
    </div>

    <div class="test-section">
        <h2>Test Instructions</h2>
        <p>Open the browser console and run the following commands:</p>
        <ol>
            <li><code>quickExclusionTest()</code> - Run a quick test to show excluded elements</li>
            <li><code>showExcluded()</code> - Show excluded elements with "ooo" markers</li>
            <li><code>hideExcluded()</code> - Clear all markers</li>
            <li><code>exclusionStats()</code> - Get detailed exclusion statistics</li>
            <li><code>debugExclusions.toggle()</code> - Toggle marker visibility</li>
        </ol>
        <p>After running <code>showExcluded()</code>, you should see red "ooo" markers on excluded elements. Click on marked elements to see exclusion details in the console.</p>
    </div>

    <noscript>
        <p>This noscript content should also be excluded</p>
    </noscript>
</body>
</html>