<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Exclusion Menu Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 { color: #333; }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #2196F3;
            background: #f8f9fa;
        }
        .excluded-content {
            padding: 10px;
            margin: 10px 0;
        }
        code { 
            background: #f1f1f1; 
            padding: 2px 6px; 
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .notranslate { color: #666; }
        [data-no-translate] { color: #999; }
        input, textarea {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1976D2; }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-item {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Exclusion Menu Integration Test</h1>
        
        <div class="instructions">
            <h3>📋 Test Instructions</h3>
            <ol>
                <li><strong>Load the extension</strong> and navigate to this page</li>
                <li><strong>Right-click anywhere</strong> on the page to see the context menu</li>
                <li>You should see new menu items:
                    <ul>
                        <li>🔍 <strong>切换排除标记</strong> - Toggle exclusion markers</li>
                        <li>📊 <strong>显示排除统计</strong> - Show exclusion statistics</li>
                        <li>🔄 <strong>重置排除标记</strong> - Reset exclusion markers</li>
                    </ul>
                </li>
                <li><strong>Exclusion markers should appear automatically</strong> as red "ooo" badges on excluded elements</li>
                <li>Click on any red "ooo" marker to see exclusion details in the console</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>Translatable Content (should NOT have exclusion markers)</h2>
            <p>This is a regular paragraph that should be translatable.</p>
            <h3>This is a heading that should be translatable</h3>
            <p>Another paragraph with <span>nested content</span> for testing.</p>
            <ul>
                <li>List item one</li>
                <li>List item two</li>
                <li>List item three</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>Excluded Content (should have red "ooo" markers)</h2>
            
            <div class="test-grid">
                <div class="test-item">
                    <h4>Code Elements</h4>
                    <p>Inline code: <code>console.log('hello')</code></p>
                    <pre><code>function test() {
    return "This should be excluded";
}</code></pre>
                </div>

                <div class="test-item">
                    <h4>Form Elements</h4>
                    <input type="text" placeholder="Text input" />
                    <textarea placeholder="Textarea content"></textarea>
                    <button>Button Element</button>
                    <select>
                        <option>Option 1</option>
                        <option>Option 2</option>
                    </select>
                </div>

                <div class="test-item">
                    <h4>No-Translate Classes</h4>
                    <p class="notranslate">This has notranslate class</p>
                    <div data-no-translate="true">This has data-no-translate attribute</div>
                    <span translate="no">This has translate="no" attribute</span>
                </div>

                <div class="test-item">
                    <h4>Script and Style</h4>
                    <script type="text/javascript">
                        // This script should be excluded
                        console.log('This is a script');
                    </script>
                    <style>
                        /* This style should be excluded */
                        .test { color: red; }
                    </style>
                    <noscript>This noscript content should be excluded</noscript>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Expected Behavior</h2>
            <ul>
                <li>✅ Red "ooo" markers should appear on excluded elements automatically</li>
                <li>✅ Right-click menu should contain exclusion debug options</li>
                <li>✅ Toggle markers should show/hide the red badges</li>
                <li>✅ Statistics should show counts by exclusion reason</li>
                <li>✅ Reset should clear and re-add markers</li>
                <li>✅ Clicking markers should log details to console</li>
                <li>❌ Regular content should NOT have markers</li>
                <li>❌ No console debugging methods should be available globally</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>Debug Information</h2>
            <p>If the exclusion markers are not showing, check:</p>
            <ul>
                <li>Browser console for any error messages</li>
                <li>Extension reload may be required after code changes</li>
                <li>Make sure you're testing with the modified extension files</li>
                <li>Verify the content script is loading properly</li>
            </ul>
        </div>
    </div>

    <script>
        // Test script that should be excluded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded - this script element should have exclusion markers');
            
            // Log current global objects (should NOT include debugExclusions anymore)
            const globalDebugMethods = [
                'debugExclusions',
                'showExcluded', 
                'hideExcluded',
                'toggleExcluded',
                'exclusionStats',
                'quickExclusionTest'
            ];
            
            const foundMethods = globalDebugMethods.filter(method => 
                typeof window[method] !== 'undefined'
            );
            
            if (foundMethods.length > 0) {
                console.warn('❌ Found global debug methods (should be removed):', foundMethods);
            } else {
                console.log('✅ No global debug methods found (correct behavior)');
            }
        });
    </script>
</body>
</html>