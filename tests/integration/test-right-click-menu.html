<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试右键菜单功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
        }
        .console-log {
            background: #263238;
            color: #a7ffeb;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 右键菜单功能测试页面</h1>
    
    <div class="test-section">
        <h2>📋 测试说明</h2>
        <p>这个页面用于测试Lucid扩展的右键菜单功能。按照以下步骤测试：</p>
        <ol>
            <li><strong>确保扩展已加载</strong>：检查Chrome扩展管理页面</li>
            <li><strong>打开开发者工具</strong>：按F12查看控制台</li>
            <li><strong>右键点击页面</strong>：应该看到"翻译 'test web noted form Extension'"菜单项</li>
            <li><strong>点击翻译菜单</strong>：应该在控制台看到日志并在页面显示翻译结果</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🎯 测试目标文本</h2>
        <p>扩展应该翻译这段文本：<span class="highlight">test web noted form Extension</span></p>
        <p>预期翻译结果：<span class="highlight">测试网络表单扩展</span></p>
    </div>

    <div class="test-section">
        <h2>🔍 预期控制台输出</h2>
        <div class="console-log">
🔧 Background Script: 创建右键菜单...<br>
✅ 菜单创建: lucid-slider<br>
✅ 菜单创建: translate-text<br>
🎯 所有右键菜单创建完成<br>
🔧 Background Script: 右键菜单点击事件<br>
📋 菜单项ID: translate-text<br>
🎯 标签页: [TAB_ID]<br>
🔧 Background Script: 右键菜单点击 - 翻译文本<br>
📝 要翻译的文本: test web noted form Extension<br>
🎯 目标标签页: [TAB_ID]<br>
✅ 消息发送成功，响应: [RESPONSE]<br>
🚀 开始右键翻译功能<br>
📝 要翻译的文本: test web noted form Extension<br>
🔄 翻译方向: auto → zh<br>
✅ 翻译成功!<br>
🌟 翻译结果: 测试网络表单扩展<br>
🔧 使用引擎: google<br>
        </div>
    </div>

    <div class="test-section">
        <h2>🚨 故障排除</h2>
        <h3>如果右键菜单不出现：</h3>
        <ul>
            <li>检查扩展是否已正确加载</li>
            <li>检查扩展是否有权限访问当前页面</li>
            <li>刷新页面并重试</li>
        </ul>
        
        <h3>如果点击菜单没有反应：</h3>
        <ul>
            <li>检查控制台是否有JavaScript错误</li>
            <li>确认Background Script是否正在运行</li>
            <li>查看是否有网络连接问题</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔧 调试工具</h2>
        <p>在控制台中运行以下命令来调试：</p>
        <div class="console-log">
// 测试Background Script连接<br>
testBackgroundScript()<br>
<br>
// 测试翻译功能<br>
testTranslation("test web noted form Extension")<br>
<br>
// 查看引擎状态<br>
showEngineStatus()
        </div>
    </div>

    <script>
        // 添加一些调试信息
        console.log('🧪 右键菜单测试页面已加载');
        console.log('📄 页面URL:', window.location.href);
        console.log('🌐 用户代理:', navigator.userAgent);
        console.log('🔧 Chrome扩展环境:', typeof chrome !== 'undefined' && chrome.runtime);
        
        // 页面加载完成后的检查
        window.addEventListener('load', () => {
            console.log('✅ 页面加载完成，可以测试右键菜单功能');
            
            // 检查扩展是否注入了内容脚本
            setTimeout(() => {
                console.log('🔍 检查内容脚本是否已注入...');
                
                // 检查全局测试函数是否可用
                const testFunctions = ['testTranslation', 'testBackgroundScript', 'showEngineStatus'];
                testFunctions.forEach(func => {
                    if (typeof window[func] === 'function') {
                        console.log(`✅ ${func} 函数可用`);
                    } else {
                        console.log(`❌ ${func} 函数不可用`);
                    }
                });
            }, 2000);
        });
    </script>
</body>
</html>