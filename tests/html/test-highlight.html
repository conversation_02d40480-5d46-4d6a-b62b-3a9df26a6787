<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lucid Highlight System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f0f0f0;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        button {
            margin: 5px;
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
            font-family: monospace;
        }
        
        /* Import highlight styles */
        @import url('../src/styles/highlight.css');
    </style>
</head>
<body>
    <h1>🎯 Lucid Highlight System Test</h1>
    
    <div class="controls">
        <h3>Controls</h3>
        <button onclick="startScan()">Start Scan</button>
        <button onclick="pauseScan()">Pause</button>
        <button onclick="resumeScan()">Resume</button>
        <button onclick="stopScan()">Stop</button>
        <button onclick="clearAll()">Clear All</button>
        <button onclick="addTestWord()">Add Test Word</button>
        <div class="status" id="status">Ready...</div>
    </div>

    <div class="test-section">
        <h2>📝 Instructions</h2>
        <p>1. <strong>Manual Highlighting:</strong> Select any word and press <kbd>Shift</kbd> to highlight it</p>
        <p>2. <strong>Auto Scanning:</strong> Click "Add Test Word" then "Start Scan" to see automatic highlighting</p>
        <p>3. <strong>Dynamic Content:</strong> New content will be automatically scanned</p>
        <p>4. <strong>Right-click</strong> any highlighted word to remove it</p>
    </div>

    <div class="test-section">
        <h2>🔤 Sample Text for Testing</h2>
        <p>This is a sample paragraph containing various words that can be highlighted. The word "test" appears multiple times in this test document. Try selecting the word "sample" and pressing Shift to highlight it.</p>
        
        <p>JavaScript is a powerful programming language. Many developers use JavaScript for web development. The word "JavaScript" should be highlighted consistently across this document.</p>
        
        <p>React is a popular library for building user interfaces. React components are reusable pieces of code. When you highlight "React", all instances should be highlighted with the same intensity.</p>
    </div>

    <div class="test-section">
        <h2>🎨 Different Text Styles</h2>
        <p style="color: #333;">This is dark text that should work well with light highlights.</p>
        <p style="color: #666;">This is medium gray text.</p>
        <p style="color: #999;">This is light gray text.</p>
        <p style="background: #333; color: #fff; padding: 10px;">This is light text on dark background.</p>
    </div>

    <div class="test-section">
        <h2>🔧 Complex HTML Structures</h2>
        <div>
            <p>Text in <strong>bold</strong> and <em>italic</em> formatting.</p>
            <p>Links like <a href="#">this link</a> should also be processable.</p>
            <ul>
                <li>List item with the word "item"</li>
                <li>Another list item containing "word"</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🚀 Dynamic Content Test</h2>
        <div id="dynamic-content">
            <p>This content will be replaced dynamically.</p>
        </div>
        <button onclick="addDynamicContent()">Add Dynamic Content</button>
    </div>

    <div class="test-section">
        <h2>📊 Statistics</h2>
        <div id="stats">
            <p>Total Words: <span id="total-words">0</span></p>
            <p>Total Highlights: <span id="total-highlights">0</span></p>
            <p>Scan Progress: <span id="scan-progress">0/0</span></p>
        </div>
    </div>

    <script type="module">
        // Mock chrome.storage API for testing
        window.chrome = {
            storage: {
                local: {
                    get: async (key) => {
                        const data = localStorage.getItem(key);
                        return data ? { [key]: JSON.parse(data) } : {};
                    },
                    set: async (data) => {
                        for (const [key, value] of Object.entries(data)) {
                            localStorage.setItem(key, JSON.stringify(value));
                        }
                    },
                    remove: async (key) => {
                        localStorage.removeItem(key);
                    },
                    clear: async () => {
                        localStorage.clear();
                    }
                }
            }
        };

        // Import and initialize the highlight system
        import { LucidHighlight } from './src/features/highlight.js';
        
        let highlighter;
        
        async function initializeHighlighter() {
            highlighter = new LucidHighlight({
                effects: ['lu-gradient', 'lu-underline'],
                enabled: true,
                minWordLength: 2,
                maxWordLength: 30,
                autoScan: true,
                scanOnLoad: false, // We'll manually control scanning for testing
                watchDynamicContent: true,
                batchSize: 10, // Smaller batch for testing
                scanDelay: 100
            });
            
            await highlighter.initialize();
            updateStatus('Highlighter initialized. Try selecting text and pressing Shift!');
            updateStats();
        }
        
        // Control functions
        window.startScan = async () => {
            updateStatus('Starting scan...');
            await highlighter.scanPage();
            updateStatus('Scan completed!');
            updateStats();
        };
        
        window.pauseScan = () => {
            highlighter.pauseScanning();
            updateStatus('Scanning paused');
        };
        
        window.resumeScan = () => {
            highlighter.resumeScanning();
            updateStatus('Scanning resumed');
        };
        
        window.stopScan = () => {
            highlighter.stopScanning();
            updateStatus('Scanning stopped');
        };
        
        window.clearAll = async () => {
            await highlighter.clearAll();
            updateStatus('All highlights cleared');
            updateStats();
        };
        
        window.addTestWord = async () => {
            // Simulate adding a word by creating a selection and highlighting it
            const testWords = ['test', 'sample', 'word', 'JavaScript', 'React', 'highlight'];
            const randomWord = testWords[Math.floor(Math.random() * testWords.length)];
            
            // Find the first occurrence of the word
            const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                null
            );
            
            let node;
            while (node = walker.nextNode()) {
                const text = node.textContent;
                const index = text.toLowerCase().indexOf(randomWord.toLowerCase());
                if (index !== -1) {
                    // Create a selection
                    const range = document.createRange();
                    range.setStart(node, index);
                    range.setEnd(node, index + randomWord.length);
                    
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);
                    
                    // Simulate Shift key press
                    const event = new KeyboardEvent('keydown', { key: 'Shift' });
                    document.dispatchEvent(event);
                    
                    updateStatus(`Added "${randomWord}" to highlights`);
                    updateStats();
                    break;
                }
            }
        };
        
        window.addDynamicContent = () => {
            const dynamicDiv = document.getElementById('dynamic-content');
            const newContent = `
                <p>This is dynamically added content at ${new Date().toLocaleTimeString()}</p>
                <p>It contains words like "dynamic", "content", and "test" that should be automatically highlighted if they exist in the system.</p>
                <p>The MutationObserver should detect this new content and process it automatically.</p>
            `;
            dynamicDiv.innerHTML = newContent;
            updateStatus('Dynamic content added - should be processed automatically');
        };
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        function updateStats() {
            if (highlighter) {
                const stats = highlighter.getHighlightStats();
                const progress = highlighter.getScanProgress();
                
                document.getElementById('total-words').textContent = stats.totalWords;
                document.getElementById('total-highlights').textContent = stats.totalHighlights;
                document.getElementById('scan-progress').textContent = 
                    `${progress.processed}/${progress.total} ${progress.isScanning ? '(scanning...)' : ''}`;
            }
        }
        
        // Update stats periodically
        setInterval(updateStats, 1000);
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeHighlighter);
    </script>
</body>
</html>