<\!DOCTYPE html>
<html>
  <head>
    <title>Test Interactive Tooltip</title>
    <style>
      body {
        padding: 20px;
        font-family: system-ui;
      }
      .test-area {
        padding: 20px;
        border: 2px dashed #ccc;
        margin: 20px 0;
      }

      /* Tooltip styles */
      .lu-pos {
        font-weight: bold;
        margin-right: 4px;
      }

      .lu-chinese-short {
        margin: 0 2px;
        padding: 2px 4px;
        border-radius: 3px;
      }

      .lu-chinese-short.interactive {
        cursor: pointer;
        user-select: none;
        border: 1px solid transparent;
        transition: all 0.15s ease;
      }

      .lu-chinese-short.interactive:hover {
        background-color: rgba(59, 130, 246, 0.15);
        border-color: rgba(59, 130, 246, 0.3);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
      }

      .lu-chinese-short.interactive:active {
        background-color: rgba(59, 130, 246, 0.25);
        border-color: rgba(59, 130, 246, 0.5);
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(59, 130, 246, 0.3);
      }
    </style>
  </head>
  <body>
    <h1>Interactive Tooltip Test ✅</h1>

    <div class="test-area">
      <h3>测试交互式Tooltip - 每个中文释义可以独立点击：</h3>
      <div style="font-size: 16px; line-height: 2">
        <span class="lu-pos">n.</span>
        <span
          class="lu-chinese-short interactive"
          onclick="handleClick(event, 'noun', '问候')"
          >问候</span
        >
        <span
          class="lu-chinese-short interactive"
          onclick="handleClick(event, 'noun', '欢迎')"
          >欢迎</span
        >
        <span class="lu-separator"> </span>
        <span class="lu-pos">v.</span>
        <span
          class="lu-chinese-short interactive"
          onclick="handleClick(event, 'verb', '招呼')"
          >招呼</span
        >
      </div>
    </div>

    <div class="test-area">
      <h3>功能验证清单：</h3>
      <ul>
        <li>
          ✨ <strong>独立高亮</strong>：每个中文释义hover时应该有蓝色边框和阴影
        </li>
        <li>
          👆
          <strong>可点击性</strong
          >：每个中文释义应该可以独立点击，不会触发tooltip隐藏
        </li>
        <li>💾 <strong>偏好记录</strong>：点击后会记录到localStorage</li>
        <li>🔄 <strong>排序功能</strong>：基于点击次数自动排序</li>
      </ul>
    </div>

    <div id="log-area">
      <h3>点击日志：</h3>
      <div id="click-log"></div>

      <h3>当前偏好数据：</h3>
      <pre id="preference-data">{}</pre>

      <button
        onclick="clearPreferences()"
        style="
          padding: 8px 16px;
          margin: 10px 0;
          background: #dc2626;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        "
      >
        清空偏好数据
      </button>
    </div>

    <script>
      function handleClick(event, pos, chinese) {
          // 阻止事件冒泡（模拟stopPropagation）
          event.stopPropagation();
          event.preventDefault();

          console.log('✅ 点击成功:', pos, chinese);

          // 记录点击
          const timestamp = new Date().toLocaleTimeString();
          const logDiv = document.getElementById('click-log');
          const logEntry = document.createElement('div');
          logEntry.textContent = `${timestamp}: 点击了 ${pos}.${chinese}`;
          logEntry.style.marginBottom = '5px';
          logDiv.appendChild(logEntry);

          // 更新localStorage（模拟preference.ts的逻辑）
          const prefs = JSON.parse(localStorage.getItem('lucid-word-preferences') || '{}');
          if (\!prefs.hello) prefs.hello = {};
          const key = `${pos}.${chinese}`;
          prefs.hello[key] = (prefs.hello[key] || 0) + 1;
          localStorage.setItem('lucid-word-preferences', JSON.stringify(prefs));

          // 更新显示
          updatePreferenceDisplay();

          console.log('📊 偏好已更新:', prefs);
      }

      function updatePreferenceDisplay() {
          const prefs = JSON.parse(localStorage.getItem('lucid-word-preferences') || '{}');
          document.getElementById('preference-data').textContent = JSON.stringify(prefs, null, 2);
      }

      function clearPreferences() {
          localStorage.removeItem('lucid-word-preferences');
          document.getElementById('click-log').innerHTML = '';
          updatePreferenceDisplay();
          console.log('🗑️ 偏好数据已清空');
      }

      // 初始化显示
      updatePreferenceDisplay();

      console.log('🚀 交互式Tooltip测试页面已加载');
      console.log('📝 请测试每个中文释义的hover效果和点击功能');
    </script>
  </body>
</html>
EOF < /dev/null
