<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Fix Validation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-content {
            background-color: #f9f9f9;
            padding: 10px;
            margin: 10px 0;
        }
        .expected {
            color: #008000;
            font-weight: bold;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Security Fix Validation Test</h1>
    <p>This page tests the fixed security validation that should now allow safe HTML content while blocking malicious content.</p>

    <!-- Test Case 1: Safe HTML with links (should pass) -->
    <div class="test-case">
        <h3>Test Case 1: Safe HTML Links</h3>
        <div class="test-content">
            Visit our <a href="https://example.com" target="_blank">website</a> for more information.
        </div>
        <div class="expected">Expected: Should pass security check and be injected successfully</div>
        <div id="result1" class="result"></div>
    </div>

    <!-- Test Case 2: Safe HTML with formatting (should pass) -->
    <div class="test-case">
        <h3>Test Case 2: Safe HTML Formatting</h3>
        <div class="test-content">
            This is <strong>important</strong> and <em>emphasized</em> text with <u>underline</u>.
        </div>
        <div class="expected">Expected: Should pass security check and be injected successfully</div>
        <div id="result2" class="result"></div>
    </div>

    <!-- Test Case 3: Malicious script (should be blocked) -->
    <div class="test-case">
        <h3>Test Case 3: Malicious Script</h3>
        <div class="test-content">
            &lt;script&gt;alert('XSS')&lt;/script&gt;
        </div>
        <div class="expected">Expected: Should be blocked by security check</div>
        <div id="result3" class="result"></div>
    </div>

    <!-- Test Case 4: Dangerous event handler (should be blocked) -->
    <div class="test-case">
        <h3>Test Case 4: Dangerous Event Handler</h3>
        <div class="test-content">
            &lt;a href="#" onclick="alert('XSS')"&gt;Click me&lt;/a&gt;
        </div>
        <div class="expected">Expected: Should be blocked by security check</div>
        <div id="result4" class="result"></div>
    </div>

    <!-- Test Case 5: Safe text without HTML (should pass) -->
    <div class="test-case">
        <h3>Test Case 5: Plain Text</h3>
        <div class="test-content">
            This is just plain text without any HTML tags.
        </div>
        <div class="expected">Expected: Should pass security check and be injected successfully</div>
        <div id="result5" class="result"></div>
    </div>

    <script type="module">
        // Simulate the security check functions
        function testSecurityValidation() {
            const testCases = [
                {
                    id: 'result1',
                    content: 'Visit our <a href="https://example.com" target="_blank">website</a> for more information.',
                    format: 'html',
                    shouldPass: true
                },
                {
                    id: 'result2',
                    content: 'This is <strong>important</strong> and <em>emphasized</em> text with <u>underline</u>.',
                    format: 'html',
                    shouldPass: true
                },
                {
                    id: 'result3',
                    content: '<script>alert(\\\'XSS\\\')</script>',
                    format: 'html',
                    shouldPass: false
                },
                {
                    id: 'result4',
                    content: '<a href="#" onclick="alert(\\\'XSS\\\')">Click me</a>',
                    format: 'html',
                    shouldPass: false
                },
                {
                    id: 'result5',
                    content: 'This is just plain text without any HTML tags.',
                    format: 'text',
                    shouldPass: true
                }
            ];

            testCases.forEach(testCase => {
                const resultElement = document.getElementById(testCase.id);
                
                try {
                    // Simulate the security validation
                    const isValid = simulateSecurityCheck(testCase.content, testCase.format);
                    
                    if (isValid === testCase.shouldPass) {
                        resultElement.className = 'result success';
                        resultElement.textContent = `✅ Test passed: ${isValid ? 'Content approved' : 'Content blocked'} as expected`;
                    } else {
                        resultElement.className = 'result error';
                        resultElement.textContent = `❌ Test failed: Expected ${testCase.shouldPass ? 'approval' : 'blocking'}, got ${isValid ? 'approval' : 'blocking'}`;
                    }
                } catch (error) {
                    resultElement.className = 'result error';
                    resultElement.textContent = `❌ Test error: ${error.message}`;
                }
            });
        }

        function simulateSecurityCheck(content, format) {
            // Malicious patterns
            const maliciousPatterns = [
                /javascript:/i,
                /on\w+\s*=/i,
                /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
                /<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi,
                /data:\s*text\/html/i,
                /vbscript:|livescript:|mocha:|tcl:/i,
                /expression\s*\(/i,
                /<iframe\b[^>]*>/gi,
                /<form\b[^>]*>/gi
            ];

            // Check malicious patterns first
            for (const pattern of maliciousPatterns) {
                if (pattern.test(content)) {
                    return false;
                }
            }

            if (format === 'html') {
                // Safe HTML tags
                const safeTags = ['a', 'strong', 'b', 'em', 'i', 'u', 'span', 'mark', 'small', 'sup', 'sub'];
                const htmlTagMatches = content.match(/<[^>]*>/g);
                
                if (htmlTagMatches) {
                    for (const tagMatch of htmlTagMatches) {
                        const tagName = tagMatch.match(/<\/?(\w+)/)?.[1]?.toLowerCase();
                        if (!tagName || !safeTags.includes(tagName)) {
                            return false;
                        }
                        
                        // Check for dangerous attributes
                        if (/\s+(on\w+|javascript:|data:|vbscript:)/i.test(tagMatch)) {
                            return false;
                        }
                    }
                }
            } else {
                // Text format should not contain HTML
                if (/<[^>]*>/.test(content)) {
                    return false;
                }
            }

            return true;
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', testSecurityValidation);
    </script>
</body>
</html>