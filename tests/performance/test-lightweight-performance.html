<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lightweight Translation System Performance Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-controls {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056CC;
        }
        
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .stats-panel {
            background: #fff;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007AFF;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        
        .test-content {
            background: #fff;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 6px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #2196F3);
            transition: width 0.3s ease;
            width: 0%;
        }
        
        .log-panel {
            background: #1e1e1e;
            color: #fff;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .success { color: #4CAF50; }
        .error { color: #F44336; }
        .warning { color: #FF9800; }
        .info { color: #2196F3; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }
        
        .testing {
            animation: pulse 1.5s infinite;
        }
    </style>
</head>
<body>
    <h1>🚀 Lightweight Translation System Performance Test</h1>
    <p>This page tests the new lightweight translation system performance and success rate improvements.</p>
    
    <div class="test-controls">
        <h3>🎯 如何测试轻量化翻译系统：</h3>
        <p><strong>通过右键菜单测试：</strong></p>
        <ul style="text-align: left; margin-left: 20px;">
            <li><strong>选择文本 + 右键</strong> → "🧪 测试单个翻译 (轻量化)" - 测试选中文本的翻译</li>
            <li><strong>页面右键</strong> → "🧪 测试页面翻译 (轻量化)" - 测试整页翻译</li>
            <li><strong>页面右键</strong> → "📊 显示翻译统计" - 查看成功率统计</li>
            <li><strong>页面右键</strong> → "🧹 清除翻译内容" - 清除所有翻译</li>
            <li><strong>页面右键</strong> → "翻译网页" - 标准翻译功能（已升级为轻量化系统）</li>
        </ul>
        
        <p><strong>⚠️ 注意：</strong> 浏览器插件环境中，测试功能通过右键菜单触发，而不是下面的按钮。</p>
        
        <div style="background: #fff3cd; padding: 10px; border-radius: 5px; border-left: 4px solid #ffc107; margin: 10px 0;">
            <strong>🎯 目标验证：</strong> 将翻译成功率从 <span style="color: #dc3545;">40.5%</span> 提升到 <span style="color: #28a745;">85%+</span>
        </div>
    </div>
    
    <div class="stats-panel">
        <h3>Performance Statistics</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value" id="successRate">--%</div>
                <div class="stat-label">Success Rate</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="avgDuration">--ms</div>
                <div class="stat-label">Avg Duration</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="totalTests">0</div>
                <div class="stat-label">Total Tests</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="cacheHits">0</div>
                <div class="stat-label">Cache Hits</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="errors">0</div>
                <div class="stat-label">Errors</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="security">0</div>
                <div class="stat-label">Security Blocks</div>
            </div>
        </div>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
    </div>
    
    <div class="test-content">
        <h2>Test Content Sections</h2>
        
        <div class="test-section">
            <h3>Simple Text Section</h3>
            <p>Hello, World! This is a simple English sentence for testing basic translation functionality.</p>
            <p>The quick brown fox jumps over the lazy dog. This pangram contains every letter of the alphabet.</p>
        </div>
        
        <div class="test-section">
            <h3>Complex Text with HTML</h3>
            <p>This paragraph contains <strong>bold text</strong>, <em>italic text</em>, and <a href="#">links</a> to test HTML preservation.</p>
            <ul>
                <li>List item one with some content</li>
                <li>List item two with <span style="color: blue;">colored text</span></li>
                <li>List item three with multiple elements</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>Technical Content</h3>
            <p>JavaScript is a high-level, interpreted programming language that conforms to the ECMAScript specification.</p>
            <p>TypeScript extends JavaScript by adding static type definitions, making it easier to catch errors during development.</p>
            <blockquote>
                "The best way to learn programming is by writing programs." - Anonymous Developer
            </blockquote>
        </div>
        
        <div class="test-section">
            <h3>Mixed Content with Special Characters</h3>
            <p>Special characters: αβγδε, 中文测试, русский текст, العربية</p>
            <p>Numbers and symbols: 123-456-7890, $100.00, 50%, <EMAIL></p>
            <p>Code snippets: <code>function hello() { return "world"; }</code></p>
        </div>
        
        <div class="test-section">
            <h3>Long Form Content</h3>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
            <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
        </div>
        
        <div class="test-section">
            <h3>Data Attributes Test</h3>
            <p data-testid="element1" data-custom="value">This element has data attributes that should not trigger security blocks.</p>
            <div data-component="test" data-value="123">Element with multiple data attributes for regression testing.</div>
        </div>
    </div>
    
    <div class="log-panel" id="logPanel">
        <div class="info">🚀 Lightweight Translation System Test Console</div>
        <div class="info">Ready for testing. Click buttons above to start performance tests.</div>
    </div>

    <script>
        class PerformanceTestRunner {
            constructor() {
                this.testResults = [];
                this.isRunning = false;
                this.initializeEventListeners();
                this.log('info', '🎯 Performance Test Runner initialized');
            }

            initializeEventListeners() {
                document.getElementById('testSingleBtn').addEventListener('click', () => this.testSingleTranslation());
                document.getElementById('testBatchBtn').addEventListener('click', () => this.testBatchTranslation());
                document.getElementById('testPageBtn').addEventListener('click', () => this.testFullPageTranslation());
                document.getElementById('stressTestBtn').addEventListener('click', () => this.runStressTest());
                document.getElementById('clearBtn').addEventListener('click', () => this.clearTranslations());
                document.getElementById('showStatsBtn').addEventListener('click', () => this.showDetailedStats());
            }

            async testSingleTranslation() {
                if (this.isRunning) return;
                this.setRunning(true);
                
                this.log('info', '🔍 Testing single element translation...');
                
                try {
                    // Test with the first paragraph
                    const element = document.querySelector('.test-section p');
                    if (!element) {
                        throw new Error('No test element found');
                    }

                    const startTime = Date.now();
                    
                    // Use the lightweight translation system
                    if (typeof window.testLightweightTranslation === 'function') {
                        const result = await window.testLightweightTranslation(element.textContent);
                        const duration = Date.now() - startTime;
                        
                        this.recordResult({
                            type: 'single',
                            success: result.success,
                            duration: duration,
                            element: element.tagName,
                            textLength: element.textContent.length
                        });
                        
                        this.log('success', `✅ Single translation completed in ${duration}ms`);
                        this.log('info', `   Original: ${element.textContent.slice(0, 50)}...`);
                        this.log('info', `   Translated: ${result.translatedText?.slice(0, 50)}...`);
                    } else {
                        this.log('error', '❌ Lightweight translation system not available');
                        this.log('warning', '💡 Please ensure the extension is loaded and try again');
                    }
                } catch (error) {
                    this.log('error', `❌ Single translation failed: ${error.message}`);
                }
                
                this.setRunning(false);
                this.updateStats();
            }

            async testBatchTranslation() {
                if (this.isRunning) return;
                this.setRunning(true);
                
                this.log('info', '📦 Testing batch translation...');
                
                try {
                    const elements = document.querySelectorAll('.test-section p');
                    this.log('info', `   Found ${elements.length} elements to translate`);
                    
                    const startTime = Date.now();
                    let successCount = 0;
                    let totalDuration = 0;
                    
                    for (let i = 0; i < elements.length; i++) {
                        const element = elements[i];
                        const elementStart = Date.now();
                        
                        try {
                            if (typeof window.testLightweightTranslation === 'function') {
                                const result = await window.testLightweightTranslation(element.textContent);
                                const elementDuration = Date.now() - elementStart;
                                totalDuration += elementDuration;
                                
                                this.recordResult({
                                    type: 'batch',
                                    success: result.success,
                                    duration: elementDuration,
                                    element: element.tagName,
                                    textLength: element.textContent.length
                                });
                                
                                if (result.success) {
                                    successCount++;
                                    this.log('success', `   ✅ Element ${i + 1}/${elements.length} - ${elementDuration}ms`);
                                } else {
                                    this.log('error', `   ❌ Element ${i + 1}/${elements.length} failed`);
                                }
                            }
                        } catch (error) {
                            this.log('error', `   ❌ Element ${i + 1}/${elements.length} error: ${error.message}`);
                        }
                        
                        // Update progress
                        const progress = ((i + 1) / elements.length) * 100;
                        this.updateProgress(progress);
                    }
                    
                    const totalTime = Date.now() - startTime;
                    const successRate = (successCount / elements.length) * 100;
                    
                    this.log('success', `🎉 Batch translation completed`);
                    this.log('info', `   Success rate: ${successRate.toFixed(1)}%`);
                    this.log('info', `   Total time: ${totalTime}ms`);
                    this.log('info', `   Avg per element: ${Math.round(totalDuration / elements.length)}ms`);
                    
                } catch (error) {
                    this.log('error', `❌ Batch translation failed: ${error.message}`);
                }
                
                this.setRunning(false);
                this.updateStats();
                this.updateProgress(0);
            }

            async testFullPageTranslation() {
                if (this.isRunning) return;
                this.setRunning(true);
                
                this.log('info', '🌐 Testing full page translation...');
                
                try {
                    const startTime = Date.now();
                    
                    if (typeof window.testLightweightPageTranslation === 'function') {
                        const result = await window.testLightweightPageTranslation();
                        const duration = Date.now() - startTime;
                        
                        this.recordResult({
                            type: 'page',
                            success: result.successCount > 0,
                            duration: duration,
                            totalCount: result.totalCount,
                            successCount: result.successCount,
                            failureCount: result.failureCount
                        });
                        
                        const successRate = (result.successCount / result.totalCount) * 100;
                        
                        this.log('success', `🎉 Page translation completed in ${duration}ms`);
                        this.log('info', `   Elements processed: ${result.totalCount}`);
                        this.log('info', `   Successful: ${result.successCount}`);
                        this.log('info', `   Failed: ${result.failureCount}`);
                        this.log('info', `   Success rate: ${successRate.toFixed(1)}%`);
                        
                        if (successRate >= 85) {
                            this.log('success', '🏆 SUCCESS! Target 85%+ success rate achieved!');
                        } else {
                            this.log('warning', '⚠️  Success rate below 85% target');
                        }
                    } else {
                        this.log('error', '❌ Page translation function not available');
                    }
                    
                } catch (error) {
                    this.log('error', `❌ Page translation failed: ${error.message}`);
                }
                
                this.setRunning(false);
                this.updateStats();
            }

            async runStressTest() {
                if (this.isRunning) return;
                this.setRunning(true);
                
                this.log('info', '🔥 Running stress test (100 elements)...');
                
                try {
                    const testTexts = [
                        "Hello, World! Simple test message.",
                        "The quick brown fox jumps over the lazy dog.",
                        "JavaScript is a versatile programming language.",
                        "This is a longer sentence with more complex structure and multiple clauses.",
                        "Special characters: αβγδε, 中文, русский, العربية",
                        "Numbers and symbols: 123-456-7890, $100.00, 50%",
                        "Technical terms: API, HTTP, JSON, XML, CSS, HTML",
                        "Mixed content with <strong>HTML</strong> tags included",
                        "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                        "Performance testing with concurrent translation requests."
                    ];
                    
                    const startTime = Date.now();
                    let successCount = 0;
                    let totalDuration = 0;
                    
                    for (let i = 0; i < 100; i++) {
                        const text = testTexts[i % testTexts.length] + ` (Test ${i + 1})`;
                        const elementStart = Date.now();
                        
                        try {
                            if (typeof window.testLightweightTranslation === 'function') {
                                const result = await window.testLightweightTranslation(text);
                                const elementDuration = Date.now() - elementStart;
                                totalDuration += elementDuration;
                                
                                this.recordResult({
                                    type: 'stress',
                                    success: result.success,
                                    duration: elementDuration,
                                    textLength: text.length
                                });
                                
                                if (result.success) {
                                    successCount++;
                                }
                                
                                if (i % 10 === 9) {
                                    this.log('info', `   Progress: ${i + 1}/100 elements processed`);
                                }
                            }
                        } catch (error) {
                            this.log('error', `   ❌ Stress test element ${i + 1} failed: ${error.message}`);
                        }
                        
                        // Update progress
                        const progress = ((i + 1) / 100) * 100;
                        this.updateProgress(progress);
                        
                        // Small delay to prevent overwhelming the system
                        if (i % 10 === 9) {
                            await new Promise(resolve => setTimeout(resolve, 50));
                        }
                    }
                    
                    const totalTime = Date.now() - startTime;
                    const successRate = (successCount / 100) * 100;
                    const avgDuration = totalDuration / 100;
                    
                    this.log('success', `🔥 Stress test completed!`);
                    this.log('info', `   Total time: ${totalTime}ms`);
                    this.log('info', `   Success rate: ${successRate.toFixed(1)}%`);
                    this.log('info', `   Average duration: ${avgDuration.toFixed(1)}ms`);
                    this.log('info', `   Throughput: ${(100000 / totalTime).toFixed(1)} translations/sec`);
                    
                    if (successRate >= 85) {
                        this.log('success', '🏆 STRESS TEST PASSED! 85%+ success rate maintained under load!');
                    } else {
                        this.log('warning', '⚠️  Stress test: Success rate below 85% target');
                    }
                    
                } catch (error) {
                    this.log('error', `❌ Stress test failed: ${error.message}`);
                }
                
                this.setRunning(false);
                this.updateStats();
                this.updateProgress(0);
            }

            clearTranslations() {
                this.log('info', '🧹 Clearing translations...');
                
                try {
                    if (typeof window.clearLightweightTranslations === 'function') {
                        const clearedCount = window.clearLightweightTranslations();
                        this.log('success', `✅ Cleared ${clearedCount} translations`);
                    } else {
                        this.log('warning', '⚠️  Clear function not available');
                    }
                } catch (error) {
                    this.log('error', `❌ Clear failed: ${error.message}`);
                }
            }

            showDetailedStats() {
                this.log('info', '📊 Detailed Statistics:');
                
                try {
                    if (typeof window.getLightweightTranslationStats === 'function') {
                        const stats = window.getLightweightTranslationStats();
                        this.log('info', JSON.stringify(stats, null, 2));
                    } else {
                        this.log('warning', '⚠️  Stats function not available');
                    }
                } catch (error) {
                    this.log('error', `❌ Stats retrieval failed: ${error.message}`);
                }
                
                this.log('info', '📈 Local Test Results Summary:');
                if (this.testResults.length > 0) {
                    const successCount = this.testResults.filter(r => r.success).length;
                    const successRate = (successCount / this.testResults.length) * 100;
                    const avgDuration = this.testResults.reduce((sum, r) => sum + r.duration, 0) / this.testResults.length;
                    
                    this.log('info', `   Total tests: ${this.testResults.length}`);
                    this.log('info', `   Success rate: ${successRate.toFixed(1)}%`);
                    this.log('info', `   Average duration: ${avgDuration.toFixed(1)}ms`);
                } else {
                    this.log('info', '   No test results yet');
                }
            }

            recordResult(result) {
                this.testResults.push({
                    ...result,
                    timestamp: Date.now()
                });
            }

            updateStats() {
                if (this.testResults.length === 0) return;
                
                const successCount = this.testResults.filter(r => r.success).length;
                const successRate = (successCount / this.testResults.length) * 100;
                const avgDuration = this.testResults.reduce((sum, r) => sum + r.duration, 0) / this.testResults.length;
                const errorCount = this.testResults.filter(r => !r.success).length;
                
                document.getElementById('successRate').textContent = `${successRate.toFixed(1)}%`;
                document.getElementById('avgDuration').textContent = `${avgDuration.toFixed(0)}ms`;
                document.getElementById('totalTests').textContent = this.testResults.length;
                document.getElementById('errors').textContent = errorCount;
                
                // Update success rate color
                const successElement = document.getElementById('successRate');
                if (successRate >= 85) {
                    successElement.style.color = '#4CAF50';
                } else if (successRate >= 70) {
                    successElement.style.color = '#FF9800';
                } else {
                    successElement.style.color = '#F44336';
                }
            }

            updateProgress(percentage) {
                document.getElementById('progressFill').style.width = `${percentage}%`;
            }

            setRunning(running) {
                this.isRunning = running;
                const buttons = document.querySelectorAll('.test-button');
                buttons.forEach(btn => {
                    btn.disabled = running;
                    if (running) {
                        btn.classList.add('testing');
                    } else {
                        btn.classList.remove('testing');
                    }
                });
            }

            log(type, message) {
                const logPanel = document.getElementById('logPanel');
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = type;
                logEntry.textContent = `[${timestamp}] ${message}`;
                logPanel.appendChild(logEntry);
                logPanel.scrollTop = logPanel.scrollHeight;
            }
        }

        // Initialize the test runner when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new PerformanceTestRunner();
        });

        // Extension availability checker
        function checkExtensionAvailability() {
            const functions = [
                'testLightweightTranslation',
                'testLightweightPageTranslation', 
                'getLightweightTranslationStats',
                'clearLightweightTranslations'
            ];
            
            const available = functions.filter(fn => typeof window[fn] === 'function');
            const missing = functions.filter(fn => typeof window[fn] !== 'function');
            
            console.log('🔍 Extension Function Availability Check:');
            console.log('✅ Available:', available);
            console.log('❌ Missing:', missing);
            
            if (missing.length > 0) {
                console.log('💡 Tip: Please ensure the Lucid extension is loaded and refresh the page');
            }
            
            return available.length === functions.length;
        }

        // Run availability check after a short delay
        setTimeout(checkExtensionAvailability, 2000);
    </script>
</body>
</html>