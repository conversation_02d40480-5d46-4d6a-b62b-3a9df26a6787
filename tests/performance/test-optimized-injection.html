<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化后的翻译注入测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            border: 2px solid #e0e0e0;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
        }
        .controls {
            background: #f5f5f5;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .stats {
            background: #e8f5e8;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        .button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .button:hover {
            background: #005a87;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .lu-wrapper {
            border-left: 3px solid #007cba;
            padding-left: 10px;
            margin: 5px 0;
        }
        .lu-block {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>翻译注入优化测试页面</h1>
    
    <div class="controls">
        <h3>测试控制</h3>
        <button class="button" onclick="runOptimizedTest()">🚀 运行优化后的批量测试</button>
        <button class="button" onclick="adjustFailureRate(0)">🛡️ 禁用模拟失败</button>
        <button class="button" onclick="adjustFailureRate(0.05)">⚠️ 5%失败率</button>
        <button class="button" onclick="adjustFailureRate(0.1)">🔥 10%失败率</button>
        <button class="button" onclick="clearAllTests()">🧹 清理所有翻译</button>
        <button class="button" onclick="showStats()">📊 显示统计</button>
        <button class="button" onclick="checkPerformance()">⚡ 性能检查</button>
    </div>

    <div id="stats" class="stats" style="display: none;"></div>
    <div id="messages"></div>

    <div class="test-section">
        <h2>测试内容区域</h2>
        <p class="test-content">This is the first test paragraph that should be translated efficiently with the new optimized injection system.</p>
        <p class="test-content">Another paragraph with different content to test the batch processing capabilities and performance improvements.</p>
        <div class="test-content">A div element containing text that previously might have been blocked by security checks.</div>
        <h3 class="test-content">Test Heading with Multiple Words</h3>
        <button class="test-content">Button Text for Translation</button>
        <span class="test-content">Short span text</span>
        <blockquote class="test-content">A blockquote element that should now pass security validation.</blockquote>
    </div>

    <div class="test-section">
        <h2>复杂HTML内容测试</h2>
        <div class="test-content">
            <strong>Bold text</strong> with <em>emphasis</em> and <u>underline</u> should be handled properly.
        </div>
        <p class="test-content">
            <a href="#" class="test-link">This is a link</a> that should be reconstructed in the translation.
        </p>
        <ul>
            <li class="test-content">First list item for translation</li>
            <li class="test-content">Second list item with more text content</li>
            <li class="test-content">Third item to test batch processing</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>性能压力测试</h2>
        <div id="stress-test-content">
            <!-- 动态生成大量内容用于性能测试 -->
        </div>
        <button class="button" onclick="generateStressTestContent()">📈 生成压力测试内容</button>
        <button class="button" onclick="runStressTest()">🔥 运行压力测试</button>
    </div>

    <script type="module">
        // 导入必要的模块（在实际扩展中这些会被自动注入）
        console.log('🧪 测试页面已加载，等待扩展注入...');

        // 模拟扩展环境
        window.extensionTest = {
            mockTranslator: null,
            injector: null,
            performanceOptimizer: null
        };

        // 全局测试函数
        window.runOptimizedTest = async function() {
            showMessage('🚀 开始优化后的翻译测试...', 'info');
            
            const elements = document.querySelectorAll('.test-content');
            const startTime = performance.now();
            
            let successCount = 0;
            let failureCount = 0;
            
            try {
                // 模拟批量注入
                for (const element of elements) {
                    try {
                        await simulateOptimizedInjection(element);
                        successCount++;
                    } catch (error) {
                        failureCount++;
                        console.warn('注入失败:', error.message);
                    }
                }
                
                const duration = performance.now() - startTime;
                const successRate = (successCount / elements.length * 100).toFixed(1);
                
                showMessage(`✅ 测试完成！成功率: ${successRate}% (${successCount}/${elements.length})，耗时: ${duration.toFixed(2)}ms`, 'success');
                
                // 显示性能统计
                if (window.setMockFailureRate) {
                    const stats = window.mockTranslateStats ? window.mockTranslateStats() : {};
                    showStats({
                        ...stats,
                        testSuccessRate: successRate,
                        testDuration: duration.toFixed(2)
                    });
                }
                
            } catch (error) {
                showMessage(`❌ 测试失败: ${error.message}`, 'error');
            }
        };

        window.adjustFailureRate = function(rate) {
            if (window.setMockFailureRate) {
                window.setMockFailureRate(rate);
                showMessage(`🔧 模拟失败率已设置为 ${(rate * 100).toFixed(1)}%`, 'info');
            } else {
                showMessage('⚠️ Mock翻译服务未加载', 'warning');
            }
        };

        window.clearAllTests = function() {
            // 清理所有翻译内容
            const wrappers = document.querySelectorAll('.lu-wrapper');
            wrappers.forEach(wrapper => wrapper.remove());
            
            const translatedElements = document.querySelectorAll('[data-lu-translated]');
            translatedElements.forEach(element => {
                element.removeAttribute('data-lu-translated');
                element.removeAttribute('data-lu-timestamp');
                element.removeAttribute('data-lu-target-lang');
            });
            
            showMessage('🧹 所有翻译内容已清理', 'info');
        };

        window.showStats = function() {
            const statsDiv = document.getElementById('stats');
            if (window.mockTranslateStats) {
                const stats = window.mockTranslateStats();
                statsDiv.innerHTML = `
                    <h4>📊 翻译统计信息</h4>
                    <pre>${JSON.stringify(stats, null, 2)}</pre>
                `;
                statsDiv.style.display = 'block';
            } else {
                showMessage('⚠️ 统计信息不可用', 'warning');
            }
        };

        window.checkPerformance = function() {
            const translationElements = document.querySelectorAll('.lu-wrapper').length;
            const domNodes = document.querySelectorAll('*').length;
            const memoryUsage = performance.memory ? 
                (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2) : '不可用';
            
            showMessage(`⚡ 性能检查：翻译元素 ${translationElements} 个，DOM节点 ${domNodes} 个，内存使用 ${memoryUsage}MB`, 'info');
        };

        window.generateStressTestContent = function() {
            const container = document.getElementById('stress-test-content');
            let content = '';
            
            for (let i = 1; i <= 100; i++) {
                content += `<p class="stress-test-item">Stress test paragraph ${i} with some content that needs translation and performance testing.</p>`;
            }
            
            container.innerHTML = content;
            showMessage('📈 已生成100个测试段落', 'info');
        };

        window.runStressTest = async function() {
            const elements = document.querySelectorAll('.stress-test-item');
            if (elements.length === 0) {
                showMessage('⚠️ 请先生成压力测试内容', 'warning');
                return;
            }

            showMessage('🔥 开始压力测试...', 'info');
            const startTime = performance.now();
            
            let processed = 0;
            const total = elements.length;
            
            // 批量处理，每次10个
            const batchSize = 10;
            for (let i = 0; i < total; i += batchSize) {
                const batch = Array.from(elements).slice(i, i + batchSize);
                
                await Promise.all(batch.map(async (element) => {
                    try {
                        await simulateOptimizedInjection(element);
                        processed++;
                    } catch (error) {
                        console.warn('压力测试注入失败:', error.message);
                    }
                }));

                // 显示进度
                const progress = ((i + batchSize) / total * 100).toFixed(1);
                showMessage(`🔄 压力测试进度: ${progress}% (${processed}/${total})`, 'info');
                
                // 小延迟避免阻塞
                await new Promise(resolve => setTimeout(resolve, 10));
            }
            
            const duration = performance.now() - startTime;
            const successRate = (processed / total * 100).toFixed(1);
            
            showMessage(`🎯 压力测试完成！处理 ${total} 个元素，成功率 ${successRate}%，耗时 ${duration.toFixed(2)}ms`, 'success');
        };

        // 模拟优化后的注入过程
        async function simulateOptimizedInjection(element) {
            const text = element.textContent.trim();
            
            if (!text || text.length < 3) {
                throw new Error('文本太短，跳过翻译');
            }

            // 检查是否已翻译
            if (element.hasAttribute('data-lu-translated')) {
                throw new Error('已存在翻译');
            }

            // 模拟翻译过程
            let translation;
            if (window.mockTranslate) {
                try {
                    translation = await window.mockTranslate(text, 'zh-CN');
                } catch (error) {
                    throw new Error(`翻译失败: ${error.message}`);
                }
            } else {
                translation = `[模拟翻译] ${text}`;
            }

            // 模拟安全检查（优化后的版本）
            if (translation.length > 5000) {
                throw new Error('翻译内容过长');
            }

            // 创建翻译元素
            const wrapper = document.createElement('div');
            wrapper.className = 'notranslate lu-wrapper';
            wrapper.setAttribute('lang', 'zh-CN');

            const lineBreak = document.createElement('br');
            const translationBlock = document.createElement('div');
            translationBlock.className = 'notranslate lu-block lu-weak';
            translationBlock.textContent = translation;

            wrapper.appendChild(lineBreak);
            wrapper.appendChild(translationBlock);

            // 注入到DOM
            element.appendChild(wrapper);
            
            // 标记为已翻译
            element.setAttribute('data-lu-translated', 'true');
            element.setAttribute('data-lu-timestamp', Date.now().toString());
            element.setAttribute('data-lu-target-lang', 'zh-CN');

            return true;
        }

        function showMessage(message, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const messageElement = document.createElement('div');
            
            const className = type === 'success' ? 'success' : 
                            type === 'warning' ? 'warning' : 
                            type === 'error' ? 'warning' : 'stats';
            
            messageElement.className = className;
            messageElement.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            
            messagesDiv.insertBefore(messageElement, messagesDiv.firstChild);
            
            // 只保留最近10条消息
            while (messagesDiv.children.length > 10) {
                messagesDiv.removeChild(messagesDiv.lastChild);
            }
        }

        function showStats(stats) {
            const statsDiv = document.getElementById('stats');
            statsDiv.innerHTML = `
                <h4>📊 性能统计</h4>
                <pre>${JSON.stringify(stats, null, 2)}</pre>
            `;
            statsDiv.style.display = 'block';
        }

        // 等待扩展加载
        setTimeout(() => {
            if (typeof window.mockTranslate === 'function') {
                showMessage('✅ Mock翻译服务已就绪', 'success');
            } else {
                showMessage('⚠️ 等待扩展加载Mock翻译服务...', 'warning');
            }
        }, 1000);
    </script>
</body>
</html>