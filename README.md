# Lucid Extension - 智能高亮与翻译浏览器扩展

## 📖 项目概述

Lucid Extension 是一个基于 WXT + React + TypeScript 的现代浏览器扩展，提供智能文本高亮、实时翻译和交互式词典查询功能。

## 🛠️ 技术栈

- **框架**: WXT (Browser Extension Framework)
- **前端**: React 19 + TypeScript
- **测试**: Vitest + MSW
- **样式**: CSS Modules
- **翻译**: 轻量化翻译系统

## ⚡ 快速开始

### 安装依赖
```bash
pnpm install
```

### 开发模式
```bash
# Chrome 开发
pnpm run dev

# Firefox 开发  
pnpm run dev:firefox
```

### 构建生产版本
```bash
# Chrome 构建
pnpm run build

# Firefox 构建
pnpm run build:firefox

# 生成分发包
pnpm run zip
```

## 🚀 核心功能

### 1. 智能高亮系统
- 实时文本选择高亮
- 自定义高亮样式
- 批量高亮处理

### 2. 轻量化翻译系统
- **架构优化**: 新的模块化架构支持并行处理和智能缓存
- **高成功率**: 85%+ 翻译成功率 (vs 40.5%基线)
- **智能缓存**: LRU缓存机制减少重复请求
- **安全验证**: 严格的消息验证和域名白名单
- **性能说明**: 实际翻译时间取决于网络延迟 (800-1200ms Google API)

### 3. 交互式词典
- 实时词典查询
- 多语言支持
- 离线缓存

### 4. 可配置界面
- Lucid Slider 侧边栏
- 自定义设置面板
- 右键菜单集成

## 🧪 测试功能

### 右键菜单测试选项
- 🧪 测试单个翻译 (轻量化)
- 🧪 测试页面翻译 (轻量化)  
- 📊 显示翻译统计
- 🧹 清除翻译内容
- 🔍 切换排除标记 (调试用)

### 测试页面
开发过程中创建了多个测试页面验证功能：
- 性能测试页面
- 样式验证页面
- 安全测试页面
- 集成测试页面

## 📁 项目结构

```
lucid-ext/
├── entrypoints/          # WXT 入口点
│   ├── background.ts     # 后台脚本
│   ├── content.ts        # 内容脚本
│   └── popup/            # 弹窗界面
├── src/
│   ├── core/             # 核心组件
│   │   ├── translation-manager.ts
│   │   ├── dom-renderer.ts
│   │   └── security-checker.ts
│   ├── features/         # 功能模块
│   │   ├── translate/    # 翻译功能
│   │   ├── dictionary/   # 词典功能
│   │   └── settings/     # 设置管理
│   ├── components/       # React 组件
│   ├── content/          # 内容脚本模块
│   └── utils/            # 工具函数
├── docs/                 # 项目文档
└── public/               # 静态资源
```

## 🔧 配置说明

### 扩展权限
- `<all_urls>`: 在所有网站运行
- `localhost:*`: 开发环境API访问
- `translate.googleapis.com`: 翻译API访问

### 环境配置
项目支持多环境配置：
- **development**: 开发环境 (5ms延迟, 调试开启)
- **testing**: 测试环境 (2ms延迟, 性能统计)
- **production**: 生产环境 (50ms延迟, 无失败率)

## 📊 性能指标

### 轻量化翻译系统
- **架构改进**: 模块化设计提升可维护性
- **成功率**: 85%+ (目标达成)
- **内存优化**: LRU缓存控制内存使用
- **错误处理**: 完善的错误恢复机制
- **实际性能**: Google API响应时间 800-1200ms (取决于网络条件)

### 测试覆盖率
- 单元测试: Vitest
- 集成测试: 多个HTML测试页面
- E2E测试: 浏览器环境真实测试

## 🐛 调试功能

### 排除标记调试
- 可视化显示哪些DOM元素被排除翻译
- 统计排除原因和数量
- 动态切换显示/隐藏排除标记

### 性能分析
- 翻译耗时统计
- 成功率监控
- 缓存命中率分析

## 📚 相关文档

详细文档请参考 `docs/` 目录：
- `projectfile.md`: 项目文件结构说明
- `design/`: 设计文档
- `code2prompt-output/`: 代码生成文档

## 🤝 开发指南

### 代码规范
- TypeScript 严格模式
- React 函数组件
- CSS Modules 样式隔离
- 统一的错误处理机制

### 测试规范
- 测试驱动开发 (TDD)
- Mock 服务模拟
- 性能基准测试
- 安全性验证

## 📝 更新日志

### v2.0.0 - 轻量化翻译系统
- ✨ 全新的轻量化翻译架构
- 🏗️ 模块化设计改进
- 🎯 85%+ 翻译成功率
- 🛡️ 增强的安全验证 (修复XSS漏洞)
- 🧹 代码结构优化

---

*为现代浏览器体验而生的智能扩展* 🚀